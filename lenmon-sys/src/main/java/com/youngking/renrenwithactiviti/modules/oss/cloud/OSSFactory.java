/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.renrenwithactiviti.modules.oss.cloud;



import com.youngking.lenmoncore.common.utils.ConfigConstant;
import com.youngking.lenmoncore.common.utils.SpringContextUtils;
import com.youngking.renrenwithactiviti.modules.sys.service.SysConfigService;

/**
 * 文件上传Factory
 *
 * @<NAME_EMAIL>
 */
public final class OSSFactory {
    private static SysConfigService sysConfigService;

    static {
        OSSFactory.sysConfigService = (SysConfigService) SpringContextUtils.getBean("sysConfigService");
    }

    public static CloudStorageService build() {
        //获取云存储配置信息
        CloudStorageConfig config = sysConfigService.getConfigObject(ConfigConstant.CLOUD_STORAGE_CONFIG_KEY, CloudStorageConfig.class);

//        if (config.getType() == Constant.CloudService.QINIU.getValue()) {
//            return new QiniuCloudStorageService(config);
//        } else if (config.getType() == Constant.CloudService.ALIYUN.getValue()) {
            return new AliyunCloudStorageService(config);
//        } else if (config.getType() == Constant.CloudService.QCLOUD.getValue()) {
//            return new QcloudCloudStorageService(config);
//        }

//        return null;
    }

}
