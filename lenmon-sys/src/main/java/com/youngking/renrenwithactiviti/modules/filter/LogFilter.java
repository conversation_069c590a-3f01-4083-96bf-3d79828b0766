package com.youngking.renrenwithactiviti.modules.filter;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.filter.util.ReqUtil;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysLogEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserTokenEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.ShiroService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by zhangyibo on 2020-04-14 17:19
 */

@Order(1)
@WebFilter(filterName = "logFilter", urlPatterns = "/**")
@Component
@Slf4j
public class LogFilter implements Filter {

    @Autowired
    private ShiroService shiroService;
    @Autowired
    private SysLogService sysLogService;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        log.info("***************************log filter");
        HttpServletRequest httprequest = (HttpServletRequest) servletRequest;
        RequestWrapper request = new RequestWrapper((HttpServletRequest) servletRequest);
        log.info("request uri: {}", request.getRequestURI());
        String params = "";
        try {
            String token = getRequestToken(request);
            Map parameterMap = request.getParameterMap();
            Map reqMap = new HashMap();
            JsonObject jsonObj = new JsonObject();
            Set<Map.Entry<String, String[]>> entry = parameterMap.entrySet();
            Iterator<Map.Entry<String, String[]>> it = entry.iterator();
            while (it.hasNext()) {
                Map.Entry<String, String[]> me = it.next();
                String key = me.getKey();
                String value = me.getValue()[0];
                reqMap.put(key, value);
                jsonObj.addProperty(key, value);
            }
            String jsonBody = ReqUtil.getRequestBody(request);
            params = jsonObj.toString() + "-" + jsonBody;
            log.info("request uri: {}, request body: {}, header: {}", request.getRequestURI(), params, JSONObject.toJSONString(request.getAllHeadersWithExclude("cookie", "token")));
            String username = null;
            Long userId = null;
            SysUserTokenEntity tokenEntity = null;
            if (StringUtils.isNotBlank(token)) {
                // 存在token
                tokenEntity = shiroService.queryByToken(token);
                userId = tokenEntity == null ? null : tokenEntity.getUserId();
                SysUserEntity user = shiroService.queryUser(userId);
                username = user == null ? null : user.getUsername();
            }
            SysLogEntity sysLog = new SysLogEntity();
            sysLog.setUsername(username);

            String ip = httprequest.getHeader("X-Forwarded-For");
            log.info("真实ip: {}", ip);
            if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                sysLog.setIp(request.getRemoteAddr());
            } else {
                sysLog.setIp(ip);
            }
            // sysLog.setIp(request.getRemoteAddr());
            sysLog.setParams(params);
            sysLog.setMethod(request.getRequestURI());
            sysLog.setCreateDate(new Date());
            sysLogService.save(sysLog);
        } catch (Exception e) {
            log.error("log-interceptor error {}", request.getRemoteAddr(), e);
        }
        if (httprequest != null
                && httprequest.getHeader("accept") != null
                && httprequest.getHeader("accept").contains(MediaType.APPLICATION_JSON_VALUE)) {
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper((HttpServletResponse) response);
            chain.doFilter(request, responseWrapper);
            String responseBody = new String(responseWrapper.getContentAsByteArray());
            try {
                if (StringUtils.isNotBlank(responseBody) && responseBody.indexOf("{") == 0) {
                    JSONObject jsonObj = JSONObject.parseObject(responseBody);
                    JSONObject newJson = new JSONObject();
                    if (responseBody.length() > 800) {
                        newJson.put("code", jsonObj.get("code"));
                        newJson.put("msg", jsonObj.get("msg"));
                    } else {
                        newJson = jsonObj;
                    }

                    log.info("request uri: {} request body: {} request headers: {} response body: {}",
                            request.getRequestURI(), params, JSONObject.toJSONString(request.getAllHeadersWithExclude("cookie", "token")), newJson);
                }
            } catch (Exception e) {
                log.info("response body parse failure body ! {}", responseBody);
                log.info("parse failure to exception", e);
            }

            responseWrapper.copyBodyToResponse();
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
    }

    /**
     * 获取请求的token
     */
    private String getRequestToken(HttpServletRequest httpRequest) {
        // 从header中获取token
        String token = httpRequest.getHeader("token");

        // 如果header中不存在token，则从参数中获取token
        if (org.apache.commons.lang.StringUtils.isBlank(token)) {
            token = httpRequest.getParameter("token");
        }

        return token;
    }
}
