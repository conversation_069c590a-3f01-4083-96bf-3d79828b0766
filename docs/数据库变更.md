## 激励 

```sql
alter table worder_pm_stimulate add first_audit_user varchar(50) null comment '一次审核人';
alter table worder_pm_stimulate add first_audit_time datetime null comment '一审时间';
alter table worder_pm_stimulate add second_audit_user varchar(50) null comment '二次审核人';
alter table worder_pm_stimulate add second_audit_time datetime null comment '二审时间';

-- 历史数据处理

```

## 比亚迪5.5 接口

1. 维护工单模板增加新字段

2. 新建表 `notify_msg`

## 老系统背书转让银票

```SQL
INSERT INTO sys_dictionary_detail (dictionary_id, detail_number, detail_name, remark, identification)
VALUES ((select id from sys_dictionary where dic_number = 'pay_form'), 'BSYP', '背书转让银票', '', 0);


```

## 不结算原因

```sql
alter table worder_information_sub
    modify electricity_state int(2) null comment '是否需要电力报桩：0：否 ，1：是' after dot_count_state;

alter table worder_information_sub
    add no_settlement_reason varchar(255) null comment '不结算原因' after electricity_state;

alter table worder_information_sub
    add no_settlement_remark varchar(255) null comment '不结算备注' after electricity_state;

alter table worder_information_sub
    add update_time datetime default current_timestamp null on update current_timestamp;

alter table worder_information_sub
    modify insert_time datetime null comment '创建时间';

DELETE FROM worder_information_sub
WHERE id NOT IN (
    SELECT id
    FROM (
        SELECT id
        FROM worder_information_sub AS t1
        WHERE t1.insert_time = (
            SELECT MAX(t2.insert_time)
            FROM worder_information_sub AS t2
            WHERE t2.worder_id = t1.worder_id
        )
    ) AS latest
);

drop index worderIdIdx on worder_information_sub;

create unique index worderIdUidx
    on worder_information_sub (worder_id);
```