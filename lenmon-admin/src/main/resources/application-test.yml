spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************
      username: root
      password: G5#df87rt!qw
      #      url: *****************************************************************************************************************************************************************************************
      #      username: bonc
      #      password: Bonc1234*
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 90
      #关闭abanded连接时输出错误日志
      log-abandoned: true
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: *************
    port: 6379
    password: rrsadmin   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 1000      # 连接池中的最大空闲连接
        min-idle: 200       # 连接池中的最小空闲连接
dataType: mysql
##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.mysql.jdbc.Driver
      url: *****************************************************************************************************************************************
      username: root
      password: G5#df87rt!qw
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 1800
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456

logging:
  path: springlog
  level:
    com.bonc.rrs.worder.dao: debug
    com.bonc.rrs.worderapp.dao: debug
    com.bonc.rrs.workManager.dao: debug
    com.bonc.rrs.worderinformationaccount.dao: debug
    com.bonc.rrs.branchbalance.dao.BranchBalanceDao: debug
    com.bonc.rrs.baidumap.dao.*: debug
    org.apache.http: debug
    org.springframework.web.client: debug
    org.springframework.web.client.RestTemplate: debug


send-msg:
  enable: false

ip:
  address: ************,*************,************

oss:
  bucketName: sh-rrs
baseUrl: https://uat.rrskjfw.com.cn/
# 360额度
quotawsdl: TransEffectiveLimitFromCBSTo360.wsdl
quotalocation: classpath:TransEffectiveLimitFromCBSTo360.wsdl
# sap余额
sapwsdl: TransSupplierBalanceFromEVSToSAP.wsdl
saplocation: classpath:TransSupplierBalanceFromEVSToSAP.wsdl
# 金税
headwsdl: InsertLSMallZssHead.wsdl
headlocation: classpath:InsertLSMallZssHead.wsdl
itemwsdl: InsertLSMallZssItemNew.wsdl
itemlocation: classpath:InsertLSMallZssItemNew.wsdl
zsszfwsdl: InsertXwsqZssZF.wsdl
zsszflocation: classpath:InsertXwsqZssZF.wsdl
cacelwsdl: QueryCancelInterface.wsdl
cacelocation: classpath:QueryCancelInterface.wsdl
caceltable: qdtest.xwsq_zzs_zfhx
table: qdtest.xwsq_zzs_kphx
#ACS
acsurl: http://************:9001/12CTEST/EAI/RoutingProxyService/EAI_REST_POST_ServiceRoot?INT_CODE=EAI_INT_2552
#商户通
busicvp: http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl
hcspcvp: http://cvp.hoptest.haier.net/services/hcsp/HCSP_Sheet_Srv?wsdl
#海尔能力平台接口配置
hscapi:
  application:
    #系统编码
    key: 5f3e05613722cc046cfae6dbd3922743
    #系统秘钥
    secret: 5a00de01bebda4332d4bebccbfc6f6ca
  #票税云
  blade-seller-business:
    sellerinvoice:
      #票税云开票申请
      setXwsqZzsHeadVO:
        url: http://hscapi.haier.net/blade-seller-business/seller-apply-master/setXwsqZzsHeadVOCs
        methodName: setXwsqZzsHeadVO
      #票税云开票结果查询
      getSellerInvoice:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/getSellerInvoiceCs
        methodName: getSellerInvoice
      #税票云凭证归档
      setXwsqZzsArchive:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/setXwsqZzsArchiveCs
        methodName: setXwsqZzsArchive


finance:
  business:
    appId: news
    key: XA12#Da
    insertOrUpdateUrl: http://47.104.102.3:8001/finance/carMergerOrder/insertOrUpdate
    deleteOrderUrl: http://47.104.102.3:8001/finance/carMergerOrder/deleteOrder
    transferSubOrderUrl: http://47.104.102.3:8001/finance/details/transferSubOrder
    detailsEditUrl: http://47.104.102.3:8001/finance/details/edit
    invoiceEditUrl: http://47.104.102.3:8001/finance/headers/invoiceEdit
    searchOrderUrl: http://47.104.102.3:8001/finance/carMergerOrder/selectOrder
    transferApprovalFormUrl: http://47.104.102.3:8001/finance/approval/transferApprovalForm
    selectSettlementStatusUrl: http://47.104.102.3:8001/lfs/hcsp/selectSettlementStatus
    settlementCancelUrl: http://47.104.102.3:8001/lfs/hcsp/SettlementCancel
    newsDataAccessUrl: http://47.104.102.3:8001/lfs/hcsp/newsDataAccess
    settleAuditAddUrl: http://47.104.102.3:8001/settleAudit/add
    advAuditAddUrl: http://47.104.102.3:8001/lfs/advAudit/add
    lfsSettleAuditAddUrl: http://47.104.102.3:8001/lfs/settleAudit/add
    acAddUrl: http://47.104.102.3:8001/finance/ac/add
    newAdvanceSettlementUrl: http://47.104.102.3:8001/fncSettleAuditNew/newAdvanceSettlement
    callback:
      appId: finance
      appSecret: hrlLLGEaShHP6yE72he1l45T8jj7SVuU
storage:
  business:
    appId: storage
    secret: hZur4Kac
    leaveStoreUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/leaveStoreIntf
    handleOutCompleteUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleOutCompleteIntf
    handleInvalidUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleInvalidIntf
byd:
  url: http://weixin109.bydauto.com.cn:18081
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs
xk:
  url: https://xkdg.test.pc.rrskjfw.com.cn/prod-api
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs

# 长安接口地址  测试域名前缀
cacfg:
  ACC_KEY: BU02J7E85SPSLBKYNQZ5
  SECRE_KEY: AUD4CDLAZZVB5YA1R6FEWFX45JTT7WN2
  SUPPLIER_CODE: *********
  baseUrl: https://cqtest.yonyouauto.com/gateway/api/
  #  正式域名前缀  https://dtd.changan.com.cn/gateway/api
  ##车企离线对接
bridgehub:
  jlUrl: https://sp-recharge.geely.com/InstallManage/orderDetail/
  url: http://************:8080/bridgehub/
  token: aaaaaa
  paramToken: 1053ce1c42e74370a77671dfe2d37cac

honeywell:
  # prod: https://dms-api.hopeful.company
  # test: http://hopeful-api.proxy.pinlor.cn
  baseUrl: http://hopeful-api.proxy.pinlor.cn
  client_id: dmj_system
  client_secret: x93hzuAXBR56Xfnv1
  grant_type: client_credentials password_sms
  scope: read

news:
  gace:
    app-id: DMJ
    app-secret: DMJ@202410150001123a
    base-url: https://srm-test.gace.com.cn:8022/ierp
    user: 12341111234
    id-number: HERRS
    page-size: 20
    company-id: 684
    query-hours: 2
    cancel-days: 1
    audit-days: 30
    field:
      woop-id: 1988
      asset-id: 1989
      woop-no: 1990
      act-finish-time: 1197
      act-check-time: 1062
      fault-tree-type: 2033
    tel: 400-9219898
    template-ids:
      - 713
      - 714
      - 715
      - 716
      - 717
    woop-wcp-id-number: DMJ
    repair-template-id: 378
    amap-param:
      key: 9d340f3139468618eb6819ff367d0f58
      url: https://restapi.amap.com/v3/geocode/geo
  lx:
    report-temp-path: /mnt2/file/
#    report-temp-path: /Users/<USER>/temp/

changcheng:
  brandId: 111
  persistentCcToken: W08Qnd1iiHzYucKiYvZLvYJI7ydkTfeb

# API接口安全配置
api:
  security:
    # 是否启用接口安全认证
    enabled: true
    # 客户端密钥过期时间（小时）
    token-expire: 24
    # 客户端密钥配置
    clients:
      # 默认测试客户端
      test_client_id: 123456
      # 企业内部系统客户端 随机密钥
      warranty_card_id: 111111
