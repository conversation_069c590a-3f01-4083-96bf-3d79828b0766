### 演示POST请求
POST {{baseUrl}}sys/login
Content-Type: application/json

{
  "username": "kf1",
  "password": "123"
}

> {% client.global.set("auth_token", response.body.token); %}

### 演示GET请求
GET {{baseUrl}}worder/worderinformation/list
token: {{auth_token}}

###
POST {{baseUrl}}worder/bizattendant/save
token: {{auth_token}}
Content-Type: application/json

{
  "name": "服务兵2",
  "gender": "0",
  "contact": "15527821361",
  "idCard": "532121199312121235",
  "branchId": 8,
  "electricianCertificate": "/20190929/2da6af4390c24085a68dbc9c1f6e34ee.jpeg",
  "idCardImg": "/20190929/6539e59973074a0f87027584ccbe8fe5.jpg",
  "idCardImgBack": "/20190929/e0e94da29cdd46008b329341cf91bf1c.jpg"
}

###

POST {{baseUrl}}worder/worderinformation/update
token: {{auth_token}}
Content-Type: application/json

{
  "worderId": 33,
  "worderType": 1,
  "worderNo": "SSH2019-09-300012",
  "worderSourceType": 0,
  "worderSourceId": null,
  "worderStatus": 3,
  "createTime": "2019-09-30 11:22:45",
  "modifyTime": "2019-09-30 11:22:45",
  "isDelete": 0,
  "worderTypeValue": "勘测工单",
  "worderSourceTypeValue": "个人",
  "worderStatusValue": "新建",
  "chargeStandard": "3.6智能壁挂",
  "sellShop": "小鹏服务9",
  "chargeModel": "AYA3AC0201003",
  "carBrand": "小鹏",
  "chargeCode": "AY02A000018111428",
  "buyDate": "2019-09-04 00:00:00",
  "carModel": "豪华版",
  "carVin": "WE45565",
  "factoryLinkMan": "罗广湖",
  "chargeCd": "RFG090",
  "linkManPhone": "13098765129",
  "userName": "李嘉信9",
  "userPhone": "13099018809",
  "parkTypeValue": "地下",
  "parkType": 0,
  "userCertificate": "6531119808129876",
  "electricType": 0,
  "parkingTypeValue": "固定车位",
  "parkingType": 0,
  "installNum": 1,
  "address": "1_32_376_栖云路1344号",
  "getDate": "2019-09-30 00:00:00",
  "addressDup": "北京市_市辖区_东城区_栖云路1344号",
  "electricTypeValue": "物业电",
  "files": "/20190930/6b172e76672844688d923dc37ff66b79.jpg",
  "candidate": "null",
  "createBy": 7,
  "updateBy": 7,
  "candidatePm": null,
  "candidateBranch": null,
  "candidateAttendant": null,
  "procInstId": "37501",
  "createByName": "kf1",
  "updateByName": "kf1"
}

###