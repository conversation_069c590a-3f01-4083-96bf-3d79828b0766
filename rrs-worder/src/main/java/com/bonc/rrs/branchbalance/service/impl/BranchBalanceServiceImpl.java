package com.bonc.rrs.branchbalance.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.attribute.dao.WorderInformationAttributeMapper;
import com.bonc.rrs.attribute.entity.WorderInformationAttribute;
import com.bonc.rrs.balanceprocess.dao.*;
import com.bonc.rrs.balanceprocess.entity.*;
import com.bonc.rrs.balancerule.dao.BalanceRuleDao;
import com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity;
import com.bonc.rrs.balancerule.entity.BalanceRuleEntity;
import com.bonc.rrs.branchbalance.dao.BranchBalanceDao;
import com.bonc.rrs.branchbalance.entity.*;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.branchbalance.service.WorderBalanceFeeDetailService;
import com.bonc.rrs.branchbalance.service.WorderUsedMaterielService;
import com.bonc.rrs.branchbalance.vo.BranchBalanceQueryVO;
import com.bonc.rrs.branchbalance.vo.WorderUserBalanceFeeDetail;
import com.bonc.rrs.branchbalance.vo.WorderUserBalanceFeeVO;
import com.bonc.rrs.efficiencyarearule.dao.EfficiencyAreaRuleMapper;
import com.bonc.rrs.efficiencyarearule.entity.EfficiencyAreaRuleEntity;
import com.bonc.rrs.pay.entity.entity.WorderImportPayDto;
import com.bonc.rrs.suite.dao.SuiteDetailDao;
import com.bonc.rrs.suite.entity.SuiteDetailEntity;
import com.bonc.rrs.util.BalanceRuleUtils;
import com.bonc.rrs.worder.dao.*;
import com.bonc.rrs.worder.dto.dto.MaterielInformationDto;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.MaterielInforService;
import com.bonc.rrs.worder.service.WorderAdvancePublishService;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worderapp.service.WorderOperationRecodeService;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import com.bonc.rrs.worderinvoice.dao.WorderWaitAccountDao;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.workManager.dao.DotInfoMapper;
import com.bonc.rrs.workManager.entity.dto.DotInfoDto;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDicEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.ExcuteSqlService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liqingchao on 2020/1/20.
 */
@Service("branchBalanceService")
@Slf4j
public class BranchBalanceServiceImpl extends ServiceImpl<BranchBalanceDao,BranchBalanceViewEntity> implements BranchBalanceService {
    @Autowired(required = false)
    private BalanceRuleDao balanceRuleDao;
    @Autowired(required = false)
    private SuiteDetailDao suiteDetailDao;
    @Autowired(required = false)
    private WorderBalanceFeeDetailService worderBalanceFeeDetailService;
    @Autowired(required = false)
    private BalanceProperties balanceProperties;
    @Autowired(required = false)
    private SysDictionaryService sysDictionaryService;
    @Autowired(required = false)
    private SysDictionaryDetailService sysDictionaryDetailService;
    @Autowired(required = false)
    private DotInfoMapper dotInfoMapper;
    @Autowired(required = false)
    BalancePublishDao balancePublishDao;
    @Autowired(required = false)
    ExcuteSqlService excuteSqlService;
    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    CompanyInvoiceDao companyInvoiceDao;
    @Autowired
    private WorderTemplateDao worderTemplateDao;
    @Autowired
    private WorderExtFieldDao worderExtFieldDao;
    @Autowired
    private WorderExtFieldService worderExtFieldService;
    @Autowired
    private WorderWaitAccountDao worderWaitAccountDao;
    @Autowired
    private CompanyReceivableDao companyReceivableDao;
    @Autowired
    WorderOperationRecodeService worderOperationRecodeService;

    @Autowired
    private WorderAdvancePublishService worderAdvancePublishService;

    @Autowired
    MaterielInforService materielInforService;

    @Autowired
    SysDictionaryDetailDao sysDictionaryDetailDao;
    @Autowired(required = false)
    private MaterialInforDao materialInforDao;

    @Autowired(required = false)
    private MaterielInformationDao materielInformationDao;

    @Autowired
    WorderUsedMaterielService worderUsedMaterielService;

    @Autowired(required = false)
    private EfficiencyAreaRuleMapper efficiencyAreaRuleMapper;

    @Autowired(required = false)
    private WorderInformationAttributeMapper worderInformationAttributeMapper;

    /**
     * 网点查询结算工单
     * @param branchBalanceQueryVO
     * @return
     */
    @Override
    public PageUtils branchQuery(BranchBalanceQueryVO branchBalanceQueryVO) {
        // 查询当前账号(网点管理员)所在网点
        SysUserEntity currUser = (SysUserEntity)SecurityUtils.getSubject().getPrincipal();
        List<DotInfoDto> dotInfos = dotInfoMapper.getDotByCurrentUser(currUser.getUserId().intValue());
        Integer dotId = null;
        if(dotInfos.size() > IntegerEnum.ZERO.getValue()){
            DotInfoDto dotInfo = dotInfos.get(IntegerEnum.ZERO.getValue());
            dotId = dotInfo != null ? dotInfo.getDotId() : null;
        }

        JSONObject jsonObject = getIds();

        Map<String, Object> params = JSONObject.parseObject(JSON.toJSONString(branchBalanceQueryVO));
        Integer pageNum = branchBalanceQueryVO.getPageNum();
        Integer pageSize = branchBalanceQueryVO.getPageSize();
        Integer startRow = (pageNum - 1) * pageSize;
        params.put("startRow", startRow);
        params.put("limit", pageSize);
        params.put("dotId", dotId);
        params.put("worderIds", jsonObject.getString("worderIds"));
        params.put("increIds", jsonObject.getString("increIds"));
        params.put("stimulateIds", jsonObject.getString("stimulateIds"));
        Integer count = baseMapper.queryBranchBalanceViewCount(params);
        List<BranchBalanceViewEntity> list = baseMapper.queryBranchBalanceViewData(params);
        return new PageUtils(list, count, pageSize, pageNum);
    }


    public JSONObject getIds(){
        JSONObject jsonObject = new JSONObject();
        // 查出所有已经发布的发布单
        List<BalancePublishEntity> balancePublishList = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>()
                .eq("status", 5));
        String worderIds = null;
        String increIds = null;
        String stimulateIds = null;
        for (BalancePublishEntity balancePublish : balancePublishList) {
            if(StringUtils.isNotBlank(worderIds)){
                if(StringUtils.isNotBlank(balancePublish.getWorderIds())){
                    worderIds = worderIds + "," + balancePublish.getWorderIds();
                }
            }else{
                worderIds = balancePublish.getWorderIds();
            }
            if(StringUtils.isNotBlank(increIds)){
                if(StringUtils.isNotBlank(balancePublish.getIncreIds())){
                    increIds = increIds + "," + balancePublish.getIncreIds();
                }
            }else{
                increIds = balancePublish.getIncreIds();
            }
            if(StringUtils.isNotBlank(stimulateIds)){
                if(StringUtils.isNotBlank(balancePublish.getStimulateIds())){
                    stimulateIds = stimulateIds + "," + balancePublish.getStimulateIds();
                }
            }else{
                stimulateIds = balancePublish.getStimulateIds();
            }
        }
        worderIds = "(" + (StringUtils.isNotBlank(worderIds) ? worderIds : null) + ")";
        increIds = "(" + (StringUtils.isNotBlank(increIds) ? increIds : null) + ")";
        stimulateIds = "(" + (StringUtils.isNotBlank(stimulateIds) ? stimulateIds : null) + ")";
        jsonObject.put("worderIds", worderIds);
        jsonObject.put("increIds", increIds);
        jsonObject.put("stimulateIds", stimulateIds);
        return jsonObject;
    }

    /**
     * 查询工单详情
     * @param worderId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorderDetailEntity queryWorderDetail(Integer worderId) {
        WorderDetailEntity entity = baseMapper.queryWorderDetail(worderId);
        //查询并整理结算详细列表
        List<WorderBalanceFeeDetailEntity> balanceList = worderBalanceFeeDetailService.listBalanceFeeDetailByWorderId(worderId);
        List<WorderPmStimulateEntity> dotStimulateEntity = worderBalanceFeeDetailService.getStimulateList(worderId,"10");//网点激励列表
        List<WorderBalanceFeeDetailEntity> companyBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> dotBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> dotIncreBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> userIncreBalanceList = new ArrayList<>();
        if (!dotStimulateEntity.isEmpty()&&dotStimulateEntity.size()>0){
            BigDecimal stimulateFee = new BigDecimal(0);
            for (WorderPmStimulateEntity worderPmStimulateEntity : dotStimulateEntity) {
                stimulateFee = stimulateFee.add(worderPmStimulateEntity.getStimulateFee());
            }
            entity.setDotStimulateBalanceFee(stimulateFee);
        }
        for (WorderBalanceFeeDetailEntity e : balanceList) {
            if(0 == e.getBalanceTarget()){
                companyBalanceList.add(e);
            }else if(1 == e.getBalanceTarget() && (0 == e.getBalanceType()||2 == e.getBalanceType())){
                dotBalanceList.add(e);
            }else if(1 == e.getBalanceTarget() && 1 == e.getBalanceType()) {
                if(StringUtils.isEmpty(e.getMaterielName())){
                    e.setMaterielName(e.getDescription());
                }
                dotIncreBalanceList.add(e);
            }else if(2 == e.getBalanceTarget() && 1 == e.getBalanceType()) {
                userIncreBalanceList.add(e);
            }
        }
        entity.setDotStimulateEntity(dotStimulateEntity);
        entity.setCompanyBalanceList(companyBalanceList);
        entity.setDotBalanceList(dotBalanceList);
        entity.setDotIncreBalanceList(dotIncreBalanceList);
        entity.setUserIncreBalanceList(userIncreBalanceList);
        return entity;
    }

    /**
     * 查询全流程工单详情
     * @param worderId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorderDetailEntity queryworderFlowDetail(Integer worderId) {
        WorderDetailEntity entity = baseMapper.queryWorderDetail(worderId);
        //查询并整理结算详细列表
        List<WorderBalanceFeeDetailEntity> balanceList = worderBalanceFeeDetailService.listBalanceFeeDetailByWorderId(worderId);
        List<WorderPmStimulateEntity> dotStimulateEntity = worderBalanceFeeDetailService.getStimulateList(worderId,"10");//网点激励列表
        List<WorderPmStimulateEntity> companyStimulateEntity = worderBalanceFeeDetailService.getStimulateList(worderId,"11");//网点激励列表
        List<WorderBalanceFeeDetailEntity> companyBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> dotBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> dotIncreBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> userIncreBalanceList = new ArrayList<>();
        if (!dotStimulateEntity.isEmpty()&&dotStimulateEntity.size()>0){
            BigDecimal stimulateFee = new BigDecimal(0);
            for (WorderPmStimulateEntity worderPmStimulateEntity : dotStimulateEntity) {
                stimulateFee = stimulateFee.add(worderPmStimulateEntity.getStimulateFee());
            }
            entity.setDotStimulateBalanceFee(stimulateFee);
        }
        if (!companyStimulateEntity.isEmpty()&&companyStimulateEntity.size()>0){
            BigDecimal stimulateFee = new BigDecimal(0);
            for (WorderPmStimulateEntity worderPmStimulateEntity : companyStimulateEntity) {
                stimulateFee = stimulateFee.add(worderPmStimulateEntity.getStimulateFee());
            }
            entity.setCompanyStimulateBalanceFee(stimulateFee);
        }
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().eq("worder_id",worderId));
        WorderAdvancePublishEntity worderAdvancePublishEntity = worderAdvancePublishService.getOne(
                new QueryWrapper<WorderAdvancePublishEntity>().eq("worder_no",worderInformationEntity.getWorderNo()).eq("is_delete",0));
        if (worderAdvancePublishEntity!=null){
            entity.setDotStatus(5);
        }else{
            String dotStatus = worderBalanceFeeDetailService.getStatusByBCB(worderId);
            if (StringUtils.isNotBlank(dotStatus)){
                switch (dotStatus){
                    case "5":
                        entity.setDotStatus(0);
                        break;
                    case "6":
                        entity.setDotStatus(1);
                        break;
                    case "11":
                        entity.setDotStatus(2);
                        break;
                    case "8":
                        entity.setDotStatus(3);
                        break;
                    case "9":
                        entity.setDotStatus(4);
                        break;
                    case "10":
                        entity.setDotStatus(5);
                        break;
                }
            }else{
                entity.setDotStatus(0);
            }
        }
        entity.setStatus(0);
        if (entity.getWorderExecStatus()!=null){
            switch (entity.getWorderExecStatus()){
                case 0:
                    entity.setStatus(1);
                    break;
                case 18:
                    entity.setStatus(2);
                    break;
                case 1:
                    entity.setStatus(3);
                    break;
                case 2:
                    entity.setStatus(4);
                    break;
                case 3:
                    entity.setStatus(5);
                    break;
                case 4:
                    entity.setStatus(6);
                    break;
                case 10:
                    entity.setStatus(7);
                    break;
                case 11:
                    entity.setStatus(8);
                    break;
                case 12:
                    entity.setStatus(9);
                    break;
                case 14:
                    entity.setStatus(10);
                    break;
                case 15:
                    entity.setStatus(11);
                    break;
                case 16:
                    entity.setStatus(12);
                    break;
                case 17:
                    entity.setStatus(13);
                    break;
            }
        }
        entity.setCompanyStatus(0);
        if (worderInformationEntity!=null&&worderInformationEntity.getInvoiceId()!=null){
            CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(worderInformationEntity.getInvoiceId());
            if (companyInvoiceEntity!=null){
                switch (companyInvoiceEntity.getStatus()){
                    case 1:
                        entity.setCompanyStatus(1);
                        break;
                    case -1:
                    case 2:
                        entity.setCompanyStatus(2);
                        break;
                    case -2:
                    case 3:
                    case 8:
                        entity.setCompanyStatus(3);
                        break;
                    case 4:
                    case 5:
                    case 6:
                    case 9:
                        entity.setCompanyStatus(4);
                        break;
                    case 7:
                        entity.setCompanyStatus(5);
                        break;
                }
                CompanyReceivableEntity companyReceivableEntity = companyReceivableDao.getCavState(companyInvoiceEntity.getId());
                if (companyReceivableEntity!=null){
                    switch (companyReceivableEntity.getStatus()){
                        case -1:
                        case 1:
                        case 2:
                            entity.setCompanyStatus(6);
                            break;
                        case -2:
                        case 3:
                            entity.setCompanyStatus(7);
                            break;
                        case 4:
                            entity.setCompanyStatus(8);
                            break;
                    }
                }
            }
        }else{
            if(entity.getWorderSetStatus()!=null){
                if (entity.getWorderSetStatus()==0||entity.getWorderSetStatus()==1){
                    entity.setCompanyStatus(0);
                }else if(entity.getWorderSetStatus()==2){
                    entity.setCompanyStatus(1);
                }
            }
        }
        for (WorderBalanceFeeDetailEntity e : balanceList) {
            if(0 == e.getBalanceTarget()){
                companyBalanceList.add(e);
            }else if(1 == e.getBalanceTarget() && (0 == e.getBalanceType()||2 == e.getBalanceType())){
                dotBalanceList.add(e);
            }else if(1 == e.getBalanceTarget() && 1 == e.getBalanceType()) {
                if(StringUtils.isEmpty(e.getMaterielName())){
                    e.setMaterielName(e.getDescription());
                }
                dotIncreBalanceList.add(e);
            }else if(2 == e.getBalanceTarget() && 1 == e.getBalanceType()) {
                userIncreBalanceList.add(e);
            }
        }
        entity.setDotStimulateEntity(dotStimulateEntity);
        entity.setCompanyStimulateEntity(companyStimulateEntity);
        entity.setCompanyBalanceList(companyBalanceList);
        entity.setDotBalanceList(dotBalanceList);
        entity.setDotIncreBalanceList(dotIncreBalanceList);
        entity.setUserIncreBalanceList(userIncreBalanceList);
        return entity;
    }

    @Override
    public WorderBalanceFeeEntity queryOneWorderForBalance(Integer worderId) {
        return baseMapper.queryOneWorderForBalance(worderId);
    }

    /**
     * 计算并返回用户增项费用信息
     * @param worderId
     * @return
     */
    @Override
    @Transactional
    public WorderUserBalanceFeeVO caculateOneUserBalanceFee(Integer worderId){
        //工单基本信息
        WorderBalanceFeeEntity worder = baseMapper.queryOneWorderForBalance(worderId);

        //套包信息
        List<SuiteDetailEntity> suiteDetailList = suiteDetailDao.selectList(new QueryWrapper<SuiteDetailEntity>()
                .eq("suite_id", worder.getSuiteId()));
        Map<Integer, BigDecimal> suiteMaterielMap = new HashMap<>();
        for (SuiteDetailEntity d : suiteDetailList) {
            suiteMaterielMap.put(d.getMaterielId(), d.getNum());
        }
        //结算规则信息
        List<BalanceRuleEntity> balanceRuleList = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleIds(new ArrayList<Integer>(){
            {
                add(worder.getCompanyBalanceRuleId());
                add(worder.getAttendantBalanceRuleId());
                add(worder.getDotBalanceRuleId());
//                add(worder.getDotIncreBalanceRuleId());
            }
        });
        BalanceRuleEntity companyBalanceRule = null;
        BalanceRuleEntity userBalanceRule = null;
        BalanceRuleEntity dotBalanceRule = null;
//        BalanceRuleEntity dotIncreBalanceRule = null;
        for (BalanceRuleEntity e : balanceRuleList) {
            if(worder.getCompanyBalanceRuleId().equals(e.getId())){
                companyBalanceRule = e;
                BalanceRuleUtils.filterBalanceRuleDetails(companyBalanceRule,worder);
            }
            if(worder.getAttendantBalanceRuleId().equals(e.getId())){
                userBalanceRule = e;
                BalanceRuleUtils.filterBalanceRuleDetails(userBalanceRule,worder);
            }
            if(worder.getDotBalanceRuleId().equals(e.getId())){
                dotBalanceRule = e;
                BalanceRuleUtils.filterBalanceRuleDetails(dotBalanceRule,worder);
            }
//            if(worder.getDotIncreBalanceRuleId().equals(e.getId())){
//                dotIncreBalanceRule = e;
//            }
        }

        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_id",worderId);
        WorderInformationEntity informationEntity = worderInformationDao.selectOne(wrapper);
        WorderTemplateDto templateInfoById = worderTemplateDao.findTemplateInfoById(informationEntity.getTemplateId());

        QueryWrapper<BalanceRuleEntity> ruleWrapper = new QueryWrapper<>();
        ruleWrapper.eq("id",templateInfoById.getAddUserBalanceId());
        BalanceRuleEntity ruleEntity = balanceRuleDao.selectOne(ruleWrapper);
        String taxRateValue = ruleEntity.getBalanceTaxRateValue();
        BigDecimal taxRate = new BigDecimal(taxRateValue);

//        BigDecimal taxRate = balanceProperties.RRS_USER_TAX_RATE; //税率
        int roundMode = balanceProperties.ROUND_MODE;
                /* -------------------------------计算网点增项结算金额 start------------------------ */
//        //整理增项结算规则中每个物料的最大数量
//        Map<Integer,BigDecimal> dotRuleMaxMaterielNumMap = new HashMap<>();
//        for (BalanceRuleDetailEntity rm : dotBalanceRule.getBalanceItemList()) {
//            Integer materielId = rm.getMaterielId();
//            BigDecimal maxNum = rm.getEndNum();
//            if(rm.getEndNum().compareTo(BigDecimal.ZERO) == 0 && rm.getStartNum().compareTo(BigDecimal.ZERO) == 0){
//                maxNum = BigDecimal.valueOf(Long.MAX_VALUE);
//            }
//            if(dotRuleMaxMaterielNumMap.containsKey(materielId)){
//                dotRuleMaxMaterielNumMap.put(materielId,
//                        maxNum.max(dotRuleMaxMaterielNumMap.get(materielId)));
//            }else{
//                dotRuleMaxMaterielNumMap.put(materielId, maxNum);
//            }
//        }
        //整理厂商结算规则中每个物料的最大数量
        Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap = this.getBalanceRuleMaxMaterielNumMap(companyBalanceRule);
        BigDecimal increFee = BigDecimal.ZERO;

        SysDictionaryEntity sysDictionary = sysDictionaryService.getOne(new QueryWrapper<SysDictionaryEntity>()
                .eq("dic_number", "balance_tax_rate"));
        String taxPoint = worder.getTaxPoint();
        BigDecimal increTaxRate = null;
        List<SysDictionaryDetailEntity> detailEntities = null;
        detailEntities = sysDictionaryDetailService.list(new QueryWrapper<SysDictionaryDetailEntity>()
                .eq("dictionary_id", sysDictionary.getId())
                .eq("detail_number", taxPoint));
        if (detailEntities.size() > 0) {
            SysDictionaryDetailEntity sysDictionaryDetail = detailEntities.get(0);
            increTaxRate = new BigDecimal(sysDictionaryDetail.getDetailName());
        } else {
            // 网点税点不存在
            log.error("网点[" + worder.getDotName() + "]税点未找到");
            throw new RRException("网点[" + worder.getDotName() + "]税点不存在");
        }
        //逐个物料计算网点增项结算金额
        for (WorderUsedMaterielEntity um : worder.getMaterielList()) {
            Integer materielId = um.getMaterielId();
            //计算数量 = 实际使用数量 - 套包包含该物料数量 - 厂商工单结算规则中包含该物料最大数量
            BigDecimal containsNum = suiteMaterielMap.getOrDefault(materielId,BigDecimal.ZERO)
                    .add(companyRuleMaxMaterielNumMap.getOrDefault(materielId,BigDecimal.ZERO)); //非增项的数量
            BigDecimal feeNum = um.getNum().subtract(containsNum);
            if (feeNum.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //分别计算所有物料的结算价和非增项物料的结算价，差值即为增项结算价
            BigDecimal increFee1 = caculateOneMaterielFee(dotBalanceRule, materielId, containsNum, null);
            increFee1 = increFee1.setScale(2, roundMode);
            BigDecimal increFee2 = caculateOneMaterielFee(dotBalanceRule, materielId, um.getNum(), null);
            increFee2 = increFee2.setScale(2, roundMode);
            BigDecimal increFee3 = increFee2.subtract(increFee1);
            if(balanceProperties.DOT_INCRE_USE_BALANCE_RATE){
                increFee3 = increFee3.multiply(worder.getDotBalanceRate()).setScale(2, roundMode);
            }
            increFee = increFee.add(increFee3);
        }
        //减去给客户的优惠
        BigDecimal dotIncreDiscountAmount = worder.getDotIncreDiscountAmount();
        if(dotIncreDiscountAmount != null && dotIncreDiscountAmount.compareTo(BigDecimal.ZERO)!=0){
            BigDecimal increFee1 = dotIncreDiscountAmount.setScale(2, roundMode).negate();//优惠金额取相反数加入计算
            increFee = increFee.add(increFee1);
        }
            /* 计算网点增项结算金额 end */
        increFee = increFee.setScale(2, roundMode);
        BigDecimal increFeeTax = increFee.multiply(increTaxRate).setScale(2, roundMode);
        BigDecimal increFeeSum = increFee.add(increFeeTax);

        /* -------------------------------计算网点增项结算金额 end------------------------ */

        //查询用户增项结算金额,已存在则不再计算
//        if(worder.getUserBalanceFee()!=null && worder.getUserBalanceFee().compareTo(BigDecimal.ZERO)!=0){
//            List<WorderBalanceFeeDetailEntity> balanaceDetailList = worderBalanceFeeDetailService.list(
//                    new QueryWrapper<WorderBalanceFeeDetailEntity>()
//                            .eq("worder_id", worderId)
//                            .eq("balance_type", 1)
//                            .eq("balance_target", 2)
//            );
//            Map<Integer, String> materielMap = new HashMap<>();
//            for (WorderUsedMaterielEntity um : worder.getMaterielList()) {
//                materielMap.put(um.getMaterielId(), um.getMaterielName());
//            }
//            if(CollectionUtils.isNotEmpty(balanaceDetailList)) {
//                List<WorderUserBalanceFeeDetail> details = new ArrayList<>();
//                for (WorderBalanceFeeDetailEntity d : balanaceDetailList) {
//                    WorderUserBalanceFeeDetail v = new WorderUserBalanceFeeDetail();
//                    BeanUtils.copyProperties(d, v);
//                    v.setMaterielId(d.getMaterielId());
//                    v.setMaterielName(materielMap.get(d.getMaterielId()));
//                    v.setNum(d.getNum());
//                    v.setDescription(d.getDescription());
//                    v.setBalanceFee(d.getBalanceFeeSum());
//                    details.add(v);
//                }
//                WorderUserBalanceFeeVO vo = new WorderUserBalanceFeeVO();
//                vo.setWorderId(worderId);
//                vo.setUserBalanceFee(worder.getUserBalanceFeeSum());
//                vo.setMinFee(increFeeSum);
//                vo.setDetails(details);
//                return vo;
//            }
//        }

        /* -------------------------------计算用户增项结算金额, 含税定价 start------------------------ */
        List<WorderBalanceFeeDetailEntity> userDetailList = new ArrayList<>();//用于记录结算详情
        BigDecimal userFee = BigDecimal.ZERO; //用于记录结算总金额
        //逐个物料计算厂商结算金额和网点结算金额
        for (WorderUsedMaterielEntity um : worder.getMaterielList()) {
            Integer materielId = um.getMaterielId();
            //计算用户增项结算金额
            List<Entity> userFeeList = new ArrayList<>();
            //计算数量 = 实际使用数量 - 套包包含该物料数量 - 厂商结算规则中包含的物料数量
            BigDecimal suiteContainsNum = suiteMaterielMap.getOrDefault(materielId, BigDecimal.ZERO);
            BigDecimal feeNum2 = um.getNum().subtract(suiteContainsNum).subtract(companyRuleMaxMaterielNumMap.getOrDefault(materielId, BigDecimal.ZERO));
            if (feeNum2.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            WorderBalanceFeeDetailEntity userDetailEntity = new WorderBalanceFeeDetailEntity();
            userDetailEntity.setWorderId(worderId);
            userDetailEntity.setMaterielName(um.getMaterielName());
            userDetailEntity.setBalanceTarget(3);
            userDetailEntity.setBalanceType(1);
            userDetailEntity.setMaterielId(materielId);
            userDetailEntity.setIsSuite(0);
            userDetailEntity.setNum(feeNum2);
//            if (feeNum2.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal userFee1 = caculateOneMaterielFee(userBalanceRule, materielId, feeNum2, userFeeList);
                //金额规范化，并计算税额和含税价
                userFee1 = userFee1.setScale(2, roundMode);
                BigDecimal tax = userFee1.divide(taxRate.add(BigDecimal.ONE), 2, roundMode).multiply(taxRate);
                BigDecimal userFeeNoTax1 = userFee1.subtract(tax);
                userFee = userFee.add(userFee1);
                Collections.sort(userFeeList);
                String userDesc = "实际计算数量: " + feeNum2 + ", 详情如下:\n" + StringUtils.join(userFeeList, '\n');
                userDetailEntity.setBalanceFee(userFeeNoTax1);
                userDetailEntity.setBalanceFeeTax(tax);
                userDetailEntity.setBalanceFeeSum(userFee1);
                userDetailEntity.setDescription(userDesc);
//            } else {
//                userDetailEntity.setBalanceFee(BigDecimal.ZERO);
//                userDetailEntity.setBalanceFeeTax(BigDecimal.ZERO);
//                userDetailEntity.setBalanceFeeSum(BigDecimal.ZERO);
//                userDetailEntity.setDescription("厂商承担费用");
//            }
            userDetailList.add(userDetailEntity);
        }
        //金额规范化，并计算税额和含税价
        userFee = userFee.setScale(2, roundMode);
        BigDecimal userTax = userFee.divide(taxRate.add(BigDecimal.ONE), 2, roundMode).multiply(taxRate).setScale(2, roundMode);
        BigDecimal userFeeNoTax = userFee.subtract(userTax);
        //调整尾差
        this.adjustedTailDiff(userFeeNoTax, userTax, userFee, userDetailList);
        /* -------------------------------计算用户增项结算金额 end------------------------ */

//        worder.setUserBalanceFee(userFee);
//        worder.setUserBalanceFeeTax(userTax);
//        worder.setUserBalanceFeeSum(userFeeSum);
//        worder.setDotIncreBalanceFee(increFee);
//        worder.setDotIncreBalanceFeeTax(increFeeTax);
//        worder.setDotIncreBalanceFeeSum(increFeeSum);
//        worder.setWorderIncreStatus(1);
//        worder.setWorderIncreStatusValue("增项待结算");
//        worder.setWorderIncreFlag(true);
        baseMapper.saveUserBalanceFee(worderId, userFeeNoTax, userTax, userFee);
//        baseMapper.saveIncreBalanceFee(worder);
        if(CollectionUtils.isNotEmpty(userDetailList)){
            worderBalanceFeeDetailService.remove(new QueryWrapper<WorderBalanceFeeDetailEntity>()
                    .eq("worder_id", worderId).eq("balance_type", 1).eq("balance_target", 3));
            worderBalanceFeeDetailService.saveBatch(userDetailList);
        }
        List<WorderUserBalanceFeeDetail> details = new ArrayList<>();
        for (WorderBalanceFeeDetailEntity d : userDetailList) {
            WorderUserBalanceFeeDetail v = new WorderUserBalanceFeeDetail();
            BeanUtils.copyProperties(d, v);
            v.setMaterielId(d.getMaterielId());
            v.setMaterielName(d.getMaterielName());
            v.setNum(d.getNum());
            v.setDescription(d.getDescription());
            v.setBalanceFee(d.getBalanceFeeSum());
            details.add(v);
        }
        WorderUserBalanceFeeVO vo = new WorderUserBalanceFeeVO();
        vo.setWorderId(worderId);
        vo.setUserBalanceFee(userFee);
        vo.setMinFee(increFeeSum);
        //vo.setDetails(details);
        vo.setUserActualCost(worder.getUserActualCost());
        //固定的物料
        List<SysDictionaryDetailEntity> all = sysDictionaryDetailDao.getAll();
        List<WorderUserBalanceFeeDetail> list =new ArrayList<>();
        all.stream().forEach(v ->{
            details.forEach(t ->{
                if(v.getDetailName().equals(t.getMaterielName())){
                    WorderUserBalanceFeeDetail worderUserBalanceFeeDetail=new WorderUserBalanceFeeDetail();
                    worderUserBalanceFeeDetail.setBalanceFee(t.getBalanceFee());
                    worderUserBalanceFeeDetail.setDescription(t.getDescription());
                    worderUserBalanceFeeDetail.setMaterielId(t.getMaterielId());
                    worderUserBalanceFeeDetail.setNum(t.getNum());
                    worderUserBalanceFeeDetail.setMaterielName(t.getMaterielName());
                    list.add(worderUserBalanceFeeDetail);
                }
            });
        });
        details.removeAll(list);
        list.addAll(details);
        vo.setDetails(list);
        return vo;
    }


    /**
     * 计算并保存网点增项费用信息
     * @param worderId
     * @return
     */
    @Override
    @Transactional
    public WorderBalanceFeeEntity caculateOneDotIncreBalanceFee(Integer worderId){
        //工单基本信息
        WorderBalanceFeeEntity worder = baseMapper.queryOneWorderForBalance(worderId);

        //套包信息
            List<SuiteDetailEntity> suiteDetailList = suiteDetailDao.selectList(new QueryWrapper<SuiteDetailEntity>()
                    .eq("suite_id", worder.getSuiteId()));
        Map<Integer, BigDecimal> suiteMaterielMap = new HashMap<>();
        for (SuiteDetailEntity d : suiteDetailList) {
            suiteMaterielMap.put(d.getMaterielId(), d.getNum());
        }
        //结算规则信息
        List<BalanceRuleEntity> balanceRuleList = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleIds(new ArrayList<Integer>(){
            {
                add(worder.getDotBalanceRuleId());
                add(worder.getCompanyBalanceRuleId());
            }
        });
        BalanceRuleEntity dotBalanceRule = null;
        BalanceRuleEntity companyBalanceRule = null;
        for (BalanceRuleEntity e : balanceRuleList) {
            if(worder.getDotBalanceRuleId().equals(e.getId())){
                dotBalanceRule = e;
                BalanceRuleUtils.filterBalanceRuleDetails(dotBalanceRule,worder);
            }
            if(worder.getCompanyBalanceRuleId().equals(e.getId())){
                companyBalanceRule = e;
                BalanceRuleUtils.filterBalanceRuleDetails(companyBalanceRule,worder);
            }
        }

        /* -------------------------------计算网点增项结算金额 start------------------------ */
        //整理增项结算规则中每个物料的最大数量
        Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap = this.getBalanceRuleMaxMaterielNumMap(companyBalanceRule);
        int roundMode = balanceProperties.ROUND_MODE;
        //BigDecimal increTaxRate = new BigDecimal(dotBalanceRule.getBalanceTaxRateValue()); //网点税率
        List<WorderBalanceFeeDetailEntity> increDetailList = new ArrayList<>();
        BigDecimal increFee = BigDecimal.ZERO;

        SysDictionaryEntity sysDictionary = sysDictionaryService.getOne(new QueryWrapper<SysDictionaryEntity>()
                .eq("dic_number", "balance_tax_rate"));
        String taxPoint = worder.getTaxPoint();
        BigDecimal increTaxRate = null;
        List<SysDictionaryDetailEntity> detailEntities = null;
        detailEntities = sysDictionaryDetailService.list(new QueryWrapper<SysDictionaryDetailEntity>()
                .eq("dictionary_id", sysDictionary.getId())
                .eq("detail_number", taxPoint));
        if (detailEntities.size() > 0) {
            SysDictionaryDetailEntity sysDictionaryDetail = detailEntities.get(0);
            increTaxRate = new BigDecimal(sysDictionaryDetail.getDetailName());
        } else {
            // 网点税点不存在
            log.error("网点[" + worder.getDotName() + "]税点未找到");
            throw new RRException("网点[" + worder.getDotName() + "]税点不存在");
        }
        //逐个物料计算网点增项结算金额
        for (WorderUsedMaterielEntity um : worder.getMaterielList()) {
            Integer materielId = um.getMaterielId();
            //计算数量 = 实际使用数量 - 套包包含该物料数量 - 厂商工单结算规则中包含该物料最大数量
            BigDecimal containsNum = suiteMaterielMap.getOrDefault(materielId,BigDecimal.ZERO)
                    .add(companyRuleMaxMaterielNumMap.getOrDefault(materielId,BigDecimal.ZERO));//非增项的数量
            BigDecimal feeNum = um.getNum().subtract(containsNum);
            if (feeNum.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            WorderBalanceFeeDetailEntity increDetailEntity = new WorderBalanceFeeDetailEntity();
            increDetailEntity.setWorderId(worderId);
            increDetailEntity.setBalanceTarget(1);
            increDetailEntity.setBalanceType(1);
            increDetailEntity.setMaterielId(materielId);
            increDetailEntity.setIsSuite(0);
            increDetailEntity.setNum(feeNum);
//            if(feeNum.compareTo(BigDecimal.ZERO) > 0){
            //分别计算所有物料的结算价和非增项物料的结算价，差值即为增项结算价
            BigDecimal increFee3 = caculateOneMaterielFee(dotBalanceRule, materielId, containsNum, null);
            increFee3 = increFee3.setScale(2, roundMode);
            BigDecimal increFee2 = caculateOneMaterielFee(dotBalanceRule, materielId, um.getNum(), null);
            increFee2 = increFee2.setScale(2, roundMode);
            BigDecimal increFee1 = increFee2.subtract(increFee3);
            String description = feeNum+","+increFee1;
            if(balanceProperties.DOT_INCRE_USE_BALANCE_RATE){
                increFee1 = increFee1.multiply(worder.getDotBalanceRate()).setScale(2, roundMode);
                description = description+",结算系数:"+worder.getDotBalanceRate();
            }
            BigDecimal increTax1 = increFee1.multiply(increTaxRate).setScale(2, roundMode);
            BigDecimal increFeeSum1 = increFee1.add(increTax1);
            increFee = increFee.add(increFee1);
            increDetailEntity.setBalanceFee(increFee1);
            increDetailEntity.setBalanceFeeTax(increTax1);
            increDetailEntity.setBalanceFeeSum(increFeeSum1);
            increDetailEntity.setDescription(description);
//            }else {
//                increDetailEntity.setBalanceFee(BigDecimal.ZERO);
//                increDetailEntity.setBalanceFeeTax(BigDecimal.ZERO);
//                increDetailEntity.setBalanceFeeSum(BigDecimal.ZERO);
//                increDetailEntity.setDescription("网点工单结算包含");
//            }
            increDetailList.add(increDetailEntity);
        }
        //给客户的优惠金额
        BigDecimal dotIncreDiscountAmount = worder.getDotIncreDiscountAmount();
        if(dotIncreDiscountAmount != null && dotIncreDiscountAmount.compareTo(BigDecimal.ZERO)!=0){
            BigDecimal increFeeSum1 = dotIncreDiscountAmount.setScale(2, roundMode).negate();//优惠金额取相反数加入计算,作为含税金额
            BigDecimal increTax1 = increFeeSum1.divide(increTaxRate.add(BigDecimal.ONE), 2, roundMode).multiply(increTaxRate).setScale(2, roundMode);
            BigDecimal increFee1 = increFeeSum1.subtract(increTax1);

            increFee = increFee.add(increFee1);
            //网点给用户的优惠金额 加入 网点增项结算详表
            WorderBalanceFeeDetailEntity increDetailEntity = new WorderBalanceFeeDetailEntity();
            increDetailEntity.setWorderId(worderId);
            increDetailEntity.setBalanceTarget(1);
            increDetailEntity.setBalanceType(1);
            increDetailEntity.setNum(BigDecimal.ONE);
            increDetailEntity.setBalanceFee(increFee1);
            increDetailEntity.setBalanceFeeTax(increTax1);
            increDetailEntity.setBalanceFeeSum(increFeeSum1);
            increDetailEntity.setDescription("网点给用户的优惠金额");
            increDetailList.add(increDetailEntity);
        }
            /* 计算网点增项结算金额 end */
        //金额规范化，并计算税额和含税价
        increFee = increFee.setScale(2, roundMode);
        BigDecimal increFeeTax = increFee.multiply(increTaxRate).setScale(2, roundMode);
        BigDecimal increFeeSum = increFee.add(increFeeTax);
        //调整尾差
        this.adjustedTailDiff(increFee, increFeeTax, increFeeSum, increDetailList);
        /* -------------------------------计算网点增项结算金额 end------------------------ */
        worder.setDotIncreBalanceFee(increFee);
        worder.setDotIncreBalanceFeeTax(increFeeTax);
        worder.setDotIncreBalanceFeeSum(increFeeSum);
        worder.setDotIncreDiscountAmount(dotIncreDiscountAmount);
        worder.setWorderIncreStatus(1);
        worder.setWorderIncreStatusValue("增项待结算");
//        worder.setWorderIncreStatus(99);
//        worder.setWorderIncreStatusValue("已收款不记账");
        baseMapper.saveDotIncreBalanceFee(worder);
        if(CollectionUtils.isNotEmpty(increDetailList)){
            worderBalanceFeeDetailService.remove(new QueryWrapper<WorderBalanceFeeDetailEntity>()
                    .eq("worder_id", worderId).eq("balance_type", 1).eq("balance_target", 1));
            worderBalanceFeeDetailService.saveBatch(increDetailList);
        }
        return worder;
    }
    @Override
    public Map<Integer,BigDecimal> getBalanceRuleMaxMaterielNumMap(BalanceRuleEntity balanceRuleEntity){
        Map<Integer,BigDecimal> maxMaterielNumMap = new HashMap<>();
        for (BalanceRuleDetailEntity rm : balanceRuleEntity.getBalanceItemList()) {
            if(rm.getIsSuite()!=null && 1 == rm.getIsSuite().intValue()){
                //如果是套包，直接跳过
                continue;
            }
            Integer materielId = rm.getMaterielId();
            BigDecimal maxNum = rm.getEndNum();
            if(rm.getEndNum().compareTo(BigDecimal.ZERO) == 0){
                maxNum = BigDecimal.valueOf(Integer.MAX_VALUE);
            }
            if(maxMaterielNumMap.containsKey(materielId)){
                maxMaterielNumMap.put(materielId,
                        maxNum.max(maxMaterielNumMap.get(materielId)));
            }else{
                maxMaterielNumMap.put(materielId, maxNum);
            }
        }
        return maxMaterielNumMap;
    }

    @Override
    public BigDecimal companySuitBalance(BalanceRuleEntity companyBalanceRule, Integer worderId,
        List<WorderBalanceFeeDetailEntity> balanaceDetailList,BigDecimal companyFee, Integer suiteId,
        BigDecimal companyTaxRate, int roundMode){
        //计算厂商套包结算金额
        for (BalanceRuleDetailEntity rm : companyBalanceRule.getBalanceItemList()) {
            if(rm.getIsSuite() == 1 && suiteId.equals(rm.getMaterielId())){
                BigDecimal price = rm.getPrice().setScale(2, roundMode);
                //加入总金额
                companyFee = companyFee.add(price);
                BigDecimal companySuitFee = BigDecimal.ZERO;
                BigDecimal companySuitTax = BigDecimal.ZERO;
                BigDecimal companySuitFeeSum = BigDecimal.ZERO;
                if(companyBalanceRule.getPriceType() == 0){
                    //含税价
                    companySuitFeeSum = price;
                    //不含税价
                    companySuitTax = companySuitFeeSum.divide(companyTaxRate.add(BigDecimal.ONE),10,roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                    //税费
                    companySuitFee = companySuitFeeSum.subtract(companySuitTax);
                }else {
                    // 不含税价
                    companySuitFee = price;
                    //价格类型不含税价格
                    companySuitTax = companyFee.multiply(companyTaxRate).setScale(2, roundMode);
                    //含税价
                    companySuitFeeSum = companyFee.add(companySuitTax);
                }
                //费用详细实体类
                WorderBalanceFeeDetailEntity companyDetailEntity = new WorderBalanceFeeDetailEntity();
                companyDetailEntity.setWorderId(worderId);
                companyDetailEntity.setBalanceTarget(0);
                companyDetailEntity.setBalanceType(0);
                companyDetailEntity.setMaterielId(suiteId);
                companyDetailEntity.setIsSuite(1);
                companyDetailEntity.setBalanceFee(companySuitFee);
                companyDetailEntity.setBalanceFeeTax(companySuitTax);
                companyDetailEntity.setBalanceFeeSum(companySuitFeeSum);
                companyDetailEntity.setNum(BigDecimal.ONE);
                balanaceDetailList.add(companyDetailEntity);
            }
        }
        return companyFee;
    }
    @Override
    public BigDecimal companyMaterielBalance(BalanceRuleEntity companyBalanceRule, Integer worderId,
        List<WorderBalanceFeeDetailEntity> balanaceDetailList,BigDecimal companyFee,
        BigDecimal companyTaxRate, int roundMode, Map<Integer,BigDecimal> suiteMaterialMap,
        WorderBalanceFeeEntity worderBalanceFee, Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap){

        //如果工单是工程结算，校验工程结算规则是否添加固定物料费用
        Result result = setProjectBlanaceRuleFee(companyBalanceRule, worderId, balanaceDetailList, companyFee, companyTaxRate, roundMode, worderBalanceFee,0);
        Set<Integer> afterBlanaceMaterielIds = result.getMaterielIds();
        if(result.getFee() != null){
            companyFee = companyFee.add(result.getFee());
        }
        //逐个物料计算厂商结算金额和网点结算金额
        for (WorderUsedMaterielEntity um : worderBalanceFee.getMaterielList()) {
            Integer materielId = um.getMaterielId();
            if(materielId == null){
                continue;
            }
            //如果工程已经结算过的物料无须在结算
            if(afterBlanaceMaterielIds != null && afterBlanaceMaterielIds.contains(materielId)){
                continue;
            }
            //计算厂商结算金额
            //计算数量 = 实际使用数量 - 套包包含该物料数量
            BigDecimal suiteContainsNum = suiteMaterialMap.getOrDefault(materielId, BigDecimal.ZERO);
            BigDecimal feeNum = um.getNum().subtract(suiteContainsNum);
            if(feeNum.compareTo(BigDecimal.ZERO) <= 0 || companyRuleMaxMaterielNumMap.get(materielId)==null){
                //计算数量小于0或者结算规则中不包含则不计算
                continue;
            }
            List<BranchBalanceServiceImpl.Entity> companyFeeList = new ArrayList<>();
            BigDecimal companyMaterielFee = caculateOneMaterielFee(companyBalanceRule, materielId, feeNum, worderBalanceFee ,companyFeeList);
            Collections.sort(companyFeeList);
            String companyDesc = "套包包含数量："+suiteContainsNum+", 实际计价数量："+feeNum.min(companyRuleMaxMaterielNumMap.getOrDefault(materielId, feeNum))+", 详情如下：\n";
            companyDesc = companyDesc + StringUtils.join(companyFeeList, '\n');
            WorderBalanceFeeDetailEntity companyDetailEntity = new WorderBalanceFeeDetailEntity();
            companyDetailEntity.setWorderId(worderId);
            companyDetailEntity.setBalanceTarget(0);
            companyDetailEntity.setBalanceType(0);
            companyDetailEntity.setMaterielId(materielId);
            companyDetailEntity.setIsSuite(0);

            //金额规范化，并计算税额和含税价
            companyMaterielFee = companyMaterielFee.setScale(2, roundMode);
            BigDecimal companyMaterielTax = BigDecimal.ZERO;
            BigDecimal companyMaterielFeeSum = BigDecimal.ZERO;
            if(companyBalanceRule.getPriceType() == 0){
                // 含税
                companyMaterielFeeSum = companyMaterielFee;
                companyMaterielTax = companyMaterielFeeSum.divide(companyTaxRate.add(BigDecimal.ONE),10,roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                companyMaterielFee = companyMaterielFeeSum.subtract(companyMaterielTax);
                // 工单结算总金额
                companyFee = companyFee.add(companyMaterielFeeSum);
            }else {
                // 不含税
                companyMaterielTax = companyMaterielFee.multiply(companyTaxRate).setScale(2, roundMode);
                companyMaterielFeeSum = companyMaterielFee.add(companyMaterielTax);
                // 工单结算总金额
                companyFee = companyFee.add(companyMaterielFee);
            }
            companyDetailEntity.setBalanceFee(companyMaterielFee);
            companyDetailEntity.setBalanceFeeTax(companyMaterielTax);
            companyDetailEntity.setBalanceFeeSum(companyMaterielFeeSum);
            companyDetailEntity.setNum(feeNum);
            companyDetailEntity.setDescription(companyDesc);
            balanaceDetailList.add(companyDetailEntity);
        }
        return companyFee;
    }

    /**
     * 如果工单是，工程类型，结算物料判断工程结算的规则
     * @param companyBalanceRule
     * @param worderId
     * @param balanaceDetailList
     * @param brforeFee
     * @param companyTaxRate
     * @param roundMode
     * @param worderBalanceFee
     * @return
     */
    private Result setProjectBlanaceRuleFee(BalanceRuleEntity companyBalanceRule, Integer worderId,
                                                  List<WorderBalanceFeeDetailEntity> balanaceDetailList, BigDecimal brforeFee,
                                                  BigDecimal companyTaxRate, int roundMode, WorderBalanceFeeEntity worderBalanceFee,Integer balanceTarget) {
        Set<Integer> materielIds = new HashSet<>();
        if (worderBalanceFee.getWorderTypeId() != 7) {
            return new Result(materielIds,null);
        }
        //获取规则下物料详情信息
        List<BalanceRuleDetailEntity> balanceItemList = companyBalanceRule.getBalanceItemList();
        //获取当前工单结算规则是否符合当前订单的规则
        List<BalanceRuleDetailEntity> balanceRuleDetails = balanceItemList.stream().filter(balanceRuleDetailEntity -> worderBalanceFee.getWorderNo().equals(balanceRuleDetailEntity.getJudgeValue())).collect(Collectors.toList());
        if (balanceRuleDetails.isEmpty()) {
            return new Result(materielIds,null);
        }
        BalanceRuleDetailEntity balanceRuleDetail = balanceRuleDetails.get(0);
        //起始数量
        BigDecimal startNum = balanceRuleDetail.getStartNum();
        //结束数量
        BigDecimal endNum = balanceRuleDetail.getEndNum();
        //获取物料使用数量(取起始数量获取)
        BigDecimal materielNum = endNum.compareTo(startNum) > 0 ? endNum : startNum;

        //拿上物料数量乘以金额
        BigDecimal fee = materielNum.subtract(startNum).multiply(balanceRuleDetail.getPrice());

        String companyDesc = "套包包含数量：" + materielNum + ", 实际计价数量：" + materielNum + ", 详情如下：\n" + startNum + "-" + materielNum + "," + balanceRuleDetail.getPrice() + "," + fee;
        WorderBalanceFeeDetailEntity companyDetailEntity = new WorderBalanceFeeDetailEntity();
        companyDetailEntity.setWorderId(worderId);
        companyDetailEntity.setBalanceTarget(balanceTarget);
        companyDetailEntity.setBalanceType(2);
        companyDetailEntity.setMaterielId(balanceRuleDetail.getMaterielId());
        companyDetailEntity.setIsSuite(0);

        //金额规范化，并计算税额和含税价
        fee = fee.setScale(2, roundMode);
        BigDecimal companyMaterielTax;
        BigDecimal companyMaterielFeeSum;
        if (companyBalanceRule.getPriceType() == 0) {
            // 含税
            companyMaterielFeeSum = fee;
            companyMaterielTax = companyMaterielFeeSum.divide(companyTaxRate.add(BigDecimal.ONE), 10, roundMode).multiply(companyTaxRate).setScale(2, roundMode);
            fee = companyMaterielFeeSum.subtract(companyMaterielTax);
            // 工单结算总金额
            brforeFee = brforeFee.add(companyMaterielFeeSum);
        } else {
            // 不含税
            companyMaterielTax = fee.multiply(companyTaxRate).setScale(2, roundMode);
            companyMaterielFeeSum = fee.add(companyMaterielTax);
            // 工单结算总金额
            brforeFee = brforeFee.add(fee);
        }
        companyDetailEntity.setBalanceFee(fee);
        companyDetailEntity.setBalanceFeeTax(companyMaterielTax);
        companyDetailEntity.setBalanceFeeSum(companyMaterielFeeSum);
        companyDetailEntity.setNum(materielNum);
        companyDetailEntity.setDescription(companyDesc);
        balanaceDetailList.add(companyDetailEntity);

        //把结算的固定物料ID添加到集合并且返回
        materielIds.add(balanceRuleDetail.getMaterielId());
        return new Result(materielIds,brforeFee);
    }

    @Override
    public BigDecimal dotBalanceFee(BalanceRuleEntity dotBalanceRule, Integer worderId,
        List<WorderBalanceFeeDetailEntity> balanaceDetailList,BigDecimal dotFee,
        BigDecimal dotTaxRate, int roundMode, Map<Integer,BigDecimal> suiteMaterialMap,
        WorderBalanceFeeEntity worderBalanceFee, Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap){

        //根据网点结算规则过滤符合条件的结算项
        BalanceRuleUtils.filterBalanceRuleDetails(dotBalanceRule, worderBalanceFee);

        //如果工单是工程结算，校验工程结算规则是否添加固定物料费用
        Result result = setProjectBlanaceRuleFee(dotBalanceRule, worderId, balanaceDetailList, dotFee, dotTaxRate, roundMode, worderBalanceFee,1);
        Set<Integer> afterBlanaceMaterielIds = result.getMaterielIds();
        if(result.getFee() != null){
            dotFee = dotFee.add(result.getFee());
        }

        for (WorderUsedMaterielEntity um : worderBalanceFee.getMaterielList()) {
            Integer materielId = um.getMaterielId();
            if(materielId == null){
                continue;
            }
            //如果工程已经结算过该物料无须重新结算
            if(afterBlanaceMaterielIds != null && afterBlanaceMaterielIds.contains(materielId)){
                continue;
            }

            // 计算网点结算金额
            //网点工单结算的数量 = min(厂商结算的数量（套包包含数量和厂商结算金额中的最大数量的和）与实际使用的数量)
            BigDecimal dotFeeNum = suiteMaterialMap.getOrDefault(materielId, BigDecimal.ZERO)
                    .add(companyRuleMaxMaterielNumMap.getOrDefault(materielId, BigDecimal.ZERO))
                    .min(um.getNum());
            if(dotFeeNum.compareTo(BigDecimal.ZERO) <= 0){
                //计算数量小于0则不计算
                continue;
            }

            //计算网点结算金额
            List<Entity> dotFeeList = new ArrayList<>();
            BigDecimal dotMaterielFee =null;
            String dotDesc ="";
            //如果敷设方式为“用户自布线”,不计算物料类型为“商品-电缆的物料”
            MaterielInformationEntity materielInformationEntity= materielInforService.queryByMaterielId(um.getMaterielId());
            WorderInfoEntity worderInfoEntity=worderInformationDao.getByWorderId(worderId);
            WorderExtFieldEntity worderExtFieldEntity=null;
            if(null!=worderInfoEntity){
                 worderExtFieldEntity =  worderExtFieldService.getOne(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", worderInfoEntity.getWorderNo()).eq("field_id",1262));
            }
            if(materielInformationEntity!=null&&worderExtFieldEntity!=null&&worderExtFieldEntity.getFieldValue().equals("用户自布线")&&materielInformationEntity.getMaterielTypeId().equals(53)){
                dotMaterielFee=BigDecimal.ZERO;
                dotDesc +="敷设方式为“用户自布线”,不计算物料类型为“商品-电缆“的物料";
            }else{
                dotMaterielFee = caculateOneMaterielFee(dotBalanceRule, materielId, dotFeeNum, worderBalanceFee ,dotFeeList);
                Collections.sort(dotFeeList);
                dotDesc += "计价数量:" + dotFeeNum +", 详情如下：\n" + StringUtils.join(dotFeeList, '\n');
            }
            if(balanceProperties.DOT_WORDER_USE_BALANCE_RATE){
                BigDecimal dotBalanceRate = worderBalanceFee.getDotBalanceRate()!=null ? worderBalanceFee.getDotBalanceRate() : BigDecimal.ONE;
                dotMaterielFee = dotMaterielFee.multiply(dotBalanceRate).setScale(2, roundMode);
                dotDesc = dotDesc+"\n结算系数:"+dotBalanceRate;
            }
            WorderBalanceFeeDetailEntity dotDetailEntity = new WorderBalanceFeeDetailEntity();
            dotDetailEntity.setWorderId(worderId);
            dotDetailEntity.setBalanceTarget(1);
            dotDetailEntity.setBalanceType(0);
            dotDetailEntity.setMaterielId(materielId);
            dotDetailEntity.setIsSuite(0);
            //金额规范化，并计算税额和含税价
            dotMaterielFee = dotMaterielFee.setScale(2, roundMode);
            BigDecimal dotMaterielTax = dotMaterielFee.multiply(dotTaxRate).setScale(2, roundMode);
            BigDecimal dotMaterielFeeSum = dotMaterielFee.add(dotMaterielTax);
            dotDetailEntity.setBalanceFee(dotMaterielFee);
            dotDetailEntity.setBalanceFeeTax(dotMaterielTax);
            dotDetailEntity.setBalanceFeeSum(dotMaterielFeeSum);
            dotDetailEntity.setNum(dotFeeNum);
            dotDetailEntity.setDescription(dotDesc);
            balanaceDetailList.add(dotDetailEntity);
            // 网点结算总金额
            dotFee = dotFee.add(dotMaterielFee);
        }
        return dotFee;
    }

    /**
     * 根据税点获得税率
     * @param taxPoint
     * @return
     */
    @Override
    public BigDecimal getTaxRateByTaxPoint(String taxPoint){
        // 网点税率
        BigDecimal dotTaxRate = BigDecimal.ZERO;
        // 根据税点到字典表中查询
        SysDicEntity sysDicEntity = new SysDicEntity();
        sysDicEntity.setDicNumber("balance_tax_rate");
        sysDicEntity.setDetailNumber(taxPoint);
        List<SysDictionaryDetailEntity> dictionary = sysDictionaryDetailService.getDictionary(sysDicEntity);
        if(dictionary.size() > IntegerEnum.ZERO.getValue()){
            SysDictionaryDetailEntity sysDictionaryDetail = dictionary.get(IntegerEnum.ZERO.getValue());
            dotTaxRate = new BigDecimal(sysDictionaryDetail.getDetailName());
        }
        return dotTaxRate;
    }


    public Boolean calculateCompanyBalanceFeeByWorderId(Integer worderId, WorderWaitAccountEntity worderWaitAccount,
        WorderBalanceFeeEntity worderBalanceFeeEntity, List<WorderBalanceFeeDetailEntity> balanaceDetailList, Boolean companyFlag) {

        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_id",worderId);
        WorderInformationEntity informationEntity = worderInformationDao.selectOne(wrapper);
        WorderTemplateDto templateInfoById = worderTemplateDao.findTemplateInfoById(informationEntity.getTemplateId());

        String brandId = templateInfoById.getBrandId();
        // 更新工单固定物料
        excuteSqlService.addWorldFixedMateriel(worderId, 3);

        //针对存在预勘测转正标识的工单需要增加一个用户使用的固定物料
        insertPresurveyUserMateriel(worderId,templateInfoById.getBrandId());

        WorderBalanceFeeEntity worderBalanceFee = baseMapper.queryWorderForBalanceByWorderId(worderId);

        if(worderBalanceFee == null){
            return false;
        }

        //签到和资料上传奖励金开始
        //查询物料id
        SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(549);
        // 网点结算规则
        Integer dotBalanceRuleId = worderBalanceFee.getDotBalanceRuleId();
        BalanceRuleEntity dotBalanceRule = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleId(dotBalanceRuleId);
        //Integer integer=worderUsedMaterielService.queryCount(Integer.parseInt(sysDictionaryDetailEntity.getDetailNumber()));
        MaterielInformationDto materielInformation = materielInformationDao.getMaterielInformation();
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(materielInformation.getMaterielId(),worderId);
        Integer integer=worderUsedMaterielService.getCountByWrderId(Integer.parseInt(sysDictionaryDetailEntity.getDetailNumber()),worderId);
        BigDecimal bigDecimal=new BigDecimal(1);

        if(integer==0)
        {
            dotBalanceRule.getBalanceItemList().forEach(v -> {
                if (Integer.parseInt(sysDictionaryDetailEntity.getDetailNumber())==(v.getMaterielId()) &&
                        worderOperationRecodeService.savaBonus(worderId) && checkEliminateBrand(brandId)){

                    WorderUsedMaterielEntity worderUsedMaterielEntity = new WorderUsedMaterielEntity();

                    worderUsedMaterielEntity.setWorderId(worderId);
                    worderUsedMaterielEntity.setMaterielId(materielInformation.getMaterielId());
                    worderUsedMaterielEntity.setBrandId(materielInformation.getMaterielBrand());
                    worderUsedMaterielEntity.setMaterielSpec(materielInformation
                            .getMaterielSpec());
                    worderUsedMaterielEntity.setNum(bigDecimal);

                    worderUsedMaterielService.savaWorderUsedMateriel(
                            worderUsedMaterielEntity);
                }
            });
        }
        //签到和资料上传奖励金结束

        // 勘测结单
        if(worderBalanceFee.getWorderStatus().intValue() == IntegerEnum.FIVE.getValue()){
            // 判断是否算钱
//            if(!worderBalanceFee.getConveyAccountChargeFlag()){
//                return false;
//            }
            // 暂时先不算钱
            return false;
        }

        //分级结算开始
       String   installEfficiency = hierarchicalSettlement(informationEntity,templateInfoById,dotBalanceRule);
        //获取安装时效

       worderBalanceFee = baseMapper.queryWorderForBalanceByWorderId(worderId);

       if(StringUtils.isNotBlank(installEfficiency)){//存在分级结算安装物料
            worderBalanceFee.setInstallEfficiency(Integer.valueOf(installEfficiency));
            //保存订单属性
            WorderInformationAttribute worderInformationAttribute=new WorderInformationAttribute();
            worderInformationAttribute.setWorderId(worderId);
            worderInformationAttribute.setAttributeCode("Efficiency-installTime");
            worderInformationAttribute.setAttribute("Efficiency");
            worderInformationAttribute.setAttributeName("分段结算工单安装时效(天)");
            worderInformationAttribute.setAttributeValue(installEfficiency);
            worderInformationAttributeMapper.insert(worderInformationAttribute);
        }
        //分级结算结束





        int roundMode = balanceProperties.ROUND_MODE;

        // 套包物料
        Integer suiteId = worderBalanceFee.getSuiteId();
        List<SuiteDetailEntity> suiteDetailList = suiteDetailDao.selectList(new QueryWrapper<SuiteDetailEntity>()
                .eq("suite_id", suiteId));
        //转换成Map，方便快速查找，格式：suiteId -> ( materielId -> num )
        Map<Integer,BigDecimal> suiteMaterialMap = new HashMap<>();
        for (SuiteDetailEntity s : suiteDetailList) {
            suiteMaterialMap.put(s.getMaterielId(), s.getNum());
        }

        Integer companyBalanceRuleId = worderBalanceFee.getCompanyBalanceRuleId();
        // 工单结算规则
        BalanceRuleEntity companyBalanceRule = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleId(companyBalanceRuleId);
        //整理厂商结算规则中每个物料的最大数量
        Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap = this.getBalanceRuleMaxMaterielNumMap(companyBalanceRule);

        Integer priceType = companyBalanceRule.getPriceType();

        informationEntity = worderInformationDao.selectOne(wrapper);

        templateInfoById = worderTemplateDao.findTemplateInfoById(informationEntity.getTemplateId());


        QueryWrapper<BalanceRuleEntity> ruleWrapper = new QueryWrapper<>();
        ruleWrapper.eq("id",templateInfoById.getWorderAutoCompanyBalanceId());
        BalanceRuleEntity ruleEntity = balanceRuleDao.selectOne(ruleWrapper);
        String taxRateValue = ruleEntity.getBalanceTaxRateValue();

        String worderNo = informationEntity.getWorderNo();

        //根据车企结算规则过滤符合条件的结算项
        BalanceRuleUtils.filterBalanceRuleDetails(companyBalanceRule, worderBalanceFee);
        if(companyFlag){
            //厂商税率
            BigDecimal companyTaxRate = new BigDecimal(taxRateValue);
//            BigDecimal companyTaxRate = balanceProperties.RRS_COMPANY_TAX_RATE;
            BigDecimal companyFee = BigDecimal.ZERO;
            // 厂商结算 +套包价格
            companyFee = companySuitBalance(companyBalanceRule, worderId, balanaceDetailList, companyFee, suiteId,
                    companyTaxRate, roundMode);
            // 厂商结算 +物料价格
            companyFee = companyMaterielBalance(companyBalanceRule, worderId, balanaceDetailList, companyFee,
                    companyTaxRate, roundMode, suiteMaterialMap, worderBalanceFee, companyRuleMaxMaterielNumMap);

            /* 计算厂商结算金额和网点结算金额 end */
            //金额规范化，并计算税额和含税价
            companyFee = companyFee.setScale(2, roundMode);
            BigDecimal companyFeeTax = BigDecimal.ZERO;
            BigDecimal companyFeeSum = BigDecimal.ZERO;
            if(priceType == 0){
                //含税价
                companyFeeSum = companyFee;
                //不含税价
                companyFeeTax = companyFeeSum.divide(companyTaxRate.add(BigDecimal.ONE),10,roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                //税费
                companyFee = companyFeeSum.subtract(companyFeeTax);
            }else {
                //价格类型不含税价格
                companyFeeTax = companyFee.multiply(companyTaxRate).setScale(2, roundMode);
                //含税价
                companyFeeSum = companyFee.add(companyFeeTax);
            }
            worderWaitAccount.setCompanyBalanceFee(companyFee);
            worderWaitAccount.setCompanyBalanceFeeTax(companyFeeTax);
            worderWaitAccount.setCompanyBalanceFeeSum(companyFeeSum);
            if (StringUtils.isNotEmpty(brandId) && brandId == "18"){
                QueryWrapper<WorderExtFieldEntity> worderExtFieldWrapper = new QueryWrapper<>();
                worderExtFieldWrapper.eq("field_id",102);
                worderExtFieldWrapper.eq("worder_no",worderNo);
                worderExtFieldWrapper.last("limit 1");
                WorderExtFieldEntity extFieldEntity = worderExtFieldDao.selectOne(worderExtFieldWrapper);
                String fieldValue = extFieldEntity.getFieldValue();
                UpdateWrapper<WorderWaitAccountEntity> worderWaitAccountWrapper = new UpdateWrapper<>();
                worderWaitAccountWrapper.eq("worder_id", worderId);
                worderWaitAccountWrapper.set("company_id", fieldValue);
                worderWaitAccountDao.update(worderWaitAccount,worderWaitAccountWrapper);
            }

            worderBalanceFeeEntity.setCompanyBalanceFee(companyFee);
            worderBalanceFeeEntity.setCompanyBalanceFeeSum(companyFeeSum);
            worderBalanceFeeEntity.setCompanyBalanceFeeTax(companyFeeTax);
        }

        BigDecimal dotFee = BigDecimal.ZERO;
        // 网点税率
        BigDecimal dotTaxRate = getTaxRateByTaxPoint(worderBalanceFee.getTaxPoint());

        /*// 网点结算规则
        Integer dotBalanceRuleId = worderBalanceFee.getDotBalanceRuleId();
        BalanceRuleEntity dotBalanceRule = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleId(dotBalanceRuleId);*/

        // 网点结算
        dotFee = dotBalanceFee(dotBalanceRule, worderId, balanaceDetailList, dotFee,
                dotTaxRate, roundMode, suiteMaterialMap, worderBalanceFee, companyRuleMaxMaterielNumMap);

        dotFee = dotFee.setScale(2, roundMode);
        BigDecimal dotFeeTax = dotFee.multiply(dotTaxRate).setScale(2, roundMode);
        BigDecimal dotFeeSum = dotFee.add(dotFeeTax);

        worderBalanceFeeEntity.setWorderId(worderId);
        worderBalanceFeeEntity.setDotBalanceFee(dotFee);
        worderBalanceFeeEntity.setDotBalanceFeeSum(dotFeeSum);
        worderBalanceFeeEntity.setDotBalanceFeeTax(dotFeeTax);
        return true;
    }

    /**
     * 校验奖励金排除的品牌
     * @param brandId
     * @return
     */
    private boolean checkEliminateBrand(String brandId) {
        // 查询字典配置的排除品牌是否包含当前品牌
        List<SysDictionaryDetailEntity> bonus = sysDictionaryDetailService.selectDetailByNumber("bonus");
        List<SysDictionaryDetailEntity> eliminateBrands = bonus.stream().filter(item -> "EliminateBrand".equals(item.getDetailName())).collect(Collectors.toList());
        if (eliminateBrands.isEmpty()) {
            return true;
        }
        SysDictionaryDetailEntity sysDictionaryDetailEntity = eliminateBrands.get(0);
        String detailNumber = sysDictionaryDetailEntity.getDetailNumber();
        if (StringUtils.isBlank(detailNumber)) {
            return true;
        }
        String[] brandIds = detailNumber.split(",");
        for (String id : brandIds) {
            if (id.equals(brandId)) {
                return false;
            }
        }
        return true;
    }

    private void insertPresurveyUserMateriel(Integer worderId, String brandId) {
        LambdaQueryWrapper<WorderInformationAttribute> worderInformationAttributeWrapper = Wrappers.lambdaQuery();
        worderInformationAttributeWrapper.eq(WorderInformationAttribute::getWorderId, worderId)
                .eq(WorderInformationAttribute::getAttributeCode, "presurvey-formal")
                .eq(WorderInformationAttribute::getAttributeValue, "Y")
                .eq(WorderInformationAttribute::getIsDelete, 0);
        Integer integer = worderInformationAttributeMapper.selectCount(worderInformationAttributeWrapper);
        if(integer > 0){
            //存在预勘测转正标识
            LambdaQueryWrapper<MaterielInformationEntity> materielInformationWrapper = Wrappers.lambdaQuery();
            materielInformationWrapper.eq(MaterielInformationEntity::getId, 581);
            MaterielInformationEntity materielInformationEntity = materialInforDao.selectOne(materielInformationWrapper);
            //插入前先删除
            worderUsedMaterielService.deleteByWorderIdAndMaterielId(581, worderId);
            //插入
            WorderUsedMaterielEntity worderUsedMaterielEntity = new WorderUsedMaterielEntity();
            worderUsedMaterielEntity.setWorderId(worderId);
            worderUsedMaterielEntity.setBrandId(Integer.parseInt(brandId));
            worderUsedMaterielEntity.setMaterielId(materielInformationEntity.getId());
            worderUsedMaterielEntity.setNum(BigDecimal.ONE);
            worderUsedMaterielEntity.setMaterielSpec(materielInformationEntity.getMaterielSpec());
            worderUsedMaterielService.savaWorderUsedMateriel(worderUsedMaterielEntity);
        }
    }

    @Override
    public Boolean calculateCompanyBalanceFeeByWorderId(Integer worderId, WorderWaitAccountEntity worderWaitAccount) {
        List<WorderBalanceFeeDetailEntity> balanaceDetailList = new ArrayList<>();
        List<WorderBalanceFeeEntity> worderBalanceList = new ArrayList<>();
        WorderBalanceFeeEntity worderBalanceFeeEntity = new WorderBalanceFeeEntity();
        worderBalanceFeeEntity.setWorderId(worderId);
        worderBalanceFeeEntity.setWorderSetStatus(1);
        worderBalanceFeeEntity.setWorderSetStatusValue("车企待结算");
        worderBalanceFeeEntity.setWorderSetFlag(true);
        worderBalanceList.add(worderBalanceFeeEntity);
        // 计算
        calculateCompanyBalanceFeeByWorderId(worderId, worderWaitAccount, worderBalanceFeeEntity, balanaceDetailList, true);
        // 保存
        if(CollectionUtils.isNotEmpty(worderBalanceList)){
            baseMapper.batchSaveWorderBalanceFee(worderBalanceList);
        }
        //修改结算明细为删除状态
        worderBalanceFeeDetailService.update(new UpdateWrapper<WorderBalanceFeeDetailEntity>().set("is_delete","1").eq("is_delete","0").eq("worder_id",worderId).in("balance_type", 0,2).in("balance_target", 0,1));
        if(CollectionUtils.isNotEmpty(balanaceDetailList)){
            worderBalanceFeeDetailService.saveBatch(balanaceDetailList);
        }
        return true;
    }


    @Override
    public Boolean recountCalculateBalanceFeeByWorderId(Integer worderId, WorderWaitAccountEntity worderWaitAccount,
        Boolean companyFlag) {

        List<WorderBalanceFeeDetailEntity> balanaceDetailList = new ArrayList<>();
        WorderBalanceFeeEntity worderBalanceFeeEntity = new WorderBalanceFeeEntity();
        worderBalanceFeeEntity.setWorderId(worderId);
        // 计算
        calculateCompanyBalanceFeeByWorderId(worderId, worderWaitAccount, worderBalanceFeeEntity, balanaceDetailList,
                companyFlag);
        // 保存
        baseMapper.updateWorderInfo(worderBalanceFeeEntity);
        if(CollectionUtils.isNotEmpty(balanaceDetailList)){
            if (companyFlag){
                worderBalanceFeeDetailService.remove(new QueryWrapper<WorderBalanceFeeDetailEntity>()
                        .eq("worder_id", worderId).in("balance_type", 0,2).eq("balance_target", 0));
            }
            worderBalanceFeeDetailService.remove(new QueryWrapper<WorderBalanceFeeDetailEntity>()
                    .eq("worder_id", worderId).in("balance_type", 0,2).eq("balance_target", 1));
            worderBalanceFeeDetailService.saveBatch(balanaceDetailList);
        }
        return true;
    }

    @Override
    public List<WorderBalanceFeeEntity> queryWorderForBalance() {
        return baseMapper.queryWorderForBalance();
    }


    /**
     * 计算结算金额（厂商结算金额、网点结算金额）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void caculateBalanceFee() {
        //查询工单结算状态为 工单待计算
        List<WorderBalanceFeeEntity> worderList = baseMapper.queryWorderForBalance();
        if (CollectionUtils.isEmpty(worderList)) {
            return;
        }
        //关联结算规则集合
        Set<Integer> balanceRuleIdSet = new HashSet<>();
        //关联套包集合
        Set<Integer> suiteIdSet = new HashSet<>();
        for (WorderBalanceFeeEntity e : worderList) {
            //关联工单结算规则ID-车企
            balanceRuleIdSet.add(e.getCompanyBalanceRuleId());
            //关联工单结算规则ID-网点
            balanceRuleIdSet.add(e.getDotBalanceRuleId());
            //关联套包ID
            suiteIdSet.add(e.getSuiteId());
        }
        //根据结算规则集合查询结算规则和详情（balance_rule，balance_rule_detail）
        List<BalanceRuleEntity> balanceRuleList = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleIds(balanceRuleIdSet);
        //创建结算规则MAP集合<key = 结算规则ID，value = 结算规则对象>
        Map<Integer, BalanceRuleEntity> balanceRuleMap = new HashMap<>();
        balanceRuleList.forEach(e -> balanceRuleMap.put(e.getId(), e));

        /* 查询所有关联的套包 start*/
        QueryWrapper<SuiteDetailEntity> qw = new QueryWrapper<SuiteDetailEntity>().in("suite_id", suiteIdSet);
        List<SuiteDetailEntity> suiteDetailList = suiteDetailDao.selectList(qw);
        //转换成Map，方便快速查找，格式：suiteId（套包ID） -> ( materielId（物料ID） -> num（数量） )
        Map<Integer, Map<Integer, BigDecimal>> suiteMap = new HashMap<>();
        for (SuiteDetailEntity s : suiteDetailList) {
            Integer suiteId = s.getSuiteId();
            if (!suiteMap.containsKey(suiteId)) {
                suiteMap.put(suiteId, new HashMap<>());
            }
            suiteMap.get(suiteId).put(s.getMaterielId(), s.getNum());
        }
        /* 查询所有关联的套包 end*/

        //日日顺对厂商收款的税率
        BigDecimal companyTaxRate = balanceProperties.RRS_COMPANY_TAX_RATE;
        //结算金额计算的舍入模式，使用BigDecimal的舍入模式 6：四舍六入五成双（银行家舍入法） 4：四舍五入 1：截取
        int roundMode = balanceProperties.ROUND_MODE;

        List<WorderBalanceFeeEntity> worderBalanceList = new ArrayList<>();
        List<WorderBalanceFeeDetailEntity> balanaceDetailList = new ArrayList<>();
        //查询结算税点字典数据
        SysDictionaryEntity sysDictionary = sysDictionaryService.getOne(new QueryWrapper<SysDictionaryEntity>().eq("dic_number", "balance_tax_rate"));
        //逐条计算结算金额
        for (WorderBalanceFeeEntity e : worderList) {
            //工单ID
            Integer worderId = e.getWorderId();
            //获取工单对应的套包
            Map<Integer, BigDecimal> suiteMaterielMap = suiteMap.get(e.getSuiteId());
            /* 计算厂商结算金额和网点结算金额 start */
            //厂商结算规则
            BalanceRuleEntity companyBalanceRule = balanceRuleMap.get(e.getCompanyBalanceRuleId());
            //网点结算规则
            BalanceRuleEntity dotBalanceRule = balanceRuleMap.get(e.getDotBalanceRuleId());
            //厂商结算费用
            BigDecimal companyFee = BigDecimal.ZERO;
            //网点结算费用
            BigDecimal dotFee = BigDecimal.ZERO;
            //网点对应税点小数值，百分比
            BigDecimal dotTaxRate = new BigDecimal(0);
            //网点税点字典ID
            String taxPoint = e.getTaxPoint();
            //查询税点详情
            List<SysDictionaryDetailEntity> detailEntities;
            detailEntities = sysDictionaryDetailService.list(new QueryWrapper<SysDictionaryDetailEntity>()
                    .eq("dictionary_id", sysDictionary.getId())
                    .eq("detail_number", taxPoint));

            if (detailEntities.size() > 0) {
                SysDictionaryDetailEntity sysDictionaryDetail = detailEntities.get(0);
                dotTaxRate = new BigDecimal(sysDictionaryDetail.getDetailName());
            } else {
                // 网点税点不存在
                log.error("网点[" + e.getDotName() + "]税点未找到,工单ID: " + e.getWorderId());
                continue;
            }
            //
            List<WorderBalanceFeeDetailEntity> companyDetailList = new ArrayList<>();
            List<WorderBalanceFeeDetailEntity> dotDetailList = new ArrayList<>();

            //计算厂商套包结算金额，比那里厂商结算规则下的详情
            for (BalanceRuleDetailEntity rm : companyBalanceRule.getBalanceItemList()) {
                //并且规则的物料id等于套包的id
                //添加工程规则结算判断
                if (rm.getIsSuite() == 1 && e.getSuiteId().equals(rm.getMaterielId())) {
                    //单价
                    BigDecimal companyFee1 = rm.getPrice();
                    //把单价保留两位小数四舍六入
                    companyFee1 = companyFee1.setScale(2, roundMode);
                    //把单价四舍六入后乘以对应税点，然后在进行四舍六入
                    BigDecimal companyTax1 = companyFee1.multiply(companyTaxRate).setScale(2, roundMode);
                    //把单价金额加上（单价乘以税点的金额）
                    BigDecimal companyFeeSum1 = companyFee1.add(companyTax1);
                    //加入进厂商结算总金额
                    companyFee = companyFee.add(companyFee1);
                    //存入费用详细实体类
                    WorderBalanceFeeDetailEntity companyDetailEntity = new WorderBalanceFeeDetailEntity();
                    companyDetailEntity.setWorderId(worderId);
                    companyDetailEntity.setBalanceTarget(0);
                    companyDetailEntity.setBalanceType(0);
                    companyDetailEntity.setMaterielId(e.getSuiteId());
                    companyDetailEntity.setIsSuite(1);
                    companyDetailEntity.setBalanceFee(companyFee1);
                    companyDetailEntity.setBalanceFeeTax(companyTax1);
                    companyDetailEntity.setBalanceFeeSum(companyFeeSum1);
                    companyDetailEntity.setNum(BigDecimal.ONE);
                    companyDetailList.add(companyDetailEntity);
                }
            }

//                //计算网点套包结算金额
//                for (BalanceRuleDetailEntity rm : dotBalanceRule.getBalanceItemList()) {
//                    if(rm.getIsSuite() == 1 && e.getSuiteId() == rm.getMaterielId()){
//                        BigDecimal dotFee1 = rm.getPrice().multiply(e.getDotBalanceRate());
//                        //金额规范化，并计算税额和含税价
//                        dotFee1 = dotFee1.setScale(2, roundMode);
//                        BigDecimal dotTax1 = dotFee1.multiply(dotTaxRate).setScale(2, roundMode);
//                        BigDecimal dotFeeSum1 = dotFee1.add(dotTax1);
//                        //加入总金额
//                        dotFee = dotFee.add(dotFee1);
//                        //费用详细实体类
//                        WorderBalanceFeeDetailEntity dotDetailEntity = new WorderBalanceFeeDetailEntity();
//                        dotDetailEntity.setWorderId(worderId);
//                        dotDetailEntity.setBalanceTarget(1);
//                        dotDetailEntity.setBalanceType(0);
//                        dotDetailEntity.setMaterielId(e.getSuiteId());
//                        dotDetailEntity.setIsSuite(1);
//                        dotDetailEntity.setBalanceFee(dotFee1);
//                        dotDetailEntity.setBalanceFeeTax(dotTax1);
//                        dotDetailEntity.setBalanceFeeSum(dotFeeSum1);
//                        dotDetailEntity.setNum(BigDecimal.ONE);
//                        dotDetailList.add(dotDetailEntity);
//                    }
//                }

            // 车企固定费用结算
            Result resultCompany = setProjectBlanaceRuleFee(companyBalanceRule, worderId, balanaceDetailList, companyFee, companyTaxRate, roundMode, e, 0);
            Set<Integer> afterCommpanyBlanaceMaterielIds = resultCompany.getMaterielIds();
            if(resultCompany.getFee() != null){
                companyFee = companyFee.add(resultCompany.getFee());
            }
            // 网点工程费用结算
            Result resultDot = setProjectBlanaceRuleFee(dotBalanceRule, worderId, balanaceDetailList, dotFee, dotTaxRate, roundMode, e, 1);
            Set<Integer> afterDotBlanaceMaterielIds = resultDot.getMaterielIds();
            if(resultDot.getFee() != null){
                dotFee = dotFee.add(resultDot.getFee());
            }
            //整理厂商结算规则中每个物料的最大数量
            Map<Integer, BigDecimal> companyRuleMaxMaterielNumMap = this.getBalanceRuleMaxMaterielNumMap(companyBalanceRule);
            //逐个物料计算厂商结算金额和网点结算金额，遍历工单使用的物料集合信息
            for (WorderUsedMaterielEntity um : e.getMaterielList()) {
                //物料id
                Integer materielId = um.getMaterielId();
                //如果工程结算已经算过该物料无须重新结算
                if(afterCommpanyBlanaceMaterielIds != null && afterCommpanyBlanaceMaterielIds.contains(materielId)){
                    continue;
                }
                if(afterDotBlanaceMaterielIds != null && afterDotBlanaceMaterielIds.contains(materielId)){
                    continue;
                }
                //厂商结算的数量（套包包含数量和厂商结算金额中的最大数量的和）与实际使用的数量，两者的小值即为网点工单结算的数量
                BigDecimal dotFeeNum = suiteMaterielMap.getOrDefault(materielId, BigDecimal.ZERO)
                        .add(companyRuleMaxMaterielNumMap.getOrDefault(materielId, BigDecimal.ZERO))
                        .min(um.getNum());
                if (dotFeeNum.compareTo(BigDecimal.ZERO) <= 0) {
                    //计算数量小于0则不计算
                    continue;
                }
                //计算网点结算金额
                List<Entity> dotFeeList = new ArrayList<>();
                BigDecimal dotFee1 = caculateOneMaterielFee(dotBalanceRule, materielId, dotFeeNum, e, dotFeeList);
                Collections.sort(dotFeeList);
                String dotDesc = "计价数量:" + dotFeeNum + ", 详情如下：\n" + StringUtils.join(dotFeeList, '\n');
                if (balanceProperties.DOT_WORDER_USE_BALANCE_RATE) {
                    BigDecimal dotBalanceRate = e.getDotBalanceRate() != null ? e.getDotBalanceRate() : BigDecimal.ONE;
                    dotFee1 = dotFee1.multiply(dotBalanceRate).setScale(2, roundMode);
                    dotDesc = dotDesc + "\n结算系数:" + dotBalanceRate;
                }
                WorderBalanceFeeDetailEntity dotDetailEntity = new WorderBalanceFeeDetailEntity();
                dotDetailEntity.setWorderId(worderId);
                dotDetailEntity.setBalanceTarget(1);
                dotDetailEntity.setBalanceType(0);
                dotDetailEntity.setMaterielId(materielId);
                dotDetailEntity.setIsSuite(0);
                //金额规范化，并计算税额和含税价
                dotFee1 = dotFee1.setScale(2, roundMode);
                BigDecimal dotTax1 = dotFee1.multiply(dotTaxRate).setScale(2, roundMode);
                BigDecimal dotFeeSum1 = dotFee1.add(dotTax1);
                dotDetailEntity.setBalanceFee(dotFee1);
                dotDetailEntity.setBalanceFeeTax(dotTax1);
                dotDetailEntity.setBalanceFeeSum(dotFeeSum1);
                dotDetailEntity.setNum(dotFeeNum);
                dotDetailEntity.setDescription(dotDesc);
                dotDetailList.add(dotDetailEntity);
                dotFee = dotFee.add(dotFee1);
                //计算厂商结算金额
                //计算数量 = 实际使用数量 - 套包包含该物料数量
                BigDecimal suiteContainsNum = suiteMaterielMap.getOrDefault(materielId, BigDecimal.ZERO);
                BigDecimal feeNum = um.getNum().subtract(suiteContainsNum);
                if (feeNum.compareTo(BigDecimal.ZERO) <= 0 || companyRuleMaxMaterielNumMap.get(materielId) == null) {
                    //计算数量小于0或者结算规则中不包含则不计算
                    continue;
                }

                List<Entity> companyFeeList = new ArrayList<>();
                BigDecimal companyFee1 = caculateOneMaterielFee(companyBalanceRule, materielId, feeNum, e, companyFeeList);
                Collections.sort(companyFeeList);
                String desc1 = "套包包含数量：" + suiteContainsNum + ", 实际计价数量：" + feeNum.min(companyRuleMaxMaterielNumMap.getOrDefault(materielId, feeNum)) + ", 详情如下：\n";
                String companyDesc = desc1 + StringUtils.join(companyFeeList, '\n');
                WorderBalanceFeeDetailEntity companyDetailEntity = new WorderBalanceFeeDetailEntity();
                companyDetailEntity.setWorderId(worderId);
                companyDetailEntity.setBalanceTarget(0);
                companyDetailEntity.setBalanceType(0);
                companyDetailEntity.setMaterielId(materielId);
                companyDetailEntity.setIsSuite(0);

                //金额规范化，并计算税额和含税价
                companyFee1 = companyFee1.setScale(2, roundMode);
//                    BigDecimal companyTax1 = companyFee1.multiply(companyTaxRate).setScale(2, roundMode);
//                    BigDecimal companyFeeSum1 = companyFee1.add(companyTax1);
                if (companyBalanceRule.getPriceType() == 0) {
                    //结算规则的价格类型为含税价
                    //含税价税额为：含税价/(税率+1)再乘以税率
                    //companyTax1 = companyFee1.divide(companyTaxRate.add(BigDecimal.ONE)).multiply(companyTaxRate).setScale(2, roundMode);
                    BigDecimal companyTax1 = companyFee1.divide(companyTaxRate.add(BigDecimal.ONE), 10, roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                    //不含税价
                    BigDecimal companyFeeSum1 = companyFee1.subtract(companyTax1);
                    companyDetailEntity.setBalanceFee(companyFeeSum1);
                    companyDetailEntity.setBalanceFeeTax(companyTax1);
                    companyDetailEntity.setBalanceFeeSum(companyFee1);
                } else {
                    //价格类型为不含税价
                    BigDecimal companyTax1 = companyFee1.multiply(companyTaxRate).setScale(2, roundMode);
                    //含税价
                    BigDecimal companyFeeSum1 = companyFee1.add(companyTax1);
                    companyDetailEntity.setBalanceFee(companyFee1);
                    companyDetailEntity.setBalanceFeeTax(companyTax1);
                    companyDetailEntity.setBalanceFeeSum(companyFeeSum1);
                }
//                    companyDetailEntity.setBalanceFee(companyFee1);
//                    companyDetailEntity.setBalanceFeeTax(companyTax1);
//                    companyDetailEntity.setBalanceFeeSum(companyFeeSum1);
                companyDetailEntity.setNum(feeNum);
                companyDetailEntity.setDescription(companyDesc);
                companyDetailList.add(companyDetailEntity);
                companyFee = companyFee.add(companyFee1);

            }
            /* 计算厂商结算金额和网点结算金额 end */
            //金额规范化，并计算税额和含税价
            companyFee = companyFee.setScale(2, roundMode);
            //BigDecimal companyFeeTax = companyFee.multiply(companyTaxRate).setScale(2, roundMode);
            //BigDecimal companyFeeSum = companyFee.add(companyFeeTax);
            dotFee = dotFee.setScale(2, roundMode);
            BigDecimal dotFeeTax = dotFee.multiply(dotTaxRate).setScale(2, roundMode);
            BigDecimal dotFeeSum = dotFee.add(dotFeeTax);

            if (companyBalanceRule.getPriceType() == 0) {
                //价格类型为含税价
                BigDecimal companyFeeTax = companyFee.divide(companyTaxRate.add(BigDecimal.ONE), 10, roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                //不含税价
                BigDecimal companyFeeSum = companyFee.subtract(companyFeeTax);
                //调整尾差
                this.adjustedTailDiff(companyFeeSum, companyFeeTax, companyFee, companyDetailList);
                e.setCompanyBalanceFee(companyFeeSum);
                e.setCompanyBalanceFeeTax(companyFeeTax);
                e.setCompanyBalanceFeeSum(companyFee);
            } else {
                //价格类型不含税价格
                BigDecimal companyFeeTax = companyFee.multiply(companyTaxRate).setScale(2, roundMode);
                //含税价
                BigDecimal companyFeeSum = companyFee.add(companyFeeTax);
                //调整尾差
                this.adjustedTailDiff(companyFee, companyFeeTax, companyFeeSum, companyDetailList);
                e.setCompanyBalanceFee(companyFee);
                e.setCompanyBalanceFeeTax(companyFeeTax);
                e.setCompanyBalanceFeeSum(companyFeeSum);
            }

            //调整尾差
            //this.adjustedTailDiff(companyFee, companyFeeTax, companyFeeSum, companyDetailList);
            this.adjustedTailDiff(dotFee, dotFeeTax, dotFeeSum, dotDetailList);

//                e.setCompanyBalanceFee(companyFee);
//                e.setCompanyBalanceFeeTax(companyFeeTax);
//                e.setCompanyBalanceFeeSum(companyFeeSum);
            e.setDotBalanceFee(dotFee);
            e.setDotBalanceFeeTax(dotFeeTax);
            e.setDotBalanceFeeSum(dotFeeSum);
            e.setWorderSetStatus(1);
            e.setWorderSetStatusValue("车企待结算");
            e.setWorderSetFlag(true);
            worderBalanceList.add(e);
            balanaceDetailList.addAll(companyDetailList);
            balanaceDetailList.addAll(dotDetailList);
        }
        //批量保存结算结果
        if (CollectionUtils.isNotEmpty(worderBalanceList)) {
            baseMapper.batchSaveWorderBalanceFee(worderBalanceList);
        }
//        if(CollectionUtils.isNotEmpty(increBalanceList)){
//            baseMapper.batchSaveIncreBalanceFee(increBalanceList);
//        }
        if (CollectionUtils.isNotEmpty(balanaceDetailList)) {
            worderBalanceFeeDetailService.saveBatch(balanaceDetailList);
        }
    }

    /**
     * 计算单项物料的结算价格
     * @param rule 使用的结算规则
     * @param materielId 计算价格的物料
     * @param materielNum 物料使用的数量
     * @param descriptionList 记录计价详情的列表
     * @return
     */
    private BigDecimal caculateOneMaterielFee(BalanceRuleEntity rule, Integer materielId, BigDecimal materielNum,WorderBalanceFeeEntity worderBalanceFee, List<Entity> descriptionList){
        BigDecimal fee = BigDecimal.ZERO;
        //判断当前物料是否被工程结算过
        for (BalanceRuleDetailEntity rm : rule.getBalanceItemList()) {
            if(materielId.equals(rm.getMaterielId())){
                BigDecimal startNum = rm.getStartNum();
                BigDecimal endNum = rm.getEndNum();
                //如果数量0-0代表所有的数量都用这个价格
                if(endNum.compareTo(BigDecimal.ZERO) == 0 && startNum.compareTo(BigDecimal.ZERO) == 0){
                    BigDecimal fee1 = materielNum.multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(BigDecimal.ZERO, "0-"+materielNum+","+rm.getPrice()+","+fee1));
                    }
                }
                //分段式计价，处于[startNum,endNum]范围，则计算[startNum,materielNum]部分价格；小于startNum，则不计算；大于endNum，计算该分段的价格
                else if((materielNum.compareTo(endNum) <= 0 || endNum.compareTo(BigDecimal.ZERO) == 0) && materielNum.compareTo(startNum) > 0){
                    BigDecimal fee1 = materielNum.subtract(startNum).multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(startNum, startNum+"-"+materielNum+","+rm.getPrice()+","+fee1));
                    }
                }else if(endNum.compareTo(BigDecimal.ZERO) > 0 && materielNum.compareTo(endNum) > 0){
                    BigDecimal fee1 = endNum.subtract(startNum).multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(startNum, startNum+"-"+endNum+","+rm.getPrice()+","+fee1));
                    }
                }
            }
        }
        return fee;
    }

    /**
     * 计算单项物料的结算价格
     * @param rule 使用的结算规则
     * @param materielId 计算价格的物料
     * @param materielNum 物料使用的数量
     * @param descriptionList 记录计价详情的列表
     * @return
     */
    private BigDecimal caculateOneMaterielFee(BalanceRuleEntity rule, Integer materielId, BigDecimal materielNum, List<Entity> descriptionList){
        BigDecimal fee = BigDecimal.ZERO;
        for (BalanceRuleDetailEntity rm : rule.getBalanceItemList()) {
            if(rm.getIsSuite() == 0 && materielId.equals(rm.getMaterielId())){
                BigDecimal startNum = rm.getStartNum();
                BigDecimal endNum = rm.getEndNum();
                //如果数量0-0代表所有的数量都用这个价格
                if(endNum.compareTo(BigDecimal.ZERO) == 0 && startNum.compareTo(BigDecimal.ZERO) == 0){
                    BigDecimal fee1 = materielNum.multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(BigDecimal.ZERO, "0-"+materielNum+","+rm.getPrice()+","+fee1));
                    }
                }
                //分段式计价，处于[startNum,endNum]范围，则计算[startNum,materielNum]部分价格；小于startNum，则不计算；大于endNum，计算该分段的价格
                else if((materielNum.compareTo(endNum) <= 0 || endNum.compareTo(BigDecimal.ZERO) == 0) && materielNum.compareTo(startNum) > 0){
                    BigDecimal fee1 = materielNum.subtract(startNum).multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(startNum, startNum+"-"+materielNum+","+rm.getPrice()+","+fee1));
                    }
                }else if(endNum.compareTo(BigDecimal.ZERO) > 0 && materielNum.compareTo(endNum) > 0){
                    BigDecimal fee1 = endNum.subtract(startNum).multiply(rm.getPrice());
                    fee = fee.add(fee1);
                    if(descriptionList != null){
                        descriptionList.add(new Entity(startNum, startNum+"-"+endNum+","+rm.getPrice()+","+fee1));
                    }
                }
            }
        }
        return fee;
    }

    /**
     * 调整尾差
     * @param fee 不含税总价
     * @param tax 总税额
     * @param feeAll 含税总价
     * @param details 费用明细
     */
    private void adjustedTailDiff(BigDecimal fee, BigDecimal tax, BigDecimal feeAll, List<WorderBalanceFeeDetailEntity> details){
        if(CollectionUtils.isEmpty(details)){
            return;
        }
        BigDecimal feeSum = BigDecimal.ZERO;
        BigDecimal taxSum = BigDecimal.ZERO;
        BigDecimal feeAllSum = BigDecimal.ZERO;
        for (int i = 0; i < details.size()-1; i++) {
            WorderBalanceFeeDetailEntity d = details.get(i);
            feeSum = feeSum.add(d.getBalanceFee());
            taxSum = taxSum.add(d.getBalanceFeeTax());
            feeAllSum = feeAllSum.add(d.getBalanceFeeSum());
        }
        WorderBalanceFeeDetailEntity d = details.get(details.size()-1);
        d.setBalanceFee(fee.subtract(feeSum));
        d.setBalanceFeeTax(tax.subtract(taxSum));
        d.setBalanceFeeSum(feeAll.subtract(feeAllSum));
        d.setDescription(d.getDescription() + "\n含尾差");
    }

    /**
     *
     * @param informationEntity  工单信息
     * @param templateInfoById   模板信息
     * @param dotBalanceRule    网点结算规则
     */
    public String  hierarchicalSettlement(WorderInformationEntity informationEntity,WorderTemplateDto templateInfoById, BalanceRuleEntity dotBalanceRule){


        //只能加一次物料
        List<Integer> allMaterielList=new ArrayList<Integer>();

        int worderId=informationEntity.getWorderId();


        //勘测完成时效激励
        //勘测满足时间
        SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.findAllByDictionaryNumber("surveyEfficiency");
        //勘测物料,
        MaterielInformationEntity converyMaterielInfo=materielInforService.queryByMaterielId(485);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(converyMaterielInfo.getId(),worderId);
        //A区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoA=materielInforService.queryByMaterielId(486);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoA.getId(),worderId);
        //B区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoB=materielInforService.queryByMaterielId(487);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoB.getId(),worderId);
        //C区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoC=materielInforService.queryByMaterielId(488);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoC.getId(),worderId);
        //D区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoD=materielInforService.queryByMaterielId(489);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoD.getId(),worderId);
        //E区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoE=materielInforService.queryByMaterielId(490);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoE.getId(),worderId);
        //F区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoF=materielInforService.queryByMaterielId(491);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoF.getId(),worderId);

        //G区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoG=materielInforService.queryByMaterielId(493);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoG.getId(),worderId);

        //H区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoH=materielInforService.queryByMaterielId(899);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoH.getId(),worderId);

        //I区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoI=materielInforService.queryByMaterielId(900);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoI.getId(),worderId);

        //J区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoJ=materielInforService.queryByMaterielId(901);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoJ.getId(),worderId);

        //K区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoK=materielInforService.queryByMaterielId(902);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoK.getId(),worderId);


        //L区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoL=materielInforService.queryByMaterielId(903);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoL.getId(),worderId);


        //M区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoM=materielInforService.queryByMaterielId(904);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoM.getId(),worderId);



        //N区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoN=materielInforService.queryByMaterielId(905);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoN.getId(),worderId);


        //O区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoO=materielInforService.queryByMaterielId(906);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoO.getId(),worderId);

        //P区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoP=materielInforService.queryByMaterielId(907);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoP.getId(),worderId);

        //Q区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoQ=materielInforService.queryByMaterielId(908);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoQ.getId(),worderId);

        //R区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoR=materielInforService.queryByMaterielId(909);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoR.getId(),worderId);




        //S区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoS=materielInforService.queryByMaterielId(910);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoS.getId(),worderId);

        //T区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoT=materielInforService.queryByMaterielId(911);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoT.getId(),worderId);

        //U区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoU=materielInforService.queryByMaterielId(913);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoU.getId(),worderId);

        //V区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoV=materielInforService.queryByMaterielId(914);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoV.getId(),worderId);



        //W区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoW=materielInforService.queryByMaterielId(915);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoW.getId(),worderId);

        //X区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoX=materielInforService.queryByMaterielId(916);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoX.getId(),worderId);

        //Y区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoY=materielInforService.queryByMaterielId(917);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoY.getId(),worderId);

        //Z区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoZ=materielInforService.queryByMaterielId(918);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoZ.getId(),worderId);


        //AB区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoAB=materielInforService.queryByMaterielId(919);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoAB.getId(),worderId);

        //AC区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoAC=materielInforService.queryByMaterielId(920);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoAC.getId(),worderId);

        //AD区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoAD=materielInforService.queryByMaterielId(921);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoAD.getId(),worderId);

        //AE区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoAE=materielInforService.queryByMaterielId(922);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoAE.getId(),worderId);


        //AF区域-安装完成时效激励物料
        MaterielInformationEntity installMaterielInfoAF=materielInforService.queryByMaterielId(923);
        //删除物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(installMaterielInfoAF.getId(),worderId);





        //获取勘测时效
        String surveyEfficiency =  baseMapper.querySurveyEfficiency(informationEntity.getWorderNo());
        //获取安装时效
        String installEfficiency =  baseMapper.queryInstallEfficiency(informationEntity.getWorderNo());

        //获取安装时效规则
        EfficiencyAreaRuleEntity efficiencyAreaRuleEntity=efficiencyAreaRuleMapper.queryOnefficiencyAreaRule(Integer.valueOf(templateInfoById.getBrandId()),Integer.valueOf(informationEntity.getAreaId()));

        //清除工单属性
        UpdateWrapper<WorderInformationAttribute> updateWorderInformationAttribute=new UpdateWrapper<WorderInformationAttribute>();
        updateWorderInformationAttribute.eq("worder_id",worderId);
        updateWorderInformationAttribute.eq("attribute","Efficiency");
        worderInformationAttributeMapper.delete(updateWorderInformationAttribute);

        //根据结算规则增加物料到物料表
        dotBalanceRule.getBalanceItemList().forEach(v -> {
            //配置了勘测物料
            if (v.getMaterielId()==485&&StringUtils.isNotBlank(surveyEfficiency)){
                //时效差  勘测资料上传news系统时间-系统派单时间勘测资料上传news系统时间-系统派单时间
                Integer surveyTimeDifference = Integer.valueOf(surveyEfficiency)-(Integer.valueOf(sysDictionaryDetailEntity.getDetailNumber()));
                //保存订单属性
                WorderInformationAttribute worderInformationAttribute=new WorderInformationAttribute();
                worderInformationAttribute.setWorderId(worderId);
                worderInformationAttribute.setAttribute("Efficiency");
                worderInformationAttribute.setAttributeCode("Efficiency-surveyTime");
                worderInformationAttribute.setAttributeName("分段结算工单勘测时效(小时)");
                worderInformationAttribute.setAttributeValue((surveyEfficiency));
                worderInformationAttributeMapper.insert(worderInformationAttribute);
                if(surveyTimeDifference<=0) {
                    savaWorderUsedMateriel(worderId,converyMaterielInfo,null,null);
                }
            }



            //配置了勘测物料
            if(null!=efficiencyAreaRuleEntity&&StringUtils.isNotBlank(efficiencyAreaRuleEntity.getExecRule())&&StringUtils.isNotBlank(installEfficiency)){
                if (v.getMaterielId()==486&&efficiencyAreaRuleEntity.getExecRule().trim().equals("A")&&!allMaterielList.contains(installMaterielInfoA.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoA,allMaterielList,efficiencyAreaRuleEntity);
                } else if (v.getMaterielId()==487&&efficiencyAreaRuleEntity.getExecRule().trim().equals("B")&&!allMaterielList.contains(installMaterielInfoB.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoB,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==488&&efficiencyAreaRuleEntity.getExecRule().trim().equals("C")&&!allMaterielList.contains(installMaterielInfoC.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoC,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==489&&efficiencyAreaRuleEntity.getExecRule().trim().equals("D")&&!allMaterielList.contains(installMaterielInfoD.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoD,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==490&&efficiencyAreaRuleEntity.getExecRule().trim().equals("E")&&!allMaterielList.contains(installMaterielInfoE.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoE,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==491&&efficiencyAreaRuleEntity.getExecRule().trim().equals("F")&&!allMaterielList.contains(installMaterielInfoF.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoF,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==493&&efficiencyAreaRuleEntity.getExecRule().trim().equals("G")&&!allMaterielList.contains(installMaterielInfoG.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoG,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==899&&efficiencyAreaRuleEntity.getExecRule().trim().equals("H")&&!allMaterielList.contains(installMaterielInfoH.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoH,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==900&&efficiencyAreaRuleEntity.getExecRule().trim().equals("I")&&!allMaterielList.contains(installMaterielInfoI.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoI,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==901&&efficiencyAreaRuleEntity.getExecRule().trim().equals("J")&&!allMaterielList.contains(installMaterielInfoJ.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoJ,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==902&&efficiencyAreaRuleEntity.getExecRule().trim().equals("K")&&!allMaterielList.contains(installMaterielInfoK.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoK,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==903&&efficiencyAreaRuleEntity.getExecRule().trim().equals("L")&&!allMaterielList.contains(installMaterielInfoL.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoL,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==904&&efficiencyAreaRuleEntity.getExecRule().trim().equals("M")&&!allMaterielList.contains(installMaterielInfoM.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoM,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==905&&efficiencyAreaRuleEntity.getExecRule().trim().equals("N")&&!allMaterielList.contains(installMaterielInfoN.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoN,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==906&&efficiencyAreaRuleEntity.getExecRule().trim().equals("O")&&!allMaterielList.contains(installMaterielInfoO.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoO,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==907&&efficiencyAreaRuleEntity.getExecRule().trim().equals("P")&&!allMaterielList.contains(installMaterielInfoP.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoP,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==908&&efficiencyAreaRuleEntity.getExecRule().trim().equals("Q")&&!allMaterielList.contains(installMaterielInfoQ.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoQ,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==909&&efficiencyAreaRuleEntity.getExecRule().trim().equals("R")&&!allMaterielList.contains(installMaterielInfoR.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoR,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==910&&efficiencyAreaRuleEntity.getExecRule().trim().equals("S")&&!allMaterielList.contains(installMaterielInfoS.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoS,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==911&&efficiencyAreaRuleEntity.getExecRule().trim().equals("T")&&!allMaterielList.contains(installMaterielInfoT.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoT,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==913&&efficiencyAreaRuleEntity.getExecRule().trim().equals("U")&&!allMaterielList.contains(installMaterielInfoU.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoU,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==914&&efficiencyAreaRuleEntity.getExecRule().trim().equals("V")&&!allMaterielList.contains(installMaterielInfoV.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoV,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==915&&efficiencyAreaRuleEntity.getExecRule().trim().equals("W")&&!allMaterielList.contains(installMaterielInfoW.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoW,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==916&&efficiencyAreaRuleEntity.getExecRule().trim().equals("X")&&!allMaterielList.contains(installMaterielInfoX.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoX,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==917&&efficiencyAreaRuleEntity.getExecRule().trim().equals("Y")&&!allMaterielList.contains(installMaterielInfoY.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoY,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==918&&efficiencyAreaRuleEntity.getExecRule().trim().equals("Z")&&!allMaterielList.contains(installMaterielInfoZ.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoZ,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==919&&efficiencyAreaRuleEntity.getExecRule().trim().equals("AB")&&!allMaterielList.contains(installMaterielInfoAB.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoAB,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==920&&efficiencyAreaRuleEntity.getExecRule().trim().equals("AC")&&!allMaterielList.contains(installMaterielInfoAC.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoAC,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==921&&efficiencyAreaRuleEntity.getExecRule().trim().equals("AD")&&!allMaterielList.contains(installMaterielInfoAD.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoAD,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==922&&efficiencyAreaRuleEntity.getExecRule().trim().equals("AE")&&!allMaterielList.contains(installMaterielInfoAE.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoAE,allMaterielList,efficiencyAreaRuleEntity);
                }else if (v.getMaterielId()==923&&efficiencyAreaRuleEntity.getExecRule().trim().equals("AF")&&!allMaterielList.contains(installMaterielInfoAF.getId())){
                    savaWorderUsedMateriel(worderId,installMaterielInfoAF,allMaterielList,efficiencyAreaRuleEntity);
                }
            }
        });

     return allMaterielList.size()>0?installEfficiency:"";

    }

    /**
     * 保存物料信息
     * @param worderId
     * @param materielInformationEntity
     * @param allMaterielList
     */
    public void savaWorderUsedMateriel(int worderId,MaterielInformationEntity materielInformationEntity,List<Integer> allMaterielList,EfficiencyAreaRuleEntity efficiencyAreaRuleEntity){
        BigDecimal bigDecimal=new BigDecimal(1);
        WorderUsedMaterielEntity worderUsedMaterielEntity = new WorderUsedMaterielEntity();
        worderUsedMaterielEntity.setWorderId(worderId);
        worderUsedMaterielEntity.setMaterielId(materielInformationEntity.getId());
        worderUsedMaterielEntity.setBrandId(materielInformationEntity.getMaterielBrand());
        worderUsedMaterielEntity.setMaterielSpec(materielInformationEntity
                .getMaterielSpec());
        worderUsedMaterielEntity.setNum(bigDecimal);
        worderUsedMaterielService.savaWorderUsedMateriel(
                worderUsedMaterielEntity);
        if(allMaterielList!=null){//分段结算安装
            allMaterielList.add(materielInformationEntity.getId());
            //保存订单属性
            WorderInformationAttribute worderInformationAttribute=new WorderInformationAttribute();
            worderInformationAttribute.setWorderId(worderId);
            worderInformationAttribute.setAttribute("Efficiency");
            worderInformationAttribute.setAttributeCode("Efficiency-ExecRule");
            worderInformationAttribute.setAttributeName("分段结算工单规则");
            worderInformationAttribute.setAttributeValue(efficiencyAreaRuleEntity.getExecRule());
            worderInformationAttributeMapper.insert(worderInformationAttribute);
        }


    }

    //根据增项费用计算增项物料
    public void calcUserMaterial(WorderInformationEntity worderInformation , ArrayList<String> errorMsgList, MaterielInformationEntity materielInformation, WorderImportPayDto worderImportPayDto, BigDecimal payActualAmount){

        WorderBalanceFeeEntity worder = this.baseMapper.queryOneNoUsedMaterialWorderForBalance(worderInformation.getWorderId());
        //结算规则信息
        List<BalanceRuleEntity> balanceRuleList = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleIds(new ArrayList<Integer>() {
            {
                add(worder.getAttendantBalanceRuleId());
            }
        });
        if (null == balanceRuleList || balanceRuleList.size() <= 0) {
            errorMsgList.add("业务单号:" + worderImportPayDto.getCompanyOrderNo() + ",,没有查询到增项结算规则");
            throw new RuntimeException(String.format("没有查询到增项结算规则,工单编号:%s", worderInformation.getWorderNo()));
        }
        //计算使用物料
        Map<String, BalanceRuleDetailEntity> feeMap = new HashMap<String, BalanceRuleDetailEntity>();
        Map<BigDecimal, BigDecimal> endMap = new HashMap<BigDecimal, BigDecimal>();

        BigDecimal taxRate=null;
        //计算用户增项结算金额
        List<Entity> descriptionList = new ArrayList<>();
        BigDecimal materielNum = new BigDecimal(0);
        for (BalanceRuleEntity userBalanceRule : balanceRuleList) {
            if (worder.getAttendantBalanceRuleId().equals(userBalanceRule.getId())) {
                //获取增项税率
                String taxRateValue=userBalanceRule.getBalanceTaxRateValue();
                taxRate = new BigDecimal(taxRateValue);
                BalanceRuleUtils.filterBalanceRuleDetails(userBalanceRule, worder);
                List<BalanceRuleDetailEntity> balanceItemList = userBalanceRule.getBalanceItemList();
                //根据增项物料规则计算物料数量
                for (BalanceRuleDetailEntity balanceRuleDetailEntity : balanceItemList) {
                    if (balanceRuleDetailEntity.getIsSuite() == 0 && String.valueOf(balanceRuleDetailEntity.getMaterielId()).equals(String.valueOf(materielInformation.getId()))) {
                        BigDecimal startNum = balanceRuleDetailEntity.getStartNum();
                        BigDecimal endNum = balanceRuleDetailEntity.getEndNum();
                        if (endNum.compareTo(BigDecimal.ZERO) == 0 && startNum.compareTo(BigDecimal.ZERO) == 0) {
                            materielNum = materielNum.add(payActualAmount.divide(balanceRuleDetailEntity.getPrice()));
                            if (descriptionList != null) {
                                descriptionList.add(new Entity(BigDecimal.ZERO, "0-" + materielNum + "," + balanceRuleDetailEntity.getPrice() + "," + payActualAmount));
                            }
                            break;
                        }
                        //分段式计价，处于[startNum,endNum]范围，则计算[startNum,materielNum]部分价格；小于startNum，则不计算；大于endNum，计算该分段的价格
                        else if (endNum.compareTo(BigDecimal.ZERO) > 0 && startNum.compareTo(BigDecimal.ZERO) >= 0) {
                            // BigDecimal fee = endNum.subtract(startNum).multiply(balanceRuleDetailEntity.getPrice());
                            feeMap.put(startNum.toString(), balanceRuleDetailEntity);
                        } else if (endNum.compareTo(BigDecimal.ZERO) == 0 && startNum.compareTo(BigDecimal.ZERO) > 0) {
                            endMap.put(startNum, balanceRuleDetailEntity.getPrice());
                        }
                    }
                }
            }
        }
        if (feeMap.isEmpty() && endMap.isEmpty() && (materielNum.compareTo(new BigDecimal(0)) == 0)) {
            errorMsgList.add("业务单号:" + worderImportPayDto.getCompanyOrderNo() + ",,没有查询到增项结算物料规则");
            throw new RuntimeException(String.format("没有查询到网点增项结算物料规则,工单编号:%s", worderInformation.getWorderNo()));
        }
        //计算规则[startNum,0]
        if (feeMap.isEmpty() && (!endMap.isEmpty()) && (materielNum.compareTo(new BigDecimal(0)) == 0)) {
            BigDecimal endStartNum = endMap.keySet().iterator().next();
            materielNum = payActualAmount.divide(endMap.get(endStartNum)).add(endStartNum);
            descriptionList.add(new Entity(endStartNum, endStartNum + "-" + materielNum + "," + endMap.get(endStartNum) + "," + payActualAmount));
        }
        //计算规则[startNum,endNum]
        if ((!feeMap.isEmpty())) {
            //key排序
            Map<String, BalanceRuleDetailEntity> sortMap = new TreeMap<String, BalanceRuleDetailEntity>(
                    new MapKeyComparator());
            sortMap.putAll(feeMap);
            //计算支付钱落在哪个区间内
            BigDecimal fee = new BigDecimal("0");
            for (String dataKey : sortMap.keySet()) {
                BalanceRuleDetailEntity balanceRuleDetailEntity = sortMap.get(dataKey);
                //获取当前区间的最大金额
                BigDecimal currFee = balanceRuleDetailEntity.getEndNum().subtract(balanceRuleDetailEntity.getStartNum()).multiply(balanceRuleDetailEntity.getPrice());
                //获取到达这个区间时的增项累计金额
                fee = fee.add(currFee);
                //如果支付金额小于累计到该区间的金额
                if (payActualAmount.compareTo(fee) <= 0) {
                    materielNum = (payActualAmount.subtract(fee.subtract(currFee))).divide(balanceRuleDetailEntity.getPrice()).add(balanceRuleDetailEntity.getStartNum());
                    descriptionList.add(new Entity(balanceRuleDetailEntity.getStartNum(), balanceRuleDetailEntity.getStartNum() + "-" + materielNum + "," + balanceRuleDetailEntity.getPrice() + "," + payActualAmount.subtract(fee.subtract(currFee))));
                } else {
                    descriptionList.add(new Entity(balanceRuleDetailEntity.getStartNum(), balanceRuleDetailEntity.getStartNum() + "-" + balanceRuleDetailEntity.getEndNum() + "," + balanceRuleDetailEntity.getPrice() + "," + currFee));
                }
            }
            //如果所有[startNum,endNum]金额加起来小于支付金额，则用[startNum,0]算出最后的数量
            if (materielNum.compareTo(BigDecimal.ZERO) == 0 && (!endMap.isEmpty())) {
                BigDecimal endStartNum = endMap.keySet().iterator().next();
                materielNum = payActualAmount.subtract(fee).divide(endMap.get(endStartNum)).add(endStartNum);
                descriptionList.add(new Entity(endStartNum, endStartNum + "-" + materielNum + "," + endMap.get(endStartNum) + "," + payActualAmount.subtract(fee)));
            }
        }

        if (materielNum.compareTo(BigDecimal.ZERO) == 0) {
            errorMsgList.add("业务单号:" + worderImportPayDto.getCompanyOrderNo() + ",,没有查询到增项结算物料规则");
            throw new RuntimeException(String.format("没有查询到网点增项结算物料规则,工单编号:%s", worderInformation.getWorderNo()));
        }

        //如果存在则删除现有物料
        worderUsedMaterielService.deleteByWorderIdAndMaterielId(materielInformation.getId(), worderInformation.getWorderId());
        //保存物料
        WorderUsedMaterielEntity worderUsedMaterielEntity = new WorderUsedMaterielEntity();
        worderUsedMaterielEntity.setWorderId(worderInformation.getWorderId());
        worderUsedMaterielEntity.setMaterielId(materielInformation.getId());
        worderUsedMaterielEntity.setBrandId(materielInformation.getMaterielBrand());
        worderUsedMaterielEntity.setMaterielSpec(materielInformation
                .getMaterielSpec());
        worderUsedMaterielEntity.setNum(materielNum);

        worderUsedMaterielService.savaWorderUsedMateriel(
                worderUsedMaterielEntity);

        //保存物料详细表
        WorderBalanceFeeDetailEntity userDetailEntity = new WorderBalanceFeeDetailEntity();
        userDetailEntity.setWorderId(worderInformation.getWorderId());
        userDetailEntity.setMaterielName(materielInformation.getMaterielName());
        userDetailEntity.setBalanceTarget(3);
        userDetailEntity.setBalanceType(1);
        userDetailEntity.setMaterielId(materielInformation.getId());
        userDetailEntity.setIsSuite(0);
        userDetailEntity.setNum(materielNum);
        //金额规范化，并计算税额和含税价
        //userFee1 = userFee1.setScale(2, roundMode);
        //获取网点税点
        List<WorderBalanceFeeDetailEntity> userDetailList = new ArrayList<>();//用于记录结算详情
        //金额规范化，并计算税额和不含税价
        BigDecimal userTax = payActualAmount.divide(taxRate.add(BigDecimal.ONE), 2, balanceProperties.ROUND_MODE).multiply(taxRate).setScale(2,balanceProperties.ROUND_MODE);
        BigDecimal userFeeNoTax = payActualAmount.subtract(userTax);

        Collections.sort(descriptionList);
        String userDesc = "实际计算数量: " + materielNum + ", 详情如下:\n" + org.apache.commons.lang.StringUtils.join(descriptionList, '\n');
        userDetailEntity.setBalanceFee(userFeeNoTax);
        userDetailEntity.setBalanceFeeTax(userTax);
        userDetailEntity.setBalanceFeeSum(payActualAmount);
        userDetailEntity.setDescription(userDesc);
        userDetailList.add(userDetailEntity);

        //调整尾差
        this.adjustedTailDiff(userFeeNoTax, userTax, payActualAmount, userDetailList);

        baseMapper.saveUserBalanceFee(worderInformation.getWorderId(), userFeeNoTax, userTax, payActualAmount);
        if(CollectionUtils.isNotEmpty(userDetailList)){
            worderBalanceFeeDetailService.remove(new QueryWrapper<WorderBalanceFeeDetailEntity>()
                    .eq("worder_id", worderInformation.getWorderId()).eq("balance_type", 1).eq("balance_target", 3));
            worderBalanceFeeDetailService.saveBatch(userDetailList);
        }

        caculateOneDotIncreBalanceFee(worderInformation.getWorderId());

    }


    static class MapKeyComparator implements Comparator<String>{

        @Override
        public int compare(String str1, String str2) {

            return str1.compareTo(str2);
        }
    }


    static class Entity implements Comparable<Entity>{
        private BigDecimal index;//索引号，排序使用
        private String desc;//描述，格式为：起止数量,单价,总价
        Entity(BigDecimal i, String s){
            index = i;
            desc = s;
        }

        @Override
        public int compareTo(Entity o) {
            return index.compareTo(o.index);
        }
        @Override
        public String toString(){
            return desc;
        }
    }

    @Data
    @AllArgsConstructor
    static
    class Result{
        Set<Integer> MaterielIds;
        BigDecimal fee;
    }
}
