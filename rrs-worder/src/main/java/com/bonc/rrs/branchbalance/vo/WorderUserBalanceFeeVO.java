package com.bonc.rrs.branchbalance.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by liqingchao on 2020/3/24.
 */
@Data
public class WorderUserBalanceFeeVO implements Serializable{
    private Integer worderId;
    private BigDecimal userBalanceFee;//用户增项结算金额(含税)-应收费用
    private BigDecimal minFee;//最小实收金额
    // 实际支付金额
    private BigDecimal userActualCost;
    private List<WorderUserBalanceFeeDetail> details;
}
