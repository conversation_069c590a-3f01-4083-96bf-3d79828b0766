/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.branchbalance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.branchbalance.dao.BranchBalanceDao;
import com.bonc.rrs.branchbalance.dao.CompanyMaterielDao;
import com.bonc.rrs.branchbalance.entity.BranchBalanceViewEntity;
import com.bonc.rrs.branchbalance.entity.CompanyMaterielEntity;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.branchbalance.service.CompanyMaterielService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/29 10:05
 * @Version 1.0.0
 */

@Service("companyMaterielService")
@Slf4j
public class CompanyMaterielServiceImpl extends ServiceImpl<CompanyMaterielDao, CompanyMaterielEntity> implements CompanyMaterielService {
}