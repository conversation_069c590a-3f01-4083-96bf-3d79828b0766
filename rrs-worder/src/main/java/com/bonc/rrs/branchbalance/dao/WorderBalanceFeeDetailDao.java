package com.bonc.rrs.branchbalance.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by liqingchao on 2020/3/1.
 */
@Mapper
public interface WorderBalanceFeeDetailDao extends BaseMapper<WorderBalanceFeeDetailEntity>{
    List<WorderBalanceFeeDetailEntity> listBalanceFeeDetailByWorderId(Integer worderId);

    List<WorderPmStimulateEntity> getStimulateList(@Param("worderId") Integer worderId,@Param("type") String type);

    String getStatusByBCB(@Param("worderId") Integer worderId);
}
