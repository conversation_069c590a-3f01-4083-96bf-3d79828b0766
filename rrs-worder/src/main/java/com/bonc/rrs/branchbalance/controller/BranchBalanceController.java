package com.bonc.rrs.branchbalance.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity;
import com.bonc.rrs.branchbalance.entity.WorderDetailEntity;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.branchbalance.vo.BranchBalanceQueryVO;
import com.bonc.rrs.branchbalance.vo.WorderUserBalanceFeeVO;
import com.bonc.rrs.worder.dto.dto.IncreaseFeeDto;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderSceneService;
import com.bonc.rrs.worderapp.entity.WorderInformation;
import com.bonc.rrs.worderapp.entity.vo.WorderInformationVo;
import com.bonc.rrs.worderinformationaccount.entity.BalanceAcsAccountingStatusEntity;
import com.bonc.rrs.worderinformationaccount.service.BalanceAcsAccountingStatusService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by liqingchao on 2020/1/14.
 */
@RestController
@RequestMapping("worder/branchbalance")
@Api(tags = {"网点结算相关接口"})
public class BranchBalanceController {
    @Autowired
    private BranchBalanceService branchBalanceService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private BalanceAcsAccountingStatusService balanceAcsAccountingStatusService;

    @PostMapping("branchquery")
    @ApiOperation(value="网点查询结算工单")
    public R branchQuery(@Validated @ApiParam("查询条件") @RequestBody BranchBalanceQueryVO branchBalanceQueryVO){
        PageUtils page = branchBalanceService.branchQuery(branchBalanceQueryVO);
        return R.ok().putPage(page);
    }

    @GetMapping("userbalance")
    @ApiOperation(value="用户结算（服务兵APP调用）")
    public R userbalance(@ApiParam("工单ID") @RequestParam Integer worderId){
        WorderUserBalanceFeeVO worderUserBalanceFeeVO = branchBalanceService.caculateOneUserBalanceFee(worderId);
        return R.ok().put("data", worderUserBalanceFeeVO);
    }

    @GetMapping("worderdetail")
    @ApiOperation(value="工单详情")
    public R worderdetail(@ApiParam("工单ID") @RequestParam Integer worderId){
        WorderDetailEntity worderDetailEntity = branchBalanceService.queryWorderDetail(worderId);
        return R.ok().put("data", worderDetailEntity);
    }

    @GetMapping("worderFlowDetail")
    @ApiOperation(value="工单详情")
    public R worderFlowDetail(@ApiParam("工单ID") @RequestParam Integer worderId){
        WorderDetailEntity worderDetailEntity = branchBalanceService.queryworderFlowDetail(worderId);
        return R.ok().put("data", worderDetailEntity);
    }

    @GetMapping("caculateBalanceFee")
    @ApiOperation(value="计算结算金额(自测用)")
    public R caculateBalanceFee(){
        branchBalanceService.caculateBalanceFee();
        return R.ok();
    }


    @GetMapping("queryWorderForBalance")
    public R queryWorderForBalance() {
        List<WorderBalanceFeeEntity> worderList = branchBalanceService.queryWorderForBalance();
        return R.ok().putList(worderList);
    }

    @GetMapping("getDotAddBalanceFee")
    @ApiOperation(value="网点增项费用")
    public R getDotAddBalanceFee(Integer worderId){

        return R.ok().putList(branchBalanceService.caculateOneDotIncreBalanceFee(worderId));
    }

    /**
     * 修改使用物料重新计算费用
     * @return
     */
    @RequestMapping("/caculateAfterUpdateUsedMaterial")
    public R caculateAfterUpdateUsedMaterial(WorderInformationVo worderInformationVo){
        Integer worderId = null;
        if(null != worderInformationVo.getWorderId()){
            worderId = worderInformationVo.getWorderId();
        }else if(StringUtils.isNotBlank(worderInformationVo.getWorderNo())){
            List<WorderInformationEntity> list = worderInformationService.list(
                new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderInformationVo.getWorderNo()));
            worderId = list.get(IntegerEnum.ZERO.getValue()).getWorderId();
        }
        if(null != worderId){
            // 计算用户增项费用
            WorderUserBalanceFeeVO worderUserBalanceFeeVO = branchBalanceService.caculateOneUserBalanceFee(worderId);
            // 修改折扣金额
            WorderInformationEntity worderInformationEntity = new WorderInformationEntity();
            worderInformationEntity.setWorderId(worderId);
            BigDecimal dotIncreDiscountAmount = worderUserBalanceFeeVO.getUserBalanceFee().subtract(worderUserBalanceFeeVO.getUserActualCost());
            worderInformationEntity.setDotIncreDiscountAmount(dotIncreDiscountAmount);
            worderInformationService.updateById(worderInformationEntity);
            // 计算网点增项费用
            WorderBalanceFeeEntity worderBalanceFeeEntity = branchBalanceService.caculateOneDotIncreBalanceFee(worderId);
            return R.ok().put("dotIncreDiscountAmount", dotIncreDiscountAmount).put("user", worderUserBalanceFeeVO)
                    .put("dot", worderBalanceFeeEntity);
        }else{
            return R.error();
        }

    }

    @RequestMapping("/testRemoveAdd")
    public R testRemoveAdd(){
        long l=System.currentTimeMillis();
        ArrayList<String> list=new ArrayList();
        ArrayList<BalanceAcsAccountingStatusEntity> balanceAcsAccountingStatusEntityList=new ArrayList();
        for(int i=0;i<10000;i++){
            BalanceAcsAccountingStatusEntity balanceAcsAccountingStatusEntity=new BalanceAcsAccountingStatusEntity();
            list.add(i+"");
            balanceAcsAccountingStatusEntity.setRowId(i+"");
            balanceAcsAccountingStatusEntityList.add(balanceAcsAccountingStatusEntity);
        }
        balanceAcsAccountingStatusService.removeByIds(list);
        balanceAcsAccountingStatusService.saveBatch(balanceAcsAccountingStatusEntityList);
        System.out.println("消耗时间（秒）："+((System.currentTimeMillis()-l)/1000));
        return R.ok();
    }


}
