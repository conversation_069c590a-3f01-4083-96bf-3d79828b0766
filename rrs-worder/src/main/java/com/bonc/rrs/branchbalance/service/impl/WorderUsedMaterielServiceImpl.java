package com.bonc.rrs.branchbalance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.branchbalance.dao.WorderUsedMaterielDao;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;
import com.bonc.rrs.branchbalance.service.WorderUsedMaterielService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WorderUsedMaterielServiceImpl extends ServiceImpl<WorderUsedMaterielDao, WorderUsedMaterielEntity> implements WorderUsedMaterielService {

    @Autowired
    private WorderUsedMaterielDao worderUsedMaterielDao;

    @Override
    public int queryCount(int id) {
        return worderUsedMaterielDao.queryCount(id);
    }

    @Override
    public int savaWorderUsedMateriel(
            WorderUsedMaterielEntity worderUsedMaterielEntity)
    {
        return worderUsedMaterielDao.savaWorderUsedMateriel(worderUsedMaterielEntity);
    }

    @Override
    public int getCountByWrderId(int materielId, int worderId)
    {
        return worderUsedMaterielDao.getCountByWrderId(materielId,worderId);
    }

    @Override
    public int deleteByWorderIdAndMaterielId(int materielId, int worderId)
    {
        return worderUsedMaterielDao.deleteByWorderIdAndMaterielId(materielId,worderId);
    }

}
