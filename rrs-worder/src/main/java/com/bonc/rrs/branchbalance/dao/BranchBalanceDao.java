package com.bonc.rrs.branchbalance.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.branchbalance.entity.BranchBalanceViewEntity;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity;
import com.bonc.rrs.branchbalance.entity.WorderDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by liqingchao on 2020/1/20.
 */
@Mapper
public interface BranchBalanceDao extends BaseMapper<BranchBalanceViewEntity>{
    Integer queryBranchBalanceViewCount(Map<String, Object> params);

    List<BranchBalanceViewEntity> queryBranchBalanceViewData(Map<String, Object> params);

    WorderDetailEntity queryWorderDetail(Integer worderId);

    /**
     * 根据工单ID获取工单结算信息
     * @param worderId
     * @return
     */
    WorderBalanceFeeEntity queryWorderForBalanceByWorderId(Integer worderId);

    List<WorderBalanceFeeEntity> queryWorderForBalance();

    WorderBalanceFeeEntity queryOneWorderForBalance(Integer worderId);
    WorderBalanceFeeEntity queryOneNoUsedMaterialWorderForBalance(Integer worderId);


    void batchSaveWorderBalanceFee(List<WorderBalanceFeeEntity> worderList);

    void batchSaveIncreBalanceFee(List<WorderBalanceFeeEntity> worderList);

    /**
     * 保存用户增项结算费用
     * @param worderId
     * @param userFee 不含税价
     * @param tax 税额
     * @param feeAll 含税价
     */
    void saveUserBalanceFee(@Param("worderId") Integer worderId, @Param("userFee") BigDecimal userFee
            , @Param("tax") BigDecimal tax, @Param("feeAll") BigDecimal feeAll);

    void saveIncreBalanceFee(WorderBalanceFeeEntity worder);

    void saveDotIncreBalanceFee(WorderBalanceFeeEntity worder);

    Integer updateWorderInfo(WorderBalanceFeeEntity worder);

    String querySurveyEfficiency(String worderNo);

    String queryInstallEfficiency(String worderNo);
}
