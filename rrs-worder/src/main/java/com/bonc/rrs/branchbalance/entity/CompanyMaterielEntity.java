package com.bonc.rrs.branchbalance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("company_materiel")
public class CompanyMaterielEntity implements Serializable {
    private Integer id;
    private String companyName; // 车企名称
    private Integer companyMaterielId; // 车企物料id
    private String itemName; // 增项名称
    private String specification; // 增项规格
    private String unit; // 单位
    private Double price; // 单价
    private Integer number; // 数量
    private String remark; // 备注
    private String type; // 类型
    private Integer systemMaterielId; // news物料id
    // 1使用 0 未使用
    private Integer isUse;

}