package com.bonc.rrs.branchbalance.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Created by liqingchao on 2020/2/2.
 */
@Data
@ApiModel(value = "网点查询结算工单条件")
public class BranchBalanceQueryVO implements Serializable {
    @ApiModelProperty(value = "工单编号")
    private String worderNo;

    @ApiModelProperty(value = "结算类型")
    private Integer balanceType;

    @ApiModelProperty(value = "结算发布时间-开始时间")
    private String startTime;

    @ApiModelProperty(value = "结算发布时间-结束时间")
    private String endTime;

    @ApiModelProperty(value = "页码 从1开始", required=true)
    @NotNull
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数", required=true)
    @NotNull
    private Integer pageSize = 10;
}
