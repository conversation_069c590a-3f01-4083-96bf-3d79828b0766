package com.bonc.rrs.branchbalance.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WorderUsedMaterielDao extends BaseMapper<WorderUsedMaterielEntity> {
    int queryCount(int id);


    int  getCountByWrderId(@Param("materielId") int materielId,@Param("worderId") int worderId);

    int savaWorderUsedMateriel(WorderUsedMaterielEntity worderUsedMaterielEntity);

    int deleteByWorderIdAndMaterielId(@Param("materielId") int materielId,@Param("worderId") int worderId);

    List<WorderUsedMaterielEntity> selectListByWorderIdAndType(@Param("worderId") Integer worderId,@Param("type") Integer type);
}
