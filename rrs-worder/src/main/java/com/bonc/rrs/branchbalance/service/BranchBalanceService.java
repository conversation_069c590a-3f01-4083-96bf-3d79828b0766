package com.bonc.rrs.branchbalance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.balancerule.entity.BalanceRuleEntity;
import com.bonc.rrs.branchbalance.entity.BranchBalanceViewEntity;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity;
import com.bonc.rrs.branchbalance.entity.WorderDetailEntity;
import com.bonc.rrs.branchbalance.vo.BranchBalanceQueryVO;
import com.bonc.rrs.branchbalance.vo.WorderUserBalanceFeeVO;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.youngking.lenmoncore.common.utils.PageUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by liqingchao on 2020/1/14.
 */
public interface BranchBalanceService extends IService<BranchBalanceViewEntity> {

    PageUtils branchQuery(BranchBalanceQueryVO branchBalanceQueryVO);

    WorderDetailEntity queryWorderDetail(Integer worderId);

    public WorderDetailEntity queryworderFlowDetail(Integer worderId);

    WorderBalanceFeeEntity caculateOneDotIncreBalanceFee(Integer worderId);


    /**
     * 根据工单ID计算厂商结算费用
     * @param worderId
     * @return
     */
    Boolean calculateCompanyBalanceFeeByWorderId(Integer worderId, WorderWaitAccountEntity worderWaitAccount);

    Boolean recountCalculateBalanceFeeByWorderId(Integer worderId, WorderWaitAccountEntity worderWaitAccountEntity, Boolean companyFlag);

    List<WorderBalanceFeeEntity> queryWorderForBalance();
    void caculateBalanceFee();

    WorderUserBalanceFeeVO caculateOneUserBalanceFee(Integer worderId);

    WorderBalanceFeeEntity queryOneWorderForBalance(Integer worderId);
    BigDecimal companySuitBalance(BalanceRuleEntity companyBalanceRule, Integer worderId,
                                  List<WorderBalanceFeeDetailEntity> balanaceDetailList, BigDecimal companyFee, Integer suiteId,
                                  BigDecimal companyTaxRate, int roundMode);
    BigDecimal companyMaterielBalance(BalanceRuleEntity companyBalanceRule, Integer worderId,
                                      List<WorderBalanceFeeDetailEntity> balanaceDetailList,BigDecimal companyFee,
                                      BigDecimal companyTaxRate, int roundMode, Map<Integer,BigDecimal> suiteMaterialMap,
                                      WorderBalanceFeeEntity worderBalanceFee, Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap);
    BigDecimal getTaxRateByTaxPoint(String taxPoint);
    BigDecimal dotBalanceFee(BalanceRuleEntity dotBalanceRule, Integer worderId,
                             List<WorderBalanceFeeDetailEntity> balanaceDetailList,BigDecimal dotFee,
                             BigDecimal dotTaxRate, int roundMode, Map<Integer,BigDecimal> suiteMaterialMap,
                             WorderBalanceFeeEntity worderBalanceFee, Map<Integer,BigDecimal> companyRuleMaxMaterielNumMap);
    Map<Integer,BigDecimal> getBalanceRuleMaxMaterielNumMap(BalanceRuleEntity balanceRuleEntity);
}
