package com.bonc.rrs.branchbalance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;

import java.util.List;

/**
 * Created by liqingchao on 2020/3/1.
 */
public interface WorderBalanceFeeDetailService extends IService<WorderBalanceFeeDetailEntity> {
    List<WorderBalanceFeeDetailEntity> listBalanceFeeDetailByWorderId(Integer worderId);

    List<WorderPmStimulateEntity> getStimulateList(Integer worderId,String type);

    String getStatusByBCB(Integer worderId);
}
