package com.bonc.rrs.branchbalance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by liqingchao on 2020/2/4.
 */
@Data
@TableName("worder_used_materiel")
public class WorderUsedMaterielEntity implements Serializable {
    private Integer id;
    private Integer worderId;
    private Integer materielId;
    private BigDecimal num;
    private Integer brandId;
    private String materielSpec;
    private String takePicture;
    @TableField(exist = false)
    private String materielName;
    @TableField(exist = false)
    private String materielBrandValue;
}
