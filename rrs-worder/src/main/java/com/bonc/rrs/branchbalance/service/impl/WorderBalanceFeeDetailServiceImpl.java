package com.bonc.rrs.branchbalance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.branchbalance.dao.WorderBalanceFeeDetailDao;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity;
import com.bonc.rrs.branchbalance.service.WorderBalanceFeeDetailService;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by liqingchao on 2020/3/1.
 */
@Service("worderBalanceFeeDetailService")
public class WorderBalanceFeeDetailServiceImpl extends ServiceImpl<WorderBalanceFeeDetailDao,WorderBalanceFeeDetailEntity> implements WorderBalanceFeeDetailService {
    @Override
    public List<WorderBalanceFeeDetailEntity> listBalanceFeeDetailByWorderId(Integer worderId) {
        return baseMapper.listBalanceFeeDetailByWorderId(worderId);
    }

    @Override
    public List<WorderPmStimulateEntity> getStimulateList(Integer worderId,String type){
        return baseMapper.getStimulateList(worderId,type);
    }

    @Override
    public String getStatusByBCB(Integer worderId){
        return baseMapper.getStatusByBCB(worderId);
    }
}
