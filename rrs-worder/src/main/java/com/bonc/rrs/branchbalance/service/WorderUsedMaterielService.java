package com.bonc.rrs.branchbalance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;

public interface WorderUsedMaterielService extends IService<WorderUsedMaterielEntity> {

    int queryCount(int id);

    int savaWorderUsedMateriel(WorderUsedMaterielEntity worderUsedMaterielEntity);

    int  getCountByWrderId( int materielId, int worderId);

    int deleteByWorderIdAndMaterielId(int materielId, int worderId);
}
