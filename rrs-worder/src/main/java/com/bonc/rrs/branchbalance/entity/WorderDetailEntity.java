package com.bonc.rrs.branchbalance.entity;

import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by liqingchao on 2020/2/3.
 */
@Data
public class WorderDetailEntity implements Serializable {
    private Integer worderId; //ID
    private Integer worderTypeId; //工单类型编号
    private String worderTypeName; //工单类型名称
    private String worderNo; //工单编号
    private String companyNo; //车企编号
    private String companyName; //车企名称
    private String dotNo;//网点编号
    private String dotName;//网点名称
    private Integer worderStatus;//工单主状态
    private String worderStatusValue;//工单主状态描述
    private Integer worderExecStatus;//工单主状态
    private String worderExecStatusValue;//工单主状态描述
    private Integer worderSetStatus;//工单结算状态
    private Integer worderIncreStatus;//工单增项结算状态
    private Integer worderExciStatus;//工单激励状态
    private String worderSetStatusValue;//工单结算状态描述
    private String worderIncreStatusValue;//工单增项结算状态描述
    private String worderExciStatusValue;//工单激励状态描述
    private BigDecimal companyBalanceFee;//厂商结算总金额(不含税)
    private BigDecimal companyBalanceFeeTax;//厂商结算税额
    private BigDecimal companyBalanceFeeSum;//厂商结算总金额(含税)
    private BigDecimal userBalanceFee;//厂商结算总金额(不含税)
    private BigDecimal userBalanceFeeTax;//厂商结算税额
    private BigDecimal userBalanceFeeSum;//厂商结算总金额(含税)
    private BigDecimal dotBalanceFee;//网点工单结算总金额(不含税)
    private BigDecimal dotBalanceFeeTax;//网点工单结算税额
    private BigDecimal dotBalanceFeeSum;//网点工单结算总金额(含税)
    private BigDecimal dotIncreBalanceFee;//网点增项结算总金额(不含税)
    private BigDecimal dotIncreBalanceFeeTax;//网点增项结算税额
    private BigDecimal dotIncreBalanceFeeSum;//网点增项结算总金额(含税)
    private BigDecimal dotStimulateBalanceFee;//网点激励结算总金额(不含税)
    private BigDecimal companyStimulateBalanceFee;//网点激励结算总金额(不含税)
    private BigDecimal attendantBalanceFee;//服务兵结算总金额
    private BigDecimal dotIncreDiscountAmount;//网点给客户的优惠金额
    private List<WorderBalanceFeeDetailEntity> companyBalanceList;//厂商结算列表
    private List<WorderBalanceFeeDetailEntity> dotBalanceList;//网点工单结算列表
    private List<WorderBalanceFeeDetailEntity> dotIncreBalanceList;//网点增项结算列表
    private List<WorderBalanceFeeDetailEntity> userIncreBalanceList;//用户增项结算列表
    private List<WorderPmStimulateEntity> companyStimulateEntity; //厂商激励列表
    private List<WorderPmStimulateEntity> dotStimulateEntity; //网点激励列表
    private Integer dotStatus;//商户通状态
    private Integer companyStatus;//车企状态枚举
    private Integer status;//工单状态枚举
}
