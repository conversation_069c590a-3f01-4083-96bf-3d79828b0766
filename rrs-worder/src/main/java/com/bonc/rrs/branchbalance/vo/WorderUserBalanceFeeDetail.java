package com.bonc.rrs.branchbalance.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by liqingchao on 2020/3/24.
 */
@Data
public class WorderUserBalanceFeeDetail implements Serializable {
    private Integer materielId;//物料ID（如果是套包，则为套包ID）
    private String materielName;//物料名称
    private BigDecimal num;//使用数量
    private BigDecimal balanceFee;//该项结算金额(含税)
    private String description;//详细描述
}
