package com.bonc.rrs.branchbalance.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by liqingchao on 2020/1/20.
 * 网点查询结算工单的数据对象
 */
@Data
public class BranchBalanceViewEntity implements Serializable {
    private Integer worderId; //ID
    private Integer worderTypeId; //工单类型编号
    private String worderTypeName; //工单类型名称
    private String worderNo; //工单编号
    private String companyNo; //车企编号
    private String companyName; //车企名称
    private Integer balanceStatus; //结算状态
    private String balanceStatusValue; //结算状态
    private String balanceType; //结算类型
    private String balanceTypeName; //结算类型名称
    private BigDecimal balanceFee; //结算金额(不含税)
    private BigDecimal balanceFeeTax; //税金
    private BigDecimal balanceFeeSum; //结算金额(含税)
    private Date balancePublishTime; //结算发布时间
}
