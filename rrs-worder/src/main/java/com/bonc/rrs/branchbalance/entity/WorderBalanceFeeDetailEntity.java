package com.bonc.rrs.branchbalance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by liqingchao on 2020/3/1.
 */
@Data
@TableName("worder_balance_fee_detail")
public class WorderBalanceFeeDetailEntity implements Serializable {
    @TableId
    private Integer id;
    private Integer worderId;//所属工单ID
    private Integer balanceType;//结算类型（数据字典balance_type）
    private Integer balanceTarget;//结算对象(数据字典balance_target)
    private Integer materielId;//物料ID（如果是套包，则为套包ID）
    private Integer isSuite;//是否套包
    private BigDecimal num;//使用数量
    private BigDecimal balanceFee;//该项结算金额(不含税)
    private BigDecimal balanceFeeTax;//该项结算税额
    private BigDecimal balanceFeeSum;//该项结算金额(含税)
    private String description;//详细描述
    @TableField(exist = false)
    private String materielName;//物料名称
}
