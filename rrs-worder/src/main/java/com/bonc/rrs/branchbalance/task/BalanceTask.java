package com.bonc.rrs.branchbalance.task;

import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import com.youngking.renrenwithactiviti.modules.sys.dao.ExcuteSqlMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liqingchao on 2020/2/9.
 * 后台结算任务
 */
//@Component("balanceTask")
public class BalanceTask implements ITask {
    private static final Logger log = LoggerFactory.getLogger(BalanceTask.class);
    @Autowired
    private BranchBalanceService branchBalanceService;
    @Autowired(required = false)
    private ExcuteSqlMapper excuteSqlMapper;
    @Override
    public void run(String params) {
        /**
         * 2020.10.12
         * 在改定时任务前执行两个sql
         */
        excuteSqlMapper.insertWorderUsedMateriel(null,3);
        excuteSqlMapper.insertWorderUsedMateriel2(null,3);
        log.info("定时计算结算金额开始");
        branchBalanceService.caculateBalanceFee();
        log.info("定时计算结算金额结束");
    }
}
