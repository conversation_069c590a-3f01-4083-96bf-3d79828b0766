package com.bonc.rrs.branchbalance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by liqingchao on 2020/2/3.
 */
@Data
@TableName("worder_information")
public class WorderBalanceFeeEntity implements Serializable {
    private Integer worderId;//工单ID
    private String worderNo;//工单编号
    private Integer worderTypeId;//工单类型id
    private Integer suiteId;//关联套包ID
    private Integer companyBalanceRuleId;//关联工单结算规则ID-车企
    private Integer dotBalanceRuleId;//关联工单结算规则ID-网点
    private Integer dotIncreBalanceRuleId;//关联增项结算规则ID-网点
    private Integer attendantBalanceRuleId;//关联增项结算规则ID-用户
    private Integer worderStatus;//工单主状态
    private Integer worderSetStatus;//工单结算状态
    private Integer worderIncreStatus;//工单增项结算状态
    private String worderSetStatusValue;//工单结算状态描述
    private String worderIncreStatusValue;//工单增项结算状态描述
    private BigDecimal companyBalanceFee;//厂商结算总金额（不含税）
    private BigDecimal userBalanceFee;//用户增项结算总金额（不含税）
    private BigDecimal dotBalanceFee;//网点工单结算总金额（不含税）
    private BigDecimal dotIncreBalanceFee;//网点增项结算总金额（不含税）
    private BigDecimal companyBalanceFeeTax;//厂商结算税额
    private BigDecimal userBalanceFeeTax;//用户增项结算税额
    private BigDecimal dotBalanceFeeTax;//网点工单结算税额
    private BigDecimal dotIncreBalanceFeeTax;//网点增项结算税额
    private BigDecimal companyBalanceFeeSum;//厂商结算总金额（含税）
    private BigDecimal userBalanceFeeSum;//用户增项结算总金额（含税）
    private BigDecimal dotBalanceFeeSum;//网点工单结算总金额（含税）
    private BigDecimal dotIncreBalanceFeeSum;//网点增项结算总金额（含税）
    private BigDecimal dotIncreDiscountAmount;//网点给客户的优惠金额
    private BigDecimal dotBalanceRate;//网点结算系数


    /**
     * 工单创建时间
     */
    private Date createTime;

    /**
     * 客服确认完成时间
     */
    private Date confirmCompletionTime;

    /**
     * 工单完成时间
     */
    private Date worderFinishTime;
    /**
     * 网点性质
     */
    @TableField(exist = false)
    private String dotQuality;

    @TableField(exist = false)
    private boolean worderSetFlag = false; //是否工单结算
    @TableField(exist = false)
    private boolean worderIncreFlag = false; //是否增项结算
    @TableField(exist = false)
    private List<WorderUsedMaterielEntity> materielList;//工单使用的物料列表
    @TableField(exist = false)
    private List<WorderBalanceFeeDetailEntity> companyBalanceList;//厂商结算列表
    @TableField(exist = false)
    private List<WorderBalanceFeeDetailEntity> dotBalanceList;//网点工单结算列表
    @TableField(exist = false)
    private List<WorderBalanceFeeDetailEntity> dotIncreBalanceList;//网点增项结算列表
    @TableField(exist = false)
    private String dotPoint;
    @TableField(exist = false)
    private String dotName;

    @TableField(exist = false)
    private String taxPoint;  // 网点税点字典ID

    // 实际支付金额
    private BigDecimal userActualCost;
    private String invoiceId;

    // 勘测结单收费标志 true: 收费 false: 不收费
    private Boolean conveyAccountChargeFlag;

    private Integer  installEfficiency;//安装时效

}
