package com.bonc.rrs.signlocation.dao;

import com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.signlocation.dao
 * @description:
 * @date 2021/10/26 11:09
 */
@Mapper
public interface SignLocationDao
{

    /**
     * 根据工单id查询
     * @param worderId
     * @return
     */
    Integer getCount(int worderId);

    /**
     * 新增用户地址扩展表
     * @param soldierSignLocation
     * @return
     */
    Integer savaSignLocation(SoldierSignLocationEntity soldierSignLocation);

    /**
     * 修改用户地址扩展表
     * @param soldierSignLocation
     * @return
     */
    Integer updateSignLocation(SoldierSignLocationEntity soldierSignLocation);

    SoldierSignLocationEntity getSignLocation(@Param("worderId") int worderId);

}
