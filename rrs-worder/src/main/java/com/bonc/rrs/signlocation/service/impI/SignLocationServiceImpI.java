package com.bonc.rrs.signlocation.service.impI;


import com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity;
import com.bonc.rrs.signlocation.service.SignLocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bonc.rrs.signlocation.dao.SignLocationDao;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.signlocation.service.impI
 * @description:
 * @date 2021/10/26 11:21
 */
@Service
public class SignLocationServiceImpI implements SignLocationService
{
    @Autowired
    SignLocationDao signLocationDao;

    @Override
    public Integer getCount(int worderId)
    {
        return signLocationDao.getCount(worderId);
    }

    @Override
    public Integer savaSignLocation(
            SoldierSignLocationEntity soldierSignLocation)
    {
        return signLocationDao.savaSignLocation(soldierSignLocation);
    }

    @Override
    public Integer updateSignLocation(SoldierSignLocationEntity soldierSignLocation) {
        return signLocationDao.updateSignLocation(soldierSignLocation);
    }

    @Override
    public SoldierSignLocationEntity getSignLocation(int worderId)
    {
        return signLocationDao.getSignLocation(worderId);
    }

}