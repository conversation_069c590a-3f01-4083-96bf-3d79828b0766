package com.bonc.rrs.signlocation.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.signlocation.entity.vo
 * @description:
 * @date 2021/10/26 14:36
 */
@Data
public class SoldierSignLocationVo
{
    /**
     * 工单主键
     */
    private Integer worderId;

    /**
     * 服务兵主键id
     */
    private Integer soldierId;

    /**
     * 服务兵定位的纬度
     */
    private String locationX;

    /**
     * 服务兵定位的经度
     */
    private String locationY;

    /**
     * 用户地址的维度
     */
    private String userX;

    /**
     * 用户地址的经度
     */
    private String userY;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}