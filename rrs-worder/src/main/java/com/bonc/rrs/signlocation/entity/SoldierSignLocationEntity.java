package com.bonc.rrs.signlocation.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.signlocation.entity
 * @description:
 * @date 2021/10/26 11:23
 */
@Data
@TableName("soldier_sign_location")
@ApiModel(value = "服务兵签到定位表 ")
public class SoldierSignLocationEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    @ApiModelProperty(value = "主键ID", required = true)
    private Integer id;

    /**
     * 工单主键
     */
    @ApiModelProperty(value = "工单id", required = true)
    private Integer worderId;

    /**
     * 服务兵主键id
     */
    @ApiModelProperty(value = "服务兵主键id", required = false)
    private Integer soldierId;

    /**
     * 服务兵定位的纬度
     */
    @ApiModelProperty(value = "服务兵定位的纬度", required = false)
    private String locationX;

    /**
     * 服务兵定位的经度
     */
    @ApiModelProperty(value = "服务兵定位的经度", required = false)
    private String locationY;

    /**
     * 用户地址的维度
     */
    @ApiModelProperty(value = "用户地址的维度", required = false)
    private String userX;

    /**
     * 用户地址的经度
     */
    @ApiModelProperty(value = "用户地址的经度", required = false)
    private String userY;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = false)
    private Timestamp updateTime;

    /**
     * 打卡点和安装电距离(米)
     */
    @ApiModelProperty(value = "打卡点和安装电距离(米)", required = false)
    private Integer distance;

    /**
     * 签到类型（sign_type）：1：勘测签到 ， 0：安装签到
     */
    @ApiModelProperty(value = "签到类型", required = false)
    private Integer signType;

}