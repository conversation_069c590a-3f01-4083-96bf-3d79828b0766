package com.bonc.rrs.signlocation.service;

import com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity;

/**
 * <AUTHOR>
 * @version V1.0
 * @package com.bonc.rrs.signlocation.service
 * @description:
 * @date 2021/10/26 11:18
 */

public interface SignLocationService
{
    Integer getCount( int worderId);

    /**
     * 新增用户地址扩展表
     * @param soldierSignLocation
     * @return
     */

    Integer savaSignLocation(SoldierSignLocationEntity soldierSignLocation);

    /**
     * 修改用户地址扩展表
     * @param soldierSignLocation
     * @return
     */
    Integer updateSignLocation(SoldierSignLocationEntity soldierSignLocation);

    SoldierSignLocationEntity getSignLocation(int worderId);
}