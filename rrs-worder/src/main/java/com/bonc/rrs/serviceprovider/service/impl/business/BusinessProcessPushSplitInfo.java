package com.bonc.rrs.serviceprovider.service.impl.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.bonc.rrs.byd.domain.PushFile;
import com.bonc.rrs.byd.domain.PushSplitInfo;
import com.bonc.rrs.byd.domain.SplitAttachments;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 服务商回传拆桩信息业务处理
 * @Author: tangchuheng
 * @Date: 2024/4/15
 * @Version: 1.0
 */
@Service("BusinessProcessPushSplitInfo")
@Slf4j
public class BusinessProcessPushSplitInfo extends AbstractBusinessProcess {

    @Autowired
    private IBydApiService iBydApiService;
    @Autowired
    private WorderExtFieldService worderExtFieldService;
    @Autowired
    private SysFilesService sysFilesService;

    @Override
    public String getProcessCode() {
        return "pushSplitInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {

        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }

        try {

            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            // 工单扩展字段
            List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getSpecificFields(businessProcessPo.getWorderNo(), null);
            Map<Integer, String> fieldMap = new HashMap<>();
            for (WorderExtFieldEntity extFieldEntity : extFieldEntities) {
                fieldMap.put(extFieldEntity.getFieldId(), extFieldEntity.getFieldValue());
            }

            String attachments = buildCheckPicAttrs(fieldMap);

            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            companyOrderNumber = StrUtil.removeSuffix(companyOrderNumber,"-拆桩");

            PushSplitInfo pushSplitInfo = PushSplitInfo.builder()
                    // 报修订单编号
                    .orderCode(companyOrderNumber)
                    // 操作人
                    .operatePerson(OPERATE_PERSON)
                    // 售后完成时间
                    .finishTime(fieldMap.get(1711))
                    // 描述
                    .desc(fieldMap.get(2185))
                    // 客户是否留桩
                    .needSaveWallBox(YesOrNo.getValueByName(fieldMap.get(2186)))
                    // 拆桩附件
                    .picAttrs(attachments)
                    .build();

            // 调用通知接口
            OtherApiResponse otherApiResponse = iBydApiService.pushSplitInfo(pushSplitInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }

        } catch (Exception e) {
            log.error("回传拆桩信息出现异常", e);
            return Result.error("回传拆桩信息失败");
        }

        return Result.success();
    }

    /**
     * 构建检查工单图片附件JSON
     * @param fieldMap 扩展字段Map
     * @return 图片附件JSON字符串
     */
    private String buildCheckPicAttrs(Map<Integer, String> fieldMap) {
        SplitAttachments picAttachment = SplitAttachments.builder()
                // 充电桩序列码
                .chargePileCodeImage(fieldMap.get(2187))
                // 人桩合照
                .personPileImage(fieldMap.get(2188))
                // 试充照片
                .tryChargePImage(fieldMap.get(2189))
                // 拆桩前电源点正面0.5m近景
                .powerPointFrontImage(fieldMap.get(2190))
                // 施工使用线缆
                .constructionUCImage(fieldMap.get(2191))
                // 拆桩后电源点正面0.5m近景
                .powerPointBackImage(fieldMap.get(2192))
                // 充电桩接线
                .chargePileCImage(fieldMap.get(2193))
                // 增项收费单
                .additionalCImage(fieldMap.get(2194))
                // 点检表
                .checkListImage(fieldMap.get(2195))
                // 其他图片1
                .otherImage(fieldMap.get(2196))
                // 其他图片2
                .otherImage2(fieldMap.get(2197))
                // 其他图片3
                .otherImage3(fieldMap.get(2198))
                .build();
        // 转换为JSON字符串
        return com.alibaba.fastjson.JSON.toJSONString(getPicAttrs(BeanUtil.beanToMap(picAttachment)));
    }

    private Map<String, String> getPicAttrs(Map<String, Object> sourceMap) {
        sourceMap.entrySet().removeIf(entry -> {
            String value = String.valueOf(entry.getValue());
            return StrUtil.isBlank(value) || "null".equals(value);
        });
        String fileIds = StringUtils.join(sourceMap.values(),  ",");

        List<SysFileEntity> fileList = sysFilesService.getSysFileByIds(fileIds);
        Map<Integer, String> fileMap = fileList.stream().collect(Collectors.toMap(SysFileEntity::getFileId, SysFileEntity::getNewName, (e1, e2) -> e2));

        Map<String, String> picAttrs = new HashMap<>();
        sourceMap.forEach((key, value) -> {
            try {
                String fileStr = value.toString();
                if (StringUtils.isNotBlank(fileStr)) {
                    // 字符串逗号分割
                    Integer fileId = Integer.valueOf(fileStr.split(",")[0]);
                    String fileNewName = fileMap.get(fileId);
                    if (fileNewName != null) {
                        String ossUrl = FileUtils.copyImage(fileNewName);
                        if (StringUtils.isNotBlank(ossUrl)) {
                            OtherApiResponse apiResponse = iBydApiService.fileUpload(PushFile.builder().url(ossUrl).build());
                            if (apiResponse.getErrno() == 0) {
                                String shotLink = apiResponse.getData().toString();
                                picAttrs.put(key, shotLink);
                            }
                        }
                    }
                }
            } catch (NumberFormatException e) {
                // 记录文件ID格式不正确的异常
                log.warn("File ID format error, key: {}, value: {}", key, value, e);
            } catch (Exception e) {
                // 记录其他可能发生的异常
                log.error("Error processing picture attributes, key: {}, value: {}", key, value, e);
            }

        });
        return picAttrs;
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("是", "1"),
        NO("否", "0");
        private final String name;
        private final String value;
        YesOrNo(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (BusinessProcessPushSplitInfo.YesOrNo yesOrNo : BusinessProcessPushSplitInfo.YesOrNo.values()) {
                if (yesOrNo.getName().equals(name)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }
}
