package com.bonc.rrs.serviceprovider.service.impl.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.bonc.rrs.byd.domain.MoveInstallAttachments;
import com.bonc.rrs.byd.domain.PushFile;
import com.bonc.rrs.byd.domain.PushMoveInstallInfo;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 服务商业务-服务商移桩安装信息回传
 * @since  2024/4/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushMoveInstallInfo extends AbstractBusinessProcess {

    private final IBydApiService iBydApiService;
    private final WorderExtFieldService worderExtFieldService;
    private final SysFilesService sysFilesService;

    @Override
    public String getProcessCode() {
        return "pushMoveInstallInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {

        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }

        try {

            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            // 工单扩展字段
            List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getSpecificFields(businessProcessPo.getWorderNo(), null);
            Map<Integer, String> fieldMap = new HashMap<>();
            for (WorderExtFieldEntity extFieldEntity : extFieldEntities) {
                fieldMap.put(extFieldEntity.getFieldId(), extFieldEntity.getFieldValue());
            }

            String attachments = buildCheckPicAttrs(fieldMap);

            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            companyOrderNumber = StrUtil.removeSuffix(companyOrderNumber,"-安装");

            PushMoveInstallInfo pushMoveInstallInfo = PushMoveInstallInfo.builder()
                    .orderCode(companyOrderNumber)
                    .operatePerson(OPERATE_PERSON)
                    // 售后完成时间
                    .finishTime(fieldMap.get(1711))
                    // 充电桩编码
                    .wallboxCode(fieldMap.get(2288))
                    // 描述
                    .desc(fieldMap.get(2202))
                    // 取电方式
                    .powerSupplyMethod(fieldMap.get(2203))
                    // 线缆品牌
                    .cableBrand(ConstantPool.CableBrand.getCodeByName(fieldMap.get(2204)))
                    // 线缆规格
                    .cableType(fieldMap.get(2205))
                    // 线缆长度
                    .cableLength(Integer.parseInt(fieldMap.get(2206)))
                    // 断路器品牌
                    .breakerBrand(fieldMap.get(2207))
                    // 断路器型号
                    .breakerType(fieldMap.get(2208))
                    // 是否安装立柱
                    .installStake(YesOrNo.getValueByName(fieldMap.get(2209)))
                    // 是否安装保护箱
                    .installProtectingBox(YesOrNo.getValueByName(fieldMap.get(2210)))
                    // 是否接地极
                    .groundElectrode(YesOrNo.getValueByName(fieldMap.get(2211)))
                    // 前端线材
                    .frontEndCable(FrontEndCableEnum.getValueByName(fieldMap.get(2212)))
                    // 移桩安装附件
                    .extendPicAttrs(attachments)
                    .build();

            // 调用通知接口
            OtherApiResponse otherApiResponse = iBydApiService.pushMoveInstallInfo(pushMoveInstallInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }

        } catch (Exception e) {
            log.error("移桩安装信息回传出现异常", e);
            return Result.error("移桩安装信息回传失败");
        }

        return Result.success();
    }

    /**
     * 构建检查工单图片附件JSON
     * @param fieldMap 扩展字段Map
     * @return 图片附件JSON字符串
     */
    private String buildCheckPicAttrs(Map<Integer, String> fieldMap) {
        MoveInstallAttachments picAttachment = MoveInstallAttachments.builder()
                // 施工使用线缆
                .constructionImage(fieldMap.get(2213))
                // 充电桩序列码
                .sequenceImage(fieldMap.get(2214))
                // 用线始端
                .lineStartImage(fieldMap.get(2215))
                // 用线末端
                .lineEndImage(fieldMap.get(2216))
                //接地线或接地极
                .groundWireImage(fieldMap.get(2217))
                //电源点火零电压
                .zeroVoltageImage(fieldMap.get(2218))
                //人桩合照
                .manPileImage(fieldMap.get(2219))
                //安装确认单
                .confirmationImage(fieldMap.get(2220))
                //增项收费单
                .increaseChargeImage(fieldMap.get(2221))
                //漏保上端火零绝缘电阻
                .fireZeroResistanceImage(fieldMap.get(2222))
                //漏保下端零地电压
                .zeroGroundVoltageImage(fieldMap.get(2223))
                //试充照片
                .trialChargeImage(fieldMap.get(2224))
                //充电桩铭牌图片
                .pileNameplateImage(fieldMap.get(2225))
                //放弃电力报装免责声明
                .disclaimersImage(fieldMap.get(2226))
                //同级负载确认书
                .loadConfirmationImage(fieldMap.get(2227))
                .build();
        // 转换为JSON字符串
        return com.alibaba.fastjson.JSON.toJSONString(getPicAttrs(BeanUtil.beanToMap(picAttachment)));
    }

    private Map<String, String> getPicAttrs(Map<String, Object> sourceMap) {
        sourceMap.entrySet().removeIf(entry -> {
            String value = String.valueOf(entry.getValue());
            return StrUtil.isBlank(value) || "null".equals(value);
        });
        String fileIds = StringUtils.join(sourceMap.values(),  ",");

        List<SysFileEntity> fileList = sysFilesService.getSysFileByIds(fileIds);
        Map<Integer, String> fileMap = fileList.stream().collect(Collectors.toMap(SysFileEntity::getFileId, SysFileEntity::getNewName, (e1, e2) -> e2));

        Map<String, String> picAttrs = new HashMap<>();
        sourceMap.forEach((key, value) -> {
            try {
                String fileStr = value.toString();
                if (StringUtils.isNotBlank(fileStr)) {
                    // 字符串逗号分割
                    Integer fileId = Integer.valueOf(fileStr.split(",")[0]);
                    String fileNewName = fileMap.get(fileId);
                    if (fileNewName != null) {
                        String ossUrl = FileUtils.copyImage(fileNewName);
                        if (StringUtils.isNotBlank(ossUrl)) {
                            OtherApiResponse apiResponse = iBydApiService.fileUpload(PushFile.builder().url(ossUrl).build());
                            if (apiResponse.getErrno() == 0) {
                                String shotLink = apiResponse.getData().toString();
                                picAttrs.put(key, shotLink);
                            }
                        }
                    }
                }
            } catch (NumberFormatException e) {
                // 记录文件ID格式不正确的异常
                log.warn("File ID format error, key: {}, value: {}", key, value, e);
            } catch (Exception e) {
                // 记录其他可能发生的异常
                log.error("Error processing picture attributes, key: {}, value: {}", key, value, e);
            }

        });
        return picAttrs;
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("是", "1"),
        NO("否", "0");
        private final String name;
        private final String value;
        YesOrNo(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (BusinessProcessPushMoveInstallInfo.YesOrNo yesOrNo : BusinessProcessPushMoveInstallInfo.YesOrNo.values()) {
                if (yesOrNo.getName().equals(name)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }

    //前端线材：1 铜 ，2铝，9其他
    @Getter
    private enum FrontEndCableEnum {
        YES("铜", "1"),
        NO("铝", "0"),
        OTHER("其他", "9");
        private final String name;
        private final String value;
        FrontEndCableEnum(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (BusinessProcessPushMoveInstallInfo.FrontEndCableEnum yesOrNo : BusinessProcessPushMoveInstallInfo.FrontEndCableEnum.values()) {
                if (yesOrNo.getName().equals(name)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }

    public static void main(String[] args) {
        String companyOrderNumber = "2023-05-09-20230509150001-安";
        companyOrderNumber = StrUtil.removeSuffix(companyOrderNumber,"-安装");
        System.out.println(companyOrderNumber);
    }
}
