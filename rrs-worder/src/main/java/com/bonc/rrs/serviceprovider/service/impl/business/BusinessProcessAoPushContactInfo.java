package com.bonc.rrs.serviceprovider.service.impl.business;

import cn.hutool.core.util.StrUtil;
import com.bonc.rrs.byd.domain.AoPushContactInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.youngking.lenmoncore.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 服务商业务-服务商报修联系信息回传
 * @Description /jumpto/openapi/sp/ao/pushContactInfo
 * @Date 2024/2/26 18:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessAoPushContactInfo extends AbstractBusinessProcess {

    @Autowired
    private WorderRemarkLogService worderRemarkLogService;

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "aoPushContactInfo";
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
        if (worderInformationEntity == null) {
            return Result.error("非法工单号");
        }
        try {
            List<WorderRemarkLogEntity> worderRemarkLogEntityList = worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());

            String companyOrderNumber = worderInformationEntity.getCompanyOrderNumber();
            companyOrderNumber = StrUtil.removeSuffix(companyOrderNumber,"-安装");
            companyOrderNumber = StrUtil.removeSuffix(companyOrderNumber,"-拆桩");

            AoPushContactInfo aoPushContactInfo = new AoPushContactInfo();
            aoPushContactInfo.setOrderCode(companyOrderNumber);
            aoPushContactInfo.setPlanToSurveyTime(worderInformationEntity.getInstallAppointTime());
            aoPushContactInfo.setOperatePerson(OPERATE_PERSON);
            if (worderRemarkLogEntityList != null && worderRemarkLogEntityList.size() > 0) {
                aoPushContactInfo.setFirstContactTime(DateUtils.format(worderRemarkLogEntityList.get(0).getCreateTime(), DateUtils.DATE_TIME_PATTERN));
            } else {
                aoPushContactInfo.setFirstContactTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
            }
            if (StringUtils.isNotBlank(businessProcessPo.getInstallAppointTime())) {
                aoPushContactInfo.setPlanToSurveyTime(businessProcessPo.getInstallAppointTime());
            }
            // 调用通知接口
            OtherApiResponse otherApiResponse = iBydApiService.aoPushContactInfo(aoPushContactInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("服务商报修联系信息回传出现异常", e);
            return Result.error("服务商报修联系信息回传失败");
        }
        return Result.success();
    }
}
