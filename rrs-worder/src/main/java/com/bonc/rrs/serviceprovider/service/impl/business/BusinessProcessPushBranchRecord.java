package com.bonc.rrs.serviceprovider.service.impl.business;

import cn.hutool.core.util.StrUtil;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 2.13 服务商安装订单信息网点记录回传
 * 接口路径：/jumpto/openapi/sp/pushBranchRecord
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushBranchRecord extends AbstractBusinessProcess {

    private final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushBranchRecord";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (Objects.isNull(businessProcessPo.getPushBranchRecord())) {
            return Result.error("参数不能为空");
        }
        OtherApiResponse otherApiResponse = null;
        try {
            // 调用通知接口
            if (businessProcessPo.getWorderTypeId() != null && businessProcessPo.getWorderTypeId() == 6) {
                String orderCode = businessProcessPo.getPushBranchRecord().getOrderCode();
                if (StrUtil.endWithIgnoreCase(orderCode, "-拆桩")) {
                    orderCode =StrUtil.removeSuffix(orderCode, "-拆桩");
                } else if (StrUtil.endWithIgnoreCase(orderCode, "-安装")) {
                    orderCode =StrUtil.removeSuffix(orderCode, "-安装");
                }
                businessProcessPo.getPushBranchRecord().setOrderCode(orderCode);
                otherApiResponse = iBydApiService.aoPushBranchRecord(businessProcessPo.getPushBranchRecord());
            } else {
                otherApiResponse = iBydApiService.pushBranchRecord(businessProcessPo.getPushBranchRecord());
            }
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("服务商联系信息回传出现异常", e);
            return Result.error("服务商联系信息回传失败");
        }
        return Result.success();
    }
}
