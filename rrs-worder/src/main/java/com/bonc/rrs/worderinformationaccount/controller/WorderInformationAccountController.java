package com.bonc.rrs.worderinformationaccount.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity;
import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.bonc.rrs.balanceprocess.vo.InvoiceProperties;
import com.bonc.rrs.invoice.acs.AcsInterfaceUtil;
import com.bonc.rrs.invoice.acs.dto.FncCollectionInformation;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.manage.utils.SpringUtils;
import com.bonc.rrs.pay.service.WorderOrderLogService;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderinformationaccount.common.WorderBalanceScheduling;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountExcelVO;
import com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单主表
 * 原来的工单开票接口，除查询待结算工单外，其他接口弃用
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-12 14:54:10
 */
@RestController
@RequestMapping("worderinformationaccount")
@Api(tags = {"工单待结算相关接口"})
@Slf4j
public class WorderInformationAccountController extends AbstractController {

    @Autowired
    private WorderInformationAccountService worderInformationAccountService;
    @Autowired
    private CompanyInvoiceService companyInvoiceService;
    @Autowired
    private WorderBalanceScheduling worderBalanceScheduling;
    @Autowired
    private AcsInterfaceUtil acsInterfaceUtil; //ACS记账服务
    //	@Autowired
    //	private VendorInvoiceService vendorInvoiceService;
    //	@Autowired
    //	private WorderBalanceScheduling worderBalanceScheduling;
    @Autowired
    private WorderInformationAccountDao worderInformationAccountDao;
    @Autowired
    private BalanceProperties balanceProperties;
    @Autowired(required = false)
    private InvoiceProperties invoiceProperties;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private WorderOrderLogService worderOrderLogService;

    public R push(List<FncCollectionInformation> fncCollectionInformationList) {
        List<FncCollectionInformation> reList = acsInterfaceUtil.acsAccount(fncCollectionInformationList);
        return R.ok().putList(reList);
    }

    @RequestMapping("acsTest")
    public void acsTest() {
        worderBalanceScheduling.companyInvoiceStatusScheduling();
    }

    @RequestMapping("invoice")
    public void companyInvoiceScheduling(Integer status) {
        List<CompanyInvoiceEntity> list =
                companyInvoiceService.list(new QueryWrapper<>(new CompanyInvoiceEntity().setStatus(status)));
        for (CompanyInvoiceEntity entity : list) {
            try {
                companyInvoiceService.pushEnterprisesInvoice(entity);
            } catch (Exception e) {
                log.error("自动推送金税开票失败，开票单ID：" + entity.getId(), e);
                e.printStackTrace();
            }
        }
    }

    @PostMapping("/listWorderId")
    public R queryWorderId(@RequestBody @Validated(value = WorderInformationAccountVO.QueryNotAccount.class) WorderInformationAccountVO worderInformation) {
        List list = worderInformationAccountService.queryWorderIdList(worderInformation);
        Map<String, String> map = new HashMap<>();
        list.forEach(item -> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(item));
            String companyOrderNumber = jsonObject.getString("companyOrderNumber");
            if (StringUtils.isNotBlank(companyOrderNumber)) {
                Integer id = jsonObject.getInteger("id");
                Integer stimulateId = jsonObject.getInteger("stimulateId");
                Integer worderInvoiceType = jsonObject.getInteger("worderInvoiceType");
                Integer worderId = jsonObject.getInteger("worderId");
                map.put(id.toString(), id + ":" + worderInvoiceType + ":" + worderId + ":" + stimulateId);
            }
        });
        return R.ok().put("worderIdJson", map);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询待结算工单", notes = "查询待结算工单")
    public R queryWorderInformationNotAccountList(@RequestBody @Validated(value = WorderInformationAccountVO.QueryNotAccount.class) WorderInformationAccountVO worderInformation) {
        PageUtils page = worderInformationAccountService.queryWorderInformationNotAccountList(worderInformation);
        return R.ok().put("page", page);
    }

    //导出
    @PostMapping("/downSettleWorder")
    @ApiOperation(value = "下载待结算工单信息", notes = "下载待结算工单信息")
    public void downPublishWorder(HttpServletResponse response, @RequestBody WorderInformationAccountVO worderInformationAccountVO) throws IOException {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");

            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            //工单待结算下载
            worderInformationAccountVO.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE);
            WriteSheet writeSheeet = EasyExcel.writerSheet(0, "待结算工单").head(WorderInformationAccountExcelVO.class).build();
            List<WorderInformationAccountExcelVO> increWorders =
                    worderInformationAccountDao.queryWorderInformationNotAccountExcel(worderInformationAccountVO);
            excelWriter.write(increWorders, writeSheeet);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    ////	@PostMapping("/worderInfoAduit")
    ////	@ApiOperation(value = "工单车企费用审核", notes = "工单车企费用审核")
    ////	@SysLog("工单车企费用审核")
    //	public R worderInfoAduit(@RequestBody @Validated WorderAuditInfoEntity auditInfoEntity) {
    //		auditInfoEntity.setAuditTime(new Date());
    //		auditInfoEntity.setUserId(this.getUser().getUserId().toString());
    //		return worderInformationAccountService.worderInfoAduit(auditInfoEntity);
    //	}
    //
    ////	@PostMapping("/update")
    ////	@ApiOperation(value = "修改工单主表", notes = "修改工单主表")
    ////	@SysLog("修改工单主表")
    ////	@Transactional
    //	public R update(@RequestBody @Validated WorderInformationAccountEntity worderInformation) {
    //		worderInformationAccountService.updateById(worderInformation);
    //		return R.ok();
    //	}
    //
    //	//原来的开票逻辑，弃用
    ////	@PostMapping("/insertVendorInvoice")
    ////	@ApiOperation(value = "新增车企发票表", notes = "新增车企发票表")
    ////	@SysLog("新增车企发票表")
    ////	@Transactional
    //	public R insertVendorInvoice(@RequestBody @Validated(value = WorderInformationAccountVO.Insert.class) WorderInformationAccountVO
    //	worderInformation) {
    //		int companyId = Integer.parseInt(worderInformation.getList().get(0).get("companyId")+"");
    //		for (Map object: worderInformation.getList()) {
    //			if(companyId != Integer.parseInt(object.get("companyId")+""))
    //				return R.error(500,"必须选择同一个车企开票");
    //		}
    //		worderInformationAccountService.insertVendorInvoice(worderInformation);
    //		return R.ok();
    //	}
    //
    ////	@GetMapping("/worderInvoice")
    ////	@ApiOperation(value = "手动调用车企已开票（自测用）")
    //	public R worderInvoice() {
    //		worderBalanceScheduling.vendorInvoiceStatusScheduling();
    //		return R.ok();
    //	}
    ////	@GetMapping("/acsAccountStatus")
    ////	@ApiOperation(value = "手动调用查询acs记账状态（自测用）")
    //	public R acsAccountStatus() {
    //		worderBalanceScheduling.queryAcsAccountScheduling();
    //		return R.ok();
    //	}
    //
    ////	@GetMapping("/balancePublish")
    ////	@ApiOperation(value = "手动调用发布结算（自测用）")
    //	public R balancePublish() {
    //		worderBalanceScheduling.worderPublishScheduling();
    //		return R.ok();
    //	}
    ////	@GetMapping("/cvpBillStatus")
    ////	@ApiOperation(value = "手动调用查询商户通开票状态（自测用）")
    //	public R cvpBillStatus() {
    //		worderBalanceScheduling.cvpBillStatusScheduling();
    //		return R.ok();
    //	}
    @GetMapping("/receivablesAcsAccountSuccess")
    @ApiOperation(value = "假装增项收款记账成功，进行下一步（自测用）")
    public R receivablesAcsAccountSuccess(Integer worderId) {
        try {
            WorderInformationAccountEntity worderInformationAccountEntity = worderInformationAccountService.getById(worderId);
            worderInformationAccountService.receivablesAcsAccountSuccess(worderInformationAccountEntity);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("失败");
        }
        return R.ok();
    }

    @GetMapping("/pushAcsAccountIncreReceivables")
    @ApiOperation(value = "增项结算费用推送ACS记账收款（服务兵APP调用）")
    public R pushAcsAccountIncreReceivables(Integer worderId) {
        worderInformationAccountService.pushAcsAccountIncreReceivables(worderId);
        return R.ok();
    }

    @GetMapping("/queryAcsAccountIncreReceivables")
    @ApiOperation(value = "查询增项结算费用ACS记账收款是否成功（服务兵APP调用）")
    public R queryAcsAccountIncreReceivables(Integer worderId) {
        boolean flag = worderInformationAccountService.queryAcsAccountIncreReceivables(worderId);
        return R.ok().put("flag", flag);
    }

    @GetMapping("/pushAcsAccountIncre")
    @ApiOperation(value = "增项结算费用推送ACS记账收入和暂估成本（服务兵APP调用）")
    public R pushAcsAccountIncre(Integer worderId) {
        worderInformationAccountService.pushAcsAccountIncre(worderId);
        return R.ok();
    }

    @GetMapping("/querySurplusLimit")
    @ApiOperation(value = "查询垫资池剩余额度", notes = "查询垫资池剩余额度")
    public R querySurplusLimit() {
        return worderInformationAccountService.querySurplusLimit();
    }

    @PostMapping("/queryWorderInfoAduitList")
    @ApiOperation(value = "查询待审核列表", notes = "查询待审核列表")
    public R queryWorderInfoAduitList(@RequestBody @Validated(value = ReceivableFeeAduitVO.Query.class) ReceivableFeeAduitVO receivableFeeAduitVO) {
        PageUtils page = worderInformationAccountService.queryWorderInfoAduitList(receivableFeeAduitVO);
        return R.ok().put("page", page);
    }

    //
    ////	@PostMapping("/receivableWorderAduit")
    ////	@ApiOperation(value = "已回款工单审核", notes = "已回款工单审核")
    //	public R receivableWorderAduit(@RequestBody @Validated(value = ReceivableFeeAduitVO.Aduit.class) ReceivableFeeAduitVO receivableFeeAduitVO) {
    //		receivableFeeAduitVO.setUserId(this.getUser().getUserId().toString());
    //		return worderInformationAccountService.receivableWorderAduit(receivableFeeAduitVO);
    //	}
    //
    ////	@PostMapping("/worderReCommitAduit")
    ////	@ApiOperation(value = "已回款工单重新提交审核", notes = "已回款工单重新提交审核")
    //	public R worderReCommitAduit(@RequestBody @Validated(value = ReceivableFeeAduitVO.ReSubmit.class) ReceivableFeeAduitVO
    //	receivableFeeAduitVO) {
    //		return worderInformationAccountService.worderReCommitAduit(receivableFeeAduitVO);
    //	}
    //
    @GetMapping("/getDotInfoList")
    @ApiOperation(value = "获取网点列表", notes = "获取网点列表")
    public R getDotInfoList() {
        return R.ok().putList(worderInformationAccountService.getDotInfoList());
    }
    //
    ////	@GetMapping("dotWorderBalancePublish")
    ////	@ApiOperation(value = "发布工单结算", notes = "发布工单结算")
    ////	public R dotWorderBalancePublish(String worderIds){
    ////		worderInformationAccountService.dotWorderBalancePublish(worderIds);
    ////		return R.ok();
    ////	}
    ////	@GetMapping("dotIncreBalancePublish")
    ////	@ApiOperation(value = "发布增项结算", notes = "发布增项结算")
    ////	public R dotIncreBalancePublish(String worderIds){
    ////		worderInformationAccountService.dotIncreBalancePublish(worderIds);
    ////		return R.ok();
    ////	}
    //
    ////	@PostMapping("dotBalancePublish")
    ////	@ApiOperation(value = "网点发布结算", notes = "网点发布结算")
    //	public R dotBalancePublish(@RequestBody PublishDetail pd){
    //		worderBalanceScheduling.checkedBalancePublish(pd);
    //		return R.ok();
    //	}
    //
    ////	@GetMapping("/queryAllAcsAccount")
    ////	@ApiOperation(value = "查询所有已经推过的ACS记录（自测用）")
    //	public R queryAllAcsAccount(){
    //		//查询所有已经推过的ACS记录
    //		worderInformationAccountService.queryAllAcsAccount();
    //		return R.ok();
    //	}

    @PostMapping("/allFlowOrdersList")
    @ApiOperation(value = "查询全流程展示列表", notes = "查询全流程展示列表")
    public R queryWorderInformationAllFlowOrdersList(@RequestBody Map<String, Object> params) {
        PageUtils page = worderInformationAccountService.queryWorderInformationAllFlowOrdersList(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/pushDealIncreInvoice")
    public R pushFinanceAccountIncreReceivables(@RequestParam(value = "worderNo") String worderNo) {
        WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                new QueryWrapper<WorderInformationEntity>().in("ticket_status", 1, 2)
                        .eq("worder_Incre_status", 0)
                        .eq("worder_no", worderNo)
        );
        if (worderInformationEntity == null) {
            return R.error("no worderInformation to process");
        }

        try {
            WorderOrderLogEntity worderOrderLogEntity = worderOrderLogService.getOne(
                    new QueryWrapper<WorderOrderLogEntity>().eq("order_status", 2).eq("apply", 1).eq("worder_no",
                            worderInformationEntity.getWorderNo()));
            if (worderOrderLogEntity != null) {
                WorderInformationAccountService worderInformationAccountService = (WorderInformationAccountService) SpringUtils.getBean(
                        "worderInformationAccountService");
                worderInformationAccountService.pushFinanceAccountIncreReceivables(worderInformationEntity.getWorderId());
            }
        } catch (Exception e) {
            log.error("error:", e);
            log.info("========================推送增项定时任务异常========================", e);
            return R.error(e.getMessage());
        }
        return R.ok();
    }

}
