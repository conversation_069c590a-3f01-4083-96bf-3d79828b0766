package com.bonc.rrs.worder.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.worderapp.entity.vo.WorderStatusVo;
import com.bonc.rrs.worderapp.service.WorderOrderService;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import io.swagger.annotations.*;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 记录备注相关接口
 * <AUTHOR>
 * @title: WorderRemarkLogController
 * @date 2020/3/1010:58
 */
@RestController
@RequestMapping("worder/remarklog")
@Api(tags = {"记录备注相关接口"})
public class WorderRemarkLogController {
    @Autowired
    private WorderRemarkLogService worderRemarkLogService;
    @Autowired
    private WorderOrderService worderOrderService;

    @Autowired
    private SysDictionaryDetailService sysDictionaryDetailService;
    /**
     * 根据工单编号获取记录备注
     * @param worderNo
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取列表",notes = "获取列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query",dataType = "String",name = "worderNo",value = "参数",required = false)
    })
    public R list(@RequestParam String worderNo){
        List<WorderRemarkLogEntity> list = worderRemarkLogService.list(new QueryWrapper<WorderRemarkLogEntity>().eq("worder_no",worderNo));
        return R.ok().putList(list);
    }

    /**
     * 添加记录备注
     * @param worderRemarkLog
     * @return
     */
    @Warning("下次联系时间")
    @PostMapping("/save")
    @ApiOperation(value = "添加记录备注",notes = "添加记录备注")
    public R save(@ApiParam @RequestBody WorderRemarkLogEntity worderRemarkLog){
        //todo 下次联系时间
        if (worderRemarkLog.getNextContactTime()!=null&&worderRemarkLog.getNextContactTime().length()>1){
            String time = worderRemarkLog.getNextContactTime();
            time = time.replace(":"," ");
            time = time.substring(0,13)+ ":00:00";
            worderRemarkLog.setNextContactTime(time);
            if (StringUtils.isNotBlank(worderRemarkLog.getNoInstallReason())&&worderRemarkLog.getNoInstallReason().equals("-1")){
                return R.error("请选择原因");
            }
            if (StringUtils.isNotBlank(worderRemarkLog.getNoInstallReason())&&!worderRemarkLog.getNoInstallReason().equals("-1")){
                if (StringUtils.isNotBlank(worderRemarkLog.getNoInstallReason())){
                    SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderRemarkLog.getNoInstallReason());
                    worderRemarkLog.setNoInstallReasonId(sysDictionaryDetailEntity.getId());
                    worderRemarkLog.setContent(sysDictionaryDetailEntity.getDetailName()+"_"+worderRemarkLog.getContent()+"下次联系时间"+worderRemarkLog.getNextContactTime());  //备注内容
                }else{
                    worderRemarkLog.setContent(worderRemarkLog.getContent()+"下次联系时间"+worderRemarkLog.getNextContactTime());
                }
            }
        }
        if (StringUtils.isBlank(worderRemarkLog.getWorderLevel())){
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(worderRemarkLog.getNoInstallReason());
            worderRemarkLog.setWorderLevel(sysDictionaryDetailEntity.getRemark());
        }
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderRemarkLog.getWorderNo());
        worderInformationDto.setWorderLevel(worderRemarkLog.getWorderLevel());
        if (StringUtils.isNotBlank(worderInformationDto.getWorderLevel())){
            worderOrderService.updateWorderLevel(worderInformationDto);
        }

        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        worderRemarkLog.setUserId(user.getUserId());
        worderRemarkLogService.add(worderRemarkLog);
        R r = R.ok().putWorderNo(worderRemarkLog.getWorderNo());
        if (StringUtils.isNotBlank(worderRemarkLog.getNextContactTime())) {
            WorderStatusVo worder = worderOrderService.getWorderStatus(worderRemarkLog.getWorderNo());
            r.putWorderExecStatus(worder.getWorderExecStatus()).putWorderTriggerEvent(WarningConstant.UPDATE_NEXT_CONTACT_TIME);
        }
        return r;
    }
}
