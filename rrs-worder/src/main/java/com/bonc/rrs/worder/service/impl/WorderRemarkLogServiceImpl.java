package com.bonc.rrs.worder.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worder.dao.WorderRemarkLogDao;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.workManager.dao.RemarkLogMapper;
import com.youngking.lenmoncore.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;

/**
 * 记录备注Service
 * <AUTHOR>
 * @title: WorderRemarkLogServiceImpl
 * @date 2020/3/1010:47
 */
@Service("worderRemarkLogService")
public class WorderRemarkLogServiceImpl extends ServiceImpl<WorderRemarkLogDao, WorderRemarkLogEntity> implements WorderRemarkLogService {

    @Autowired(required = false)
    WorderOrderDao worderOrderDao;
    @Autowired
    private WorderRemarkLogDao worderRemarkLogDao;
    @Autowired
    private RemarkLogMapper remarkLogMapper;

    /**
     * 添加记录
     * @param worderRemarkLog
     * @return
     */
    @Override
    public boolean add(WorderRemarkLogEntity worderRemarkLog) {
        if (worderRemarkLog != null && StringUtils.isNotBlank(worderRemarkLog.getNextContactTime())) {
            WorderInformationDto worderInformationDto = new WorderInformationDto();
            worderInformationDto.setWorderNo(worderRemarkLog.getWorderNo());
            worderInformationDto.setNextContactTime(worderRemarkLog.getNextContactTime());
            worderOrderDao.updateWorderInformation(worderInformationDto);
        }
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        //生成标题，日期加添加人
        String title = formatDate.format(Date.from(Instant.now()))+" "+worderRemarkLog.getUserName()+"添加";
        worderRemarkLog.setTitle(title);
        worderRemarkLog.setCreateTime(Date.from(Instant.now()));
        return this.save(worderRemarkLog);
    }

    @Override
    public List<WorderRemarkLogEntity> queryConnectTime(String worderNo) {
        return worderRemarkLogDao.queryConnectTime(worderNo);
    }

    @Override
    public void addOperationLog(String worderNo, String title, String content, Integer userId) {
        remarkLogMapper.addOperationLog(worderNo, title, content, userId);
    }

    @Override
    public void closeOrCancelUpdateStatus(Integer id, Integer status, String statusValue, Integer execStatus, String execValue, Integer worderStatus) {
        remarkLogMapper.updateWordeInfoState(id, status, statusValue, execStatus, execValue, worderStatus);
    }
}
