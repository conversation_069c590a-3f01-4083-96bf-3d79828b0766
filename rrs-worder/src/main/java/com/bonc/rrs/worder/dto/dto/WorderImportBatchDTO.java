package com.bonc.rrs.worder.dto.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/30 16:48
 */
@Data
@NoArgsConstructor
@ToString
public class WorderImportBatchDTO {

    @ExcelProperty(index = 0)
    private String a;

    @ExcelProperty(index = 1)
    private String b;

    @ExcelProperty(index = 2)
    private String c;

    @ExcelProperty(index = 3)
    private String d;

    @ExcelProperty(index = 4)
    private String e;

    @ExcelProperty(index = 5)
    private String f;

    @ExcelProperty(index = 6)
    private String g;

    @ExcelProperty(index = 7)
    private String h;

    @ExcelProperty(index = 8)
    private String i;

    @ExcelProperty(index = 9)
    private String j;

    @ExcelProperty(index = 10)
    private String k;

    @ExcelProperty(index = 11)
    private String l;

    @ExcelProperty(index = 12)
    private String m;

    @ExcelProperty(index = 13)
    private String n;

    @ExcelProperty(index = 14)
    private String o;

    @ExcelProperty(index = 15)
    private String p;

    @ExcelProperty(index = 16)
    private String q;

    @ExcelProperty(index = 17)
    private String r;

    @ExcelProperty(index = 18)
    private String s;

    @ExcelProperty(index = 19)
    private String t;

    @ExcelProperty(index = 20)
    private String u;

    @ExcelProperty(index = 21)
    private String v;

    @ExcelProperty(index = 22)
    private String w;

    @ExcelProperty(index = 23)
    private String x;

    @ExcelProperty(index = 24)
    private String y;

    @ExcelProperty(index = 25)
    private String z;




    @ExcelProperty(index = 26)
    private String aa;

    @ExcelProperty(index = 27)
    private String ab;

    @ExcelProperty(index = 28)
    private String ac;

    @ExcelProperty(index = 29)
    private String ad;

    @ExcelProperty(index = 30)
    private String ae;

    @ExcelProperty(index = 31)
    private String af;



    @ExcelProperty(index = 32)
    private String ag;

    @ExcelProperty(index = 33)
    private String ah;

    @ExcelProperty(index = 34)
    private String ai;

    @ExcelProperty(index = 35)
    private String aj;

    @ExcelProperty(index = 36)
    private String ak;

;
}
