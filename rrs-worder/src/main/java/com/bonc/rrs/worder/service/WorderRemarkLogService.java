package com.bonc.rrs.worder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;

import java.util.List;

/**
 * 记录备注Service
 *
 * <AUTHOR>
 * @title: WorderRemarkLogService
 * @date 2020/3/1010:42
 */
public interface WorderRemarkLogService extends IService<WorderRemarkLogEntity> {

    boolean add(WorderRemarkLogEntity worderRemarkLog);

    /**
     * 查询联系时间
     *
     * @param worderNo
     * @return
     */
    List<WorderRemarkLogEntity> queryConnectTime(String worderNo);

    /**
     * add worder_remark_log
     */
    void addOperationLog(String worderNo, String title, String content, Integer userId);

    /**
     * 取消或者关闭订单更新订单状态
     */
    void closeOrCancelUpdateStatus(Integer id, Integer status, String statusValue, Integer execStatus, String execValue, Integer worderStatus);
}
