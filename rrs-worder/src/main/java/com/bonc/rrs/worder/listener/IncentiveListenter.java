package com.bonc.rrs.worder.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.worder.controller.WorderInformationController;
import com.bonc.rrs.worder.dao.DotInformationDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.dto.IncentiveImpDto;
import com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2021/6/21 15:03
 * @Version: 1.0
 */
@AllArgsConstructor
@Log4j2
public class IncentiveListenter extends AnalysisEventListener<IncentiveImpDto> {

    private final WorderInformationDao worderInformationDao;

    private final DotInformationDao dotInformationDao;

    private final WorderPmStimulateDao worderPmStimulateDao;

    private final SysUserEntity user;

    private final List<String> failureList;

    private final Integer[] fileIds;

    private final WorderInformationController worderInformationController;



    @Override
    public void invoke(IncentiveImpDto data, AnalysisContext context) {
        //获取行号
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        //校验上传参数必填
        if (!checkParams(data, rowIndex, failureList)) {
            return;
        }

        WorderPmStimulateVo worderPmStimulateVo = new WorderPmStimulateVo();
//        WorderPmStimulateEntity worderPmStimulate = new WorderPmStimulateEntity();

        QueryWrapper<WorderInformationEntity> worderInformationWrapper = new QueryWrapper<>();
        worderInformationWrapper.eq("worder_no", data.getWorderNo().trim());
        List<WorderInformationEntity> worderInformations = worderInformationDao.selectList(worderInformationWrapper);
        if (worderInformations.size() < 1) {
            failureList.add("第" + rowIndex + "行：根据工单号：[" + data.getWorderNo() + "]未查询到工单记录！");
            return;
        }
        WorderInformationEntity worderInformation = worderInformations.get(0);
        if (worderInformation.getDotId() == null) {
            failureList.add("第" + rowIndex + "行：根据工单号：[" + data.getWorderNo() + "]未派单给网点！");
            return;
        }
        worderPmStimulateVo.setDotId(worderInformation.getDotId());
        worderPmStimulateVo.setStimulateType("10");
        worderPmStimulateVo.setWorderId(worderInformation.getWorderId());
//        worderPmStimulate.setStimulateType(10);

        if (data.getPriceType().trim().equals("含税价")){
            //价格类型
            worderPmStimulateVo.setPriceType(0);
//            worderPmStimulate.setPriceType(0);
        }else if (data.getPriceType().trim().equals("不含税价")){
            //价格类型
            worderPmStimulateVo.setPriceType(1);
//            worderPmStimulate.setPriceType(1);
        }else{
            failureList.add("第" + rowIndex + "行：价格类型：[" + data.getPriceType() + "] 值不正确！");
            return;
        }
        //查询网点
        DotInformationEntity dotInformation = dotInformationDao.selectById(worderInformation.getDotId());
        if (Objects.nonNull(dotInformation)) {
//            worderPmStimulate.setDotId(dotInformation.getDotId());
        }
//        worderPmStimulate.setWorderId(worderInformation.getWorderId());
//        worderPmStimulate.setStatus(10);
        worderPmStimulateVo.setStatus(10);
        List<Map<String, Object>> stimulateReason = null;
        if (data.getIncentiveType().equals("正激励")){
            worderPmStimulateVo.setIncentiveType(0);
            stimulateReason = worderInformationDao.getStimulateDotReason();
        }else if (data.getIncentiveType().equals("负激励")){
            worderPmStimulateVo.setIncentiveType(1);
            stimulateReason = worderInformationDao.getStimulateDotNegativeReason();
        }else{
            failureList.add("第" + rowIndex + "行：正负激励：[" + data.getIncentiveType() + "] 值不正确！");
            return;
        }
        //校验奖惩原因编码是否正确
        long count = stimulateReason.stream().filter(item -> item.get("stimulateReason").toString().equals(data.getStimulateReason())).count();
        if (count < 1) {
            failureList.add("第" + rowIndex + "行：奖惩原因编码：[" + data.getStimulateReason() + "] 值不正确！");
            return;
        }
        worderPmStimulateVo.setStimulateReason(Integer.parseInt(data.getStimulateReason().trim()));
//        worderPmStimulate.setStimulateReason(data.getStimulateReason().trim());
        if (data.getIncentiveType().equals("正激励")&&Integer.parseInt(data.getStimulateFee().trim())>0
                ||data.getIncentiveType().equals("负激励")&&Integer.parseInt(data.getStimulateFee().trim())<0){
//            worderPmStimulate.setStimulateFee(new BigDecimal(data.getStimulateFee().trim()));
            worderPmStimulateVo.setStimulateFee(new BigDecimal(data.getStimulateFee().trim()));
        }else{
            failureList.add("第" + rowIndex + "行：奖惩金额：[" + data.getStimulateFee() + "] 值不正确！");
            return;
        }

        worderInformationController.setStimulate(worderPmStimulateVo);
//        worderPmStimulate.setCreateTime(new Date());
//        worderPmStimulate.setPublishTime(new Date());
//        worderPmStimulate.setUserId(user.getUserId());
//        worderPmStimulate.setUserName(user.getEmployeeName());

//        int insert = worderPmStimulateDao.insert(worderPmStimulate);
//        if (insert < 1) {
//            failureList.add("第" + rowIndex + "行：插入激励信息入库失败！");
//        } else if (fileIds != null) {
//            for(Integer fileId : fileIds){
//                worderPmStimulateDao.addStimulateFile(StimulateFileEntity.builder()
//                        .stimulateId(worderPmStimulate.getId())
//                        .fileId(fileId).build());
//            }
//        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadWorkbookHolder readWorkbookHolder = context.readWorkbookHolder();
        InputStream inputStream = readWorkbookHolder.getInputStream();
        if (Objects.nonNull(inputStream)) {
            try {
                inputStream.close();
            } catch (IOException ignored) {
            }
        }
    }

    /**
     * 校验上传参数必填
     */
    public boolean checkParams(IncentiveImpDto data, Integer rowIndex, List<String> failureList) {
        Field[] declaredFields = data.getClass().getDeclaredFields();
        List<String> paramName = new ArrayList<>();
        try {
            for (Field field : declaredFields) {
                //设置允许访问私有成员变量
                field.setAccessible(true);
                //获取ExcelProperty注解对象
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                //获取睡醒值
                String param = (String) field.get(data);
                if (StringUtils.isBlank(param)) {
                    paramName.add(annotation.value()[0]);
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            log.error("======================处理Excel第" + rowIndex + "行，出现异常=====================", e);
            return false;
        }
        if (paramName.size() > 0) {
            failureList.add("第" + rowIndex + "行：缺少" + paramName + "！");
            return false;
        } else {
            return true;
        }
    }
}
