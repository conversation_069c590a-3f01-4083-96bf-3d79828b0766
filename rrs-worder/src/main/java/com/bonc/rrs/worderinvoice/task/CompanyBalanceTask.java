package com.bonc.rrs.worderinvoice.task;

import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON>hangyibo on 2020-11-02 14:40
 */

@Component("companyBalanceTask")
public class CompanyBalanceTask implements ITask {

    @Autowired(required = false)
    WorderWaitAccountService worderWaitAccountService;

    @Override
    public void run(String params) {
        // 车企待结算工单计算添加
        worderWaitAccountService.addWorderWaitAccountQueryNotAccount(true, true, false);
    }
}
