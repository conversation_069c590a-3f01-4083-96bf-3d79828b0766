package com.bonc.rrs.worderinvoice.dao;

import com.bonc.rrs.pay.entity.entity.IncreDto;
import com.bonc.rrs.worderinvoice.entity.WorderInvoiceEntity;
import com.bonc.rrs.worderinvoice.entity.query.WorderInvoiceQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON>hangyibo on 2020-10-14 16:15
 */

@Mapper
public interface WorderInvoiceMapper {

    /**
     * 工单详情-费用信息-开票状态列表信息\
     * @param worderInvoiceQuery
     * @return
     */
    List<WorderInvoiceEntity> listInvoiceStatusInfoByWorderNo(WorderInvoiceQuery worderInvoiceQuery);


    List<IncreDto> listIncreInfoByWorderNo(@Param("worderNos") List<String> worderNos);

    void updateByInvoiceId(@Param("dto") IncreDto increDto);
}
