package com.bonc.rrs.worderinvoice.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by zhangyibo on 2020-10-30 15:31
 */

@TableName("worder_wait_account")
@Data
public class WorderWaitAccountEntity implements Serializable {

    @TableId
    private Integer id;

    /**
     * 工单id
     */
    private Integer worderId;
    /**
     * 工单结算开票类型： 0: 工单 1: 激励
     */
    private Integer worderInvoiceType;

    /**
     * 工单激励ID
     */
    private Integer stimulateId;

    /**
     * 结算车企ID
     */
    private String companyId;

    /**
     * 开票ID
     */
    private String invoiceId;


    /**
     * 厂商结算总金额(不含税)
     */
    private BigDecimal companyBalanceFee;
    /**
     * 厂商结算总金额(含税)
     */
    private BigDecimal companyBalanceFeeSum;
    /**
     * 厂商结算税额
     */
    private BigDecimal companyBalanceFeeTax;

    /**
     * 创建时间
     */
    private String gmtCreate;
    /**
     * 修改时间
     */
    private String gmtModified;
    /**
     * 是否删除 0: 默认 1: 删除
     */
    private Integer deleted;

    /**
     * 价格类型 0：含税价，1：不含税价
     */
    @TableField(exist = false)
    private Integer priceType;

}
