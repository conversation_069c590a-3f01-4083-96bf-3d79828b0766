package com.bonc.rrs.worderinvoice.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyibo on 2020-10-30 15:36
 */

public interface WorderWaitAccountDao extends BaseMapper<WorderWaitAccountEntity> {

    /**
     * 根据id列表查询信息
     * @param ids
     * @return
     */
    List<WorderWaitAccountEntity> listInfoByIds(List ids);

    List<WorderInformationEntity> listNotAccount();

    List<WorderInformationAccountEntity> queryWorderCompanyAccountList(Integer invoiceId);

}
