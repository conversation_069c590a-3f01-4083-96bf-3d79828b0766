package com.bonc.rrs.worderinvoice.entity;

import com.bonc.rrs.invoice.enterprises.util.InvoiceResults;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangyibo on 2020-10-14 16:53
 */

@Data
public class WorderInvoiceEntity implements Serializable {

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * email
     */
    private String email;

    /**
     *
     */
    private String billingId;

    /**
     * 开票状态
     *  1  未开票
     *  2  开票中
     *  3  开票成功
     *  4  开票失败
     */
    private String billingStatus;

    /**
     * 发票编号
     */
    private String invoiceCode;

    /**
     * 开票成功时间
     */
    private String updateTime;

    /**
     * 税号
     */
    private String customerCode;

    /**
     * 抬头全称
     */
    private String customerName;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 记账记收状态  9 ：记账中 ，10：记账成功 ，11：记收中 ，1 ：记收成功
     */
    private String worderIncreStatus;

    /**
     * 操作流水号
     */
    private String serialNo;
    /**
     * 发票链接
     */
    private String receiptUrl;

    /**
     * 开票单号
     */
    private String companyInvoiceNo;

    private List list;
}
