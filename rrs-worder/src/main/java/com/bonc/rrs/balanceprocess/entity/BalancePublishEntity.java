package com.bonc.rrs.balanceprocess.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 结算发布表
 *
 * <AUTHOR>
 * @date 2020-07-03 17:47:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("balance_publish")
@ApiModel(value = "发布结算实体类")
public class BalancePublishEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    @ApiModelProperty(value = "ID, 暂存后重新提交必传")
    private Integer id;

    /**
     * 发布批次号
     */
    @ApiModelProperty(value = "发布批次号", hidden = true)
    private String batchNo;

    /**
     * 发布单号
     */
    @ApiModelProperty(value = "发布单号", hidden = true)
    private String balancePublishNo;
    /**
     * 网点ID
     */
    @ApiModelProperty(value = "网点ID", required = true)
    private Integer dotId;
    /**
     * 品牌ID
     */
    @ApiModelProperty(value = "品牌ID", required = true)
    private Integer brandId;
    /**
     * 发布类型
     */
    @ApiModelProperty(value = "发布类型", required = true)
    private Integer publishType;
    /**
     * 发布的结算总金额
     */
    @ApiModelProperty(value = "发布的结算总金额", required = true)
    private BigDecimal publishFee;
    /**
     * 工单结算的工单ID
     */
    @ApiModelProperty(value = "工单结算的工单ID")
    private String worderIds;
    /**
     * 增项结算的工单ID
     */
    @ApiModelProperty(value = "增项结算的工单ID")
    private String increIds;
    /**
     * 激励结算的激励ID
     */
    @ApiModelProperty(value = "激励结算的激励ID")
    private String stimulateIds;
    /**
     * 状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过
     */
    @ApiModelProperty(value = "状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过", required = true)
    private Integer status;

    /**
     * 批次状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过
     */
    @ApiModelProperty(value = "批次状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过", required = true)
    private Integer batchStatus;

    @TableField(exist = false)
    private String balanceStatus;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    private Integer creator;
    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名", hidden = true)
    private String creatorName;
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间", hidden = true)
    private Date submitTime;
    /**
     * 推送商户通对账单号
     */
    @ApiModelProperty(value = "推送商户通对账单号", hidden = true)
    private Integer cvpBillId;

    /**
     * 网点结算税率
     */
    @ApiModelProperty(value = "网点结算税率", hidden = true)
    private String dotTaxRate;

    /**
     * 导入发布的文件地址
     */
    @ApiModelProperty(value = "导入发布时的文件地址")
    @TableField(exist = false)
    private String importFileUrl;

    @TableField(exist = false)
    @ApiModelProperty(value = "暂存的回款重新提交时删除的文件ID")
    private List<Integer> delFileIds;

    @TableField(exist = false)
    private List<Integer> fileIds;
    @TableField(exist = false)
    private Integer userId;
    @TableField(exist = false)
    private Map<Integer, BigDecimal> worderIdFee;
    @TableField(exist = false)
    private Map<Integer, BigDecimal> stimulateIdFee;

    @TableField(exist = false)
    private String companyInvoiceNo;
    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    private String payForm;

    /**
     * 付款银行
     */
    @ApiModelProperty(value = "付款银行")
    private String payBank;

    /**
     * 付款银行
     */
    @ApiModelProperty(value = "出账银行")
    private String outBank;


    //回款时间开始
    @TableField(exist = false)
    private String receivableStartTime;

    //回款时间结束
    @TableField(exist = false)
    private String receivableEndTime;
}
