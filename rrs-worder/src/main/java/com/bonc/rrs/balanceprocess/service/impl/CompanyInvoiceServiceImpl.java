package com.bonc.rrs.balanceprocess.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.dao.*;
import com.bonc.rrs.balanceprocess.entity.*;
import com.bonc.rrs.balanceprocess.service.*;
import com.bonc.rrs.balanceprocess.vo.*;
import com.bonc.rrs.balancerule.dao.BalanceRuleDao;
import com.bonc.rrs.balancerule.entity.BalanceRuleEntity;
import com.bonc.rrs.byd.domain.PushSettleInfo;
import com.bonc.rrs.invoice.acs.dto.FncCollectionInformation;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.FinanceCallbackUtil;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.Request;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.ResponseBody;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.TokenUtil;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.auditNotify.AuditNotify;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.infoSync.InfoSync;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.infoSync.InfoSyncResp;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.settlementAuditNotify.SettlementAuditNotify;
import com.bonc.rrs.invoice.enterprises.finance.business.deleteOrder.req.ReqDeleteOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.deleteOrder.resp.RespDeleteOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.detailsEdit.req.ReqDetailsEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.detailsEdit.resp.RespDetailsEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.*;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.resp.RespInsertOrUpdate;
import com.bonc.rrs.invoice.enterprises.finance.business.invoiceEdit.req.InvoiceDetails;
import com.bonc.rrs.invoice.enterprises.finance.business.invoiceEdit.req.ReqInvoiceEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.invoiceEdit.resp.RespInvoiceEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.transferSubOrder.req.ReqTransferSubOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.transferSubOrder.resp.RespTransferSubOrder;
import com.bonc.rrs.invoice.enterprises.util.*;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.util.DataUtil;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.po.BrandPo;
import com.bonc.rrs.worder.service.CompanyInformationService;
import com.bonc.rrs.worder.service.WorderAuditRecordService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.InvoiceBusinessTypeDetailDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.entity.*;
import com.bonc.rrs.worderinformationaccount.entity.vo.InvoiceBusinessTypeDetailVo;
import com.bonc.rrs.worderinformationaccount.service.*;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.common.pay.wxpay.util.WXPayUtil;
import com.google.gson.JsonObject;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.validator.Assert;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service("companyInvoiceService")
@Slf4j
public class CompanyInvoiceServiceImpl extends ServiceImpl<CompanyInvoiceDao, CompanyInvoiceEntity> implements CompanyInvoiceService {
    @Autowired(required = false)
    private WorderInformationAccountDao worderInformationAccountDao;
    @Autowired(required = false)
    private WorderInformationAccountService worderInformationAccountService;
    @Autowired(required = false)
    private BalanceProperties balanceProperties;
    @Autowired(required = false)
    private InvoiceProperties invoiceProperties;
    @Autowired(required = false)
    private WorderPmStimulateService worderPmStimulateService;
    @Autowired(required = false)
    private WorderInformationService worderInformationService;
    @Autowired(required = false)
    private BalanceFileService balanceFileService;
    @Autowired(required = false)
    private CompanyInvoiceFileService companyInvoiceFileService;
    @Autowired(required = false)
    private SerialNoVersionService serialNoVersionService;
    @Autowired(required = false)
    private EnterprisesService enterprisesService; //金税开票服务
    @Autowired(required = false)
    private BalanceEnterprisesDetailRecordService balanceEnterprisesDetailRecordService;
    @Autowired(required = false)
    private BalanceEnterprisesHeaderRecordService balanceEnterprisesHeaderRecordService;
    @Autowired(required = false)
    WorderWaitAccountService worderWaitAccountService;
    @Autowired(required = false)
    CompanyInvoiceDao companyInvoiceDao;
    @Autowired(required = false)
    private CompanyReceivableNoticeDao companyReceivableNoticeDao;
    @Autowired(required = false)
    private InvoiceBusiness invoiceBusiness;
    @Autowired(required = false)
    private InvoiceVoucherRecordService invoiceVoucherRecordService;
    @Autowired
    private CompanyInformationService companyInformationService;

    @Autowired
    WorderInformationAttributeDao worderInformationAttributeDao;

    @Autowired
    private ProviderBusinessService providerBusinessService;

    @Autowired
    private CompanyReceivableCapitalpoolDao companyReceivableCapitalpoolDao;

    @Autowired
    private BalanceRuleDao balanceRuleDao;

    @Autowired
    private WorderInformationDao worderInformationdao;

    @Autowired
    private FinanceBusiness financeBusiness;

    @Autowired
    private CompanyReceivableRecordService companyReceivableRecordService;

    @Autowired
    private WorderAuditRecordService worderAuditRecordService;

    @Autowired
    private WorderChildInformationService worderChildInformationService;

    @Autowired
    private CostInformationService costInformationService;

    @Autowired
    private CompanyReceivableDao companyReceivableDao;

    @Autowired
    private BalancePublishDao balancePublishDao;

    @Autowired
    private CompanyInvoiceService companyInvoiceService;

    @Autowired
    private SysDictionaryDetailService sysDictionaryDetailService;

    @Autowired
    private InvoiceBusinessTypeDetailDao invoiceBusinessTypeDetailDao;

    /**
     * 批量开票获取发票基本信息
     */
    @Override
    public CompanyInvoiceVO getInvoiceBasic(CompanyInvoiceForm companyInvoiceForm) {
        //数据校验
        Integer companyId = companyInvoiceForm.getCompanyId();
        if (companyId == null) {
            throw new RRException("开票的厂商ID不能为空");
        }
        List<Integer> worderIds = companyInvoiceForm.getWorderIds();
        List<Integer> ids = companyInvoiceForm.getIds();
        if (CollectionUtils.isEmpty(worderIds)) {
            throw new RRException("开票必须选择一个或多个工单");
        }
        List<WorderWaitAccountEntity> worderWaitAccounts = worderWaitAccountService.listInfoByIds(ids);


        // 验证是否是同一结算价格类型
        Integer priceType = null;
        Set<Integer> priceTypeSet = new HashSet<>();
        BigDecimal companyBalanceFee = BigDecimal.ZERO;
        BigDecimal companyBalanceFeeSum = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(worderWaitAccounts)) {
            for (WorderWaitAccountEntity worderWaitAccount : worderWaitAccounts) {
                priceTypeSet.add(worderWaitAccount.getPriceType());
                companyBalanceFee = companyBalanceFee.add(worderWaitAccount.getCompanyBalanceFee());
                companyBalanceFeeSum = companyBalanceFeeSum.add(worderWaitAccount.getCompanyBalanceFeeSum());
            }
        }
        if (companyBalanceFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RRException("开票工单的总金额必须大于零");
        }

        if (priceTypeSet.size() == IntegerEnum.ONE.getValue()) {
            for (Integer type : priceTypeSet) {
                priceType = type;
            }
        }
        //验证所有工单的税率是否一致
        //根据工单ID查询绑定的所有车企结算规则
        List<BalanceRuleEntity> balanceRuleEntities = balanceRuleDao.selectListByWorderIds(worderIds);
        if (balanceRuleEntities.isEmpty()) {
            throw new RRException("未查询到结算规则");
        }
        if (balanceRuleEntities.size() != worderIds.size()) {
            throw new RRException("当前开票单下存在未查询到结算规则的工单");
        }
        //过滤出税率
        Set<String> taxrate = balanceRuleEntities.stream().map(BalanceRuleEntity::getBalanceTaxRateValue).collect(Collectors.toSet());
        if (!taxrate.isEmpty() && taxrate.size() > 1) {
            throw new RRException("当前开票单下存在税率不一致的工单");
        }
        //获取结算规则的税率
        AtomicReference<BigDecimal> companTaxRates = new AtomicReference<>();
        taxrate.forEach(item -> {
            companTaxRates.set(new BigDecimal(item));
        });

        BigDecimal invoiceTax = BigDecimal.ZERO;
        BigDecimal allNoTaxFee = BigDecimal.ZERO;
        BigDecimal invoiceFee = BigDecimal.ZERO;
        BigDecimal companTaxRate = companTaxRates.get();
        Integer roundMode = balanceProperties.ROUND_MODE;
        //价格计算
        if (null != priceType) {
            // 含税价
            if (IntegerEnum.ZERO.getValue().equals(priceType)) {
                invoiceFee = companyBalanceFeeSum;
                invoiceTax = companyBalanceFeeSum.divide(companTaxRate.add(BigDecimal.ONE), 10, roundMode)
                        .multiply(companTaxRate).setScale(2, roundMode);
                allNoTaxFee = invoiceFee.subtract(invoiceTax);
            }
            // 不含税价
            else if (IntegerEnum.ONE.getValue().equals(priceType)) {
                allNoTaxFee = companyBalanceFee;
                invoiceTax = allNoTaxFee.multiply(companTaxRate).setScale(2, roundMode);
                invoiceFee = allNoTaxFee.add(invoiceTax);
            }
        } else {
            throw new RRException("开票工单结算价格类型必须一致");
        }

        //填充发票明细信息
        CompanyInvoiceVO companyInvoiceVO = new CompanyInvoiceVO();
        companyInvoiceVO.setInvoiceFee(invoiceFee);
        companyInvoiceVO.setTaxPrice(invoiceFee);
        companyInvoiceVO.setTax(invoiceTax);
        companyInvoiceVO.setNoTaxFee(allNoTaxFee);
        companyInvoiceVO.setTaxRate(companTaxRate.multiply(BigDecimal.valueOf(100L)));
        companyInvoiceVO.setGoodsName(balanceProperties.GOODS_NAME);
        companyInvoiceVO.setGoodsModel(balanceProperties.GOODS_MODEL);
        companyInvoiceVO.setGoodsUnit(balanceProperties.PRODUCT_UNIT);
        companyInvoiceVO.setGoodsNum(BigDecimal.ONE);
        companyInvoiceVO.setCompanyId(companyId);
        companyInvoiceVO.setPriceType(priceType);
        // 发票信息的销售方和购买方信息填充
        invoiceCustomerAndSellers(companyId, companyInvoiceVO);

        return companyInvoiceVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInvoice(CompanyInvoiceForm companyInvoiceForm, MultipartFile[] files) throws IOException {
        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        /* ------------------第一步：校验发票金额是否正确--------------------*/
//        //发票总金额计算
//        List<WorderWaitAccountEntity> worderWaitAccounts = new ArrayList<>(worderWaitAccountService.listByIds(companyInvoiceForm.getWorderIds()));
//        BigDecimal allNoTaxFee = BigDecimal.ZERO;
//        BigDecimal allTax = BigDecimal.ZERO;
//        BigDecimal allTaxFee = BigDecimal.ZERO;
//        if(CollectionUtils.isNotEmpty(worderWaitAccounts)) {
//            for (WorderWaitAccountEntity e : worderWaitAccounts) {
//                allNoTaxFee = allNoTaxFee.add(e.getCompanyBalanceFee());
//                allTax = allTax.add(e.getCompanyBalanceFeeTax());
//                allTaxFee = allTaxFee.add(e.getCompanyBalanceFeeSum());
//            }
//        }
//        if(allNoTaxFee.compareTo(BigDecimal.ZERO)<=0){
//            throw new RRException("开票工单的总金额必须大于零");
//        }
//        //根据不含税金额的累加和，计算票面的含税金额、税额，校验
//        BigDecimal invoiceTax = allNoTaxFee.multiply(balanceProperties.RRS_COMPANY_TAX_RATE)
//                .setScale(2, balanceProperties.ROUND_MODE);
//        BigDecimal invoiceFee = allNoTaxFee.add(invoiceTax);
//        if(invoiceFee.compareTo(companyInvoiceForm.getInvoiceFee()) != 0){
//            throw new RRException("开票的总金额有误，应为"+invoiceFee+"，实为"+companyInvoiceForm.getInvoiceFee());
//        }
//        if(invoiceTax.compareTo(companyInvoiceForm.getTax()) != 0){
//            throw new RRException("开票的税额有误，应为"+invoiceTax+"，实为"+companyInvoiceForm.getTax());
//        }
        BigDecimal invoiceFee = companyInvoiceForm.getInvoiceFee();
        BigDecimal allNoTaxFee = companyInvoiceForm.getNoTaxFee();
        //商品数量校验
        BigDecimal goodsNum = companyInvoiceForm.getGoodsNum();
        if (goodsNum == null || goodsNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new RRException("商品数量不能为空且不能为0");
        }
        //计算单价，校验
        BigDecimal taxPrice = invoiceFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        BigDecimal noTaxPrice = allNoTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
//        if(taxPrice.compareTo(companyInvoiceForm.getTaxPrice()) != 0){
//            throw new RRException("开票的单价有误，应为"+taxPrice+"，实为"+companyInvoiceForm.getTaxPrice());
//        }
        /* ------------------第二步：数据入库--------------------*/
        if (companyInvoiceForm.getId() != null) {
            //重新提交的话，校验用户
            Integer invoiceId = companyInvoiceForm.getId();
            CompanyInvoiceEntity entity = this.getById(invoiceId);
            if (userId != entity.getCreator().intValue()) {
                throw new RRException("当前登录用户不是开票信息的创建人，不能修改");
            }
            //校验开票信息的状态
            if (entity.getStatus() > 1) {
                throw new RRException("开票信息已提交，不能修改");
            }
            //更新开票信息
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }

            //调整尾差
            //用最后一个工单来调整尾差
//            WorderWaitAccountEntity adjectedWorder = worderWaitAccounts.get(worderWaitAccounts.size() - 1);
//            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
//            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
//            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
//            if(invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0 ) {
//                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
//                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
//                Integer adjectedWorderId = adjectedWorder.getWorderId();
//                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
//                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
//                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
//                adjectedWorder.setCompanyBalanceFeeTax(newTax);
//
//                entity.setAdjustedWorderId(adjectedWorderId);
//                entity.setAdjustedDesc(adjectedDesc);
//            }
            //回退原来选的工单的结算状态
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("invoice_id", invoiceId)
                    .set("invoice_id", null)
                    .set("worder_set_status", 1)
                    .set("worder_set_status_value", "车企待结算"));
            //更新新选工单结算状态
//            for (WorderInformationAccountEntity e : worderWaitAccounts) {
//                e.setInvoiceId(invoiceId);
//                e.setWorderSetStatus(2);
//                e.setWorderSetStatusValue("等待开票");
//            }
//            worderInformationAccountService.updateBatchById(worderList);

            //更新开票信息
            this.updateById(entity);
            //处理页面上进行删除的已提交的关联文件
            balanceFileService.deleteFiles(companyInvoiceForm.getDelFileIds());
            companyInvoiceFileService.remove(new QueryWrapper<CompanyInvoiceFileEntity>()
                    .eq("invoice_id", invoiceId)
                    .in("file_id", companyInvoiceForm.getDelFileIds()));
            //保存页面上新增的关联文件
            if (files != null && files.length > 0) {
                addFiles(invoiceId, userId, files);
            }
        } else {
            //首次暂存或提交的话，生成开票单号
            String companyInvoiceNo = serialNoVersionService.businessNoMaker('k');
            //整理开票实体类对象
            CompanyInvoiceEntity entity = new CompanyInvoiceEntity();
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setCreator(userId);
            entity.setCreatorName(user.getEmployeeName());
            entity.setCreateTime(date);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            entity.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE.multiply(BigDecimal.valueOf(100L)));
            entity.setCompanyInvoiceNo(companyInvoiceNo);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }
            //调整尾差
            //用最后一个工单来调整尾差
//            WorderWaitAccountEntity adjectedWorder = worderWaitAccounts.get(worderWaitAccounts.size() - 1);
//            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
//            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
//            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
//            if(invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0 ) {
//                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
//                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
//                Integer adjectedWorderId = adjectedWorder.getWorderId();
//                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
//                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
//                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
//                adjectedWorder.setCompanyBalanceFeeTax(newTax);
//
//                entity.setAdjustedWorderId(adjectedWorderId);
//                entity.setAdjustedDesc(adjectedDesc);
//            }
            //保存开票信息
            this.save(entity);
            Integer invoiceId = entity.getId();
            //更新工单结算状态
//            for (WorderInformationAccountEntity e : worderList) {
//                e.setInvoiceId(invoiceId);
//                e.setWorderSetStatus(2);
//                e.setWorderSetStatusValue("等待开票");
//            }
//            worderInformationAccountService.updateBatchById(worderList);
            //保存关联文件
            if (files != null && files.length > 0) {
                addFiles(invoiceId, userId, files);
            }
        }

    }

    /**
     * 提交发票信息
     */
//    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInvoice2(CompanyInvoiceForm companyInvoiceForm, MultipartFile[] files) throws IOException {
        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        /* ------------------第一步：校验发票金额是否正确--------------------*/
        //发票总金额计算
        List<WorderInformationAccountEntity> worderList = worderInformationAccountDao.selectBatchIds(companyInvoiceForm.getWorderIds());
        BigDecimal allNoTaxFee = BigDecimal.ZERO;
        BigDecimal allTax = BigDecimal.ZERO;
        BigDecimal allTaxFee = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(worderList)) {
            for (WorderInformationAccountEntity e : worderList) {
                allNoTaxFee = allNoTaxFee.add(e.getCompanyBalanceFee());
                allTax = allTax.add(e.getCompanyBalanceFeeTax());
                allTaxFee = allTaxFee.add(e.getCompanyBalanceFeeSum());
            }
        }
        if (allNoTaxFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RRException("开票工单的总金额必须大于零");
        }
        //根据不含税金额的累加和，计算票面的含税金额、税额，校验
        BigDecimal invoiceTax = allNoTaxFee.multiply(balanceProperties.RRS_COMPANY_TAX_RATE)
                .setScale(2, balanceProperties.ROUND_MODE);
        BigDecimal invoiceFee = allNoTaxFee.add(invoiceTax);
        if (invoiceFee.compareTo(companyInvoiceForm.getInvoiceFee()) != 0) {
            throw new RRException("开票的总金额有误，应为" + invoiceFee + "，实为" + companyInvoiceForm.getInvoiceFee());
        }
        if (invoiceTax.compareTo(companyInvoiceForm.getTax()) != 0) {
            throw new RRException("开票的税额有误，应为" + invoiceTax + "，实为" + companyInvoiceForm.getTax());
        }
        //商品数量校验
        BigDecimal goodsNum = companyInvoiceForm.getGoodsNum();
        if (goodsNum == null || goodsNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new RRException("商品数量不能为空且不能为0");
        }
        //计算单价，校验
        BigDecimal taxPrice = invoiceFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        BigDecimal noTaxPrice = allNoTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        if (taxPrice.compareTo(companyInvoiceForm.getTaxPrice()) != 0) {
            throw new RRException("开票的单价有误，应为" + taxPrice + "，实为" + companyInvoiceForm.getTaxPrice());
        }
        /* ------------------第二步：数据入库--------------------*/
        if (companyInvoiceForm.getId() != null) {
            //重新提交的话，校验用户
            Integer invoiceId = companyInvoiceForm.getId();
            CompanyInvoiceEntity entity = this.getById(invoiceId);
            if (userId != entity.getCreator().intValue()) {
                throw new RRException("当前登录用户不是开票信息的创建人，不能修改");
            }
            //校验开票信息的状态
            if (entity.getStatus() > 1) {
                throw new RRException("开票信息已提交，不能修改");
            }
            //更新开票信息
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }

            //调整尾差
            //用最后一个工单来调整尾差
            WorderInformationAccountEntity adjectedWorder = worderList.get(worderList.size() - 1);
            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
            if (invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
                Integer adjectedWorderId = adjectedWorder.getWorderId();
                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
                adjectedWorder.setCompanyBalanceFeeTax(newTax);

                entity.setAdjustedWorderId(adjectedWorderId);
                entity.setAdjustedDesc(adjectedDesc);
            }
            //回退原来选的工单的结算状态
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("invoice_id", invoiceId)
                    .set("invoice_id", null)
                    .set("worder_set_status", 1)
                    .set("worder_set_status_value", "车企待结算"));
            //更新新选工单结算状态
            for (WorderInformationAccountEntity e : worderList) {
                e.setInvoiceId(invoiceId);
                e.setWorderSetStatus(2);
                e.setWorderSetStatusValue("等待开票");
            }
            worderInformationAccountService.updateBatchById(worderList);

            //更新开票信息
            this.updateById(entity);
            //处理页面上进行删除的已提交的关联文件
            balanceFileService.deleteFiles(companyInvoiceForm.getDelFileIds());
            companyInvoiceFileService.remove(new QueryWrapper<CompanyInvoiceFileEntity>()
                    .eq("invoice_id", invoiceId)
                    .in("file_id", companyInvoiceForm.getDelFileIds()));
            //保存页面上新增的关联文件
            if (files != null && files.length > 0) {
                addFiles(invoiceId, userId, files);
            }
        } else {
            //首次暂存或提交的话，生成开票单号
            String companyInvoiceNo = serialNoVersionService.businessNoMaker('k');
            //整理开票实体类对象
            CompanyInvoiceEntity entity = new CompanyInvoiceEntity();
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setCreator(userId);
            entity.setCreatorName(user.getEmployeeName());
            entity.setCreateTime(date);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            entity.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE.multiply(BigDecimal.valueOf(100L)));
            entity.setCompanyInvoiceNo(companyInvoiceNo);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }
            //调整尾差
            //用最后一个工单来调整尾差
            WorderInformationAccountEntity adjectedWorder = worderList.get(worderList.size() - 1);
            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
            if (invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
                Integer adjectedWorderId = adjectedWorder.getWorderId();
                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
                adjectedWorder.setCompanyBalanceFeeTax(newTax);

                entity.setAdjustedWorderId(adjectedWorderId);
                entity.setAdjustedDesc(adjectedDesc);
            }
            //保存开票信息
            this.save(entity);
            Integer invoiceId = entity.getId();
            //更新工单结算状态
            for (WorderInformationAccountEntity e : worderList) {
                e.setInvoiceId(invoiceId);
                e.setWorderSetStatus(2);
                e.setWorderSetStatusValue("等待开票");
            }
            worderInformationAccountService.updateBatchById(worderList);
            //保存关联文件
            if (files != null && files.length > 0) {
                addFiles(invoiceId, userId, files);
            }
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInvoice(CompanyInvoiceForm companyInvoiceForm) throws IOException {
        //获取发票号ID
        Integer invoiceId = companyInvoiceForm.getId();

        List<Integer> ids = companyInvoiceForm.getIds();
        //工单ID
        List<Integer> worderIds = companyInvoiceForm.getWorderIds();
        //激励ID
        List<Integer> stimulateIds = companyInvoiceForm.getStimulateIds();

        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();

        // 含税金额最大开票限制额度
        BigDecimal invoiceLimitPrice = balanceProperties.INVOICE_LIMIT_PRICE;
        // 不含税金额最大开票限制额度
        BigDecimal noTaxInvoiceLimitPrice = balanceProperties.NO_TAX_INVOICE_LIMIT_PRICE;

        /* ------------------第一步：校验发票金额是否正确--------------------*/
        //发票总金额计算
        List<WorderWaitAccountEntity> worderWaitAccounts = new ArrayList<>();
        //含税总金额
        BigDecimal invoiceFee = companyInvoiceForm.getInvoiceFee();
        //不含税总金额
        BigDecimal allNoTaxFee = companyInvoiceForm.getNoTaxFee();
        //税额
        BigDecimal tax = companyInvoiceForm.getTax();

        //费用类型
        Integer priceType = companyInvoiceForm.getPriceType();

        //校验
        if (allNoTaxFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RRException("开票工单的总金额必须大于零");
        }

        //如果手动拆分发票需要校验金额
        List<InvoiceData> invoiceList = companyInvoiceForm.getInvoiceList();
        if (invoiceList != null && invoiceList.size() > 0) {
            BigDecimal tempTotalInvoiceFee = BigDecimal.ZERO;
            BigDecimal tempTotalNoTaxFee = BigDecimal.ZERO;
            BigDecimal tempTotalTaxFee = BigDecimal.ZERO;

            for (InvoiceData invoiceData : invoiceList) {
                BigDecimal singleInvoiceFee = invoiceData.getInvoiceFee();
                BigDecimal singleNoTaxFee = invoiceData.getNoTaxFee();
                BigDecimal singleTax = invoiceData.getTax();
                if (singleInvoiceFee == null || singleInvoiceFee.compareTo(BigDecimal.ZERO) == 0 || singleNoTaxFee == null || singleNoTaxFee.compareTo(BigDecimal.ZERO) == 0) {
                    throw new RRException("单张发票的含税或不含税金额必须大于0");
                }
                //校验单张发票金额是否超过限额
                if (priceType == 0) {
                    Assert.isTrue(singleInvoiceFee.compareTo(invoiceLimitPrice) <= 0, "单张发票含税金额超过限额！");
                } else if (priceType == 1) {
                    Assert.isTrue(singleNoTaxFee.compareTo(noTaxInvoiceLimitPrice) <= 0, "单张发票不含税金额超过限额！");
                }
                tempTotalInvoiceFee = tempTotalInvoiceFee.add(singleInvoiceFee);
                tempTotalNoTaxFee = tempTotalNoTaxFee.add(singleNoTaxFee);
                tempTotalTaxFee = tempTotalTaxFee.add(singleTax);
            }
            //校验总金额是否超过限制
            if (priceType == 0) {
                Assert.isTrue(tempTotalInvoiceFee.compareTo(invoiceFee) == 0, "拆分的单张发票总和不等于含税总金额！");
            } else if (priceType == 1) {
                Assert.isTrue(tempTotalNoTaxFee.compareTo(allNoTaxFee) == 0, "拆分的单张发票总和不等于不含税总金额！");
            }
            //如果单张发票累积和开票单金额不一致一累积为准
            if (invoiceFee.compareTo(tempTotalInvoiceFee) != 0) {
                invoiceFee = tempTotalInvoiceFee.setScale(2, balanceProperties.ROUND_MODE);
            }

            if (allNoTaxFee.compareTo(tempTotalNoTaxFee) != 0) {
                allNoTaxFee = tempTotalNoTaxFee.setScale(2, balanceProperties.ROUND_MODE);
            }

            if (tax.compareTo(tempTotalTaxFee) != 0) {
                tax = tempTotalTaxFee.setScale(2, balanceProperties.ROUND_MODE);
            }

        }
        //商品数量校验
        BigDecimal goodsNum = companyInvoiceForm.getGoodsNum();
        if (goodsNum == null || goodsNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new RRException("商品数量不能为空且不能为0");
        }

        //含税单价
        BigDecimal taxPrice = invoiceFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        //不含税单价
        BigDecimal noTaxPrice = allNoTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);

        /* ------------------第二步：数据入库--------------------*/
        if (invoiceId != null) {
            //重新提交的话，校验用户
            CompanyInvoiceEntity entity = this.getById(invoiceId);
            if (userId != entity.getCreator()) {
                throw new RRException("当前登录用户不是开票信息的创建人，不能修改");
            }

            //校验开票信息的状态
            if (entity.getStatus() > 1) {
                throw new RRException("开票信息已提交，不能修改");
            }

            //更新开票信息
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setTaxPrice(taxPrice);

            entity.setNoTaxFee(allNoTaxFee);
            entity.setInvoiceFee(invoiceFee);
            entity.setTax(tax);

            if (companyInvoiceForm.getStatus() == 2) {
                entity.setSubmitTime(date);
            }
            entity.setInvoiceType(companyInvoiceForm.getInvoiceType());
            entity.setMailbox(companyInvoiceForm.getMailbox());

            // 提交待审核
            worderWaitAccountService.update(new UpdateWrapper<WorderWaitAccountEntity>()
                    .eq("invoice_id", invoiceId)
                    .set("status", 1)
                    .set("status_value", "等待开票"));
            // 待开票
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("invoice_id", invoiceId)
                    .set("worder_set_status", 2)
                    .set("worder_set_status_value", "等待开票"));

            //更新开票信息
            this.updateById(entity);
            //处理页面上进行删除的已提交的关联文件
            balanceFileService.deleteFiles(companyInvoiceForm.getDelFileIds());
            companyInvoiceFileService.remove(new QueryWrapper<CompanyInvoiceFileEntity>()
                    .eq("invoice_id", invoiceId)
                    .in("file_id", companyInvoiceForm.getDelFileIds()));
            //保存关联文件
            List<CompanyInvoiceFileEntity> companyInvoiceFiles = new ArrayList<>();
            List<Integer> fileIds = companyInvoiceForm.getFileIds();
            fileIds.forEach(fileId -> {
                CompanyInvoiceFileEntity companyInvoiceFileEntity = new CompanyInvoiceFileEntity();
                companyInvoiceFileEntity.setFileId(fileId);
                companyInvoiceFileEntity.setInvoiceId(invoiceId);
                companyInvoiceFiles.add(companyInvoiceFileEntity);
            });
            companyInvoiceFileService.saveBatch(companyInvoiceFiles);
        } else {
            //首次暂存或提交的话，生成开票单号
            String companyInvoiceNo = serialNoVersionService.businessNoMaker('k');
            //整理开票实体类对象
            CompanyInvoiceEntity entity = new CompanyInvoiceEntity();
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setCreator(userId);
            entity.setCreatorName(user.getEmployeeName());
            entity.setCreateTime(date);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setTaxPrice(taxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            entity.setInvoiceFee(invoiceFee);
            entity.setTax(tax);
            entity.setCavState(0);
            entity.setPriceType(companyInvoiceForm.getPriceType());
            entity.setInvoiceType(companyInvoiceForm.getInvoiceType());
            entity.setMailbox(companyInvoiceForm.getMailbox());
            entity.setBusinessType(companyInvoiceForm.getBusinessType());
            //entity.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE.multiply(BigDecimal.valueOf(100L)));
            entity.setCompanyInvoiceNo(companyInvoiceNo);
            if (companyInvoiceForm.getStatus() == 2) {
                entity.setSubmitTime(date);
            }
            //保存开票信息
            this.save(entity);
            Integer saveInvoiceId = entity.getId();

            InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetailEntity = new InvoiceBusinessTypeDetailEntity();
            invoiceBusinessTypeDetailEntity.setJzfwfsd(companyInvoiceForm.getJzfwfsd());
            invoiceBusinessTypeDetailEntity.setFsdxxdz(companyInvoiceForm.getFsdxxdz());
            invoiceBusinessTypeDetailEntity.setJzxmmc(companyInvoiceForm.getJzxmmc());
            invoiceBusinessTypeDetailEntity.setKdsbz(companyInvoiceForm.getKdsbz());
            invoiceBusinessTypeDetailEntity.setTdzzsxmbh(companyInvoiceForm.getTdzzsxmbh());
            entity.setInvoiceBusinessTypeDetailEntity(invoiceBusinessTypeDetailEntity);
            //拆分和保存发票信息
            splitInvoiceData(entity, invoiceList);


            if (!worderIds.isEmpty()) {
                worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                        .in("worder_id", worderIds)
                        .set("invoice_id", saveInvoiceId)
                        .set("worder_set_status", 2)
                        .set("worder_set_status_value", "等待开票"));
            }
            if (!ids.isEmpty()) {
                worderWaitAccountService.update(new UpdateWrapper<WorderWaitAccountEntity>()
                        .in("id", ids)
                        .set("invoice_id", saveInvoiceId)
                        .set("status", 1)
                        .set("status_value", "等待开票"));

                //更新激励信息
                List<Integer> stimulateList = worderInformationdao.getStimulateIdList(ids);
                if (stimulateList.size() > IntegerEnum.ZERO.getValue()) {
                    worderPmStimulateService.update(new UpdateWrapper<WorderPmStimulateEntity>()
                            .in("id", stimulateList)
                            .set("status", 31)
                            .set("invoice_id", saveInvoiceId));
                }
            }

            //保存关联文件
            List<CompanyInvoiceFileEntity> companyInvoiceFiles = new ArrayList<>();
            List<Integer> fileIds = companyInvoiceForm.getFileIds();
            fileIds.forEach(fileId -> {
                CompanyInvoiceFileEntity companyInvoiceFileEntity = new CompanyInvoiceFileEntity();
                companyInvoiceFileEntity.setFileId(fileId);
                companyInvoiceFileEntity.setInvoiceId(saveInvoiceId);
                companyInvoiceFiles.add(companyInvoiceFileEntity);
            });
            companyInvoiceFileService.saveBatch(companyInvoiceFiles);
        }
    }

    /**
     * 拆分发票信息
     *
     * @param entity
     * @param invoiceList
     */
    private void splitInvoiceData(CompanyInvoiceEntity entity, List<InvoiceData> invoiceList) {
        Integer companyId = entity.getCompanyId();
        Integer priceType = entity.getPriceType();
        log.info("======================开票单：{}==开始拆分发票信息:{}============================", entity.getCompanyInvoiceNo(), entity);
        //查询车企信息
        Map<String, Object> companyMap = worderInformationAccountDao.getCompanyById(companyId);
        // 所有拆分开票信息
        List<CompanyInvoiceEntity> companyInvoiceList = new ArrayList<>();
        //如果不是手动拆分走原有逻辑
        // TODO: 不拆分发票
        companyInvoiceList.add(entity);
//        if (invoiceList == null || invoiceList.isEmpty()) {
//            log.info("======================开票单：{}==原有逻辑拆分发票信息:{}============================", entity.getCompanyInvoiceNo(), entity);
//            autoSplitInviceData(companyInvoiceList, entity, priceType);
//        } else {
//            log.info("======================开票单：{}==手动拆分逻辑拆分发票信息:{}============================", entity.getCompanyInvoiceNo(), entity);
//            manualSplitInviceData(companyInvoiceList, entity, invoiceList);
//        }
//        log.info("==========================开票单：{}==拆分出的发票信息：{}===================================", entity.getCompanyInvoiceNo(), JSONObject.toJSONString(companyInvoiceList));
//        Assert.isTrue(!companyInvoiceList.isEmpty(), "拆分发票信息失败！");
        String invoiceOrderNo = "";
        //开始根据拆分的发票信息进行保存入库
        for (int i = 0; i < companyInvoiceList.size(); i++) {
            invoiceOrderNo = saveInvoiceDetail(companyInvoiceList.get(i), companyMap, (i + 1));
            if (i == 0) {
                entity.setInvoiceOrderNo(invoiceOrderNo);
                this.update(null, new UpdateWrapper<CompanyInvoiceEntity>()
                        .eq("company_invoice_no", entity.getCompanyInvoiceNo())
                        .set("invoice_order_no", invoiceOrderNo));
            }
        }

    }

    /**
     * 保存开票信息
     *
     * @param entity
     * @param companyMap
     * @param detailsQuenc
     */
    public String saveInvoiceDetail(CompanyInvoiceEntity entity, Map<String, Object> companyMap, int detailsQuenc) {
        String companyNo = getStringValue(companyMap, "company_no"); //车企编码
        String companyName = getStringValue(companyMap, "company_name"); //车企名称
        String taxNo = getStringValue(companyMap, "tax_no"); //税号
        String companyBank = getStringValue(companyMap, "company_bank"); //开户行
        String bankAccount = getStringValue(companyMap, "bank_account"); //账号
        String companyMobile = getStringValue(companyMap, "company_mobile"); //电话

        String invoiceOrderNo = "ZLW" + new Date().getTime();
        /*---------------------  金税开票保存信息 -----------------------*/
        //整理发票体DTO
        List<InvoiceDetail> invoiceDetailList = new ArrayList<>();
        InvoiceDetail detail = new InvoiceDetail();
        detail.setInvoiceId(invoiceOrderNo);
        detail.setCompany(balanceProperties.RRS_COMPANY_CODE);
        detail.setDetailsQuenc(detailsQuenc); //明细序号(即费用项目序号)
        detail.setGoodsNo(balanceProperties.GOODS_NO);
        detail.setGoodsName(entity.getGoodsName());
        detail.setModel(entity.getGoodsModel());
        detail.setStoreId(entity.getGoodsUnit());
        detail.setGoodsNum(entity.getGoodsNum());
        detail.setTaxPrice(entity.getTaxPrice());
        detail.setTaxAmount(entity.getInvoiceFee());
        detail.setNoTaxPrice(entity.getNoTaxPrice());
        detail.setNoTaxAmount(entity.getNoTaxFee());
        detail.setTexForehead(entity.getTax());
        detail.setTaxRate(entity.getTaxRate());
//        detail.setTypesNo(balanceProperties.PRODUCT_TAX_CODE);
        if (StringUtils.isNotBlank(entity.getProductTaxCode())) {
            detail.setTypesNo(entity.getProductTaxCode());
        } else {
            detail.setTypesNo(balanceProperties.PRODUCT_TAX_CODE);
        }

        invoiceDetailList.add(detail);
        //整理发票头DTO
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        invoiceHeader.setInvoiceId(invoiceOrderNo);
        invoiceHeader.setCompany(balanceProperties.RRS_COMPANY_CODE);
        invoiceHeader.setCustomerCode(companyNo);
        invoiceHeader.setCustomerHeading(taxNo);
        invoiceHeader.setCustomerName(companyName);
        invoiceHeader.setCustomerMobile(companyMobile);
        String addressDup = getStringValue(companyMap, "address_dup") != null ? getStringValue(companyMap, "address_dup") : "";
        String[] addr = addressDup.split("_");
        invoiceHeader.setCustomerAddress(addr[addr.length - 1]);
        invoiceHeader.setCustomerBank(companyBank);
        invoiceHeader.setCustomerBankNo(bankAccount);
        invoiceHeader.setPayee(balanceProperties.PAYEE);
        invoiceHeader.setReviewer(balanceProperties.REVIEWER);
        invoiceHeader.setInvoiceTypes(balanceProperties.INVOICE_TYPE);
        invoiceHeader.setRemark(entity.getRemark());
        invoiceHeader.setGoodsModel(entity.getGoodsModel());
        invoiceHeader.setGoodsUnit(entity.getGoodsUnit());
        /*----------------------  金税开票保存信息 ----------------------*/

        /*----------------------  信息入库 start ----------------------*/
        //保存推送记录
        BalanceEnterprisesHeaderRecordEntity headerRecordEntity = new BalanceEnterprisesHeaderRecordEntity();
        BeanUtils.copyProperties(invoiceHeader, headerRecordEntity);
        headerRecordEntity.setCompanyInvoiceNo(entity.getCompanyInvoiceNo());
        headerRecordEntity.setBusinessType(entity.getBusinessType());
        //模式是删除状态，真正开票成功后更新为正常状态
        balanceEnterprisesHeaderRecordService.save(headerRecordEntity);
        // 保存发票特殊业务类型详细信息表
        if ("03".equals(headerRecordEntity.getBusinessType())) {
            InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetailEntity = entity.getInvoiceBusinessTypeDetailEntity();
            invoiceBusinessTypeDetailEntity.setEnterprisesHeaderId(headerRecordEntity.getId());
            invoiceBusinessTypeDetailDao.insert(invoiceBusinessTypeDetailEntity);
        }

        List<BalanceEnterprisesDetailRecordEntity> detailRecordEntities = new ArrayList<>();
        for (InvoiceDetail d : invoiceDetailList) {
            BalanceEnterprisesDetailRecordEntity de = new BalanceEnterprisesDetailRecordEntity();
            BeanUtils.copyProperties(d, de);
            de.setHeaderId(headerRecordEntity.getId());
            de.setCompanyInvoiceNo(entity.getCompanyInvoiceNo());
            //模式是删除状态，真正开票成功后更新为正常状态
            detailRecordEntities.add(de);
        }
        balanceEnterprisesDetailRecordService.saveBatch(detailRecordEntities);
        return invoiceOrderNo;
    }

    public void manualSplitInviceData(List<CompanyInvoiceEntity> companyInvoiceLis, CompanyInvoiceEntity entity, List<InvoiceData> invoiceList) {
        invoiceList.forEach(invoiceData -> {
            CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
            //复制属性
            BeanUtils.copyProperties(entity, companyInvoiceEntity);
            if (StringUtils.isNotBlank(invoiceData.getGoodsModel())) {
                companyInvoiceEntity.setGoodsModel(invoiceData.getGoodsModel());
            }
            if (StringUtils.isNotBlank(invoiceData.getGoodsUnit())) {
                companyInvoiceEntity.setGoodsUnit(invoiceData.getGoodsUnit());
            }
            if (StringUtils.isNotBlank(invoiceData.getRemark())) {
                companyInvoiceEntity.setRemark(invoiceData.getRemark());
            }
            if (StringUtils.isNotBlank(invoiceData.getGoodsName())) {
                companyInvoiceEntity.setGoodsName(invoiceData.getGoodsName());
            }
            if (StringUtils.isNotBlank(invoiceData.getProductTaxCode())) {
                companyInvoiceEntity.setProductTaxCode(invoiceData.getProductTaxCode());
            }

            if (invoiceData.getTax() != null) {
                companyInvoiceEntity.setTax(invoiceData.getTax());
            } else {
                companyInvoiceEntity.setTax(invoiceData.getInvoiceFee().subtract(invoiceData.getNoTaxFee()));
            }

            //数量
            if (invoiceData.getGoodsNum() == null
                    || BigDecimal.valueOf(IntegerEnum.ZERO.getValue()).equals(invoiceData.getGoodsNum())) {
                companyInvoiceEntity.setGoodsNum(BigDecimal.ONE);
            } else {
                companyInvoiceEntity.setGoodsNum(invoiceData.getGoodsNum());
            }

            if (invoiceData.getInvoiceFee() != null) {
                companyInvoiceEntity.setInvoiceFee(invoiceData.getInvoiceFee());
            }

            if (invoiceData.getNoTaxFee() != null) {
                companyInvoiceEntity.setNoTaxFee(invoiceData.getNoTaxFee());
            }

            //单价含税金额
            companyInvoiceEntity.setTaxPrice(invoiceData.getInvoiceFee().divide(companyInvoiceEntity.getGoodsNum(), 2, balanceProperties.ROUND_MODE));
            //单价不含税金额
            companyInvoiceEntity.setNoTaxPrice(invoiceData.getNoTaxFee().divide(companyInvoiceEntity.getGoodsNum(), 2, balanceProperties.ROUND_MODE));
            companyInvoiceLis.add(companyInvoiceEntity);
        });
    }

    /**
     * 原有自动拆分发票逻辑
     *
     * @param companyInvoiceList
     */
    public void autoSplitInviceData(List<CompanyInvoiceEntity> companyInvoiceList, CompanyInvoiceEntity entity, Integer priceType) {
        // 含税金额最大开票限制额度
        BigDecimal invoiceLimitPrice = balanceProperties.INVOICE_LIMIT_PRICE;
        // 不含税金额最大开票限制额度
        BigDecimal noTaxInvoiceLimitPrice = balanceProperties.NO_TAX_INVOICE_LIMIT_PRICE;
        // 含税金额没有超过限额
        if (invoiceLimitPrice.compareTo(entity.getInvoiceFee()) == IntegerEnum.ONE.getValue()) {
            log.debug(entity.toString());
            companyInvoiceList.add(entity);
        }
        // 含税金额超过限额, 单价没有超过限额
        else if (invoiceLimitPrice.compareTo(entity.getTaxPrice()) == IntegerEnum.ONE.getValue()) {
            throw new RRException("开票金额超过发票限额！" + entity);
        }
        // 含税金额超过限额, 单价超过限额 拆分开票
        else if (IntegerEnum.ZERO.getValue().equals(priceType)) {
            // 对含税金额价格进行拆分
            BigDecimal invoiceFee = entity.getInvoiceFee();
            do {
                BigDecimal fee = invoiceFee.min(invoiceLimitPrice);
                // 含税金额计算开票信息
                this.calculateInvoiceEntity(fee, entity, companyInvoiceList);
                invoiceFee = invoiceFee.subtract(invoiceLimitPrice);
            } while (invoiceFee.compareTo(BigDecimal.ZERO) == IntegerEnum.ONE.getValue());
        } else if (IntegerEnum.ONE.getValue().equals(priceType)) {
            // 对不含税金额价格进行拆分
            BigDecimal noTaxFee = entity.getNoTaxFee();
            do {
                BigDecimal fee = noTaxFee.min(noTaxInvoiceLimitPrice);
                // 不含税金额计算开票信息
                this.calculateNoTaxFeeInvoiceEntity(fee, entity, companyInvoiceList);
                noTaxFee = noTaxFee.subtract(noTaxInvoiceLimitPrice);
            } while (noTaxFee.compareTo(BigDecimal.ZERO) == IntegerEnum.ONE.getValue());
        } else {
            throw new RRException("company_invoice表中价格类型错误");
        }
    }

    @Override
    public void recountInvoice(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = baseMapper.selectById(invoiceId);
        if (null != companyInvoiceEntity) {
            BigDecimal goodsNum = companyInvoiceEntity.getGoodsNum();
            List<WorderWaitAccountEntity> worderWaitAccountList = worderWaitAccountService.list(new QueryWrapper<WorderWaitAccountEntity>()
                    .eq("invoice_id", invoiceId));

            BigDecimal allNoTaxFee = BigDecimal.ZERO;
            BigDecimal allTax = BigDecimal.ZERO;
            BigDecimal allTaxFee = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(worderWaitAccountList)) {
                for (WorderWaitAccountEntity e : worderWaitAccountList) {
                    allNoTaxFee = allNoTaxFee.add(e.getCompanyBalanceFee());
                    allTax = allTax.add(e.getCompanyBalanceFeeTax());
                    allTaxFee = allTaxFee.add(e.getCompanyBalanceFeeSum());
                }
            }
            BigDecimal taxPrice = allTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
            BigDecimal noTaxPrice = allNoTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);

            companyInvoiceEntity.setNoTaxFee(allNoTaxFee);
            companyInvoiceEntity.setInvoiceFee(allTaxFee);
            companyInvoiceEntity.setTax(allTax);
            companyInvoiceEntity.setTaxPrice(taxPrice);
            companyInvoiceEntity.setNoTaxPrice(noTaxPrice);
            baseMapper.updateById(companyInvoiceEntity);
        }
    }

    /**
     * 提交发票信息
     */
//    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInvoice2(CompanyInvoiceForm companyInvoiceForm) throws IOException {
        Date date = new Date();
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        /* ------------------第一步：校验发票金额是否正确--------------------*/
        //发票总金额计算
        List<WorderInformationAccountEntity> worderList = worderInformationAccountDao.selectBatchIds(companyInvoiceForm.getWorderIds());
        BigDecimal allNoTaxFee = BigDecimal.ZERO;
        BigDecimal allTax = BigDecimal.ZERO;
        BigDecimal allTaxFee = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(worderList)) {
            for (WorderInformationAccountEntity e : worderList) {
                allNoTaxFee = allNoTaxFee.add(e.getCompanyBalanceFee());
                allTax = allTax.add(e.getCompanyBalanceFeeTax());
                allTaxFee = allTaxFee.add(e.getCompanyBalanceFeeSum());
            }
        }
        if (allNoTaxFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RRException("开票工单的总金额必须大于零");
        }
        //根据不含税金额的累加和，计算票面的含税金额、税额，校验
        BigDecimal invoiceTax = allNoTaxFee.multiply(balanceProperties.RRS_COMPANY_TAX_RATE)
                .setScale(2, balanceProperties.ROUND_MODE);
        BigDecimal invoiceFee = allNoTaxFee.add(invoiceTax);
        if (invoiceFee.compareTo(companyInvoiceForm.getInvoiceFee()) != 0) {
            throw new RRException("开票的总金额有误，应为" + invoiceFee + "，实为" + companyInvoiceForm.getInvoiceFee());
        }
        if (invoiceTax.compareTo(companyInvoiceForm.getTax()) != 0) {
            throw new RRException("开票的税额有误，应为" + invoiceTax + "，实为" + companyInvoiceForm.getTax());
        }
        //商品数量校验
        BigDecimal goodsNum = companyInvoiceForm.getGoodsNum();
        if (goodsNum == null || goodsNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new RRException("商品数量不能为空且不能为0");
        }
        //计算单价，校验
        BigDecimal taxPrice = invoiceFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        BigDecimal noTaxPrice = allNoTaxFee.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
//        if(taxPrice.compareTo(companyInvoiceForm.getTaxPrice()) != 0){
//            throw new RRException("开票的单价有误，应为"+taxPrice+"，实为"+companyInvoiceForm.getTaxPrice());
//        }
        /* ------------------第二步：数据入库--------------------*/
        if (companyInvoiceForm.getId() != null) {
            //重新提交的话，校验用户
            Integer invoiceId = companyInvoiceForm.getId();
            CompanyInvoiceEntity entity = this.getById(invoiceId);
            if (userId != entity.getCreator().intValue()) {
                throw new RRException("当前登录用户不是开票信息的创建人，不能修改");
            }
            //校验开票信息的状态
            if (entity.getStatus() > 1) {
                throw new RRException("开票信息已提交，不能修改");
            }
            //更新开票信息
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setTaxPrice(taxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }

            //调整尾差
            //用最后一个工单来调整尾差
            WorderInformationAccountEntity adjectedWorder = worderList.get(worderList.size() - 1);
            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
            if (invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
                Integer adjectedWorderId = adjectedWorder.getWorderId();
                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
                adjectedWorder.setCompanyBalanceFeeTax(newTax);

                entity.setAdjustedWorderId(adjectedWorderId);
                entity.setAdjustedDesc(adjectedDesc);
            }
            //回退原来选的工单的结算状态
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("invoice_id", invoiceId)
                    .set("invoice_id", null)
                    .set("worder_set_status", 1)
                    .set("worder_set_status_value", "车企待结算"));
            //更新新选工单结算状态
            for (WorderInformationAccountEntity e : worderList) {
                e.setInvoiceId(invoiceId);
                e.setWorderSetStatus(2);
                e.setWorderSetStatusValue("等待开票");
            }
            worderInformationAccountService.updateBatchById(worderList);

            //更新开票信息
            this.updateById(entity);
            //处理页面上进行删除的已提交的关联文件
            balanceFileService.deleteFiles(companyInvoiceForm.getDelFileIds());
            companyInvoiceFileService.remove(new QueryWrapper<CompanyInvoiceFileEntity>()
                    .eq("invoice_id", invoiceId)
                    .in("file_id", companyInvoiceForm.getDelFileIds()));
            //保存关联文件
            List<CompanyInvoiceFileEntity> companyInvoiceFiles = new ArrayList<>();
            List<Integer> fileIds = companyInvoiceForm.getFileIds();
            fileIds.forEach(fileId -> {
                CompanyInvoiceFileEntity companyInvoiceFileEntity = new CompanyInvoiceFileEntity();
                companyInvoiceFileEntity.setFileId(fileId);
                companyInvoiceFileEntity.setInvoiceId(invoiceId);
                companyInvoiceFiles.add(companyInvoiceFileEntity);
            });
            companyInvoiceFileService.saveBatch(companyInvoiceFiles);
        } else {
            //首次暂存或提交的话，生成开票单号
            String companyInvoiceNo = serialNoVersionService.businessNoMaker('k');
            //整理开票实体类对象
            CompanyInvoiceEntity entity = new CompanyInvoiceEntity();
            BeanUtils.copyProperties(companyInvoiceForm, entity);
            entity.setCreator(userId);
            entity.setCreatorName(user.getEmployeeName());
            entity.setCreateTime(date);
            entity.setNoTaxPrice(noTaxPrice);
            entity.setTaxPrice(taxPrice);
            entity.setNoTaxFee(allNoTaxFee);
            entity.setTaxRate(balanceProperties.RRS_COMPANY_TAX_RATE.multiply(BigDecimal.valueOf(100L)));
            entity.setCompanyInvoiceNo(companyInvoiceNo);
            if (companyInvoiceForm.getStatus().intValue() == 2) {
                entity.setSubmitTime(date);
            }
            //调整尾差
            //用最后一个工单来调整尾差
            WorderInformationAccountEntity adjectedWorder = worderList.get(worderList.size() - 1);
            BigDecimal oldFeeSum = adjectedWorder.getCompanyBalanceFeeSum();//原来的含税金额
            BigDecimal oldTax = adjectedWorder.getCompanyBalanceFeeTax();//原来的税额
            BigDecimal oldFee = adjectedWorder.getCompanyBalanceFee();//不含税金额
            if (invoiceFee.subtract(allTaxFee).compareTo(BigDecimal.ZERO) != 0 || invoiceTax.subtract(allTax).compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal newFeeSum = oldFeeSum.add(invoiceFee.subtract(allTaxFee));
                BigDecimal newTax = oldTax.add(invoiceTax.subtract(allTax));
                Integer adjectedWorderId = adjectedWorder.getWorderId();
                String adjectedDesc = new StringBuilder().append(oldFee).append(" ").append(oldTax).append(" ").append(oldFeeSum)
                        .append(" >> ").append(oldFee).append(" ").append(newTax).append(" ").append(newFeeSum).toString();
                adjectedWorder.setCompanyBalanceFeeSum(newFeeSum);
                adjectedWorder.setCompanyBalanceFeeTax(newTax);

                entity.setAdjustedWorderId(adjectedWorderId);
                entity.setAdjustedDesc(adjectedDesc);
            }
            //保存开票信息
            this.save(entity);
            Integer invoiceId = entity.getId();
            //更新工单结算状态
            for (WorderInformationAccountEntity e : worderList) {
                e.setInvoiceId(invoiceId);
                e.setWorderSetStatus(2);
                e.setWorderSetStatusValue("等待开票");
            }
            worderInformationAccountService.updateBatchById(worderList);
            //保存关联文件
            List<CompanyInvoiceFileEntity> companyInvoiceFiles = new ArrayList<>();
            List<Integer> fileIds = companyInvoiceForm.getFileIds();
            fileIds.forEach(fileId -> {
                CompanyInvoiceFileEntity companyInvoiceFileEntity = new CompanyInvoiceFileEntity();
                companyInvoiceFileEntity.setFileId(fileId);
                companyInvoiceFileEntity.setInvoiceId(invoiceId);
                companyInvoiceFiles.add(companyInvoiceFileEntity);
            });
            companyInvoiceFileService.saveBatch(companyInvoiceFiles);
        }
    }


    /**
     * 查询发票详情信息
     */
    @Override
    public CompanyInvoiceVO getInvoiceDetail(Integer invoiceId) {
        //查询发票信息
        CompanyInvoiceEntity companyInvoiceEntity = this.baseMapper.selectById(invoiceId);
        //查询发票关联的工单信息
        List<WorderInformationAccountEntity> worderList = worderWaitAccountService.queryWorderCompanyAccountList(invoiceId);
        //发票信息和工单信息填充入视图对象
        CompanyInvoiceVO companyInvoiceVO = new CompanyInvoiceVO();
        BeanUtils.copyProperties(companyInvoiceEntity, companyInvoiceVO);

        companyInvoiceVO.setWorderList(worderList);

        List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecordEntityList = this.balanceEnterprisesDetailRecordService.list(new QueryWrapper<BalanceEnterprisesDetailRecordEntity>().eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()).orderByAsc("details_quenc"));

        List<BalanceEnterprisesDetailVo> balanceEnterprisesDetailVoList = new ArrayList<>();

        for (BalanceEnterprisesDetailRecordEntity enterprisesDetailRecordEntity : balanceEnterprisesDetailRecordEntityList) {
            BalanceEnterprisesDetailVo balanceEnterprisesDetailVo = new BalanceEnterprisesDetailVo();
            BeanUtils.copyProperties(enterprisesDetailRecordEntity, balanceEnterprisesDetailVo);

            if (null != enterprisesDetailRecordEntity.getHeaderId()) {
                BalanceEnterprisesHeaderRecordEntity balanceEnterprisesHeaderRecordEntity = this.balanceEnterprisesHeaderRecordService.getById(enterprisesDetailRecordEntity.getHeaderId());

                balanceEnterprisesDetailVo.setGoodsModel(balanceEnterprisesHeaderRecordEntity.getGoodsModel());
                balanceEnterprisesDetailVo.setGoodsUnit(balanceEnterprisesHeaderRecordEntity.getGoodsUnit());
                balanceEnterprisesDetailVo.setRemark(balanceEnterprisesHeaderRecordEntity.getRemark());
                companyInvoiceVO.setBusinessType(balanceEnterprisesHeaderRecordEntity.getBusinessType());
                // 查询发票头部信息
                InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetail = invoiceBusinessTypeDetailDao.selectOne(new QueryWrapper<InvoiceBusinessTypeDetailEntity>().eq("enterprises_header_id", balanceEnterprisesHeaderRecordEntity.getId()));
                if (invoiceBusinessTypeDetail != null) {
                    companyInvoiceVO.setJzfwfsd(invoiceBusinessTypeDetail.getJzfwfsd());
                    companyInvoiceVO.setFsdxxdz(invoiceBusinessTypeDetail.getFsdxxdz());
                    companyInvoiceVO.setJzxmmc(invoiceBusinessTypeDetail.getJzxmmc());
                    companyInvoiceVO.setKdsbz(invoiceBusinessTypeDetail.getKdsbz());
                    companyInvoiceVO.setTdzzsxmbh(invoiceBusinessTypeDetail.getTdzzsxmbh());
                }
            }

            balanceEnterprisesDetailVoList.add(balanceEnterprisesDetailVo);
        }

        companyInvoiceVO.setBalanceEnterprisesDetailVoList(balanceEnterprisesDetailVoList);


        //关联的文件信息填充入视图对象
        List<CompanyInvoiceFileEntity> relationList = companyInvoiceFileService.list(
                new QueryWrapper<CompanyInvoiceFileEntity>().eq("invoice_id", invoiceId));    //查询发票与文件的关联关系
        if (CollectionUtils.isNotEmpty(relationList)) {
            List<Integer> fileIds = relationList.stream().map(CompanyInvoiceFileEntity::getFileId).collect(Collectors.toList()); //获取文件ID
            List<BalanceFileEntity> fileList = (List<BalanceFileEntity>) balanceFileService.listByIds(fileIds);//查询文件
            companyInvoiceVO.setFileList(fileList);
        } else {
            companyInvoiceVO.setFileList(new ArrayList<>());
        }
        // 发票信息的销售方和购买方信息填充
        invoiceCustomerAndSeller(invoiceId, companyInvoiceVO);
        return companyInvoiceVO;
    }

    /**
     * 撤销开票
     *
     * @param invoiceId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInvoice(Integer invoiceId) {
        //校验，只有暂存的开票才能撤销
        CompanyInvoiceEntity companyInvoiceEntity = this.baseMapper.selectById(invoiceId);
        if (companyInvoiceEntity.getStatus() > 1) {
            throw new RRException("开票信息已提交，不能撤销");
        }

        if (companyInvoiceEntity.getStatus() == -2) {
            RespDeleteOrder respDeleteOrder = deleteMergeOrder(companyInvoiceEntity.getCompanyInvoiceNo(), "10");
            if (!FinanceBusiness.getSUCCESS().equals(respDeleteOrder.getCode())) {
                throw new RRException("开票单撤销失败，失败原因:" + respDeleteOrder.getMsg());
            }
        }

        /* --------------去掉工单与发票的关联关系,工单结算状态回退------------*/
        worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                .eq("invoice_id", invoiceId)
                .set("invoice_id", null)
                .set("worder_set_status", 1)
                .set("worder_set_status_value", "车企待结算"));

        // 提交待审核
        worderWaitAccountService.update(new UpdateWrapper<WorderWaitAccountEntity>()
                .eq("invoice_id", invoiceId)
                .set("status", 0)
                .set("invoice_id", null));
        //把拆分的子发票信息更改为删除状态
        balanceEnterprisesHeaderRecordService.update(new UpdateWrapper<BalanceEnterprisesHeaderRecordEntity>().set("is_delete", 1).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));
        balanceEnterprisesDetailRecordService.update(new UpdateWrapper<BalanceEnterprisesDetailRecordEntity>().set("is_delete", 1).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));

        /* --------------删除发票关联的文件------------*/
        //查询发票与文件的关联关系
        List<CompanyInvoiceFileEntity> relationList = companyInvoiceFileService.list(
                new QueryWrapper<CompanyInvoiceFileEntity>().eq("invoice_id", invoiceId));
        if (CollectionUtils.isNotEmpty(relationList)) {
            //获取文件ID，并删除文件
            List<Integer> fileIds = relationList.stream().map(CompanyInvoiceFileEntity::getFileId).collect(Collectors.toList());
            balanceFileService.deleteFiles(fileIds);
            //删除关联关系
            List<Integer> relationIds = relationList.stream().map(CompanyInvoiceFileEntity::getId).collect(Collectors.toList());
            companyInvoiceFileService.removeByIds(relationIds);
        }
        /* --------------删除开票信息------------*/
        this.baseMapper.deleteById(invoiceId);
    }

    @Override
    public PageUtils pageCompanyInvoice(CompanyInvoiceQuery query) {
        //获取登录信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        int userId = user.getUserId().intValue();
        //查询条件
        query.setUserId(userId);
        Page<CompanyInvoiceEntity> paget = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<CompanyInvoiceVO> page = companyInvoiceDao.queryInvocice(paget, query);
        List<CompanyInvoiceVO> list = new ArrayList<>();
        for (CompanyInvoiceVO entity : page.getRecords()) {
            //1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:开票中 6:记账中 7:完成 -1:第一次审核不通过 -2:第二次审核不通过
            if (null != entity.getStatus() && entity.getStatus() == 1) {
                entity.setInvoiceStatus("暂存");
            } else if (null != entity.getStatus() && entity.getStatus() == 2) {
                entity.setInvoiceStatus("提交");
            } else if (null != entity.getStatus() && entity.getStatus() == 3) {
                entity.setInvoiceStatus("第一次审核通过");
            } else if (null != entity.getStatus() && entity.getStatus() == 4) {
                entity.setInvoiceStatus("第二次审核通过");
            } else if (null != entity.getStatus() && entity.getStatus() == 5) {
                entity.setInvoiceStatus("开票中");
            } else if (null != entity.getStatus() && entity.getStatus() == 6) {
                entity.setInvoiceStatus("记账中");
            } else if (null != entity.getStatus() && entity.getStatus() == 7) {
                entity.setInvoiceStatus("记收暂估完成");
            } else if (null != entity.getStatus() && entity.getStatus() == 8) {
                entity.setInvoiceStatus("财务中台审核中");
            } else if (null != entity.getStatus() && entity.getStatus() == 9) {
                entity.setInvoiceStatus("已传输子订单");
            } else if (null != entity.getStatus() && entity.getStatus() == -1) {
                entity.setInvoiceStatus("第一次审核不通过");
            } else if (null != entity.getStatus() && entity.getStatus() == -2) {
                entity.setInvoiceStatus("第二次审核不通过");
            }
            list.add(entity);
        }
        PageUtils pageUtils = new PageUtils(list, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
        return pageUtils;
    }

    /**
     * 调用税票云开票
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void pushEnterprisesInvoice(CompanyInvoiceEntity entity) {
        Integer companyId = entity.getCompanyId();
        Integer priceType = entity.getPriceType();
        log.info("========================调用税票云开票：{}==========================", entity.getCompanyInvoiceNo());
        log.info(entity.getInvoiceFee() + "--" + entity.getNoTaxFee() + "--" + entity.getTax());
        //查询车企信息
        Map<String, Object> companyMap = worderInformationAccountDao.getCompanyById(companyId);
        // 税率
        //BigDecimal taxRate = entity.getTaxRate().divide(new BigDecimal(100));
        // 含税金额最大开票限制额度
        BigDecimal invoiceLimitPrice = balanceProperties.INVOICE_LIMIT_PRICE;
        // 不含税金额最大开票限制额度
        BigDecimal noTaxInvoiceLimitPrice = balanceProperties.NO_TAX_INVOICE_LIMIT_PRICE;
        // 所有拆分开票信息
        List<CompanyInvoiceEntity> companyInvoiceList = new ArrayList<>();

        /*
         * 查询当前开票单是否关联的有子发票信息，balance_enterprises_detail_record和balance_enterprises_header_record
         * 如果存在说明已经在申请开票时，已经拆分，否则按照原有逻辑拆分
         */
        if (pushGoldenTax(entity)) {
            return;
        }

        // 含税金额没有超过限额
        if (invoiceLimitPrice.compareTo(entity.getInvoiceFee()) == IntegerEnum.ONE.getValue()) {
            log.debug(entity.toString());
            companyInvoiceList.add(entity);
        }
        // 含税金额超过限额, 单价没有超过限额
        else if (invoiceLimitPrice.compareTo(entity.getTaxPrice()) == IntegerEnum.ONE.getValue()) {
            throw new RRException("开票金额超过发票限额！" + entity.toString());
        }
        // 含税金额超过限额, 单价超过限额 拆分开票
        else if (IntegerEnum.ZERO.getValue().equals(priceType)) {
            // 对含税金额价格进行拆分
            BigDecimal invoiceFee = entity.getInvoiceFee();
            do {
                BigDecimal fee = invoiceFee.min(invoiceLimitPrice);
                // 含税金额计算开票信息
                this.calculateInvoiceEntity(fee, entity, companyInvoiceList);
                invoiceFee = invoiceFee.subtract(invoiceLimitPrice);
            } while (invoiceFee.compareTo(BigDecimal.ZERO) == IntegerEnum.ONE.getValue());
        } else if (IntegerEnum.ONE.getValue().equals(priceType)) {
            // 对不含税金额价格进行拆分
            BigDecimal noTaxFee = entity.getNoTaxFee();
            do {
                BigDecimal fee = noTaxFee.min(noTaxInvoiceLimitPrice);
                // 不含税金额计算开票信息
                this.calculateNoTaxFeeInvoiceEntity(fee, entity, companyInvoiceList);
                noTaxFee = noTaxFee.subtract(noTaxInvoiceLimitPrice);
            } while (noTaxFee.compareTo(BigDecimal.ZERO) == IntegerEnum.ONE.getValue());
        } else {
            throw new RRException("company_invoice表中价格类型错误");
        }
        // 推送金税
        this.pushGoldenTax(companyInvoiceList, companyMap);
        // 调整税差
//        if(companyInvoiceList.size() > 1){
//            entity.setInvoiceOrderNo(companyInvoiceList.get(0).getInvoiceOrderNo());
//            this.adjustCompanyInvoiceDifferential(entity);
//        }else{
//            CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
//            companyInvoiceEntity.setStatus(5);
//            companyInvoiceEntity.setInvoiceOrderNo(companyInvoiceList.get(0).getInvoiceOrderNo());
//            companyInvoiceEntity.setId(entity.getId());
//            this.updateById(companyInvoiceEntity);
//        }
//        for (int i = 0;i<companyInvoiceList.size();i++) {
        CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
        companyInvoiceEntity.setStatus(5);
        companyInvoiceEntity.setInvoiceOrderNo(companyInvoiceList.get(0).getInvoiceOrderNo());
        companyInvoiceEntity.setId(entity.getId());
//            companyInvoiceEntity.setId(companyInvoiceList.get(i).getId());
        this.updateById(companyInvoiceEntity);
//        }
    }

    @Override
    public PageUtils companyInvoiceList(CompanyInvoiceQuery query) {
        IPage<CompanyInvoiceVO> page = companyInvoiceDao.selectCompanyInvoicePage(new Page<>(query.getPageNum(), query.getPageSize()), query);
        return new PageUtils(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public R calculatePrice(CalculatePriceQuery query) {
        List<InvoiceData> invoiceList;
        //结果错误描述
        String msg = null;
        // 含税金额最大开票限制额度
        BigDecimal invoiceLimitPrice = balanceProperties.INVOICE_LIMIT_PRICE;
        // 不含税金额最大开票限制额度
        BigDecimal noTaxInvoiceLimitPrice = balanceProperties.NO_TAX_INVOICE_LIMIT_PRICE;
        try {
            //获取含税总金额
            BigDecimal totalInvoiceFee = query.getTotalInvoiceFee();
            //获取不含税总金额
            BigDecimal totalNoTaxFee = query.getTotalNoTaxFee();
            //获取税率
            BigDecimal taxRate = query.getTaxRate() == null ? new BigDecimal(1) : query.getTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            //获取金额类型
            String priceType = query.getPriceType();
            //获取要计算对象在集合的下标
            Integer index = query.getIndex();
            //获取拆分发票信息集合
            invoiceList = query.getInvoiceList();
            //获取输入金额的对象
            if (index != null && invoiceList.size() >= index) {
                InvoiceData invoiceData = invoiceList.get(index);
                //含税金额
                BigDecimal invoiceFee = new BigDecimal(0);
                //不含税金额
                BigDecimal tax = new BigDecimal(0);
                //税额
                BigDecimal noTaxFee = new BigDecimal(0);
                //通过含税金额，计算不含税金额和税额
                if ("0".equals(priceType)) {
                    invoiceFee = invoiceData.getInvoiceFee() != null ? invoiceData.getInvoiceFee() : BigDecimal.ZERO;
                    //校验输入金额大于0
                    if (invoiceFee.compareTo(BigDecimal.ZERO) <= 0) {
                        invoiceData.setCheck(false);
                        invoiceData.setMsg("输入的含税发票金额必须大于零！");
                        msg = "输入的含税发票金额必须大于零！";
                    }

                    //校验含税金额不要超过限额
                    if (invoiceFee.compareTo(invoiceLimitPrice) > 0) {
                        invoiceData.setCheck(false);
                        invoiceData.setMsg("输入的含税发票超过限额！");
                        msg = "输入的含税发票超过限额！";
                    }

                    //计算税额
//                    tax = invoiceFee.multiply(taxRate);
                    tax = invoiceFee.divide(taxRate.add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_EVEN).multiply(taxRate).setScale(2, balanceProperties.ROUND_MODE);
                    //获取不含税金额
                    noTaxFee = invoiceFee.subtract(tax);
                    //通过不含税金额，计算含税金额和税额
                } else if ("1".equals(priceType)) {
                    noTaxFee = invoiceData.getNoTaxFee() != null ? invoiceData.getNoTaxFee() : BigDecimal.ZERO;
                    //校验输入金额大于0
                    if (noTaxFee.compareTo(BigDecimal.ZERO) <= 0) {
                        invoiceData.setCheck(false);
                        invoiceData.setMsg("输入的不含税发票金额必须大于零！");
                        msg = "输入的不含税发票金额必须大于零！";
                    }
                    //校验单张发票不含税金额是否超过限额
                    if (noTaxFee.compareTo(noTaxInvoiceLimitPrice) > 0) {
                        invoiceData.setCheck(false);
                        invoiceData.setMsg("输入的不含税发票超过限额！");
                        msg = "输入的不含税发票超过限额！";
                    }
                    //计算含税金额
                    invoiceFee = noTaxFee.multiply(new BigDecimal(1).add(taxRate));
                    //税额
                    tax = invoiceFee.subtract(noTaxFee);
                }

                invoiceData.setInvoiceFee(invoiceFee);
                invoiceData.setTax(tax);
                invoiceData.setNoTaxFee(noTaxFee);
            }
            //计算每个单张发票内剩余可拆分的金额
            for (int i = 0; i < invoiceList.size(); i++) {
                InvoiceData data = invoiceList.get(i);
                data.setCheck(true);
                data.setMsg(null);
                if (data.getInvoiceFee() != null && data.getInvoiceFee().compareTo(BigDecimal.ZERO) <= 0 || data.getNoTaxFee() != null && data.getNoTaxFee().compareTo(BigDecimal.ZERO) <= 0) {
                    data.setCheck(false);
                    data.setMsg("输入的发票金额必须大于零！");
                }
                //剩余拆分金额 = 总金额 - 输入金额
                totalInvoiceFee = totalInvoiceFee.subtract(data.getInvoiceFee() != null ? data.getInvoiceFee() : BigDecimal.ZERO);
                data.setSurplusInvoiceFee(totalInvoiceFee);
                //剩余拆分金额 = 总金额 - 输入金额
                totalNoTaxFee = totalNoTaxFee.subtract(data.getNoTaxFee() != null ? data.getNoTaxFee() : BigDecimal.ZERO);
                data.setSurplurNoTaxFee(totalNoTaxFee);
                //校验当前输入的含税金额是否大于剩余的可拆分含税总金额
                if ("0".equals(priceType) && totalInvoiceFee.compareTo(BigDecimal.ZERO) < 0) {
                    data.setCheck(false);
                    data.setMsg("发票的含税金额能超过发票剩余含税总金额！");
                    msg = "第" + (i + 1) + "发票的含税金额能超过发票剩余含税总金额！";
                }
                //校验当前输入的不含税金额是否大于剩余的可拆分不含税总金额
                if ("1".equals(priceType) && totalNoTaxFee.compareTo(BigDecimal.ZERO) < 0) {
                    data.setCheck(false);
                    data.setMsg("发票的不含税金额超过发票剩余不含税总金额！");
                    msg = "第" + (i + 1) + "发票的不含税金额超过发票剩余不含税总金额！";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("费用计算异常！！！");
            return R.error(1, "费用计算异常，请重新拆分！！").put("data", null);
        }
        if (msg != null) {
            return R.error(1, msg).put("data", invoiceList);
        } else {
            return R.ok().put("data", invoiceList);
        }
    }

    @Override
    public R autosplitInvoice(CalculatePriceQuery param) {
        List<InvoiceData> invoiceDataList = new ArrayList<>();
        // 含税金额最大开票限制额度
        BigDecimal invoiceLimitPrice = balanceProperties.INVOICE_LIMIT_PRICE;
        // 不含税金额最大开票限制额度
        BigDecimal noTaxInvoiceLimitPrice = balanceProperties.NO_TAX_INVOICE_LIMIT_PRICE;

        //获取单价
        BigDecimal unitPrice;

        if ("0".equals(param.getPriceType())) {
            //自动拆分发票信息，如果含税价超过限额，数量必须是1
            Assert.isFalse(param.getTotalInvoiceFee().compareTo(invoiceLimitPrice) > 0 && param.getGoodsNum() != 1, "含税价超过限额数量必须为1！");
            unitPrice = param.getTotalInvoiceFee().divide(new BigDecimal(param.getGoodsNum()), 2, RoundingMode.HALF_DOWN);
        } else {
            //自动拆分发票信息，如果不含税价超过限额，数量必须是1
            Assert.isFalse(param.getTotalNoTaxFee().compareTo(noTaxInvoiceLimitPrice) > 0 && param.getGoodsNum() != 1, "不含税价超过限额数量必须为1！");
            unitPrice = param.getTotalNoTaxFee().divide(new BigDecimal(param.getGoodsNum()), 2, RoundingMode.HALF_DOWN);
        }
        // 对金额价格进行拆分
        BigDecimal fee = "0".equals(param.getPriceType()) ? param.getTotalInvoiceFee() : param.getTotalNoTaxFee();
        Integer num = 1;
        do {

            if (num >= param.getGoodsNum()) {
                //拆分单张发票
                fee = fee.subtract(splitUnitPrice(invoiceDataList, fee, "0".equals(param.getPriceType()) ? invoiceLimitPrice : noTaxInvoiceLimitPrice, param));
            } else {
                //拆分单张发票
                fee = fee.subtract(splitUnitPrice(invoiceDataList, unitPrice, "0".equals(param.getPriceType()) ? invoiceLimitPrice : noTaxInvoiceLimitPrice, param));
            }

            num += 1;
        } while (fee.compareTo(BigDecimal.ZERO) > 0);
        //遍历invoiceDataList计算剩余总金额
        fee = "0".equals(param.getPriceType()) ? param.getTotalInvoiceFee() : param.getTotalNoTaxFee();

        for (InvoiceData invoiceData : invoiceDataList) {

            if ("0".equals(param.getPriceType())) {
                fee = fee.subtract(invoiceData.getInvoiceFee());
                invoiceData.setSurplusInvoiceFee(fee);
            } else {
                fee = fee.subtract(invoiceData.getNoTaxFee());
                invoiceData.setSurplurNoTaxFee(fee);
            }


        }
        return R.ok().put("invoiceList", invoiceDataList);
    }

    /**
     * 拆分单张发票
     *
     * @param invoiceDataList
     * @param unitPrice
     * @param limit
     * @return
     */
    private BigDecimal splitUnitPrice(List<InvoiceData> invoiceDataList, BigDecimal unitPrice, BigDecimal limit, CalculatePriceQuery param) {
        //定价类型
        String priceType = param.getPriceType();
        //税率
        BigDecimal taxRate = param.getTaxRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //判断单张发票是否超过限额
        if (unitPrice.compareTo(limit) > 0) {
            //单价超过循环每张发票的金额为当前的限额
            BigDecimal unitPriceTemp = unitPrice;
            while (unitPriceTemp.compareTo(limit) >= 0) {
                unitPriceTemp = unitPriceTemp.subtract(splitUnitPrice(invoiceDataList, limit, limit, param));
            }
            //剩余大于0的金额单独拆分一张
            if (unitPriceTemp.compareTo(BigDecimal.ZERO) > 0) {
                splitUnitPrice(invoiceDataList, unitPriceTemp, limit, param);
            }
        } else {
            //单价没有超过限额的直接拆分一张
            InvoiceData invoiceData = new InvoiceData();
            invoiceData.setId(RandomUtil.randomLong(10000, *********));

            if ("0".equals(priceType)) {
                //含税
                invoiceData.setInvoiceFee(unitPrice);
                //不含税 = 含税 / (1 + 税率)
                BigDecimal noTaxFee = unitPrice.divide(BigDecimal.ONE.add(taxRate), 2, balanceProperties.ROUND_MODE);
                //税额 = 含税 X 税率
                invoiceData.setTax(unitPrice.subtract(noTaxFee));
                invoiceData.setNoTaxFee(noTaxFee);
            } else {
                //不含税
                invoiceData.setNoTaxFee(unitPrice);
                //含税 = 不含税 X (1 + 税率)
                BigDecimal invoiceFee = unitPrice.multiply((taxRate.add(BigDecimal.ONE)));
                invoiceData.setInvoiceFee(invoiceFee);
                invoiceData.setTax(invoiceFee.subtract(unitPrice));
            }
            invoiceData.setCheck(true);
            invoiceDataList.add(invoiceData);
        }
        return unitPrice;
    }


    /**
     * 重置索引为index的发票信息
     *
     * @param query
     * @param index
     * @param invoiceData
     */
    private void resetInviceData(CalculatePriceQuery query, Integer index, InvoiceData invoiceData) {
        if (index != 0) {
            InvoiceData invoice = query.getInvoiceList().get(index - 1);
            invoiceData.setSurplusInvoiceFee(invoice.getSurplusInvoiceFee());
            invoiceData.setSurplurNoTaxFee(invoice.getSurplurNoTaxFee());
        } else {
            invoiceData.setSurplusInvoiceFee(query.getTotalInvoiceFee());
            invoiceData.setSurplurNoTaxFee(query.getTotalNoTaxFee());
        }
    }

    public void adjustCompanyInvoiceDifferential(CompanyInvoiceEntity entity) {
        BigDecimal allCompanyBalanceFeeSum = BigDecimal.ZERO;  // 含税总额
        BigDecimal allCompanyBalanceFee = BigDecimal.ZERO;   // 不含税总额
        BigDecimal allCompanyBalanceFeeTax = BigDecimal.ZERO;   // 税额总额
        List<WorderWaitAccountEntity> worderWaitAccountList = worderWaitAccountService.list(new QueryWrapper<WorderWaitAccountEntity>()
                .eq("invoice_id", entity.getId()));
        for (WorderWaitAccountEntity worderWaitAccount : worderWaitAccountList) {
            allCompanyBalanceFeeSum = allCompanyBalanceFeeSum.add(worderWaitAccount.getCompanyBalanceFeeSum());
            allCompanyBalanceFee = allCompanyBalanceFee.add(worderWaitAccount.getCompanyBalanceFee());
            allCompanyBalanceFeeTax = allCompanyBalanceFeeTax.add(worderWaitAccount.getCompanyBalanceFeeTax());
        }
        BigDecimal invoiceTaxFeeSum = entity.getInvoiceTaxFeeSum();
        // 含税总额误差
        BigDecimal taxFeeDifferential = allCompanyBalanceFeeSum.subtract(invoiceTaxFeeSum);
        BigDecimal invoiceNoTaxFeeSum = entity.getInvoiceNoTaxFeeSum();
        // 不含税总额误差
        BigDecimal noTaxFeeDifferential = allCompanyBalanceFee.subtract(invoiceNoTaxFeeSum);
        BigDecimal invoiceTaxSum = entity.getInvoiceTaxSum();
        // 税额总额误差
        BigDecimal taxDifferential = allCompanyBalanceFeeTax.subtract(invoiceTaxSum);

        // 最后一条数据
        WorderWaitAccountEntity worderWaitAccount = worderWaitAccountList.get(worderWaitAccountList.size() - IntegerEnum.ONE.getValue());
        BigDecimal companyBalanceFeeSum = worderWaitAccount.getCompanyBalanceFeeSum();
        BigDecimal companyBalanceFee = worderWaitAccount.getCompanyBalanceFee();
        BigDecimal companyBalanceFeeTax = worderWaitAccount.getCompanyBalanceFeeTax();
        // 减去误差
        companyBalanceFeeSum = companyBalanceFeeSum.subtract(taxFeeDifferential);
        companyBalanceFee = companyBalanceFee.subtract(noTaxFeeDifferential);
        companyBalanceFeeTax = companyBalanceFeeTax.subtract(taxDifferential);
        worderWaitAccount.setCompanyBalanceFeeSum(companyBalanceFeeSum);
        worderWaitAccount.setCompanyBalanceFee(companyBalanceFee);
        worderWaitAccount.setCompanyBalanceFeeTax(companyBalanceFeeTax);
        // 修改
        worderWaitAccountService.updateById(worderWaitAccount);
        Integer worderInvoiceType = worderWaitAccount.getWorderInvoiceType();
        // 工单类型
        if (IntegerEnum.ZERO.getValue().equals(worderInvoiceType)) {
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("worder_id", worderWaitAccount.getWorderId())
                    .set("company_balance_fee", companyBalanceFee)
                    .set("company_balance_fee_sum", companyBalanceFeeSum)
                    .set("company_balance_fee_tax", companyBalanceFeeTax));
        }
        //更新开票单状态
        BigDecimal goodsNum = entity.getGoodsNum();
        BigDecimal taxPrice = invoiceTaxFeeSum.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        BigDecimal noTaxPrice = invoiceNoTaxFeeSum.divide(goodsNum, 2, balanceProperties.ROUND_MODE);

        CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
        companyInvoiceEntity.setStatus(5);
        companyInvoiceEntity.setInvoiceOrderNo(entity.getInvoiceOrderNo());
        companyInvoiceEntity.setId(entity.getId());
        companyInvoiceEntity.setInvoiceFee(invoiceTaxFeeSum);
        companyInvoiceEntity.setNoTaxFee(invoiceNoTaxFeeSum);
        companyInvoiceEntity.setTax(invoiceTaxSum);
        companyInvoiceEntity.setTaxPrice(taxPrice);
        companyInvoiceEntity.setNoTaxPrice(noTaxPrice);
        this.updateById(companyInvoiceEntity);
    }

    // 根据含税金额重新计算开票信息
    public void calculateInvoiceEntity(BigDecimal invoiceFee, CompanyInvoiceEntity entity,
                                       List<CompanyInvoiceEntity> companyInvoiceList) {

        invoiceFee = invoiceFee.setScale(2, balanceProperties.ROUND_MODE);
        // 税率
        BigDecimal taxRate = entity.getTaxRate().divide(new BigDecimal(100));
        // 数量
        BigDecimal goodsNum = entity.getGoodsNum();
        // 含税金额
        entity.setInvoiceFee(invoiceFee);
        // 含税单价 含税金额/数量
        entity.setTaxPrice(invoiceFee.divide(goodsNum));
        // 税额 税额=含税金额/（1+税率）*税率
        BigDecimal tax = invoiceFee.divide(taxRate.add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_EVEN).multiply(taxRate).setScale(2, balanceProperties.ROUND_MODE);
        entity.setTax(tax);
        // 不含税金额=含税金额-税额
        BigDecimal noTaxFee = invoiceFee.subtract(tax);
        entity.setNoTaxFee(noTaxFee);
        // 不含税单价=不含税金额/数量
        entity.setNoTaxPrice(noTaxFee.divide(goodsNum));

        // 复制添加
        CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
        BeanUtils.copyProperties(entity, companyInvoiceEntity);
        companyInvoiceList.add(companyInvoiceEntity);

        entity.setInvoiceTaxFeeSum(entity.getInvoiceTaxFeeSum().add(invoiceFee));
        entity.setInvoiceNoTaxFeeSum(entity.getInvoiceNoTaxFeeSum().add(noTaxFee));
        entity.setInvoiceTaxSum(entity.getInvoiceTaxSum().add(tax));
    }

    // 根据不含税金额重新计算开票信息
    public void calculateNoTaxFeeInvoiceEntity(BigDecimal noTaxFee, CompanyInvoiceEntity entity,
                                               List<CompanyInvoiceEntity> companyInvoiceList) {
        noTaxFee = noTaxFee.setScale(2, balanceProperties.ROUND_MODE);
        // 税率
        BigDecimal taxRate = entity.getTaxRate().divide(new BigDecimal(100));
        // 数量
        BigDecimal goodsNum = entity.getGoodsNum();

        // 税额 = 不含税金额 * 税率
        BigDecimal tax = noTaxFee.multiply(taxRate).setScale(2, balanceProperties.ROUND_MODE);
        // 含税金额 = 不含税金额 + 税额
        BigDecimal invoiceFee = noTaxFee.add(tax);

        // 不含税金额
        entity.setNoTaxFee(noTaxFee);
        // 含税金额
        entity.setInvoiceFee(invoiceFee);
        // 税额
        entity.setTax(tax);
        // 含税单价 含税金额/数量
        entity.setTaxPrice(invoiceFee.divide(goodsNum));
        // 不含税单价=不含税金额/数量
        entity.setNoTaxPrice(noTaxFee.divide(goodsNum));

        // 复制添加
        CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
        BeanUtils.copyProperties(entity, companyInvoiceEntity);
        companyInvoiceList.add(companyInvoiceEntity);

        entity.setInvoiceTaxFeeSum(entity.getInvoiceTaxFeeSum().add(invoiceFee));
        entity.setInvoiceNoTaxFeeSum(entity.getInvoiceNoTaxFeeSum().add(noTaxFee));
        entity.setInvoiceTaxSum(entity.getInvoiceTaxSum().add(tax));
    }

    // 推送金税
    public void pushGoldenTax(List<CompanyInvoiceEntity> entityList, Map<String, Object> companyMap) {
        for (int i = 0, len = entityList.size(); i < len; i++) {
            CompanyInvoiceEntity entity = entityList.get(i);
            pushGoldenTax(entity, entity.getCompanyId(), companyMap, i + 1);
        }

    }


    // 推送金税
    public void pushGoldenTax(CompanyInvoiceEntity entity, Integer companyId, Map<String, Object> companyMap,
                              int detailsQuenc) {
        String companyNo = getStringValue(companyMap, "company_no"); //车企编码
        String companyName = getStringValue(companyMap, "company_name"); //车企名称
        String taxNo = getStringValue(companyMap, "tax_no"); //税号
        String companyBank = getStringValue(companyMap, "company_bank"); //开户行
        String bankAccount = getStringValue(companyMap, "bank_account"); //账号
        String companyMobile = getStringValue(companyMap, "company_mobile"); //电话
        String companyIdStr = companyId.toString();
        if (companyIdStr.length() > 3) {
            companyIdStr = companyIdStr.substring(companyIdStr.length() - 3);
        } else {
            while (companyIdStr.length() < 3) {
                companyIdStr = "0" + companyIdStr;
            }
        }
        Date date = new Date();
        //发票标识为ZLW+6位日期+3位厂商ID+3位序列
//        String invoiceOrderNo = "ZLW"+new SimpleDateFormat("MMdd").format(date)+companyIdStr+getSerialNumber();
        String invoiceOrderNo = "ZLW" + date.getTime();
        /*---------------------  金税开票 start -----------------------*/


        //整理发票体DTO
        List<InvoiceDetail> invoiceDetailList = new ArrayList<>();
        InvoiceDetail detail = new InvoiceDetail();
        detail.setInvoiceId(invoiceOrderNo);
        detail.setCompany(balanceProperties.RRS_COMPANY_CODE);
        detail.setDetailsQuenc(detailsQuenc); //明细序号(即费用项目序号)
        detail.setGoodsNo(balanceProperties.GOODS_NO);
        detail.setGoodsName(entity.getGoodsName());
        detail.setModel(entity.getGoodsModel());
        detail.setStoreId(entity.getGoodsUnit());
        detail.setGoodsNum(entity.getGoodsNum());
        detail.setTaxPrice(entity.getTaxPrice());
        detail.setTaxAmount(entity.getInvoiceFee());
        detail.setNoTaxPrice(entity.getNoTaxPrice());
        detail.setNoTaxAmount(entity.getNoTaxFee());
        detail.setTexForehead(entity.getTax());
        detail.setTaxRate(entity.getTaxRate());
//        detail.setTypesNo(balanceProperties.PRODUCT_TAX_CODE);
        if (StringUtils.isNotBlank(entity.getProductTaxCode())) {
            detail.setTypesNo(entity.getProductTaxCode());
        } else {
            detail.setTypesNo(balanceProperties.PRODUCT_TAX_CODE);
        }
        invoiceDetailList.add(detail);
        //整理发票头DTO
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        invoiceHeader.setInvoiceId(invoiceOrderNo);
        invoiceHeader.setCompany(balanceProperties.RRS_COMPANY_CODE);
        invoiceHeader.setCustomerCode(companyNo);
        invoiceHeader.setCustomerHeading(taxNo);
        invoiceHeader.setCustomerName(companyName);
        invoiceHeader.setCustomerMobile(companyMobile);
        String addressDup = getStringValue(companyMap, "address_dup") != null ? getStringValue(companyMap, "address_dup") : "";
        String[] addr = addressDup.split("_");
        invoiceHeader.setCustomerAddress(addr[addr.length - 1]);
        invoiceHeader.setCustomerBank(companyBank);
        invoiceHeader.setCustomerBankNo(bankAccount);
        invoiceHeader.setPayee(balanceProperties.PAYEE);
        invoiceHeader.setReviewer(balanceProperties.REVIEWER);
        invoiceHeader.setInvoiceTypes(balanceProperties.INVOICE_TYPE);
        invoiceHeader.setRemark(entity.getRemark());
        //调用金税开票接口
        //boolean enterprisesFlag = enterprisesService.invoiceToJinShui(invoiceDetailList, invoiceHeader);
        //if(!enterprisesFlag){
        //    throw new RRException("调用金税开票接口失败");
        //}
        //调用新的开票接口
        boolean enterprisesFlag = invoiceBusiness.setCompanyXwsqZzsHead(invoiceDetailList, invoiceHeader);
        if (!enterprisesFlag) {
            throw new RRException("调用税票云开票接口失败");
        }
        /*----------------------  金税开票 end ----------------------*/


        /*----------------------  信息入库 start ----------------------*/
        //保存推送记录
        BalanceEnterprisesHeaderRecordEntity headerRecordEntity = new BalanceEnterprisesHeaderRecordEntity();
        BeanUtils.copyProperties(invoiceHeader, headerRecordEntity);
        headerRecordEntity.setCompanyInvoiceNo(entity.getCompanyInvoiceNo());
        balanceEnterprisesHeaderRecordService.save(headerRecordEntity);

        List<BalanceEnterprisesDetailRecordEntity> detailRecordEntities = new ArrayList<>();
        for (InvoiceDetail d : invoiceDetailList) {
            BalanceEnterprisesDetailRecordEntity de = new BalanceEnterprisesDetailRecordEntity();
            BeanUtils.copyProperties(d, de);
            de.setHeaderId(headerRecordEntity.getId());
            de.setCompanyInvoiceNo(entity.getCompanyInvoiceNo());
            detailRecordEntities.add(de);
        }
        balanceEnterprisesDetailRecordService.saveBatch(detailRecordEntities);
        if (StringUtils.isBlank(entity.getInvoiceOrderNo())) {
            entity.setInvoiceOrderNo(invoiceOrderNo);
        }
    }


    public void createFinance(String type) {
        JsonObject data = new JsonObject();
        if (type.equals("1")) {
            //车企
            data.addProperty("orderNo", "");//news的车企合并单号、增项单号，激励单号
            data.addProperty("orderType", "10");//单据类型 10-车企合并单号、20-增项单号，30-激励单号
            data.addProperty("brandName", "");//品牌名称
            data.addProperty("brandCode", "");//news的品牌Id
            data.addProperty("customerName", "");//客户名称
            data.addProperty("customer88Code", "");//客户88码
            data.addProperty("customerVcode", "");//暂空
            data.addProperty("amountTaxExcluded", "");//金额未税
            data.addProperty("amountTaxIncluded", "");//金额含税
            data.addProperty("orderCount", "");//工单数量
            data.addProperty("taxAmount", "");//税率
            data.addProperty("taxRate", "");//税额
            data.addProperty("collectionStatus", "10");//回款状态 10-未回款  20已回款完成
            data.addProperty("financialProcess", "20");//财务流程 10-未收款记帐，20-未开票，30-开票中，40-已开票，50-记收完成，60-暂估完成
            data.addProperty("invoicingMethodName", "先开票后付款");//开票方式名称 例：先开票后付款
            data.addProperty("invoiceCode", "");//发票号
            data.addProperty("invoiceType", "");//发票类型 3-电票、0-增值税专票
            data.addProperty("accountCode", "");//sap记收凭证
            data.addProperty("accountYear", "");//会计年度(记账)
            data.addProperty("accountDate", "");//记账日期(记账)
            data.addProperty("customerHeading", "");//税码
            data.addProperty("classificationName", "");//分类名称
            data.addProperty("invoicingInfo", "");//开票信息
            data.addProperty("specialFlag", "");//财务特殊标记
            data.addProperty("companyCode", "");//记账公司代码
            data.addProperty("orderCreateTime", "");//单据创建时间
            data.addProperty("branchCode", "");//工贸
            data.addProperty("branchName", "");//工贸名称
            data.addProperty("supplierName", "");//供应商名称
            data.addProperty("supplier88code", "");//供应商88码
            data.addProperty("supplierVcode", "");//供应商V码
            data.addProperty("costAmountTaxExcluded", "");//暂估成本金额（未税）
            data.addProperty("costAmountTaxIncluded", "");//暂估成本金额（含税）
            data.addProperty("city", "");//城市
            data.addProperty("payForm", "");//支付类型   alipay-支付宝 wxpay-微信 kjt-快捷通 dh-电汇
            data.addProperty("carOrderList", "");//明细单列表 List
            data.addProperty("encourageNo", "");
            data.addProperty("encourageReason", "");//激励原因
            data.addProperty("encourageTime", "");//激励时间
            data.addProperty("encourageType", "");//10-正激励  20-负激励
            data.addProperty("associatedDocNo", "");//关联单号
            data.addProperty("fileList", "");//文件列表
            data.addProperty("fncInvoiceHeadersList", "");//发票主信息
        } else if (type.equals("2")) {
            //增项
            data.addProperty("orderNo", "");//news的车企合并单号、增项单号，激励单号
            data.addProperty("orderType", "20");//单据类型 10-车企合并单号、20-增项单号，30-激励单号
            data.addProperty("brandName", "");//品牌名称
            data.addProperty("brandCode", "");//news的品牌Id
            data.addProperty("customerName", "");//客户名称
            data.addProperty("customer88Code", "");//客户88码
            data.addProperty("customerVcode", "");//暂空
            data.addProperty("amountTaxExcluded", "");//金额未税
            data.addProperty("amountTaxIncluded", "");//金额含税
            data.addProperty("orderCount", "");//工单数量
            data.addProperty("taxAmount", "");//税率
            data.addProperty("taxRate", "");//税额
            data.addProperty("collectionStatus", "10");//回款状态 10-未回款  20已回款完成
            data.addProperty("financialProcess", "null");//财务流程 10-未收款记帐，20-未开票，30-开票中，40-已开票，50-记收完成，60-暂估完成
            data.addProperty("invoicingMethodName", "先开票后付款");//开票方式名称 例：先开票后付款
            data.addProperty("invoiceCode", "");//发票号
            data.addProperty("invoiceType", "");//发票类型 3-电票、0-增值税专票
            data.addProperty("accountCode", "");//sap记收凭证
            data.addProperty("accountYear", "");//会计年度(记账)
            data.addProperty("accountDate", "");//记账日期(记账)
            data.addProperty("customerHeading", "");//税码
            data.addProperty("classificationName", "");//分类名称
            data.addProperty("invoicingInfo", "");//开票信息
            data.addProperty("specialFlag", "");//财务特殊标记
            data.addProperty("companyCode", "");//记账公司代码
            data.addProperty("orderCreateTime", "");//单据创建时间
            data.addProperty("branchCode", "");//工贸
            data.addProperty("branchName", "");//工贸名称
            data.addProperty("supplierName", "");//供应商名称
            data.addProperty("supplier88code", "");//供应商88码
            data.addProperty("supplierVcode", "");//供应商V码
            data.addProperty("costAmountTaxExcluded", "");//暂估成本金额（未税）
            data.addProperty("costAmountTaxIncluded", "");//暂估成本金额（含税）
            data.addProperty("city", "");//城市
            data.addProperty("payForm", "");//支付类型   alipay-支付宝 wxpay-微信 kjt-快捷通 dh-电汇
            data.addProperty("carOrderList", "");//明细单列表 List
            data.addProperty("encourageNo", "");
            data.addProperty("encourageReason", "");//激励原因
            data.addProperty("encourageTime", "");//激励时间
            data.addProperty("encourageType", "");//10-正激励  20-负激励
            data.addProperty("associatedDocNo", "");//关联单号
            data.addProperty("fileList", "");//文件列表
            data.addProperty("fncInvoiceHeadersList", "");//发票主信息
        } else if (type.equals("3")) {
            //激励
            data.addProperty("encourageNo", "");
            data.addProperty("orderType", "30");
            data.addProperty("encourageType", "");//10-正激励  20-负激励
            data.addProperty("amountTaxExcluded", "");//金额未税
            data.addProperty("amountTaxIncluded", "");//金额含税
            data.addProperty("branchCode", "");//工贸
            data.addProperty("branchName", "");//工贸名称
            data.addProperty("companyCode", "");//记账公司代码
            data.addProperty("costAmountTaxExcluded", "");//暂估成本金额（未税）
            data.addProperty("costAmountTaxIncluded", "");//暂估成本金额（含税）
            data.addProperty("delFlag", "");
            data.addProperty("encourageReason", "");//激励原因
            data.addProperty("encourageTime", "");//激励时间
            data.addProperty("supplierName", "");//供应商名称
            data.addProperty("supplier88code", "");//供应商88码
            data.addProperty("supplierVcode", "");//供应商V码
            data.addProperty("associatedDocNo", "");//关联单号
            data.addProperty("payForm", "");//支付类型   alipay-支付宝 wxpay-微信 kjt-快捷通 dh-电汇
        }

        //推送财务中台
        doPushFinance(data);

    }

    /**
     *
     */
    public void doPushFinance(JsonObject data) {
        //获取当前时间戳
        long timestamp = System.currentTimeMillis();
        System.out.println(timestamp);

        try {
            String sign = WXPayUtil.MD5("appId=" + invoiceProperties.APP_ID + "&dateTime=" + timestamp + "&key=" + invoiceProperties.KEY);
            JsonObject headers = new JsonObject();
            headers.addProperty("dateTime", timestamp);
            headers.addProperty("appId", invoiceProperties.APP_ID);
            headers.addProperty("sign", sign);
            JsonObject jsonObject = new JsonObject();
            jsonObject.add("headers", headers);
            jsonObject.add("data", data);
            pushFinance(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 推送财务中台
     */
    @Override
    public String pushFinance(JsonObject params) {

        //创建http请求
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(invoiceProperties.URL1);
            // 设置请求头
            post.setHeader("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            post.setHeader("Accept", MediaType.APPLICATION_JSON_VALUE);
            post.setHeader("appId", invoiceProperties.APP_ID);
            //封住入参
            String param = params.toString();
            log.info("==================调用URL:{}====请求入参：{}=====================", invoiceProperties.URL1, param);
            StringEntity se = new StringEntity(param, StandardCharsets.UTF_8);
            se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE));
            post.setEntity(se);

            // 发起调用票税云开票结果查询接口出参
            CloseableHttpResponse httpResponse = httpClient.execute(post);
            if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                //返回调用结果
                String result = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                log.info("===============调用URL:{}====响应出参：{}=====================", invoiceProperties.URL1, result);
                return result;
            }
        } catch (IOException e) {
            e.printStackTrace();
//            log.error("==================调用URL:{}====调用出现异常！====================", url);
        }
        return null;
    }

    /**
     * 已经拆分过发票信息调用金税开票
     *
     * @param entity
     * @return
     */
    public boolean pushGoldenTax(CompanyInvoiceEntity entity) {
        //查询发票抬头信息
        List<BalanceEnterprisesHeaderRecordEntity> balanceEnterprisesHeaderRecordList = balanceEnterprisesHeaderRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesHeaderRecordEntity>()
                .eq("company_invoice_no", entity.getCompanyInvoiceNo()));
        //如果没有查询到发票抬头信息直接走原有逻辑
        if (balanceEnterprisesHeaderRecordList == null || balanceEnterprisesHeaderRecordList.isEmpty()) {
            return false;
        }
        //遍历发票抬头信息
        balanceEnterprisesHeaderRecordList.forEach(balanceEnterprisesHeaderRecord -> {
            InvoiceHeader invoiceHeader = new InvoiceHeader();
            //复制属性
            BeanUtils.copyProperties(balanceEnterprisesHeaderRecord, invoiceHeader);
            //根据抬头信息查询子发票信息
            List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecords = balanceEnterprisesDetailRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesDetailRecordEntity>()
                    .eq("company_invoice_no", entity.getCompanyInvoiceNo()).eq("invoice_id", balanceEnterprisesHeaderRecord.getInvoiceId()));
            if (balanceEnterprisesDetailRecords != null && !balanceEnterprisesDetailRecords.isEmpty()) {
                List<InvoiceDetail> invoiceDetailList = new ArrayList<>();
                //复制属性
                balanceEnterprisesDetailRecords.forEach(balanceEnterprisesDetailRecord -> {
                    InvoiceDetail invoiceDetail = new InvoiceDetail();
                    BeanUtils.copyProperties(balanceEnterprisesDetailRecord, invoiceDetail);
                    invoiceDetailList.add(invoiceDetail);
                });
                //调用金税票云
                boolean enterprisesFlag = invoiceBusiness.setCompanyXwsqZzsHead(invoiceDetailList, invoiceHeader);
                if (!enterprisesFlag) {
                    throw new RRException("调用金税票云开票接口失败");
                } else {
                    //更新时间
                    balanceEnterprisesHeaderRecord.setPushTime(new Date());
                    balanceEnterprisesHeaderRecord.setUpdateTime(new Date());
                    balanceEnterprisesHeaderRecordService.updateById(balanceEnterprisesHeaderRecord);
                    //更新时间
                    balanceEnterprisesDetailRecords.forEach(balanceEnterprisesDetailRecord -> balanceEnterprisesDetailRecord.setUpdateTime(new Date()));
                    balanceEnterprisesDetailRecordService.updateBatchById(balanceEnterprisesDetailRecords);
                }
            }
        });
        this.update(new UpdateWrapper<CompanyInvoiceEntity>().set("invoice_order_no", balanceEnterprisesHeaderRecordList.get(0).getInvoiceId()).set("status", 5).eq("id", entity.getId()));
        return true;
    }

    /**
     * 上传文件信息入库
     */
    private void addFiles(Integer invoiceId, Integer userId, MultipartFile... files) throws IOException {
        List<BalanceFileEntity> newFiles = balanceFileService.addFiles(userId, 20, files);
        List<CompanyInvoiceFileEntity> newRelationList =
                newFiles.stream()
                        .map(e -> new CompanyInvoiceFileEntity().setFileId(e.getFileId()).setInvoiceId(invoiceId))
                        .collect(Collectors.toList());
        companyInvoiceFileService.saveBatch(newRelationList);
    }

    /**
     * 填充发票信息的销售方和购买方信息
     *
     * @param invoiceId
     * @param companyInvoiceVO
     */
    private void invoiceCustomerAndSeller(Integer invoiceId, CompanyInvoiceVO companyInvoiceVO) {
        //查询车企信息
        List<Map<String, Object>> companyMapList = worderInformationAccountDao.getInvoiceCustomerByInvoiceId(invoiceId);
        if (companyMapList != null && companyMapList.size() > 0) {
            Map<String, Object> companyMap = companyMapList.get(0);
            String companyName = (String) companyMap.get("company_name"); //车企名称
            String taxNo = (String) companyMap.get("tax_no"); //税号
            String companyBank = (String) companyMap.get("company_bank"); //开户行
            String bankAccount = (String) companyMap.get("bank_account"); //账号
            String companyMobile = (String) companyMap.get("company_mobile"); //电话
            String companyAddress = (String) companyMap.get("address_dup"); //电话
            //填充发票信息购买方信息
            companyInvoiceVO.setCustomerName(companyName);
            companyInvoiceVO.setCustomerTaxNo(taxNo);
            companyInvoiceVO.setCustomerAddress(companyAddress);
            companyInvoiceVO.setCustomerMobile(companyMobile);
            companyInvoiceVO.setCustomerBank(companyBank);
            companyInvoiceVO.setCustomerBankNo(bankAccount);
        }

        //填充发票信息销售方信息
        companyInvoiceVO.setSellerName(balanceProperties.RRS_COMPANY_NAME);
        companyInvoiceVO.setSellerTaxNo(balanceProperties.RRS_COMPANY_TAX_NO);
        companyInvoiceVO.setSellerAddress(balanceProperties.RRS_COMPANY_ADDRESS);
        companyInvoiceVO.setSellerMobile(balanceProperties.RRS_COMPANY_MOBILE);
        companyInvoiceVO.setSellerBank(balanceProperties.RRS_COMPANY_BANK);
        companyInvoiceVO.setSellerBankNo(balanceProperties.RRS_COMPANY_BANK_NO);
    }

    /**
     * 填充发票信息的销售方和购买方信息
     *
     * @param companyId
     * @param companyInvoiceVO
     */
    private void invoiceCustomerAndSellers(Integer companyId, CompanyInvoiceVO companyInvoiceVO) {
        //查询车企信息
        Map<String, Object> companyMap = worderInformationAccountDao.getCompanyById(companyId);

        String companyName = (String) companyMap.get("company_name"); //车企名称
        String taxNo = (String) companyMap.get("tax_no"); //税号
        String companyBank = (String) companyMap.get("company_bank"); //开户行
        String bankAccount = (String) companyMap.get("bank_account"); //账号
        String companyMobile = (String) companyMap.get("company_mobile"); //电话
        String companyAddress = (String) companyMap.get("address_dup"); //电话
        //填充发票信息购买方信息
        companyInvoiceVO.setCustomerName(companyName);
        companyInvoiceVO.setCustomerTaxNo(taxNo);
        companyInvoiceVO.setCustomerAddress(companyAddress);
        companyInvoiceVO.setCustomerMobile(companyMobile);
        companyInvoiceVO.setCustomerBank(companyBank);
        companyInvoiceVO.setCustomerBankNo(bankAccount);

        //填充发票信息销售方信息
        companyInvoiceVO.setSellerName(balanceProperties.RRS_COMPANY_NAME);
        companyInvoiceVO.setSellerTaxNo(balanceProperties.RRS_COMPANY_TAX_NO);
        companyInvoiceVO.setSellerAddress(balanceProperties.RRS_COMPANY_ADDRESS);
        companyInvoiceVO.setSellerMobile(balanceProperties.RRS_COMPANY_MOBILE);
        companyInvoiceVO.setSellerBank(balanceProperties.RRS_COMPANY_BANK);
        companyInvoiceVO.setSellerBankNo(balanceProperties.RRS_COMPANY_BANK_NO);
    }

    private String getStringValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        return (value == null) ? "" : value.toString();
    }

    private Integer getIntegerValue(Map worderMap, Object key) {
        Object value = worderMap.get(key);
        if (null == value) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof Boolean) {
            boolean b = ((Boolean) value).booleanValue();
            if (b) {
                return 1;
            } else {
                return 0;
            }
        }
        return Integer.valueOf(value.toString());
    }

    private static Integer serialNo = 0;

    private synchronized static String getSerialNumber() {
        String serialStr = serialNo.toString();
        while (serialStr.length() < 3) {
            serialStr = "0" + serialStr;
        }
        serialNo = (serialNo + 1) % 10000;
        return serialStr;
    }

    @Override
    public List<SysDictionaryDetailEntity> getProductTaxCodeList(Long taxRate) {
        return companyInvoiceDao.queryProductTaxCodeList(BigDecimal.valueOf(taxRate).divide(BigDecimal.valueOf(100L)).toString());
    }

    @Override
    public List<SysDictionaryDetailEntity> getDictionaryGoodsNameList() {
        return companyInvoiceDao.queryDictionaryGoodsNameList();
    }

    /**
     * 发票凭证归档
     */
    @Override
    public void invoiceVoucherArchive() {
        List<InvoiceVoucherDetail> invoiceVoucherDetailList = companyInvoiceDao.queryInvoiceVoucherDetail();

        if (invoiceVoucherDetailList == null || invoiceVoucherDetailList.size() == 0) {
            log.info("====================== 发票凭证归档任务 没有需要归档的开票单 ============================");
            return;
        }

        Map<String, List<InvoiceVoucherDetail>> invoiceVoucherDetailMap = invoiceVoucherDetailList.stream().collect(Collectors.groupingBy(InvoiceVoucherDetail::getCompanyInvoiceNo, Collectors.toList()));

        log.info("====================== 发票凭证归档任务 == 开票单数量:{} ============================", invoiceVoucherDetailMap.size());
        Set<String> keys = invoiceVoucherDetailMap.keySet();
        for (String companyInvoiceNo : keys) {
            log.info("开票单:{} 开始归档", companyInvoiceNo);
            List<InvoiceVoucherDetail> invoiceVoucherDetais = invoiceVoucherDetailMap.get(companyInvoiceNo);
            Boolean isAllArchive = true;
            for (InvoiceVoucherDetail invoiceVoucherDetail : invoiceVoucherDetais) {
                log.info("发票单:{}", invoiceVoucherDetail.getInvoiceId());
                String code = invoiceVoucherRecordService.valiRecord(invoiceVoucherDetail.getInvoiceId());
                if ("0".equals(code)) {
                    // 调用凭证归档接口
                    String respData = invoiceBusiness.setInvoiceXwsqZzsArchive(invoiceVoucherDetail);

                    // 判断归档是否失败
                    if (!voucherReturnDataProccessor(companyInvoiceNo, invoiceVoucherDetail.getInvoiceId(), respData)) {
                        isAllArchive = false;
                    }

                } else if ("2".equals(code)) {
                    isAllArchive = false;
                }
            }

            if (isAllArchive) {
                companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>()
                        .eq("company_invoice_no", companyInvoiceNo)
                        .set("is_archive", IntegerEnum.ONE.getValue()));
            }
            log.info("开票单:{} 归档结束", companyInvoiceNo);
        }

        log.info("====================== 发票凭证归档任务结束 ============================");
    }

    private Boolean voucherReturnDataProccessor(String companyInvoiceNo, String invoiceId, String respData) {

        Boolean isSuccess = false;

        JSONObject respJson = null;
        if (StringUtils.isNotBlank(respData)) {
            respJson = JSONObject.parseObject(respData);
        }

        if (null != respJson && InvoiceBusiness.SUCCESS.equals(respJson.getInteger("code"))) {
            invoiceVoucherRecordService.updateCode(invoiceId, InvoiceBusiness.SUCCESS);
            isSuccess = true;
        } else if (null != respJson && null != respJson.get("code")) {
//            invoiceVoucherRecordService.removeRecord(companyInvoiceNo);
            invoiceVoucherRecordService.updateCode(invoiceId, respJson.getInteger("code"));

            log.info("开票单:{} - 发票单{} 归档失败:{}", companyInvoiceNo, invoiceId, respJson.get("msg"));
        }

        return isSuccess;
    }

    @Override
    public JSONObject companyPushFinanceAuditByInvoiceId(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(invoiceId);
        return companyPushFinanceAudit(companyInvoiceEntity);
    }


    @Override
    public R companyPushFinanceAuditByCompanyInvoiceNo(String companyInvoiceNo) {
        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyInvoiceDao.selectList(new QueryWrapper<CompanyInvoiceEntity>().eq("company_invoice_no", companyInvoiceNo));
        if (companyInvoiceEntityList != null && companyInvoiceEntityList.size() > 0) {
            CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceEntityList.get(0);
            JSONObject resp = companyPushFinanceAudit(companyInvoiceEntity);

            return pushFinanceAuditProcess(resp, companyInvoiceEntity);
        } else {
            return R.error("传输合并单失败:未找到开票单");
        }
    }

    public R pushFinanceAuditProcess(JSONObject response, CompanyInvoiceEntity companyInvoiceEntity) {
        RespInsertOrUpdate respInsertOrUpdate = JSONObject.toJavaObject(response, RespInsertOrUpdate.class);
        // 传输成功
        if (respInsertOrUpdate != null && FinanceBusiness.getSUCCESS().equals(respInsertOrUpdate.getCode())) {
            companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>().set("status", 8).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));
            return R.ok();
        } else if (respInsertOrUpdate != null && StringUtils.isNotBlank(respInsertOrUpdate.getMsg())) {
            return R.error("传输合并单失败:" + respInsertOrUpdate.getMsg());
        } else {
            return R.error("调用传输合并单接口失败");
        }
    }


    @Override
    public RespDeleteOrder testDeleteMergeOrder(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(invoiceId);
        return deleteMergeOrder(companyInvoiceEntity.getCompanyInvoiceNo(), "10");
    }

    @Override
    public RespTransferSubOrder testTransferSubOrder(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(invoiceId);
        return companyPushFinanceTransferSubOrder(companyInvoiceEntity);
    }


    private String getSplitAddress(String addressDup) {
        String[] adds = addressDup.split("_");
        String address = addressDup;

        if (adds != null && adds.length > 0) {
            for (String add : adds) {
                address = add;
            }
        }

        return address;
    }

    private String getTaxPoint(String taxPoint) {
        return StringUtils.isBlank(taxPoint) ? taxPoint : taxPoint.replace("%", "");
    }

    /**
     * 车企推送财务中心申请开票与计收
     */
    @Override
    public JSONObject companyPushFinanceAudit(CompanyInvoiceEntity entity) {
        Integer invoiceId = entity.getId();
        ReqInsertOrUpdate reqInsertOrUpdate = new ReqInsertOrUpdate();

        String orderNo = entity.getCompanyInvoiceNo();

        // 默认全电专票 81
        String invoiceType = "81";
        if (StringUtils.isNotBlank(entity.getInvoiceType())) {
            invoiceType = entity.getInvoiceType();
        }

        final String finalInvoiceType = invoiceType;

        reqInsertOrUpdate.setOrderNo(orderNo);
        reqInsertOrUpdate.setOrderType("10");
        reqInsertOrUpdate.setInvoiceType(finalInvoiceType);
        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(invoiceId);

        reqInsertOrUpdate.setBrandCode(brand.getId().toString());
        reqInsertOrUpdate.setBrandName(brand.getBrandName());

        reqInsertOrUpdate.setAmountTaxExcluded(entity.getNoTaxFee().toString());
        reqInsertOrUpdate.setAmountTaxIncluded(entity.getInvoiceFee().toString());

        List<WorderInformationEntity> worderInformationList = companyInformationService.queryAccountWorderByInvoiceId(invoiceId);

        List<WorderPmStimulateEntity> worderPmStimulateEntityList = companyInvoiceDao.queryAccountWorderPmStimulateByInvoiceId(invoiceId);

        reqInsertOrUpdate.setTaxAmount(entity.getTax().toString());
        reqInsertOrUpdate.setTaxRate(entity.getTaxRate().toString());

        reqInsertOrUpdate.setInvoicingMethodName("先开票后付款");

        // 单据创建时间
        reqInsertOrUpdate.setOrderCreateTime(DateUtils.format(entity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));

        // 工贸
        reqInsertOrUpdate.setBranchCode("");

        // 工贸名称
        reqInsertOrUpdate.setBranchName("");

        // 城市
        reqInsertOrUpdate.setCity("");

        // 支付类型 alipay-支付宝 wxpay-微信 kjt-快捷通 dh-电汇
        reqInsertOrUpdate.setPayForm("dh");

        CompanyInformationEntity company = companyInformationService.getByCompanyId(entity.getCompanyId());
        List<CarOrderInfo> carOrderInfoList = new ArrayList<>();

        // 工单明细
        worderInformationList.forEach(worderInformationEntity -> {

            CarOrderInfo carOrderInfo = new CarOrderInfo();

            carOrderInfo.setOrderNo(orderNo);
            // 单据类型 10-工单 20-激励单
            carOrderInfo.setOrderType("10");
            // 客户名称
            carOrderInfo.setCustomerName(company.getCompanyName());
            // 客户88码
            carOrderInfo.setCustomer88Code(company.getCompanyCode());

            carOrderInfo.setWorkOrderNo(worderInformationEntity.getWorderNo());

            DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByWorderId(worderInformationEntity.getWorderId());

            // 客户V码
//            carOrderInfo.setCustomerVcode(dotInformationEntity.getVCode());

            carOrderInfo.setBranchCode(dotInformationEntity.getBranchCode());

            carOrderInfo.setBranchName(dotInformationEntity.getBranchName());

            carOrderInfo.setOrderCreateTime(DateUtils.format(worderInformationEntity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));

            carOrderInfo.setUserName(worderInformationEntity.getUserName());

            carOrderInfo.setUserTelephone(worderInformationEntity.getUserPhone());

            carOrderInfo.setUserAddress(getSplitAddress(worderInformationEntity.getAddressDup()));
            // 金额（未税）
            carOrderInfo.setAmountTaxExcluded(worderInformationEntity.getCompanyBalanceFee().toString());
            // 金额（含税）
            carOrderInfo.setAmountTaxIncluded(worderInformationEntity.getCompanyBalanceFeeSum().toString());
            // 成本（未税）
            carOrderInfo.setCostAmountTaxExcluded(worderInformationEntity.getDotBalanceFee().toString());
            // 成本（含税）
            carOrderInfo.setCostAmountTaxIncluded(worderInformationEntity.getDotBalanceFeeSum().toString());

            Integer taxRate = companyInvoiceDao.queryCompanyTaxRateByWorderId(worderInformationEntity.getWorderId());
            carOrderInfo.setTaxRate(taxRate + "");

            carOrderInfo.setBrandCode(brand.getId() + "");
            carOrderInfo.setBrandName(brand.getBrandName());

            String worderTypeName = companyInvoiceDao.getWorderTypeNameById(worderInformationEntity.getWorderTypeId());
            carOrderInfo.setServiceType(worderTypeName);

            carOrderInfoList.add(carOrderInfo);
        });

        // 激励明细
        if (worderPmStimulateEntityList != null && worderPmStimulateEntityList.size() > 0) {
            worderPmStimulateEntityList.forEach(worderPmStimulateEntity -> {

                WorderInformationEntity worderInformationEntity = worderInformationdao.selectById(worderPmStimulateEntity.getWorderId());

                CarOrderInfo carOrderInfo = new CarOrderInfo();

                carOrderInfo.setOrderNo(orderNo);
                // 单据类型 10-工单 20-激励单
                carOrderInfo.setOrderType("20");
                // 客户名称
                carOrderInfo.setCustomerName(company.getCompanyName());
                // 客户88码
                carOrderInfo.setCustomer88Code(company.getCompanyCode());

                carOrderInfo.setWorkOrderNo(worderPmStimulateEntity.getId() + "");

                DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByWorderId(worderInformationEntity.getWorderId());

                carOrderInfo.setBranchCode(dotInformationEntity.getBranchCode());

                carOrderInfo.setBranchName(dotInformationEntity.getBranchName());

                carOrderInfo.setOrderCreateTime(DateUtils.format(worderInformationEntity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));

                carOrderInfo.setUserName(worderInformationEntity.getUserName());

                carOrderInfo.setUserTelephone(worderInformationEntity.getUserPhone());

                carOrderInfo.setUserAddress(getSplitAddress(worderInformationEntity.getAddressDup()));
                // 金额（未税）
                carOrderInfo.setAmountTaxExcluded(worderPmStimulateEntity.getBalanceFee().toString());
                // 金额（含税）
                carOrderInfo.setAmountTaxIncluded(worderPmStimulateEntity.getBalanceFeeTax().toString());

                carOrderInfo.setTaxRate(getTaxPoint(worderPmStimulateEntity.getTaxPoint()));

                carOrderInfo.setBrandCode(brand.getId() + "");
                carOrderInfo.setBrandName(brand.getBrandName());

                String worderTypeName = companyInvoiceDao.getWorderTypeNameById(worderInformationEntity.getWorderTypeId());
                carOrderInfo.setServiceType(worderTypeName);

                carOrderInfo.setAssociatedDocNo(worderInformationEntity.getWorderNo());

                String encourageType = "";

                String dicNumber = "";

                // 正激励
                if (worderPmStimulateEntity.getIncentiveType() == 0) {
                    encourageType = "10";
                    dicNumber = "incentive_reason";
                    // 负激励
                } else if (worderPmStimulateEntity.getIncentiveType() == 1) {
                    encourageType = "20";
                    dicNumber = "negative_incentive_reason";
                }

                carOrderInfo.setEncourageType(encourageType);

                String encourageReason = companyInvoiceDao.getWorderPmStimulateReason(dicNumber, worderPmStimulateEntity.getStimulateReason());

                carOrderInfo.setEncourageReason(encourageReason);

                carOrderInfo.setEncourageTime(DateUtils.format(worderPmStimulateEntity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));


                carOrderInfoList.add(carOrderInfo);

            });
        }

        // 工单数量
        reqInsertOrUpdate.setOrderCount(carOrderInfoList.size() + "");
        reqInsertOrUpdate.setCarOrderList(carOrderInfoList);

        List<SysFileEntity> sysFileEntityList = companyInvoiceDao.queryFileByInvoiceId(invoiceId);

        List<FileInfo> fileInfoList = new ArrayList<>();

        sysFileEntityList.forEach(sysFileEntity -> {
            FileInfo fileInfo = new FileInfo();

            fileInfo.setBusinessCode(orderNo);
            fileInfo.setFileName(sysFileEntity.getOldName());
            fileInfo.setFileUrl(sysFileEntity.getPath());

            fileInfoList.add(fileInfo);
        });

        reqInsertOrUpdate.setFileList(fileInfoList);

        //查询发票抬头信息
        List<BalanceEnterprisesHeaderRecordEntity> balanceEnterprisesHeaderRecordList = balanceEnterprisesHeaderRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesHeaderRecordEntity>()
                .eq("company_invoice_no", entity.getCompanyInvoiceNo()));
        //如果没有查询到发票抬头信息直接走原有逻辑
        if (balanceEnterprisesHeaderRecordList == null || balanceEnterprisesHeaderRecordList.isEmpty()) {
            return null;
        }
        List<FncInvoiceHeaders> fncInvoiceHeadersList = new ArrayList<>();
        //遍历发票抬头信息
        balanceEnterprisesHeaderRecordList.forEach(balanceEnterprisesHeaderRecord -> {


            FncInvoiceHeaders fncInvoiceHeaders = new FncInvoiceHeaders();
            fncInvoiceHeaders.setOrderNo(orderNo);
            // NEWS发票ID
            fncInvoiceHeaders.setOtherOrderId(balanceEnterprisesHeaderRecord.getInvoiceId());
            // 3-电票  0-增值税专票
            fncInvoiceHeaders.setInvoiceTypes(finalInvoiceType);
            // 抬头类型 0-个人  1-公司
            fncInvoiceHeaders.setBusinessType("1");
            // 抬头名称
            fncInvoiceHeaders.setCustomerName(balanceEnterprisesHeaderRecord.getCustomerName());
            // 单据公司
            fncInvoiceHeaders.setCompany(balanceEnterprisesHeaderRecord.getCompany());
            // 税号
            fncInvoiceHeaders.setCustomerHeading(balanceEnterprisesHeaderRecord.getCustomerHeading());
            // 电话
            fncInvoiceHeaders.setCustomerMobile(balanceEnterprisesHeaderRecord.getCustomerMobile());
            // 地址
            fncInvoiceHeaders.setCustomerAddress(balanceEnterprisesHeaderRecord.getCustomerAddress());
            // 开户行
            fncInvoiceHeaders.setCustomerBank(balanceEnterprisesHeaderRecord.getCustomerBank());
            // 开户行帐号
            fncInvoiceHeaders.setCustomerBankNo(balanceEnterprisesHeaderRecord.getCustomerBankNo());
            String remark = balanceEnterprisesHeaderRecord.getRemark() == null ? balanceEnterprisesHeaderRecord.getRemark() : balanceEnterprisesHeaderRecord.getRemark().replace("\n", "\r\n");
            // 备注
            fncInvoiceHeaders.setRemark(remark);
            // 单据时间
            fncInvoiceHeaders.setOrderDate(DateUtils.format(entity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));
//            // 发票通知类型
//            fncInvoiceHeaders.setNoticeType("");
//            // 发票通知类型值
            fncInvoiceHeaders.setNoticeValue(entity.getMailbox());
            // 订单类型 Z-蓝票 N-红票
            fncInvoiceHeaders.setOrderType("Z");
            if ("03".equals(balanceEnterprisesHeaderRecord.getBusinessType())) {
                fncInvoiceHeaders.setSpecificInvoiceType(balanceEnterprisesHeaderRecord.getBusinessType());
                InvoiceBusinessTypeDetailEntity invoiceBusinessTypeDetailEntity = invoiceBusinessTypeDetailDao.queryByHeaderId(balanceEnterprisesHeaderRecord.getId());
                if (invoiceBusinessTypeDetailEntity != null) {
                    InvoiceBusinessTypeDetailVo invoiceBusinessTypeDetailVo = DataUtil.copyBean(invoiceBusinessTypeDetailEntity, InvoiceBusinessTypeDetailVo.class);
                    JSONObject businessTypeJson = new JSONObject();
                    businessTypeJson.put("jzfw", com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(invoiceBusinessTypeDetailVo)));
                    fncInvoiceHeaders.setSpecificBusiness(businessTypeJson.toJSONString());
                }
            }


            reqInsertOrUpdate.setCustomerName(balanceEnterprisesHeaderRecord.getCustomerName());
            reqInsertOrUpdate.setCustomer88Code(balanceEnterprisesHeaderRecord.getCustomerCode());

            // 税码
            reqInsertOrUpdate.setCustomerHeading(balanceEnterprisesHeaderRecord.getCustomerHeading());
            ;

            // 财务特殊标记  10-正常流程 20-跳过开票  30-跳过开票及记收
            reqInsertOrUpdate.setSpecialFlag("10");

            // 记帐公司代码
            reqInsertOrUpdate.setCompanyCode(balanceEnterprisesHeaderRecord.getCompany());

            //根据抬头信息查询子发票信息
            List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecords = balanceEnterprisesDetailRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesDetailRecordEntity>()
                    .eq("company_invoice_no", entity.getCompanyInvoiceNo()).eq("invoice_id", balanceEnterprisesHeaderRecord.getInvoiceId()));

            if (balanceEnterprisesDetailRecords != null && !balanceEnterprisesDetailRecords.isEmpty()) {
                List<FncInvoiceDetails> fncInvoiceDetailsList = new ArrayList<>();
                //复制属性
                balanceEnterprisesDetailRecords.forEach(balanceEnterprisesDetailRecord -> {

                    FncInvoiceDetails fncInvoiceDetails = new FncInvoiceDetails();

                    fncInvoiceDetails.setOrderNo(orderNo);
                    // 订单类型 Z-蓝票 N-红票
                    fncInvoiceDetails.setOrderType("Z");

                    fncInvoiceDetails.setCompany(balanceEnterprisesDetailRecord.getCompany());

                    fncInvoiceDetails.setOtherOrderId(balanceEnterprisesDetailRecord.getInvoiceId());
                    // 数量
                    fncInvoiceDetails.setGoodsNum(balanceEnterprisesDetailRecord.getGoodsNum().toString());
                    // 含税单价
                    fncInvoiceDetails.setTaxPrice(balanceEnterprisesDetailRecord.getTaxPrice().toString());
                    // 含税金额
                    fncInvoiceDetails.setTaxAmount(balanceEnterprisesDetailRecord.getTaxAmount().toString());
                    // 不含税单价
                    fncInvoiceDetails.setNoTaxPrice(balanceEnterprisesDetailRecord.getNoTaxPrice().toString());
                    // 不含税金额
                    fncInvoiceDetails.setNoTaxAmount(balanceEnterprisesDetailRecord.getNoTaxAmount().toString());
                    // 税额
                    fncInvoiceDetails.setTexForehead(balanceEnterprisesDetailRecord.getTexForehead().toString());
                    // 税率
                    fncInvoiceDetails.setTaxRate(balanceEnterprisesDetailRecord.getTaxRate().toString());
                    // 税目
                    fncInvoiceDetails.setTypesNo(balanceEnterprisesDetailRecord.getTypesNo());
                    // 商品名称
                    fncInvoiceDetails.setGoodsName(balanceEnterprisesDetailRecord.getGoodsName());

                    String storeId = StringUtils.isBlank(balanceEnterprisesDetailRecord.getStoreId()) ? "EA" : balanceEnterprisesDetailRecord.getStoreId();
                    fncInvoiceDetails.setStoreId(storeId);
//                    fncInvoiceDetails.setModel(StringUtils.isBlank(balanceEnterprisesDetailRecord.getModel()) ? " " : balanceEnterprisesDetailRecord.getModel());

                    fncInvoiceDetailsList.add(fncInvoiceDetails);

//                    // 分类名称
//                    reqInsertOrUpdate.setClassificationName(balanceEnterprisesDetailRecord.getGoodsName());
//                    // 发票抬头
//                    reqInsertOrUpdate.setInvoicingInfo(balanceEnterprisesDetailRecord.getGoodsName());

                });

                fncInvoiceHeaders.setFncInvoiceDetailsList(fncInvoiceDetailsList);
            }

            fncInvoiceHeadersList.add(fncInvoiceHeaders);
            reqInsertOrUpdate.setFncInvoiceHeadersList(fncInvoiceHeadersList);
        });

        // 调用传输合并单、增项、激励工单接口
        return financeBusiness.companyInsertOrUpdate(reqInsertOrUpdate);

    }

    /**
     * 合并单、增项单、激励单作废
     *
     * @param orderNo
     * @param orderType 10-车企合并单号、20-增项单号，30-激励单号
     * @return
     */
    @Override
    public RespDeleteOrder deleteMergeOrder(String orderNo, String orderType) {
        ReqDeleteOrder reqDeleteOrder = new ReqDeleteOrder();

        reqDeleteOrder.setOrderNo(orderNo);
        reqDeleteOrder.setOrderType(orderType);

        JSONObject respJson = financeBusiness.deleteMergeOrder(reqDeleteOrder);

        RespDeleteOrder resp = new RespDeleteOrder();
        if (respJson != null) {
            resp = respJson.toJavaObject(RespDeleteOrder.class);
        } else {
            resp.setCode("500");
            resp.setMsg("调用财务中台合并单作废接口失败");
        }

        return resp;
    }

    @Override
    public void financeSplitBalanceOrder() {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(4);
        statusList.add(5);
        statusList.add(6);
        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyInvoiceDao.selectList(new QueryWrapper<CompanyInvoiceEntity>().in("status", statusList).eq("send_child_worder", 0));

        log.info("财务审核处理中 --- 处理数量:" + companyInvoiceEntityList.size());

        for (CompanyInvoiceEntity companyInvoiceEntity : companyInvoiceEntityList) {
            log.info("财务审核处理中 --- 处理发票:" + companyInvoiceEntity.getCompanyInvoiceNo());
            //更新开票单关联激励结算子工单状态
//            worderChildInformationService.updateChildStimulateWorderStatus(companyInvoiceEntity.getId(),3,"已开票");

            // 传输子订单明细接口
            RespTransferSubOrder respTransferSubOrder = companyPushFinanceTransferSubOrder(companyInvoiceEntity);

            if (FinanceBusiness.getSUCCESS().equals(respTransferSubOrder.getCode())) {
                // 修改状态已传输子订单
                companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>().set("send_child_worder", 1).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));
            }
        }

    }

    /**
     * insertOrUpdate审核结果通知
     *
     * @param requestJson
     * @return
     */
    @Override
    public ResponseBody callbackAuditNotify(JSONObject requestJson) {
        ResponseBody responseBody;
        log.info("==================== 合并单审核结果通知接口入参:{}", requestJson);
        Request<AuditNotify> request = requestJson.toJavaObject(new TypeReference<Request<AuditNotify>>() {
        });

        responseBody = TokenUtil.checkHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 合并单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }

        AuditNotify auditNotify = request.getMsg();

        responseBody = FinanceCallbackUtil.checkAuditNotify(auditNotify);
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 合并单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }


        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyInvoiceDao.selectList(new QueryWrapper<CompanyInvoiceEntity>().eq("company_invoice_no", auditNotify.getInvoiceNo()));

        if (companyInvoiceEntityList == null || companyInvoiceEntityList.size() == 0) {
            return ResponseBody.error("无效合并单号");
        }

        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceEntityList.get(0);
        if (!IntegerEnum.EIGHT.getValue().equals(companyInvoiceEntity.getStatus())) {
            return ResponseBody.error("合并单不在审核中状态");
        }


        WorderAuditRecordEntity record = new WorderAuditRecordEntity();

        record.setApplyNo(auditNotify.getInvoiceNo());
        record.setAuditStatus(2);
        record.setAuditType(1);

        record.setNoPassReason("审核人:" + auditNotify.getCheckUser() + ";" + auditNotify.getCheckReason());
        record.setCreateTime(new Date());

        // 审核通过
        if ("1".equals(auditNotify.getCheckCode())) {
            //根据开票单拆分结算子订单
            worderChildInformationService.auditNotifySplitBalanceOrder(companyInvoiceEntity.getId());
            companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>().set("status", IntegerEnum.FOUR.getValue()).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));
        } else {
            record.setAuditStatus(-2);

            companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>().set("status", -2).eq("company_invoice_no", companyInvoiceEntity.getCompanyInvoiceNo()));
        }


        worderAuditRecordService.saveBatchAuditRecord(record);

        log.info("==================== 合并单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
        return responseBody;
    }

    /**
     * 回款凭证通知接口
     *
     * @param requestJson
     * @return
     */
    @Override
    public ResponseBody callbackReceivableNotice(JSONObject requestJson) {
        ResponseBody responseBody;
        log.info("==================== 回款凭证通知接口入参:{}", requestJson);
        Request<CompanyReceivableNoticeVO> request = requestJson.toJavaObject(new TypeReference<Request<CompanyReceivableNoticeVO>>() {
        });

        responseBody = TokenUtil.checkHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 回款凭证通知接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }
        try {
            //todo 判断该条回款凭证不存在表内 88码+回款凭证号 并且 不是失效状态和删除状态
            CompanyReceivableNoticeEntity entity = companyReceivableNoticeDao.queryByVoucherNoEightCode(request.getMsg().getReceivableVoucherNo(), request.getMsg().getCompanyEightCode());
            if (entity == null) {
                CompanyReceivableNoticeEntity noticeEntity = new CompanyReceivableNoticeEntity();
                noticeEntity.setCompanyEightCode(request.getMsg().getCompanyEightCode());
                noticeEntity.setCompanyName(request.getMsg().getCompanyName());
                noticeEntity.setReceivablePrice(request.getMsg().getReciceablePrice());
                noticeEntity.setReceivableTime(request.getMsg().getReceivableTime());
                noticeEntity.setReceivableVoucherNo(request.getMsg().getReceivableVoucherNo());
                noticeEntity.setStatus(0);
                noticeEntity.setIsDelete(0);
                companyReceivableNoticeDao.insert(noticeEntity);
            } else {
                log.info("==================== 该条回款凭证已记录:{}", request.getMsg().getReceivableVoucherNo() + "====" + request.getMsg().getCompanyEightCode());
                return ResponseBody.error("该条回款凭证已记录");
            }

        } catch (Exception e) {
            log.info("=============== 回款凭证通知接口内部异常" + e);
            e.printStackTrace();
        }

        log.info("==================== 回款凭证通知接口结果:{}", JSONObject.toJSONString(responseBody));
        return responseBody;
    }

    /**
     * 回款凭证通知作废接口
     *
     * @param requestJson
     * @return
     */
    @Override
    public ResponseBody callbackReceivableNoticeInvalid(JSONObject requestJson) {
        ResponseBody responseBody;
        log.info("==================== 回款凭证作废接口入参:{}", requestJson);
        Request<CompanyReceivableNoticeVO> request = requestJson.toJavaObject(new TypeReference<Request<CompanyReceivableNoticeVO>>() {
        });

        responseBody = TokenUtil.checkHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 回款凭证作废接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }
        try {
            CompanyReceivableNoticeEntity entity = companyReceivableNoticeDao.queryByVoucherNoEightCode(request.getMsg().getReceivableVoucherNo(), request.getMsg().getCompanyEightCode());
            if (entity == null) {
                return ResponseBody.error("未查到该条回款凭证号，无法作废");
            } else {
                if (entity.getStatus() == 1) {
                    //状态为核销
                    return ResponseBody.error("该回款凭证号已核销，无法作废");
                }
                //todo 进行回退资金池和撤销
                CompanyReceivableNoticeEntity noticeEntity = new CompanyReceivableNoticeEntity();
//                noticeEntity.setIsDelete(1);
                noticeEntity.setStatus(2);
                noticeEntity.setId(entity.getId());
                companyReceivableNoticeDao.updateById(noticeEntity);
                if (entity.getStatus() != 0) {
                    QueryWrapper<CompanyInformationEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("company_no", entity.getCompanyEightCode());
                    CompanyInformationEntity companyInformationEntity = companyInformationService.getOne(queryWrapper);
                    if (companyInformationEntity != null) {
                        CompanyReceivableCapitalpoolEtity capitalpoolEtity = companyReceivableCapitalpoolDao.selectEntityByCompanyIdAndVoucherNo(companyInformationEntity.getCompanyId().toString(), entity.getReceivableVoucherNo());
                        if (capitalpoolEtity != null) {
                            BigDecimal availableBalance = capitalpoolEtity.getAvailableBalance();
                            BigDecimal balance = capitalpoolEtity.getBalance();
                            availableBalance = availableBalance.subtract(new BigDecimal(entity.getReceivablePrice()));
                            balance = balance.subtract(new BigDecimal(entity.getReceivablePrice()));
                            if (availableBalance.compareTo(balance) == 0) {
                                capitalpoolEtity.setBalance(balance);
                                capitalpoolEtity.setAvailableBalance(availableBalance);
                                companyReceivableCapitalpoolDao.updateById(capitalpoolEtity);
                            } else {
                                throw new IOException("回款审核单进行中，无法撤回");
                            }
                        } else {
                            log.info("未查询到资金池，不进行回退资金池");
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.info("=============== 回款审核单进行中，无法撤回" + e);
            e.printStackTrace();
            return ResponseBody.error("回款审核单进行中，无法撤回");
        } catch (Exception e) {
            log.info("=============== 回款凭证作废接口内部异常" + e);
            e.printStackTrace();
            return ResponseBody.error("回款凭证作废接口异常");
        }

        log.info("==================== 回款凭证作废接口结果:{}", JSONObject.toJSONString(responseBody));
        return responseBody;
    }


    @Override
    public ResponseBody callbackReceivableAudit(JSONObject requestJson) throws Exception {
        ResponseBody responseBody = new ResponseBody();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("==================== 回款审核结果通知接口入参:{}", requestJson);
        try {
            Request<CompanyReceivableAuditVo> request = requestJson.toJavaObject(new TypeReference<Request<CompanyReceivableAuditVo>>() {
            });
            responseBody = TokenUtil.checkHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
            if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
                log.info("==================== 回款审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
                return responseBody;
            }
            CompanyReceivableAuditVo companyReceivableAuditVo = request.getMsg();
            if (StringUtils.isBlank(companyReceivableAuditVo.getReceivableNo())) {
                return ResponseBody.error("新资金单号/回款核销审核单号不能为空");
            }
            if (StringUtils.isBlank(companyReceivableAuditVo.getCheckCode())) {
                return ResponseBody.error("审核结果编码不能为空");
            }
            if (!companyReceivableAuditVo.getCheckCode().equals("1") && !companyReceivableAuditVo.getCheckCode().equals("2")) {
                return ResponseBody.error("审核结果编码不正确");
            }
            if (StringUtils.isBlank(companyReceivableAuditVo.getCheckReason()) && companyReceivableAuditVo.getCheckCode().equals("2")) {
                return ResponseBody.error("审核不通过时需填写审核不通过原因");
            }
            if (StringUtils.isBlank(companyReceivableAuditVo.getCheckUser())) {
                return ResponseBody.error("审核人不能为空");
            }
            if (StringUtils.isBlank(companyReceivableAuditVo.getCheckTime())) {
                return ResponseBody.error("审核时间不能为空");
            }
            if (companyReceivableAuditVo.getCheckCode().equals("1")) {
                CompanyReceivableEntity companyReceivableEntity = companyReceivableDao.selectOne(new QueryWrapper<CompanyReceivableEntity>().eq("company_receivable_no", companyReceivableAuditVo.getReceivableNo()));
                if (companyReceivableEntity == null) {
                    //未查询到回款单
                    return ResponseBody.error("该条单号未查询到相应订单");
                }
                if (companyReceivableEntity.getStatus() != 3) {
                    //未查询到回款单
                    return ResponseBody.error("该条单号不在当前状态，无法进行审核操作");
                }
                //审核通过
                companyReceivableDao.update(null,
                        new UpdateWrapper<CompanyReceivableEntity>()
                                .set("status", 4)
                                .eq("company_receivable_no", companyReceivableAuditVo.getReceivableNo()));
                String[] balanceIdArr = null;
                String balanceIds = companyReceivableEntity.getBalanceIds();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(balanceIds)) {
                    balanceIdArr = balanceIds.split(",");
                }

                CompanyReceivableRecordEtity companyReceivableRecordEtity = companyReceivableRecordService.getOne(
                        new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", companyReceivableEntity.getCompanyReceivableNo()).eq("type", 1));

                CompanyInvoiceEntity companyInvoice = companyInvoiceService.getById(companyReceivableRecordEtity.getInvoiceId());

                if (companyInvoice.getCavState() == 3) {
                    worderAuditRecordService.updateBalanceAdvanceMoneyCompanyInvoiceCavState(companyInvoice, balanceIdArr, companyReceivableEntity);
                    worderAuditRecordService.updateCompanyReceivableCapitalpoolByCompanyReceivableRecord(companyReceivableEntity.getCompanyReceivableNo(), "pushReceivableTask");
                    return ResponseBody.success();
                }

                //修改核销子工单状态
                if (balanceIdArr != null && companyInvoice.getCavState() != 5) {
                    worderAuditRecordService.receivableSucessUpdateWorderInformation(balanceIdArr);
                }
                if (companyReceivableEntity.getType() == 1) {
                    //                companyInvoiceDao.update(null,new UpdateWrapper<CompanyInvoiceEntity>()
                    //                        .set("cav_state", 2)
                    //                        .eq("id",companyReceivableRecordEtity.getInvoiceId()));
                    //新资金审核通过
                    CompanyInvoiceEntity companyInvoiceEntity = getById(companyReceivableRecordEtity.getInvoiceId());
                    companyInvoiceEntity.setCavState(2);
                    companyInvoiceEntity.setType(1);
                    updateById(companyInvoiceEntity);
                    worderChildInformationService.update(null,
                            new UpdateWrapper<WorderChildInformationEntity>()
                                    .set("type", 1)
                                    .in("id", balanceIdArr)
                                    .eq("is_delete", 0));
                } else {

                    WorderAuditRecordEntity worderAuditRecordEntity = new WorderAuditRecordEntity();
                    worderAuditRecordEntity.setApplyNo(companyReceivableAuditVo.getReceivableNo());
                    worderAuditRecordEntity.setBalanceIds(balanceIds);
                    //更新开票单核销状态
                    worderAuditRecordService.updateCompanyInvoiceCavState(worderAuditRecordEntity);
                    //更新资金池金额
                    worderAuditRecordService.updateCompanyReceivableCapitalpoolByCompanyReceivableRecord(companyReceivableEntity.getCompanyReceivableNo(), "admin");
                }
                for (String balanceId : balanceIdArr) {
                    WorderChildInformationEntity worderChildInformationEntity = worderChildInformationService.getById(balanceId);
                    WorderInformationEntity worderInformationEntity = worderInformationService.getOne(
                            new QueryWrapper<WorderInformationEntity>().eq("worder_id", worderChildInformationEntity.getWorderId()));
                    if (checkBydOrderByWorderId(worderChildInformationEntity.getWorderId())) {
                        if (worderInformationService.checkTransferOrder(worderInformationEntity.getWorderNo())) {
                            PushSettleInfo pushSettleInfo = new PushSettleInfo();
                            //
                            pushSettleInfo.setOrderCode(worderInformationEntity.getCompanyOrderNumber());
                            pushSettleInfo.setAmountTax(worderInformationEntity.getCompanyBalanceFeeSum().toString());
                            pushSettleInfo.setAmountNotTax(worderInformationEntity.getCompanyBalanceFee().toString());
                            pushSettleInfo.setPaymentTime(companyReceivableAuditVo.getCheckTime());
                            Result pushSubmitReviewInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().pushSettleInfo(pushSettleInfo).build(), "pushSettle");
                            if (pushSubmitReviewInfoResult.getCode() != 0) {
                                return ResponseBody.error(pushSubmitReviewInfoResult.getMsg());
                            }
                        }
                    }
                }
                WorderAuditRecordEntity worderAuditRecordEntity = new WorderAuditRecordEntity();
                worderAuditRecordEntity.setApplyNo(companyReceivableAuditVo.getReceivableNo());
                worderAuditRecordEntity.setAuditType(2);
                worderAuditRecordEntity.setAuditStatus(2);
                worderAuditRecordEntity.setAuditUserId(1);
                worderAuditRecordEntity.setNoPassReason("财务中台审核通过");
                if (StringUtils.isNotBlank(companyReceivableAuditVo.getCheckTime())) {
                    worderAuditRecordEntity.setCreateTime(format.parse(companyReceivableAuditVo.getCheckTime()));
                }
                worderAuditRecordService.saveBatchAuditRecord(worderAuditRecordEntity);
            } else {
                //审核不通过
                companyReceivableDao.update(null,
                        new UpdateWrapper<CompanyReceivableEntity>()
                                .set("status", -2)
                                .eq("company_receivable_no", companyReceivableAuditVo.getReceivableNo()));
                WorderAuditRecordEntity worderAuditRecordEntity = new WorderAuditRecordEntity();
                worderAuditRecordEntity.setApplyNo(companyReceivableAuditVo.getReceivableNo());
                worderAuditRecordEntity.setAuditType(2);
                worderAuditRecordEntity.setAuditStatus(-2);
                worderAuditRecordEntity.setAuditUserId(1);
                worderAuditRecordEntity.setNoPassReason("财务中台审核不通过,不通过原因为:" + companyReceivableAuditVo.getCheckReason());

                if (StringUtils.isNotBlank(companyReceivableAuditVo.getCheckTime())) {
                    worderAuditRecordEntity.setCreateTime(format.parse(companyReceivableAuditVo.getCheckTime()));
                }
                worderAuditRecordService.saveBatchAuditRecord(worderAuditRecordEntity);
            }
        } catch (Exception e) {
            log.error("回款审核接口异常:", e);
            throw new Exception("回款审核接口异常");
        }


        return responseBody;
    }

    public boolean checkBydOrderByWorderId(Integer worderId) {
        if (worderId == null) {
            return false;
        }
        // 查询是否存在订单属性信息
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderInfoAttrWrapper = Wrappers.lambdaQuery();
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderId);
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttribute, "pushOrder");
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, "worder_source");
        worderInfoAttrWrapper.eq(WorderInformationAttributeEntity::getIsDelete, 0);
        List<WorderInformationAttributeEntity> worderInformationAttributeEntities = worderInformationAttributeDao.selectList(worderInfoAttrWrapper);
        if (worderInformationAttributeEntities.isEmpty()) {
            return false;
        }
        WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeEntities.get(0);
        return "byd".equals(worderInformationAttributeEntity.getAttributeValue());
    }

    /**
     * 信息同步接口
     *
     * @param requestJson
     * @return
     */
    @Override
    public InfoSyncResp callbackInfoSync(JSONObject requestJson) {
        InfoSyncResp responseBody;
        log.info("==================== 信息同步接口入参:{}", requestJson);
        Request<InfoSync> request = requestJson.toJavaObject(new TypeReference<Request<InfoSync>>() {
        });

        responseBody = TokenUtil.infoCheckHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 信息同步接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }
        try {
            if (request.getMsg().getType().equals("1")) {
                //查询品牌
                List<BrandPo> brandPoList = worderInformationService.listBrands();
                responseBody.setBrand(brandPoList);
            } else {
                log.info("type类型不存在,type===" + request.getMsg().getType());
                return InfoSyncResp.error("type类型有误");
            }
        } catch (Exception e) {
            log.info("=============== 信息同步接口内部异常" + e);
            e.printStackTrace();
        }

        log.info("==================== 信息同步接口结果:{}", JSONObject.toJSONString(responseBody));
        return responseBody;
    }

    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     *
     * @param worderId
     * @param type     0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    private String rowId(Integer worderId, Integer type) {
        return rowId("ZLW", worderId, type);
    }

    /**
     * RowId为ZLW+1位类型编号+11位工单ID(或激励ID)
     *
     * @param prefix   前缀
     * @param worderId
     * @param type     0: 网点工单结算 1：网点增项结算  2：激励结算 3:网点增项收款 4:网点增项收入 5:网点增项暂估成本
     * @return
     */
    private String rowId(String prefix, Integer worderId, Integer type) {
        //11位工单ID
        String worderIdStr = worderId.toString();
        if (worderIdStr.length() > 11) {
            worderIdStr = worderIdStr.substring(worderIdStr.length() - 3);
        } else {
            while (worderIdStr.length() < 11) {
                worderIdStr = "0" + worderIdStr;
            }
        }
        //RowId为ZLW+1位类型编号+11位工单ID
        String rowId = prefix + type + worderIdStr;
//        log.info("商户通接口的对账单号：{}", rowId);
        return rowId;
    }

    /**
     * 传输子订单明细
     *
     * @param entity
     * @return
     */
    @Override
    public RespTransferSubOrder companyPushFinanceTransferSubOrder(CompanyInvoiceEntity entity) {

        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(entity.getId());

        CompanyInformationEntity company = companyInformationService.getByCompanyId(entity.getCompanyId());


        //根据开票单ID获取下面的工单
        List<WorderInformationEntity> worderList = worderInformationdao.selectList(new QueryWrapper<WorderInformationEntity>().eq("invoice_id", entity.getId()));

        Set<Integer> worderIds = worderList.stream().map(WorderInformationEntity::getWorderId).collect(Collectors.toSet());

        List<WorderChildInformationEntity> worderChildInformationEntityListAll = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("worder_id", worderIds).eq("balance_source", 0));

        Map<Integer, List<WorderChildInformationEntity>> worderChildInformationMap = worderChildInformationEntityListAll.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getWorderId, Collectors.toList()));

        Integer orderCount = worderChildInformationEntityListAll.size();
        Date date = new Date();
        String ds = DateUtils.format(date, DateUtils.DATE_PATTERN);

        List<ReqTransferSubOrder> reqTransferSubOrderList = new ArrayList<>();

        AtomicInteger i = new AtomicInteger();

        List<FncCollectionInformation> fncCollectionInformationList = new ArrayList<>();

        List<CostInformationEntity> costInformationEntityList = new ArrayList<>();
        List<DotInformationEntity> dotInformationEntityList = companyInvoiceDao.queryDotByWorderIdList(worderIds);
        worderList.forEach(worderInformationEntity -> {

            //   DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByWorderId(worderInformationEntity.getWorderId());
            DotInformationEntity dotInformationEntity = dotInformationEntityList.stream().filter(d -> d.getWorderId().equals(worderInformationEntity.getWorderId())).findFirst().orElse(null);
            if (dotInformationEntity != null) {
                List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationMap.get(worderInformationEntity.getWorderId());

                if (worderChildInformationEntityList != null && worderChildInformationEntityList.size() > 0) {
                    worderChildInformationEntityList.forEach(worderChildInformationEntity -> {

                        String rowId = rowId(worderChildInformationEntity.getId(), 0);

                        String xref3 = entity.getCompanyInvoiceNo() + "-" + (i.incrementAndGet());
                        ReqTransferSubOrder reqTransferSubOrder = new ReqTransferSubOrder();

                        reqTransferSubOrder.setOrderNo(entity.getCompanyInvoiceNo());
                        reqTransferSubOrder.setWorkOrderNo(worderInformationEntity.getWorderNo());
                        reqTransferSubOrder.setSubOrderNo(worderChildInformationEntity.getBalanceNo());
                        reqTransferSubOrder.setBrandCode(brand.getId() + "");
                        reqTransferSubOrder.setBrandName(brand.getBrandName());

                        reqTransferSubOrder.setCustomerName(company.getCompanyName());
                        reqTransferSubOrder.setCustomer88Code(company.getCompanyCode());

                        // 工单金额未税
                        reqTransferSubOrder.setAmountTaxExcluded(worderInformationEntity.getCompanyBalanceFee().toString());
                        // 工单金额（含税）
                        reqTransferSubOrder.setAmountTaxIncluded(worderInformationEntity.getCompanyBalanceFeeSum().toString());

                        // 子订单销售金额（未税）
                        reqTransferSubOrder.setOrderAmountTaxExcluded(worderChildInformationEntity.getCompanyBalanceFee().toString());
                        // 子订单销售金额（含税）
                        reqTransferSubOrder.setOrderAmountTaxIncluded(worderChildInformationEntity.getCompanyBalanceFeeSum().toString());

                        // 子订单销售金额
//                    reqTransferSubOrder.setOrderAmount(worderChildInformationEntity.getDotBalanceFeeSum().toString());
                        // 子订单成本金额（未税）（暂估）
                        reqTransferSubOrder.setDmbtr(worderChildInformationEntity.getDotBalanceFee().toString());
                        // 子订单成本金额（含税）
                        reqTransferSubOrder.setSonTaxIncluded(worderChildInformationEntity.getDotBalanceFeeSum().toString());

                        reqTransferSubOrder.setCompanyCode("ORK0");

                        reqTransferSubOrder.setOrderDate(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));

                        reqTransferSubOrder.setSupplierName(dotInformationEntity.getDotName());

                        reqTransferSubOrder.setSupplierVcode(dotInformationEntity.getVCode());
                        // alipay-支付宝  wxpay-微信   kjt-快捷通  dh-电汇
                        reqTransferSubOrder.setJylx("dh");
                        // 过帐日期
                        reqTransferSubOrder.setBudat(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
                        // 参考代码3
                        reqTransferSubOrder.setXref3(xref3);
                        // 长文本
                        reqTransferSubOrder.setSgtxt("ST_ZLW+" + company.getCompanyCode() + "+" + reqTransferSubOrder.getXref3() + "+" + dotInformationEntity.getVCode());

                        reqTransferSubOrderList.add(reqTransferSubOrder);


                        // 记录成本信息
                        FncCollectionInformation fncInformation = new FncCollectionInformation();
                        fncInformation.setRowId(rowId);
                        fncInformation.setOrderNo(entity.getInvoiceOrderNo());
                        fncInformation.setPayNo(entity.getInvoiceOrderNo());
                        fncInformation.setOrderCount(orderCount);
                        fncInformation.setSource("ZLW");
                        fncInformation.setSourceMode("10");
                        fncInformation.setSourceType("ST_ZLW");
                        fncInformation.setOrderType("Z");
                        fncInformation.setOrderDate(date);
                        fncInformation.setOrderAmount(worderChildInformationEntity.getDotBalanceFeeSum());
                        fncInformation.setPayType("电汇");
                        fncInformation.setYwms("C26");
                        fncInformation.setJylx("6");
                        fncInformation.setBukrs(balanceProperties.RRS_COMPANY_CODE);
                        fncInformation.setBudat(ds);
                        fncInformation.setBktxt("桩联网暂估成本");
                        fncInformation.setWaers("CNY");
                        fncInformation.setKunnr(company.getCompanyNo());
                        fncInformation.setLifnr(dotInformationEntity.getVCode());
                        fncInformation.setIfOnce("N");
                        fncInformation.setName(dotInformationEntity.getDotName());
                        fncInformation.setCity(getDotCity(dotInformationEntity));
                        fncInformation.setDmbtr(worderChildInformationEntity.getDotBalanceFee());
                        fncInformation.setDmbtr1(BigDecimal.ZERO);
                        fncInformation.setDmbtr2(BigDecimal.ZERO);
                        fncInformation.setXref3(xref3);
                        fncInformation.setSgtxt("ST_ZLW+" + company.getCompanyNo() + "+" + xref3 + "+" + dotInformationEntity.getVCode());

                        CostInformationEntity costInformationEntity = new CostInformationEntity();
                        BeanUtils.copyProperties(fncInformation, costInformationEntity);
                        costInformationEntity.setDotId(dotInformationEntity.getDotId());
                        costInformationEntity.setWorderId(worderInformationEntity.getWorderId());
                        costInformationEntity.setBalanceId(worderChildInformationEntity.getId());
                        costInformationEntityList.add(costInformationEntity);
                    });
                }
            }
        });

        //保存成本信息
        costInformationService.saveOrUpdateBathch(costInformationEntityList);

        JSONObject respJson = financeBusiness.transferSubOrder(reqTransferSubOrderList);
        RespTransferSubOrder resp = new RespTransferSubOrder();
        if (respJson != null) {
            resp = respJson.toJavaObject(RespTransferSubOrder.class);
        } else {
            resp.setCode("500");
            resp.setMsg("调用财务中台传输子订单明细接口失败");
        }
        return resp;
    }

    private String getDotCity(DotInformationEntity dotInformationEntity) {
        String dotAreaName = dotInformationEntity.getDotAreaName();
        String dotCityName = dotInformationEntity.getDotCityName();
        String dotCity;
        if ("市辖区".equals(dotCityName)) {
            dotCity = dotAreaName.substring(0, dotAreaName.length() - 1);
        } else {
            dotCity = dotCityName.substring(0, dotCityName.length() - 1);
        }
        return dotCity;
    }

    @Override
    public List<RespDetailsEdit> companyPushFinanceDetailsEditByInvoiceId(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(invoiceId);
        return companyPushFinanceDetailsEdit(companyInvoiceEntity);
    }

    /**
     * 传输子订单明细
     *
     * @param entity
     * @return
     */
    @Override
    public List<RespDetailsEdit> companyPushFinanceDetailsEdit(CompanyInvoiceEntity entity) {

        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(entity.getId());

        CompanyInformationEntity company = companyInformationService.getByCompanyId(entity.getCompanyId());


        //根据开票单ID获取下面的工单
        List<WorderInformationEntity> worderList = worderInformationdao.selectList(new QueryWrapper<WorderInformationEntity>().eq("invoice_id", entity.getId()));

        Set<Integer> worderIds = worderList.stream().map(WorderInformationEntity::getWorderId).collect(Collectors.toSet());

        List<WorderChildInformationEntity> worderChildInformationEntityListAll = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>().in("worder_id", worderIds).eq("balance_source", 0));

        Map<Integer, List<WorderChildInformationEntity>> worderChildInformationMap = worderChildInformationEntityListAll.stream().collect(Collectors.groupingBy(WorderChildInformationEntity::getWorderId, Collectors.toList()));

        List<RespDetailsEdit> respDetailsEditList = new ArrayList<>();

        AtomicInteger i = new AtomicInteger();

        worderList.forEach(worderInformationEntity -> {

            DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByWorderId(worderInformationEntity.getWorderId());
            List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationMap.get(worderInformationEntity.getWorderId());

            if (worderChildInformationEntityList != null && worderChildInformationEntityList.size() > 0) {
                worderChildInformationEntityList.forEach(worderChildInformationEntity -> {

                    ReqDetailsEdit reqDetailsEdit = new ReqDetailsEdit();

                    reqDetailsEdit.setOrderNo(entity.getCompanyInvoiceNo());
                    reqDetailsEdit.setWorkOrderNo(worderInformationEntity.getWorderNo());
                    reqDetailsEdit.setSubOrderNo(worderChildInformationEntity.getBalanceNo());
                    reqDetailsEdit.setBrandCode(brand.getId() + "");
                    reqDetailsEdit.setBrandName(brand.getBrandName());

                    reqDetailsEdit.setCustomerName(company.getCompanyName());
                    reqDetailsEdit.setCustomer88Code(company.getCompanyCode());

                    // 工单金额未税
                    reqDetailsEdit.setAmountTaxExcluded(worderInformationEntity.getCompanyBalanceFee().toString());
                    // 工单金额（含税）
                    reqDetailsEdit.setAmountTaxIncluded(worderInformationEntity.getCompanyBalanceFeeSum().toString());

                    // 子订单销售金额（未税）
                    reqDetailsEdit.setOrderAmountTaxExcluded(worderChildInformationEntity.getCompanyBalanceFee().toString());
                    // 子订单销售金额（含税）
                    reqDetailsEdit.setOrderAmountTaxIncluded(worderChildInformationEntity.getCompanyBalanceFeeSum().toString());

                    // 子订单销售金额
//                    reqDetailsEdit.setOrderAmount(worderChildInformationEntity.getDotBalanceFeeSum().toString());
                    // 子订单成本金额（未税）（暂估）
                    reqDetailsEdit.setDmbtr(worderChildInformationEntity.getDotBalanceFee().toString());
                    // 子订单成本金额（含税）
                    reqDetailsEdit.setSonTaxIncluded(worderChildInformationEntity.getDotBalanceFeeSum().toString());

                    reqDetailsEdit.setCompanyCode(company.getCompanyCode());

                    reqDetailsEdit.setOrderDate(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));

                    reqDetailsEdit.setSupplierName(dotInformationEntity.getDotName());

                    reqDetailsEdit.setSupplierVcode(dotInformationEntity.getVCode());
                    // alipay-支付宝  wxpay-微信   kjt-快捷通  dh-电汇
                    reqDetailsEdit.setJylx("dh");
                    // 过帐日期
                    reqDetailsEdit.setBudat(DateUtils.format(new Date(), DateUtils.DATE_PATTERN));
                    // 参考代码3
                    reqDetailsEdit.setXref3(entity.getCompanyInvoiceNo() + "-" + (i.incrementAndGet()));
                    // 长文本
                    reqDetailsEdit.setSgtxt("ST_ZLW+" + company.getCompanyCode() + "+" + reqDetailsEdit.getXref3() + "+" + dotInformationEntity.getVCode());

                    JSONObject respJson = financeBusiness.detailsEdit(reqDetailsEdit);
                    RespDetailsEdit resp = new RespDetailsEdit();
                    if (respJson != null) {
                        resp = respJson.toJavaObject(RespDetailsEdit.class);
                        resp.setMsg("子订单:" + reqDetailsEdit.getSubOrderNo() + "," + resp.getMsg());
                    } else {
                        resp.setCode("500");
                        resp.setMsg("子订单:" + reqDetailsEdit.getSubOrderNo() + ",调用财务中台传输子订单明细接口失败");
                    }
                    respDetailsEditList.add(resp);
                });
            }

        });


        return respDetailsEditList;
    }

    @Override
    public RespInvoiceEdit companyPushFinanceInvoiceEditByInvoiceId(Integer invoiceId) {
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceDao.selectById(invoiceId);
        return companyPushFinanceInvoiceEdit(companyInvoiceEntity);
    }


    /**
     * 传输子订单明细
     *
     * @param entity
     * @return
     */
    @Override
    public RespInvoiceEdit companyPushFinanceInvoiceEdit(CompanyInvoiceEntity entity) {

        Integer invoiceId = entity.getId();

        String orderNo = entity.getCompanyInvoiceNo();

        String invoiceType = "81";
        if (entity.getInvoiceType() != null) {
            invoiceType = entity.getInvoiceType() + "";
        }
        final String finalInvoiceType = invoiceType;

        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(invoiceId);

        List<WorderInformationEntity> worderInformationList = worderInformationdao.selectList(new QueryWrapper<WorderInformationEntity>().eq("invoice_id", invoiceId));

        //查询发票抬头信息
        List<BalanceEnterprisesHeaderRecordEntity> balanceEnterprisesHeaderRecordList = balanceEnterprisesHeaderRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesHeaderRecordEntity>()
                .eq("company_invoice_no", entity.getCompanyInvoiceNo()));
        //如果没有查询到发票抬头信息直接走原有逻辑
        if (balanceEnterprisesHeaderRecordList == null || balanceEnterprisesHeaderRecordList.isEmpty()) {
            return null;
        }
        List<ReqInvoiceEdit> reqInvoiceEditList = new ArrayList<>();
        //遍历发票抬头信息
        balanceEnterprisesHeaderRecordList.forEach(balanceEnterprisesHeaderRecord -> {

            ReqInvoiceEdit reqInvoiceEdit = new ReqInvoiceEdit();
            reqInvoiceEdit.setOrderNo(orderNo);
            // NEWS发票ID
            reqInvoiceEdit.setOrderId(balanceEnterprisesHeaderRecord.getId().toString());
            // 3-电票  0-增值税专票
            reqInvoiceEdit.setInvoiceTypes(finalInvoiceType);
            // 抬头类型 0-个人  1-公司
            reqInvoiceEdit.setBusinessType("1");
            // 抬头名称
            reqInvoiceEdit.setCustomerName(balanceEnterprisesHeaderRecord.getCustomerName());
            // 单据公司
            reqInvoiceEdit.setCompany(balanceEnterprisesHeaderRecord.getCompany());
            // 税号
            reqInvoiceEdit.setCustomerHeading(balanceEnterprisesHeaderRecord.getCustomerHeading());
            // 电话
            reqInvoiceEdit.setCustomerMobile(balanceEnterprisesHeaderRecord.getCustomerMobile());
            // 地址
            reqInvoiceEdit.setCustomerAddress(balanceEnterprisesHeaderRecord.getCustomerAddress());
            // 开户行
            reqInvoiceEdit.setCustomerBank(balanceEnterprisesHeaderRecord.getCustomerBank());
            // 开户行帐号
            reqInvoiceEdit.setCustomerBankNo(balanceEnterprisesHeaderRecord.getCustomerBankNo());
            // 备注
            reqInvoiceEdit.setRemark(balanceEnterprisesHeaderRecord.getRemark());
            // 单据时间
            reqInvoiceEdit.setOrderDate(DateUtils.format(entity.getCreateTime(), DateUtils.DATE_TIME_PATTERN));
            // 发票通知类型
            reqInvoiceEdit.setNoticeType("");
            // 发票通知类型值
            reqInvoiceEdit.setNoticeValue("");
            // 订单类型 Z-正向 N-逆向 Y-换票的新发票类型
            reqInvoiceEdit.setOrderType("Z");

            //根据抬头信息查询子发票信息
            List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecords = balanceEnterprisesDetailRecordService.getBaseMapper().selectList(new QueryWrapper<BalanceEnterprisesDetailRecordEntity>()
                    .eq("company_invoice_no", entity.getCompanyInvoiceNo()).eq("invoice_id", balanceEnterprisesHeaderRecord.getInvoiceId()));

            if (balanceEnterprisesDetailRecords != null && !balanceEnterprisesDetailRecords.isEmpty()) {
                List<InvoiceDetails> invoiceDetailsList = new ArrayList<>();
                //复制属性
                balanceEnterprisesDetailRecords.forEach(balanceEnterprisesDetailRecord -> {

                    InvoiceDetails invoiceDetails = new InvoiceDetails();

                    invoiceDetails.setOrderNo(orderNo);

                    invoiceDetails.setCompany(balanceEnterprisesDetailRecord.getCompany());

                    invoiceDetails.setOrderId(balanceEnterprisesDetailRecord.getId().toString());
                    // 数量
                    invoiceDetails.setGoodsNum(balanceEnterprisesDetailRecord.getGoodsNum().toString());
                    // 含税单价
                    invoiceDetails.setTaxPrice(balanceEnterprisesDetailRecord.getTaxPrice().toString());
                    // 含税金额
                    invoiceDetails.setTaxAmount(balanceEnterprisesDetailRecord.getTaxAmount().toString());
                    // 不含税单价
                    invoiceDetails.setNoTaxPrice(balanceEnterprisesDetailRecord.getNoTaxPrice().toString());
                    // 不含税金额
                    invoiceDetails.setNoTaxAmount(balanceEnterprisesDetailRecord.getNoTaxAmount().toString());
                    // 税额
                    invoiceDetails.setTexForehead(balanceEnterprisesDetailRecord.getTexForehead().toString());
                    // 税率
                    invoiceDetails.setTaxRate(balanceEnterprisesDetailRecord.getTaxRate().toString());
                    // 税目
                    invoiceDetails.setTypesNo(balanceEnterprisesDetailRecord.getTypesNo());
                    // 商品名称
                    invoiceDetails.setGoodsName(balanceEnterprisesDetailRecord.getGoodsName());

//                    invoiceDetails.setModel(StringUtils.isBlank(balanceEnterprisesDetailRecord.getModel()) ? " " : balanceEnterprisesDetailRecord.getModel());

                    invoiceDetailsList.add(invoiceDetails);

                });

                reqInvoiceEdit.setInvoiceDetails(invoiceDetailsList);
            }

            reqInvoiceEditList.add(reqInvoiceEdit);
        });

        JSONObject respJson = financeBusiness.invoiceEdit(reqInvoiceEditList);
        RespInvoiceEdit resp = new RespInvoiceEdit();
        if (respJson != null) {
            resp = respJson.toJavaObject(RespInvoiceEdit.class);
        } else {
            resp.setCode("500");
            resp.setMsg("调用财务中台更新合并单发票明细接口失败");
        }
        return resp;
    }

//
//    private synchronized static String getSerialNumber(String no){
//        String serialStr = serialNo.toString();
//        while(serialStr.length()<3){
//            serialStr = "0"+serialStr;
//        }
//        serialNo = (serialNo + 1) % 10000;
//        return serialStr;
//    }


    /**
     * 根据回款单号查询开票单号
     */
    @Override
    public CompanyInvoiceEntity queryByReceivableNo(String companyReceivableNo) {
        return companyInvoiceDao.queryByReceivableNo(companyReceivableNo);
    }

    /**
     * 根据发票Id查询文件Url
     *
     * @param invoiceId
     * @return
     */
    @Override
    public SysFileEntity getFileUrlByInvoiceId(Integer invoiceId) {
        return companyInvoiceDao.getFileUrlByInvoiceId(invoiceId);
    }

    @Override
    public SysFileEntity getFileUrlByReceivableId(Integer receivableId) {
        return companyInvoiceDao.getFileUrlByReceivableId(receivableId);
    }

    @Override
    public R invoiceTypeDis() {
        // 查询数据字典
        List<SysDictionaryDetailEntity> invoiceTypes = sysDictionaryDetailService.selectDetailByNumber("invoice_type");
        List<SysDictionaryDetailEntity> businessTypes = sysDictionaryDetailService.selectDetailByNumber("business_type");
        return R.ok().put("invoiceTypes", invoiceTypes).put("businessTypes", businessTypes);
    }

    /**
     * insertOrUpdate审核结果通知
     *
     * @param requestJson
     * @return
     */
    @Override
    public ResponseBody callbackSettlementAuditNotify(JSONObject requestJson) {
        ResponseBody responseBody;
        log.info("==================== 结算审核单审核结果通知接口入参:{}", requestJson);
        Request<SettlementAuditNotify> request = requestJson.toJavaObject(new TypeReference<Request<SettlementAuditNotify>>() {
        });

        responseBody = TokenUtil.checkHeader(request.getHeader(), financeBusiness.getCallBackAppSecret());
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 结算审核单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }

        SettlementAuditNotify settlementAuditNotify = request.getMsg();

        responseBody = FinanceCallbackUtil.checkSettlementAuditNotify(settlementAuditNotify);
        if (!FinanceCallbackUtil.SUCCESS.equals(responseBody.getRespCode())) {
            log.info("==================== 结算审核单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
            return responseBody;
        }


        List<BalancePublishEntity> balancePublishEntityList = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", settlementAuditNotify.getBillNo()));

        if (balancePublishEntityList == null || balancePublishEntityList.size() == 0) {
            return ResponseBody.error("无效结算申请单号");
        }

        BalancePublishEntity balancePublishEntity = balancePublishEntityList.get(0);
        if (balancePublishEntity.getStatus() != 14) {
            return ResponseBody.error("结算申请单不在审核中状态");
        }

        WorderAuditRecordEntity record = new WorderAuditRecordEntity();
        record.setAuditStatus(1);
        record.setBatchNo(balancePublishEntity.getBatchNo());
        record.setAuditType(3);
        record.setPublishType(balancePublishEntity.getPublishType());
        record.setNoPassReason(settlementAuditNotify.getCheckReason());
        record.setNoPassReason("审核人:" + settlementAuditNotify.getCheckUser() + ";" + settlementAuditNotify.getCheckReason());
        record.setCreateTime(new Date());

        String[] worderNoArr = null;

        if (balancePublishEntity.getPublishType() == 34) {

            //获取结算子工单ID
            List<Integer> worderIdList = new ArrayList<>();
            for (BalancePublishEntity balancePublish : balancePublishEntityList) {
                String worderIds = balancePublish.getIncreIds();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(worderIds)) {
                    String[] worderIdArr = worderIds.split(",");
                    for (String worderId : worderIdArr) {
                        worderIdList.add(Integer.parseInt(worderId));
                    }
                }
            }
            if (worderIdList != null && worderIdList.size() > 0) {
                List<WorderInformationEntity> worderInformationEntityList = worderInformationdao.selectList(new QueryWrapper<WorderInformationEntity>().in("worder_id", worderIdList));
                List<String> worderNoList = worderInformationEntityList.stream().map(WorderInformationEntity::getWorderNo).collect(Collectors.toList());
                worderNoArr = new String[worderNoList.size()];
                worderNoList.toArray(worderNoArr);
            }
        }

        //获取结算子工单ID
        List<String> worderChildIds = new ArrayList<>();
        for (BalancePublishEntity balancePublish : balancePublishEntityList) {
            String balanceIds = balancePublish.getWorderIds();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(balanceIds)) {
                String[] balanceIdArr = balanceIds.split(",");
                for (String balanceId : balanceIdArr) {
                    worderChildIds.add(balanceId);
                }
            }
        }
        String[] worderChildIdArr = new String[worderChildIds.size()];
        worderChildIds.toArray(worderChildIdArr);

        // 审核通过
        if ("1".equals(settlementAuditNotify.getCheckCode())) {
            //更新工单结算状态和增项结算状态
            worderAuditRecordService.updatePublishWorderStatus(record, worderChildIdArr, worderNoArr);
        } else {
            Map params = new HashMap();
            params.put("batchNo", balancePublishEntity.getBatchNo());
            params.put("status", -1);
            worderAuditRecordService.updateBatchBalancePublish(params);
        }

        log.info("==================== 结算申请单审核结果通知接口结果:{}", JSONObject.toJSONString(responseBody));
        return responseBody;
    }
}
