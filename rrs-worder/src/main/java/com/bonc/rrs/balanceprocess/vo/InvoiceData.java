package com.bonc.rrs.balanceprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Description: 单张发票拆分详情
 * @Author: liujunpeng
 * @Date: 2021/12/10 14:05
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class InvoiceData {

    @ApiModelProperty(value = "唯一标识")
    private Long id;

    @ApiModelProperty(value = "含税费用")
    private BigDecimal invoiceFee;

    @ApiModelProperty(value = "不含税费用")
    private BigDecimal noTaxFee;

    @ApiModelProperty(value = "税额")
    private BigDecimal tax;

    @ApiModelProperty(value = "数量")
    private BigDecimal goodsNum;

    @ApiModelProperty(value = "规格型号")
    private String goodsModel;

    @ApiModelProperty(value = "单位")
    private String goodsUnit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "剩余含税费用")
    private BigDecimal surplusInvoiceFee;

    @ApiModelProperty(value = "剩余不含税费用")
    private BigDecimal surplurNoTaxFee;

    private Boolean check;

    private String msg;

    @ApiModelProperty(value = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "税目")
    private String productTaxCode;
}
