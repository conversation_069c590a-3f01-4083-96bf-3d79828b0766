package com.bonc.rrs.balanceprocess.controller;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.bonc.rrs.balanceprocess.entity.BalancePublishEntity;
import com.bonc.rrs.balanceprocess.listener.PublishCompanyListener;
import com.bonc.rrs.balanceprocess.listener.PublishIncreListener;
import com.bonc.rrs.balanceprocess.service.BalancePublishService;
import com.bonc.rrs.balanceprocess.vo.*;
import com.bonc.rrs.invoice.enterprises.finance.business.settlementCancel.resp.RespSettlementCancel;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.constant.IdempotentConstant;
import com.bonc.rrs.worder.constant.RedisConstant;
import com.bonc.rrs.worder.entity.po.IdempotentPo;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.validator.Assert;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 结算发布表
 *
 * <AUTHOR>
 * @date 2020-07-03 17:47:19
 */
@RestController
@RequestMapping("balancePublish")
@RequiredArgsConstructor
public class BalancePublishController {

    final BalancePublishService balancePublishService;

    final SysDictionaryDetailDao sysDictionaryDetailDao;

    @Autowired
    private WorderInformationAccountDao worderInformationAccountDao;

    @Autowired
    private BalanceProperties balanceProperties;

    @Autowired
    private IdempotentCheck idempotentCheck;

    /**
     * 提交发布信息
     */
    @ApiOperation(value = "暂存或提交发布信息", notes = "暂存或提交发布信息")
    public R submitPublish(BalancePublishEntity balancePublishEntity, @RequestParam(value = "files", required = false) MultipartFile[] files) throws IOException {
        balancePublishService.submitPublish(balancePublishEntity, files);
        return R.ok();
    }

    @PostMapping("/submitPublish")
    @ApiOperation(value = "暂存或提交发布信息", notes = "暂存或提交发布信息")
    public R submitPublish(@RequestBody BalancePublishEntity balancePublishEntity) throws IOException {
        String idempotentKey = SecureUtil.md5(JSON.toJSONString(balancePublishEntity));
        // 发布提交幂等校验
        IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                , "submitPublish"
                , idempotentKey);

        // 首次执行完成将返回首次结果
        if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
            return R.error("禁止重复调用");
            // 首次调用未执行完成放回重复调用
        } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
            return R.error("禁止重复调用");
        }

        R r = balancePublishService.submitPublish(balancePublishEntity);

        // 执行结束更新执行状态
        idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                , JSON.toJSONString(r)
                , "submitPublish"
                , idempotentKey);
        return r;
    }

    @PostMapping("/commitPublish")
    @ApiOperation(value = "提交发布信息", notes = "提交发布信息")
    public R commitPublish(@RequestBody BalancePublishEntity balancePublishEntity) throws IOException {
        balancePublishService.commitPublish(balancePublishEntity);
        return R.ok();
    }

    @PostMapping("/downLoadPublish")
    @ApiOperation(value = "下载批次号下的发布单号", notes = "下载批次号下的发布单号")
    public void downLoadPublish(HttpServletResponse response, @RequestBody BalancePublishEntity balancePublishEntity) throws IOException {
        Optional.ofNullable(balancePublishEntity.getBatchNo()).orElseThrow(() -> new RRException("缺少发布批次号"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;");
        List<BalancePublishExcelProperty> list = balancePublishService.selectPublishByBatchNo(balancePublishEntity.getBatchNo());
        EasyExcel.write(response.getOutputStream(), BalancePublishExcelProperty.class).autoCloseStream(true).sheet(0, "发布单信息").doWrite(list);
    }


    @PostMapping("/downPublishWorder")
    @ApiOperation(value = "下载待发布工单信息", notes = "下载待发布工单信息")
    public void downPublishWorder(HttpServletResponse response, @RequestBody BalancePublishEntity balancePublishEntity) throws IOException {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");
            Optional.ofNullable(balancePublishEntity.getPublishType()).orElseThrow(() -> new RRException("缺少结算类型！"));
            //获取数据字典配置税点
            List<SysDictionaryDetailEntity> taxPointList = sysDictionaryDetailDao.findByDictNumber("tax_point");
            Map<String, String> taxPointMap = taxPointList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName));

            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            List<Integer> worderIds = null;

            List<Map> dotMap = worderInformationAccountDao.getAllDotList();
            Map<String,Object> dotTaxMap = new HashMap<>();
            for (Map map : dotMap) {
                dotTaxMap.put(getStringValue(map, "dot_id"),getStringValue(map, "dotTaxPoint"));
            }
            //工单类型下载
            if (balancePublishEntity.getPublishType() == 33) {
                //查询待发布工单
                List<PublishCompanyExcelProperty> companyWorders = balancePublishService.selectCompanyWorders(balancePublishEntity);
                companyWorders.forEach(publishCompanyExcelProperty -> publishCompanyExcelProperty.setTaxPoint(StringUtils.isNotBlank(publishCompanyExcelProperty.getTaxPoint())  ? taxPointMap.containsKey(publishCompanyExcelProperty.getTaxPoint()) ? taxPointMap.get(publishCompanyExcelProperty.getTaxPoint()) : publishCompanyExcelProperty.getTaxPoint() : null));
                for (PublishCompanyExcelProperty companyWorder : companyWorders) {
                    if (companyWorder.getStimulateId()==null){
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(companyWorder.getDotId().toString()).toString().replaceAll("%", "").trim());
                        BigDecimal noFee = new BigDecimal(String.valueOf(companyWorder.getDotBalanceFee()));
                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = noFee.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(noFee);
                        companyWorder.setDotBalanceFeetax(Tax);
                        companyWorder.setDotBalanceFeeSum(includedMoney);
                    }
                }
                WriteSheet writeSheeet = EasyExcel.writerSheet(0, "工单信息").head(PublishCompanyExcelProperty.class).build();
                excelWriter.write(companyWorders, writeSheeet);
                //获取工单集合
                worderIds = companyWorders.stream().map(PublishCompanyExcelProperty::getWorderId).collect(Collectors.toList());
            } else if (balancePublishEntity.getPublishType() == 34) {
                //查询增项发布工单
                List<PublishIncreExcelProperty> increWorders = balancePublishService.selectPublishIncreWorders(balancePublishEntity);
                increWorders.forEach(publishIncreExcelProperty -> publishIncreExcelProperty.setTaxPoint(StringUtils.isNotBlank(publishIncreExcelProperty.getTaxPoint())  ? taxPointMap.containsKey(publishIncreExcelProperty.getTaxPoint()) ? taxPointMap.get(publishIncreExcelProperty.getTaxPoint()) : publishIncreExcelProperty.getTaxPoint() : null));
                for (PublishIncreExcelProperty companyWorder : increWorders) {
                    if (companyWorder.getStimulateId()==null){
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(companyWorder.getDotId().toString()).toString().replaceAll("%", "").trim());
                        BigDecimal noFee = new BigDecimal(String.valueOf(companyWorder.getDotBalanceFee()));
                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = noFee.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(noFee);
                        companyWorder.setDotBalanceFeetax(Tax);
                        companyWorder.setDotBalanceFeeSum(includedMoney);
                    }
                }
                WriteSheet writeSheeet = EasyExcel.writerSheet(0, "工单信息").head(PublishIncreExcelProperty.class).build();
                excelWriter.write(increWorders, writeSheeet);
                //获取工单集合
                worderIds = increWorders.stream().map(PublishIncreExcelProperty::getWorderId).collect(Collectors.toList());
                //工单新资金类型下载
            } else if (balancePublishEntity.getPublishType() == 36) {
                //查询待发布新资金工单
                List<PublishCompanyExcelProperty> companyWorders = balancePublishService.queryBalanceAdwanceMoneyWorderInfoExport(balancePublishEntity);
                companyWorders.forEach(publishCompanyExcelProperty -> publishCompanyExcelProperty.setTaxPoint(StringUtils.isNotBlank(publishCompanyExcelProperty.getTaxPoint())  ? taxPointMap.containsKey(publishCompanyExcelProperty.getTaxPoint()) ? taxPointMap.get(publishCompanyExcelProperty.getTaxPoint()) : publishCompanyExcelProperty.getTaxPoint() : null));
                for (PublishCompanyExcelProperty companyWorder : companyWorders) {
                    if (companyWorder.getStimulateId()==null){
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(companyWorder.getDotId().toString()).toString().replaceAll("%", "").trim());
                        BigDecimal noFee = new BigDecimal(String.valueOf(companyWorder.getDotBalanceFee()));
                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = noFee.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(noFee);
                        companyWorder.setDotBalanceFeetax(Tax);
                        companyWorder.setDotBalanceFeeSum(includedMoney);
                    }
                }
                WriteSheet writeSheeet = EasyExcel.writerSheet(0, "工单信息").head(PublishCompanyExcelProperty.class).build();
                excelWriter.write(companyWorders, writeSheeet);
                //获取工单集合
                worderIds = companyWorders.stream().map(PublishCompanyExcelProperty::getWorderId).collect(Collectors.toList());
            } else if (balancePublishEntity.getPublishType() == 37) {
                //查询待发布网点激励
                List<PublishCompanyExcelProperty> companyWorders = balancePublishService.queryStimulateInfoAduitList(balancePublishEntity);
                companyWorders.forEach(publishCompanyExcelProperty -> publishCompanyExcelProperty.setTaxPoint(StringUtils.isNotBlank(publishCompanyExcelProperty.getTaxPoint())  ? taxPointMap.containsKey(publishCompanyExcelProperty.getTaxPoint()) ? taxPointMap.get(publishCompanyExcelProperty.getTaxPoint()) : publishCompanyExcelProperty.getTaxPoint() : null));
                for (PublishCompanyExcelProperty companyWorder : companyWorders) {
                    if (companyWorder.getStimulateId()==null){
                        BigDecimal dotTaxRate = new BigDecimal(dotTaxMap.get(companyWorder.getDotId().toString()).toString().replaceAll("%", "").trim());
                        BigDecimal noFee = new BigDecimal(String.valueOf(companyWorder.getDotBalanceFee()));
                        BigDecimal dotTax = dotTaxRate.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
                        BigDecimal includedMoney = noFee.multiply(dotTax.add(new BigDecimal(1))).setScale(2,balanceProperties.ROUND_MODE);
                        BigDecimal Tax = includedMoney.subtract(noFee);
                        companyWorder.setDotBalanceFeetax(Tax);
                        companyWorder.setDotBalanceFeeSum(includedMoney);
                    }
                }
                WriteSheet writeSheeet = EasyExcel.writerSheet(0, "激励信息").head(PublishCompanyExcelProperty.class).build();
                excelWriter.write(companyWorders, writeSheeet);
                //获取工单集合
                worderIds = companyWorders.stream().map(PublishCompanyExcelProperty::getWorderId).collect(Collectors.toList());
            }
            //查询工单下的物料
            WriteSheet materialWriteSheet = EasyExcel.writerSheet(1, "物料信息").head(PublishMaterialExcelProperty.class).build();
            //查询使用的物料信息
            List<PublishMaterialExcelProperty> publishMaterials = balancePublishService.selectCompanyMaterial(worderIds);
            excelWriter.write(publishMaterials, materialWriteSheet);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    private String getStringValue(Map worderMap, Object key){
        Object value = worderMap.get(key);
        return (value==null) ? "" : value.toString();
    }

    @PostMapping("/uploadPublishWorder")
    @ApiOperation(value = "导入发布信息", notes = "导入发布信息")
    public R uploadPublishWorder(@RequestParam("file") MultipartFile file, @RequestParam("publishType") Integer publishType) throws IOException {
        Assert.isNull(publishType, "缺少结算类型！");
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件的后缀名为xlsx
        assert fileName != null;
        String fileXlsx = fileName.substring(fileName.length() - 5);
        String fileXls = fileName.substring(fileName.length() - 4);
        //校验文件扩展名
        if (!(fileXlsx.equals(Constant.XLSX) || fileXls.equals(Constant.XLSX))) {
            return R.error().put("msg", "文件类型不正确！");
        }
        //工单总金额
        BigDecimal publishFee = new BigDecimal(0);
        //工单ID集合
        Set<Integer> worderIds = new HashSet<>();
        //激励单ID集合
        Set<Integer> stimulateIds = new HashSet<>();
        //工单ID和工单费用Map
        Map<Integer, BigDecimal> worderIdFee = new HashMap<>(256);
        //激励ID和激励费用Map
        Map<Integer, BigDecimal> stimulateIdFee = new HashMap<>(256);
        // 含税与不含税结算费用统计
        PublishBalanceFeeVO publishBalanceFeeVO = new PublishBalanceFeeVO(new BigDecimal(0), new BigDecimal(0), new BigDecimal(0), new BigDecimal(0));

//        String payForm = null;
//
//        List<Map<String,String>> payMap = balancePublishService.queryPayFormList();
//
//        Map<String,String> payFormMap = new HashMap<>();
//        for (Map<String, String> map : payMap) {
//            payFormMap.put(map.get("payFormCode"),map.get("payFormName"));
//        }
        //失败工单统计
        List<String> failureDatas = new ArrayList<>();
        if (publishType == 33) {
            Assert.isTrue(fileName.contains("工单类型"), "上传文件和结算类型不一致");
            EasyExcel.read(file.getInputStream(), PublishCompanyExcelProperty.class, new PublishCompanyListener(publishType, worderIds, stimulateIds, worderIdFee, stimulateIdFee, failureDatas, publishBalanceFeeVO)).autoCloseStream(Boolean.TRUE).sheet().doRead();
        } else if (publishType == 34) {
            Assert.isTrue(fileName.contains("增项类型"), "上传文件和结算类型不一致");
            EasyExcel.read(file.getInputStream(), PublishIncreExcelProperty.class, new PublishIncreListener(publishType, worderIds, stimulateIds, worderIdFee, stimulateIdFee, failureDatas, publishBalanceFeeVO)).autoCloseStream(Boolean.TRUE).sheet().doRead();
        } else if (publishType == 36) {
            Assert.isTrue(fileName.contains("工单新资金类型"), "上传文件和结算类型不一致");
            EasyExcel.read(file.getInputStream(), PublishCompanyExcelProperty.class, new PublishCompanyListener(publishType, worderIds, stimulateIds, worderIdFee, stimulateIdFee, failureDatas, publishBalanceFeeVO)).autoCloseStream(Boolean.TRUE).sheet().doRead();
        } else if (publishType == 37) {
            Assert.isTrue(fileName.contains("激励类型"), "上传文件和结算类型不一致");
            EasyExcel.read(file.getInputStream(), PublishCompanyExcelProperty.class, new PublishCompanyListener(publishType, worderIds, stimulateIds, worderIdFee, stimulateIdFee, failureDatas, publishBalanceFeeVO)).autoCloseStream(Boolean.TRUE).sheet().doRead();
        }
        if (worderIds.size() < 1 && stimulateIds.size() < 1) {
            return R.error("上传的工单为空！");
        }
        if (failureDatas.size() > 0) {
            return R.error("文件上传失败！").put("failureDatas", failureDatas);
        }

        //备份上传OSS文件
        Map<String, String> url = FileUtils.getUrl(file);
        //叠加费用总金额
        for (Map.Entry<Integer, BigDecimal> entry : worderIdFee.entrySet()) {
            publishFee = publishFee.add(entry.getValue());
        }
        for (Map.Entry<Integer, BigDecimal> entry : stimulateIdFee.entrySet()) {
            publishFee = publishFee.add(entry.getValue());
        }
        return Objects.requireNonNull(Objects.requireNonNull(Objects.requireNonNull(R.ok()
                .put("publishFee", publishFee))
                .put("worderIds", worderIds))
                .put("stimulateIds", stimulateIds)
                .put("worderIdFee", worderIdFee))
                .put("stimulateIdFee", stimulateIdFee)
                .put("fileUrl", url.get("url"))
                .put("increBalanceFee", publishBalanceFeeVO.getIncreBalanceFee())
                .put("increBalanceFeeSum", publishBalanceFeeVO.getIncreBalanceFeeSum())
                .put("stimulateBalanceFee", publishBalanceFeeVO.getStimulateBalanceFee())
                .put("stimulateBalanceFeeSum", publishBalanceFeeVO.getStimulateBalanceFeeSum());
    }


    /**
     * 查询发布详情信息
     */
    @GetMapping("/publishDetail")
    @ApiOperation(value = "查询发布详情信息", notes = "查询发布详情信息")
    public R getPublishDetail(@RequestParam(value = "publishId") Integer publishId) {
        BalancePublishVO balancePublishVO = balancePublishService.getPublishDetail(publishId);
        return R.ok().put("data", balancePublishVO);
    }

    /**
     * 查询发布详情信息
     */
    @GetMapping("/publishBatchDetail")
    @ApiOperation(value = "根据批次查询发布详情信息", notes = "根据批次查询发布详情信息")
    public R getPublishBatchDetail(@RequestParam(value = "batchNo") String batchNo) {
        BalancePublishVO balancePublishVO = balancePublishService.getPublishBatchDetail(batchNo);
        return R.ok().put("data", balancePublishVO);
    }

    /**
     * 撤销发布
     */
    @GetMapping("/deletePublish")
    @ApiOperation(value = "撤销发布", notes = "撤销发布")
    public R deletePublish(@RequestParam(value = "batchNo") String batchNo) {
        balancePublishService.deletePublishByBathNo(batchNo);
        return R.ok();
    }

    /**
     * 分页查询发布信息列表
     *
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询发布信息列表", notes = "分页查询发布信息列表")
    public R pageBalancePublish(@Validated @RequestBody BalancePublishQuery query) {
        PageUtils page = balancePublishService.pageBalancePublish(query);
        return R.ok().put("data", page);
    }

    /**
     * 撤销发布
     */
    @GetMapping("/settlementCancel")
    @ApiOperation(value = "结算单撤销", notes = "结算单撤销")
    public RespSettlementCancel settlementCancel(@RequestParam(value = "billNo") String billNo, @RequestParam(value = "reason") String reason) {
        return balancePublishService.settlementCancel(billNo, reason);
    }

    @PostMapping("/queryAdvanceCompanyNo")
    @ApiOperation(value = "查询所有新资金发布单号", notes = "查询所有新资金发布单号")
    public R queryAdvanceCompanyNo() {
        List<String> companyNoList = balancePublishService.queryAdvanceCompanyNo();
        return R.ok().put("data", companyNoList);
    }

    @PostMapping("/queryPayFormList")
    @ApiOperation(value = "查询所有付款方式选项", notes = "查询所有付款方式选项")
    public R queryPayFormList() {
        List<Map<String, String>> payFormList = balancePublishService.queryPayFormList();
        return R.ok().put("data", payFormList);
    }

    @PostMapping("/queryPayBankList")
    @ApiOperation(value = "查询所有付款银行选项", notes = "查询所有付款银行选项")
    public R queryPayBankList() {
        List<Map<String, String>> payFormList = balancePublishService.queryPayBankList();
        return R.ok().put("data", payFormList);
    }

    @PostMapping("/queryOutBankList")
    @ApiOperation(value = "查询所有付款银行选项", notes = "查询所有付款银行选项")
    public R queryOutBankList() {
        List<Map<String, String>> outFormList = balancePublishService.queryOutBankList();
        return R.ok().put("data", outFormList);
    }

}
