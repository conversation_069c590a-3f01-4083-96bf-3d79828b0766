package com.bonc.rrs.spider;

import cn.hutool.core.map.MapUtil;
import com.bonc.rrs.lx.dto.LxApixResponse;
import com.bonc.rrs.spider.dto.ApixResponse;
import com.bonc.rrs.spider.dto.OrderApiDto;
import com.bonc.rrs.spider.strategy.OrderContext;
import com.bonc.rrs.spider.strategy.OrderCreationStrategy;
import com.bonc.rrs.spider.strategy.OrderStrategyFactory;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/dmj/openapi/news/spider")
@Api(value = "/dmj/openapi/news/spider", tags = "爬虫工单接口")
@RequiredArgsConstructor
public class SpiderController {
    private final OrderStrategyFactory strategyFactory;

    @Value("${bridgehub.paramToken}")
    private String paramToken;

    // 同步创建工单
    @PostMapping("/create")
    @ApiOperation(value = "爬虫推送工单接口", notes = "爬虫推送工单接口")
    public ApixResponse createOrder(@RequestHeader(name = "x-accesstoken") String token, @RequestBody OrderApiDto dto) {
        try {

            if (StringUtils.isBlank(token) || !token.equals(paramToken)) {
                return ApixResponse.error("token校验失败");
            }

            OrderCreationStrategy strategy = strategyFactory.getStrategy(dto.getTemplateId());

            OrderContext context = strategy.createOrder(dto);

            Throwable error = context.getError();
            if (error != null) {
                log.error("创建工单失败", error);
                // 提供更详细的异常信息
                return ApixResponse.error(error.getMessage());
            }

            return ApixResponse.success(MapUtil.builder().put("worderNo", context.getWorderNo()).build());
        } catch (Exception e) {
            log.error("创建工单失败", e);
            return ApixResponse.error(e.getMessage());
        }
    }


}
