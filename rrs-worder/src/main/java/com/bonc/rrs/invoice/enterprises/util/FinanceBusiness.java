package com.bonc.rrs.invoice.enterprises.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.intf.service.IntfLogService;
import com.bonc.rrs.invoice.enterprises.finance.business.deleteOrder.req.ReqDeleteOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.detailsEdit.req.ReqDetailsEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqAdvAudit;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqInsertOrUpdate;
import com.bonc.rrs.invoice.enterprises.finance.business.insertOrUpdate.req.ReqSearchOrder;
import com.bonc.rrs.invoice.enterprises.finance.business.invoiceEdit.req.ReqInvoiceEdit;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.ReqNewAdvanceSettlement;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.ReqNewsDataAccess;
import com.bonc.rrs.invoice.enterprises.finance.business.selectSettlementStatus.req.ReqSelectSettlementStatus;
import com.bonc.rrs.invoice.enterprises.finance.business.settleAuditAdd.req.ReqSettleAuditAdd;
import com.bonc.rrs.invoice.enterprises.finance.business.settlementCancel.req.ReqSettlementCancel;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.req.ReqTransferApprovalForm;
import com.bonc.rrs.invoice.enterprises.finance.business.transferSubOrder.req.ReqTransferSubOrder;
import com.youngking.lenmoncore.common.validator.Assert;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 财务对接业务接口
 * @Date 2023/1/30 16:38
 */
@Component
@Log4j2
public class FinanceBusiness {

    @Value("${finance.business.key}")
    private String key;

    @Value("${finance.business.appId}")
    private String appId;

    @Value("${finance.business.insertOrUpdateUrl}")
    private String insertOrUpdateUrl;

    @Value("${finance.business.deleteOrderUrl}")
    private String deleteOrderUrl;

    @Value("${finance.business.transferSubOrderUrl}")
    private String transferSubOrderUrl;

    @Value("${finance.business.searchOrderUrl}")
    private String searchOrderUrl;

    @Value("${finance.business.detailsEditUrl}")
    private String detailsEditUrl;

    @Value("${finance.business.detailsEditUrl}")
    private String invoiceEditUrl;

    @Value("${finance.business.transferApprovalFormUrl}")
    private String transferApprovalFormUrl;

    @Value("${finance.business.acAddUrl}")
    private String acAddUrl;

    @Value("${finance.business.selectSettlementStatusUrl}")
    private String selectSettlementStatusUrl;

    @Value("${finance.business.settlementCancelUrl}")
    private String settlementCancelUrl;

    @Value("${finance.business.newsDataAccessUrl}")
    private String newsDataAccessUrl;

    @Value("${finance.business.newAdvanceSettlementUrl}")
    private String newAdvanceSettlementUrl;

    @Value("${finance.business.settleAuditAddUrl}")
    private String settleAuditAddUrl;

    @Value("${finance.business.advAuditAddUrl}")
    private String advAuditAddUrl;

    @Value("${finance.business.lfsSettleAuditAddUrl}")
    private String lfsSettleAuditAddUrl;

    @Value("${finance.business.callback.appId}")
    private String callBackAppId;

    @Value("${finance.business.callback.appSecret}")
    private String callBackAppSecret;


    public String getCallBackAppId() {
        return callBackAppId;
    }

    public String getCallBackAppSecret() {
        return callBackAppSecret;
    }

    private static String HEADERS = "headers";

    private static String DATA = "data";

    private static String SUCCESS = "200";

    @Autowired
    private IntfLogService intfLogService;

    public static String getSUCCESS() {
        return SUCCESS;
    }

    private JSONObject financePost(String url, JSONObject requestBody) {
        JSONObject result = new JSONObject();
        //创建http请求
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(url);
            // 设置请求头
            post.setHeader("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            post.setHeader("Accept", MediaType.APPLICATION_JSON_VALUE);
//            post.setHeader("appId", requestBody.getJSONObject());
            log.info("==================调用URL:{}====请求入参:{}=====================", url, requestBody.toString());
            StringEntity se = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
            se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE));
            post.setEntity(se);

            // 发起调用票税云开票结果查询接口出参
            CloseableHttpResponse httpResponse = httpClient.execute(post);
            if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                //返回调用结果
                String resultStr = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                log.info("===============调用URL:{}====响应出参：{}=====================", url, resultStr);
                return JSONObject.parseObject(resultStr);
            } else {
                log.info("===============调用URL:{}====httpCode:{}====reasonPhrase:{}", url, httpResponse.getStatusLine().getStatusCode(), httpResponse.getStatusLine().getReasonPhrase());
            }
        } catch (IOException e) {
            log.error("==================调用URL:{}====调用出现异常！====================", url, e);
            result.put("code", "600");
            result.put("msg", StringUtils.isBlank(result.getString("msg")) ? "调用传输合并单、增项、激励工单接口失败，" + e.toString() : e.toString() + "," + result.get("msg"));

        }
        return result;
    }

    /**
     * 传输合并单、增项、激励工单接口
     *
     * @param reqInsertOrUpdate
     */
    public JSONObject companyInsertOrUpdate(@NotNull ReqInsertOrUpdate reqInsertOrUpdate) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put(DATA, reqInsertOrUpdate);
            //调用接口
            result = financePost(insertOrUpdateUrl, param);
            intfLogService.saveIntfLog("insertOrUpdate", reqInsertOrUpdate.getOrderNo(), insertOrUpdateUrl, "传输合并单、增项、激励工单接口", 1, param.toString(), result.toString());
            log.info("==================传输合并单、增项、激励工单接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "传输合并单、增项、激励工单接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用传输合并单、增项、激励工单接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            log.error("errpr:", e);
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }


    /**
     * 合并单、增项单、激励单作废接口
     *
     * @param reqDeleteOrder
     */
    public JSONObject deleteMergeOrder(@NotNull ReqDeleteOrder reqDeleteOrder) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put(DATA, reqDeleteOrder);
            //调用接口
            result = financePost(deleteOrderUrl, param);
            intfLogService.saveIntfLog("deleteOrder", reqDeleteOrder.getOrderNo(), deleteOrderUrl, "合并单、增项单、激励单作废接口", 1, param.toString(), result.toString());
            log.info("==================合并单、增项单、激励单作废接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "合并单、增项单、激励单作废接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用合并单、增项单、激励单作废接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }


    /**
     * 传输子订单明细
     * @param reqTransferSubOrderList
     */
    public JSONObject transferSubOrder(@NotNull List<ReqTransferSubOrder> reqTransferSubOrderList) {
        JSONObject result = new JSONObject();
        try {
            String companyInvoiceNo = "";
            if (reqTransferSubOrderList != null && reqTransferSubOrderList.size() > 0) {
                companyInvoiceNo = reqTransferSubOrderList.get(0).getOrderNo();
            }
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncCarOrderDetails", reqTransferSubOrderList);
            //调用接口
            result = financePost(transferSubOrderUrl, param);
            intfLogService.saveIntfLog("transferSubOrder", companyInvoiceNo, transferSubOrderUrl, "传输子订单明细接口", 1, param.toString(), result.toString());
            log.info("==================传输子订单明细接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "传输子订单明细接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用传输子订单明细接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }

    /**
     * 更新子订单明细接口
     * @param reqDetailsEdit
     */
    public JSONObject detailsEdit(@NotNull ReqDetailsEdit reqDetailsEdit) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncCarOrderDetails", reqDetailsEdit);
            //调用接口
            result = financePost(detailsEditUrl, param);
            intfLogService.saveIntfLog("detailsEdit", reqDetailsEdit.getSubOrderNo(), detailsEditUrl, "更新子订单明细接口", 1, param.toString(), result.toString());
            log.info("==================更新子订单明细接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "更新子订单明细接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用更新子订单明细接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }

    /**
     * 更新发票明细接口
     * @param reqInvoiceEditList
     */
    public JSONObject invoiceEdit(@NotNull List<ReqInvoiceEdit> reqInvoiceEditList) {
        JSONObject result = new JSONObject();
        try {
            String companyInvoiceNo = "";
            if (reqInvoiceEditList != null && reqInvoiceEditList.size() > 0) {
                companyInvoiceNo = reqInvoiceEditList.get(0).getOrderNo();
            }
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncCarInvoiceHeadersList", reqInvoiceEditList);
            //调用接口
            result = financePost(invoiceEditUrl, param);
            intfLogService.saveIntfLog("invoiceEdit", companyInvoiceNo, invoiceEditUrl, "更新发票明细接口", 1, param.toString(), result.toString());
            log.info("==================更新发票明细接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "更新发票明细接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用更新发票明细接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }


    /**
     * 查询合并单、增项、激励工单接口
     *
     * @param reqSearchOrder
     */
    public JSONObject companySearchOrder(@NotNull ReqSearchOrder reqSearchOrder) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put(DATA, reqSearchOrder);
            //调用接口
            result = financePost(searchOrderUrl, param);
            intfLogService.saveIntfLog("searchOrder", reqSearchOrder.getOrderNo(), searchOrderUrl, "查询合并单、增项、激励工单接口", 1, param.toString(), result.toString());
            log.info("==================查询合并单、增项、激励工单接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "查询合并单、增项、激励工单接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用查询合并单、增项、激励工单接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用查询合并单、增项、激励工单接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 传输回款审核单1216接口
     *
     * @param reqTransferApprovalForm
     */
    public JSONObject transferApprovalForm(@NotNull ReqTransferApprovalForm reqTransferApprovalForm) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncWriteOffAudit", reqTransferApprovalForm);
            //调用接口
            result = financePost(acAddUrl, param);
            intfLogService.saveIntfLog("fncWriteOffAudit", reqTransferApprovalForm.getWriteOffNo(), acAddUrl, "传输回款审核单1216接口", 1, param.toString(), result.toString());
            log.info("==================传输回款审核单1216接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "传输回款审核单1216接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "调用传输回款审核单1216接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用传输回款审核单1216接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 查询结算单状态接口
     *
     * @param reqSelectSettlementStatus
     */
    public JSONObject selectSettlementStatus(@NotNull ReqSelectSettlementStatus reqSelectSettlementStatus) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("param", reqSelectSettlementStatus);
            //调用接口
            result = financePost(selectSettlementStatusUrl, param);
            intfLogService.saveIntfLog("selectSettlementStatus", reqSelectSettlementStatus.getBillNo(), selectSettlementStatusUrl, "查询结算单状态接口", 1, param.toString(), result.toString());
            log.info("==================查询结算单状态接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "查询结算单状态接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "查询结算单状态接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用查询结算单状态接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 结算单作废接口
     *
     * @param reqSettlementCancel
     */
    public JSONObject settlementCancel(@NotNull ReqSettlementCancel reqSettlementCancel) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("param", reqSettlementCancel);
            //调用接口
            result = financePost(settlementCancelUrl, param);
            intfLogService.saveIntfLog("settlementCancel", reqSettlementCancel.getBillNo(), settlementCancelUrl, "结算单作废接口", 1, param.toString(), result.toString());
            log.info("==================结算单作废接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "结算单作废接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "结算单作废接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用结算单作废接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 结算单传输接口
     *
     * @param reqNewsDataAccess
     */
    public JSONObject newsDataAccess(@NotNull ReqNewsDataAccess reqNewsDataAccess) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("hcspBillInfo", reqNewsDataAccess);
            //调用接口
            result = financePost(newsDataAccessUrl, param);
            intfLogService.saveIntfLog("newsDataAccess", reqNewsDataAccess.getBillNo(), newsDataAccessUrl, "结算单传输接口", 1, param.toString(), result.toString());
            log.info("==================结算单传输接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "结算单传输接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "结算单传输接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用结算单传输接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 新垫资结算审核接口
     *
     * @param reqNewAdvanceSettlement
     */
    public JSONObject newAdvanceSettlement(@NotNull ReqNewAdvanceSettlement reqNewAdvanceSettlement) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("data", reqNewAdvanceSettlement);
            //调用接口
            result = financePost(newAdvanceSettlementUrl, param);
            intfLogService.saveIntfLog("newsDataAccess", reqNewAdvanceSettlement.getSettleNo(), newAdvanceSettlementUrl, "新垫资结算审核接口", 1, param.toString(), result.toString());
            log.info("==================新垫资结算审核接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "新垫资结算审核接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "新垫资结算审核接口失败：" + result.getString("msg"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", "调用新垫资结算审核接口失败，" + e.toString());
        }
        return result;
    }

    /**
     * 传输垫资审核单接口
     * @param reqAdvAudit
     */
    public JSONObject pushAdvAuditAdd(@NotNull ReqAdvAudit reqAdvAudit) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncAdvanceAudit", reqAdvAudit);
            //调用接口
            result = financePost(advAuditAddUrl, param);
            intfLogService.saveIntfLog("pushAdvAuditAdd", reqAdvAudit.getOrderNo(), advAuditAddUrl, "传输垫资审核单", 1, param.toString(), result.toString());
            log.info("==================传输垫资审核单接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "传输垫资审核单接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "传输垫资审核单接口失败：" + result.getString("msg"));
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }

    /**
     * 结算审核单传输接口
     * @param reqSettleAuditAdd
     */
    public JSONObject settleAuditAdd(@NotNull ReqSettleAuditAdd reqSettleAuditAdd) {
        JSONObject result = new JSONObject();
        try {
            JSONObject param = new JSONObject();
            param.put(HEADERS, new FinanceHeaders(appId, key));
            param.put("fncSettleAudit", reqSettleAuditAdd);
            //调用接口
            result = financePost(lfsSettleAuditAddUrl, param);
            intfLogService.saveIntfLog("settleAuditAdd", reqSettleAuditAdd.getSettleNo(), lfsSettleAuditAddUrl, "结算审核单传输", 1, param.toString(), result.toString());
            log.info("==================结算审核单传输接口出参：{}=====================", result.toJSONString());
            Assert.isNull(result, "结算审核单传输接口调用出现异常！");
            Assert.isTrue(SUCCESS.equals(result.getString("code")), "结算审核单传输接口失败：" + result.getString("msg"));
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", "600");
            result.put("msg", e.toString());
        }
        return result;
    }

    static void  searchOrder(){
        FinanceBusiness financeBusiness=new FinanceBusiness();
        ReqSearchOrder reqSearchOrder = new ReqSearchOrder();
        reqSearchOrder.setOrderNo("k2023-04-240004");
        reqSearchOrder.setOrderType("10");
        JSONObject param = new JSONObject();
        param.put(HEADERS, new FinanceHeaders("news", "XA12#Da"));
        param.put("data", reqSearchOrder);
        //调用接口
        JSONObject result = financeBusiness.financePost("http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/selectOrder", param);
        if(result.getIntValue("code")==200){
            //System.out.println(result.getJSONObject("data").getJSONObject("bookkeepingMap").getJSONArray("estimatedBookkeeping"));
            JSONArray ret=result.getJSONObject("data").getJSONObject("bookkeepingMap").getJSONArray("estimatedBookkeeping");
            System.out.println(ret);
        }
    }

    static  void  settlementCancel(){
        JSONArray ret=new JSONArray();
        //调用接口
        String[] str=new String[] {"ZLW230508173500000023","ZLW230510095713000000","ZLW230510095715000003","ZLW230510095716000005","ZLW230510095717000006","ZLW230510095717000007","ZLW230510095718000009","ZLW230524182202000001","ZLW230524182203000002","ZLW230524182205000003","ZLW230524182210000005","ZLW230524182211000006","ZLW230524182214000007","ZLW230524182216000008","ZLW230524182217000009","ZLW230524182220000011","ZLW230524182221000012","ZLW230524182223000013","ZLW230524182226000015","ZLW230524182228000017","ZLW230524182229000018","ZLW230524182229000019","ZLW230524182247000035","ZLW230524182249000037","ZLW230524182252000039","ZLW230524182257000041","ZLW230530180405000010","ZLW230530180406000012","ZLW230530180407000018","ZLW230605175955000123"};
        FinanceBusiness financeBusiness=new FinanceBusiness();
        System.out.println(str.length);
        for(int i=0;i<str.length;i++){
            JSONObject param = new JSONObject();
            ReqSettlementCancel reqSettlementCancel=new ReqSettlementCancel();
            reqSettlementCancel.setBillNo(str[i]);
            reqSettlementCancel.setReason("负激励问题回退");
            param.put(HEADERS, new FinanceHeaders("news", "XA12#Da"));
            param.put("param", reqSettlementCancel);
            //调用接口
            JSONObject result = financeBusiness.financePost("http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/SettlementCancel", param);
            if(result.getIntValue("code")!=200){
                ret.add(str[i]);
            }
            System.out.println(result);
        }
        System.out.println(str.length);
        System.out.println(ret);
    }

}
