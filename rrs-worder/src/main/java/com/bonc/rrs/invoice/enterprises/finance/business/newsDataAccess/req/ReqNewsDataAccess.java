package com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 结算单传输接口
 * @Date 2023/3/10 15:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class ReqNewsDataAccess {
    /**
     * 结算单汇总单号
     * 必填
     */
    private String billNo;
    /**
     * 供应商V码 受益人
     * 必填
     */
    private String vendorCode;
    /**
     * 供应商名称
     * 必填
     */
    private String vendorName;
    /**
     * 发布时间
     * 必填
     */
    private String deliveryTime;
    /**
     * 汇总金额（含税）（应结总金额）
     * 必填
     */
    private String apAmt;
    /**
     * 操作人名称
     * 必填
     */
    private String oprUserId;
    /**
     * 发票税率
     * 必填
     */
    private String invoiceTaxRate;
    /**
     * 对帐单明细行数
     * 必填
     */
    private String detailCount;
    /**
     * 对帐单激励金额  默认0
     * 必填
     */
    private String rewardAmt;
    /**
     * 对帐单索赔金额  默认0
     * 必填
     */
    private String reduceAmt;
    /**
     * 应付总金额=汇总金额
     * 必填
     */
    private String acAmt;
    /**
     * 工贸编码
     * 必填
     */
    private String stEntityCode;
    /**
     * 工贸名称
     * 必填
     */
    private String stEntityName;
    /**
     * 预算体编码   例如：BD1001070960，详见 ：预算体 sheet
     * 必填
     */
    private String bdEntityCode;
    /**
     * 预算体名称 新能源总部
     * 必填
     */
    private String bdEntityName;
    /**
     * 费用项目编码cFeeItemCode  详见 ：预算体 sheet
     * 必填
     */
    private String cFeeItemCode;
    /**
     * 费用项目名称  详见 ：预算体 sheet
     * 必填
     */
    private String cFeeItemName;
    /**
     * 结算员工名
     * 必填
     */
    private String stUserName;
    /**
     * 结算员工号
     * 必填
     */
    private String stUserCode;
    /**
     * 所属账期（结算年月）  yyyyMM
     * 必填
     */
    private String periodChar;
    /**
     * 结算日期  yyyy-MM-dd HH:mm:ss
     * 必填
     */
    private String createdTime;
    /**
     * 结算开始日期  yyyy-MM-dd HH:mm:ss
     * 必填
     */
    private String billBeginDate;
    /**
     * 结算结束日期  yyyy-MM-dd HH:mm:ss
     * 必填
     */
    private String billEndDate;
    /**
     * 业务模式=财务类型 "1、ST_ZZSA 充电桩增项,2、ST_ZLW 桩联网车企网点结算
     * 必填
     */
    private String businessType;
    /**
     * 业务系统编码  ZLWYCPT  ？LJ
     * 必填
     */
    private String originApp;
    /**
     * 出账公司编码  0RK0
     * 必填
     */
    private String cAccountCode;
    /**
     * 出账公司名称  日日顺科技服务(上海)有限公司
     * 必填
     */
    private String cAccountName;
    /**
     * 出账公司账户  31050174400000000657
     * 必填
     */
    private String cAccountNoOut;
    /**
     * 出账银行名称  中国建设银行股份有限公司上海番禺路支行
     * 必填
     */
    private String cAccountBankOut;
    /**
     * （内销/外销） 内销
     * 必填
     */
    private String cSaleType;
    /**
     * 客户编码
     * 否
     */
    private String cCusCode;
    /**
     * 客户名称
     * 否
     */
    private String cCusName;
    /**
     * 收款单位账号
     * 必填
     */
    private String cAccountNo;
    /**
     * 收款银行名称
     * 必填
     */
    private String cAccountBank;
    /**
     * 供应商类型 供应商
     * 必填
     */
    private String cSsupType;
    /**
     * 付款方式 X',  传：X
     * 必填
     */
    private String cPayTypeCode;
    /**
     * 计划付款日期  yyyy-MM-dd HH:mm:ss
     * 必填
     */
    private String dPayable;
    /**
     * 付款金额  B41应结金额
     * 必填
     */
    private String iApplied;
    /**
     * 是否签合必填
     * 否
     */
    private String bBargain;
    /**
     * WBS要素
     * 否
     */
    private String cWbsCode;
    /**
     * 付款类型  正常付款
     * 必填
     */
    private String cPayType;
    /**
     * 发票类型（正常发票、无需发票） 正常发票
     * 必填
     */
    private String cInvoiceType;
    /**
     * 工作项目编码
     * 否
     */
    private String cProCode;
    /**
     * 申请人工号 结算员工
     * 必填
     */
    private String cUserCode;
    /**
     * 申请人姓名 结算员工
     * 必填
     */
    private String cUserName;
    /**
     * 发票选项（发票先到、发票后到、无需发票）  发票先到
     * 必填
     */
    private String cInvoiceOption;
    /**
     * 业务描述  "核销第三方平台采购" + 业务类型 + 主键 + 供应商V码
     * 必填
     */
    private String cDescriPtionON;
    /**
     * 合同号 合同号和无合同原因必须填一个
     * 否
     */
    private String cHtCode;
    /**
     * 无合同原因原因
     * 否
     */
    private String cReason;
    /**
     * 预留字段
     * 否
     */
    private String add1;
    /**
     * 预留字段
     * 否
     */
    private String add2;
    /**
     * 预留字段
     * 否
     */
    private String add3;
    /**
     * 预留字段
     * 否
     */
    private String add4;
    /**
     * 预留字段
     * 否
     */
    private String add5;
    /**
     * 经办人 工号
     * 必填
     */
    private String handledBy;

    /**
     * 转账日志
     */
    private String cvpPayTime;

    private String acceptingBank;

    private List<SettleOrderInfoForHscpsInfo> settleOrderInfoForHscpsList;

}
