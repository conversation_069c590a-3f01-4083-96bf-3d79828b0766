package com.bonc.rrs.byd.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 拆桩附件
 * @Author: tangchuheng
 * @Date: 2024/4/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SplitAttachments implements Serializable {
    /**
     * 充电桩序列码
     */
    @ApiModelProperty(value = "充电桩序列码", required = true)
    private String chargePileCodeImage;

    /**
     * 人桩合照
     */
    @ApiModelProperty(value = "人桩合照", required = true)
    private String personPileImage;

    /**
     * 试充照片
     */
    @ApiModelProperty(value = "试充照片", required = true)
    private String tryChargePImage;

    /**
     * 拆桩前电源点正面0.5m近景
     */
    @ApiModelProperty(value = "拆桩前电源点正面0.5m近景", required = true)
    private String powerPointFrontImage;

    /**
     * 施工使用线缆
     */
    @ApiModelProperty(value = "施工使用线缆", required = true)
    private String constructionUCImage;

    /**
     * 拆桩后电源点正面0.5m近景
     */
    @ApiModelProperty(value = "拆桩后电源点正面0.5m近景", required = true)
    private String powerPointBackImage;

    /**
     * 充电桩接线
     */
    @ApiModelProperty(value = "充电桩接线", required = true)
    private String chargePileCImage;

    /**
     * 增项收费单
     */
    @ApiModelProperty(value = "增项收费单", required = true)
    private String additionalCImage;

    /**
     * 点检表
     */
    @ApiModelProperty(value = "点检表", required = true)
    private String checkListImage;

    /**
     * 其他图片1
     */
    @ApiModelProperty(value = "其他图片1")
    private String otherImage;

    /**
     * 其他图片2
     */
    @ApiModelProperty(value = "其他图片2")
    private String otherImage2;

    /**
     * 其他图片3
     */
    @ApiModelProperty(value = "其他图片3")
    private String otherImage3;
}
