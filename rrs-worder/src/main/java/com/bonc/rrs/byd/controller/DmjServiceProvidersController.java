package com.bonc.rrs.byd.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.byd.service.DmjServiceProvidersService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Description: 到每家服务商处理控制器
 * @Author: liujunpeng
 * @Date: 2024/2/27 10:06订单审核信息推送
 * @Version: 1.0
 */
@RestController
@RequestMapping("/dmj/openapi/bydApi")
@Api(value = "/dmj/openapi/bydApi", tags = "服务商订单处理控制器")
@RequiredArgsConstructor
public class DmjServiceProvidersController {

    final DmjServiceProvidersService dmjServiceProvidersService;
    @Autowired
    private ProviderBusinessService providerBusinessService;

    @PostMapping("/pushAuditOrder")
    @ApiOperation(value = "CPIM 报修订单审核信息推送服务商接口", notes = "CPIM 报修订单审核信息推送服务商数据")
    PushApiResponse orderAuditInfoPush(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        OrderAuditPushReq orderAuditPushReq= JSON.parseObject(req.toJSONString(), OrderAuditPushReq.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(orderAuditPushReq);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 业务调用
        return dmjServiceProvidersService.orderAuditInfoPush(orderAuditPushReq);
    }

    @PostMapping("/pushSusPendOrder")
    @ApiOperation(value = "CPIM 安装订单暂停信息推送服务商接口", notes = "CPIM 安装订单暂停信息推送服务商数据")
    PushApiResponse orderPauseInfoPush(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushSusPendOrder pushSusPendOrder= JSON.parseObject(req.toJSONString(), PushSusPendOrder.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushSusPendOrder);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 业务调用
        return dmjServiceProvidersService.orderPuaseInfoPush(pushSusPendOrder);
    }

    @PostMapping("/pushRestoreOrder")
    @ApiOperation(value = "CPIM 安装订单恢复执行信息推送服务商接口", notes = "CPIM 安装订单恢复执行信息推送服务商接口")
    PushApiResponse pushRestoreOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushRestoreOrder pushRestoreOrder= JSON.parseObject(req.toJSONString(), PushRestoreOrder.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushRestoreOrder);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 业务调用
        return dmjServiceProvidersService.orderRestoreInfoPush(pushRestoreOrder);
    }


    @PostMapping("/repair/order/cancel")
    @ApiOperation(value = "CPIM 用户报修订单取消信息推送服务商接口", notes = "CPIM 用户报修订单取消信息推送服务商")
    PushApiResponse repairOrderCancel(HttpServletRequest request,@RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushRepairOrderCancel pushRepairOrderCancel= JSON.parseObject(req.toJSONString(), PushRepairOrderCancel.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushRepairOrderCancel);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 业务调用
        return dmjServiceProvidersService.repairOrderCancel(pushRepairOrderCancel);
    }

    @PostMapping("/repair/order/close")
    @ApiOperation(value = "CPIM 用户报修订单关闭信息推送服务商接口", notes = "CPIM 用户报修订单关闭信息推送服务商")
    PushApiResponse repairOrderClose(HttpServletRequest request,@RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushRepairOrderClose pushRepairOrderClose= JSON.parseObject(req.toJSONString(), PushRepairOrderClose.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushRepairOrderClose);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 业务调用
        return dmjServiceProvidersService.repairOrderClose(pushRepairOrderClose);
    }

    /**
     *  关闭订单后推送关闭订单信息
     */
    @PostMapping("/pushCloseOrder")
    public PushApiResponse pushCloseOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCloseOrder pushCloseOrder=JSON.parseObject(req.toJSONString(),PushCloseOrder.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCloseOrder);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        return dmjServiceProvidersService.installOrderClose(pushCloseOrder);
    }

    /**
     *  用户取消安装后推送取消信息至服务商
     */
    @PostMapping("/pushCancelOrder")
    public PushApiResponse pushCancelOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCancelOrder pushCancelOrder=JSON.parseObject(req.toJSONString(),PushCancelOrder.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCancelOrder);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        return dmjServiceProvidersService.installOrderCancel(pushCancelOrder);
    }

    /**
     *  厂端审核后推送审核信息
     */
    @PostMapping("/pushSubmitInfo")
    public PushApiResponse PushSubmitInfo(HttpServletRequest request, @RequestBody JSONObject req){
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushSubmitInfo pushSubmitInfo=JSON.parseObject(req.toJSONString(),PushSubmitInfo.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushSubmitInfo);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        createDefaultLoginUser();
        return dmjServiceProvidersService.companyAuditInformation(pushSubmitInfo);
    }

    @PostMapping("/pushOrder")
    @ApiOperation(value = "CPIM 推送安装订单基本安装信息给指定供应商接口", notes = "CPIM 推送安装订单基本安装信息给指定供应商接口")
    PushApiResponse pushOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        // 获取JSONObject包含集合的属性值
        JSONArray paramArray = req.getJSONArray("data");
        JSONArray tempArray =new JSONArray();
        for(int i=0;i<paramArray.size();i++){
            Map<String,Object> tempMap=JSON.parseObject(JSON.toJSONString(paramArray.get(i)),Map.class);
            tempArray.add(new TreeMap<>(tempMap));
        }
        if (!dmjServiceProvidersService.verificationArrayDataOfSignatures(request, tempArray)) {
            return new PushApiResponse("鉴权失败");
        }
        ParamBody paramBody= JSON.parseObject(req.toJSONString(), ParamBody.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(paramBody);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }

        List<PushOrderBody> data = paramBody.getData();
        List<Object> dataList = new ArrayList<>();
        for (PushOrderBody pushOrderBody : data) {
            msg = dmjServiceProvidersService.validateRequiredFields(pushOrderBody);
            if (StringUtils.isNotBlank(msg)) {
                Map map = new HashMap();
                map.put("orderCode", pushOrderBody.getOrderCode());
                map.put("reason", msg);
                dataList.add(map);
            }
        }
        if (dataList.size() > 0) {
            PushApiResponse pushApiResponse = new PushApiResponse("partially success");
            pushApiResponse.setData(dataList);
            return pushApiResponse;
        }
        // TODO: 业务调用

        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .worderTypeId(5)
                .orderSouce("byd")
                .paramBody(paramBody).build(), "pushOrder");
        return providerBusinessResult.getPushApiResponse();
    }

    private void createDefaultLoginUser() {
        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }

    @PostMapping("/pushRepairOrder")
    @ApiOperation(value = "CPIM 报修订单信息推送服务商", notes = "CPIM 报修订单信息推送服务商接口")
    PushApiResponse pushRepairOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        JSONArray paramArray = req.getJSONArray("data");
        JSONArray tempArray =new JSONArray();
        for(int i=0;i<paramArray.size();i++){
            Map<String,Object> tempMap=JSON.parseObject(JSON.toJSONString(paramArray.get(i)),Map.class);
            tempArray.add(new TreeMap<>(tempMap));
        }
        if (!dmjServiceProvidersService.verificationArrayDataOfSignatures(request, tempArray)) {
            return new PushApiResponse("鉴权失败");
        }
        PushRepairOrderBody pushRepairOrderBody= JSON.parseObject(req.toJSONString(), PushRepairOrderBody.class);
        // TODO: 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushRepairOrderBody);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        List<PushRepairOrderData> data = pushRepairOrderBody.getData();
        List<Object> dataList = new ArrayList<>();
        for (PushRepairOrderData pushRepairOrderData : data) {
            msg = dmjServiceProvidersService.validateRequiredFields(pushRepairOrderData);
            if (StringUtils.isNotBlank(msg)) {
                Map map = new HashMap();
                map.put("orderCode", pushRepairOrderData.getOrderCode());
                map.put("reason", msg);
                dataList.add(map);
            }
            // 移装订单必填字段校验
            if ("30".equals(pushRepairOrderData.getRequirmentType())) {
                String validateMsg = dmjServiceProvidersService.validateTransferPileRequiredFields(pushRepairOrderData);
                if (validateMsg != null) {
                    Map map = new HashMap();
                    map.put("orderCode", pushRepairOrderData.getOrderCode());
                    map.put("reason", validateMsg);
                    dataList.add(map);
                }
            }
        }
        if (dataList.size() > 0) {
            PushApiResponse pushApiResponse = new PushApiResponse("partially success");
            pushApiResponse.setData(dataList);
            return pushApiResponse;
        }
        // TODO: 业务调用
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .worderTypeId(6)
                .orderSouce("byd")
                .pushRepairOrderBody(pushRepairOrderBody).build(), "pushRepairOrder");
        return providerBusinessResult.getPushApiResponse();
    }

    @PostMapping("/pushUpdateOrder")
    @ApiOperation(value = "CPIM 安装订单信息更新推送服务商服务商", notes = "CPIM 安装订单信息更新推送服务商服务商")
    PushApiResponse pushUpdateOrder(HttpServletRequest request,@RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        // TODO: 参数校验
        PushUpdateOrder pushUpdateOrder = JSON.parseObject(req.toJSONString(), PushUpdateOrder.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushUpdateOrder);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 参数校验
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .pushUpdateOrder(pushUpdateOrder).build(), "pushUpdateOrder");
        return providerBusinessResult.getPushApiResponse();
    }

    @PostMapping("/pushNotifyMsg")
    @ApiOperation(value = "CPIM推送消息通知给指定供应商接口", notes = "CPIM推送消息通知给指定供应商接口")
    PushApiResponse pushNotifyMsg(HttpServletRequest request, @RequestBody JSONObject req) {
        // TODO: 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        // TODO: 参数校验
        PushNotifyMsg pushNotifyMsg = JSON.parseObject(req.getString("data"), PushNotifyMsg.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(pushNotifyMsg);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // TODO: 参数校验
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .pushNotifyMsg(pushNotifyMsg).build(), "pushNotifyMsg");
        return providerBusinessResult.getPushApiResponse();
    }

    @PostMapping("/claim/orderFee")
    @ApiOperation(value = "CPIM索赔订单费用推送服务商", notes = "CPIM索赔订单费用推送服务商")
    PushApiResponse claimOrderFee(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        // 参数校验
        ClaimOrderFeeDto claimOrderFee = JSON.parseObject(req.toJSONString(), ClaimOrderFeeDto.class);
        String msg = dmjServiceProvidersService.validateRequiredFields(claimOrderFee);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .claimOrderFee(claimOrderFee).build(), "claimOrderFee");
        return providerBusinessResult.getPushApiResponse();
    }

    // ==================== 检查订单相关接口 ====================

    @PostMapping("/pushCheckOrderAudit")
    @ApiOperation(value = "CPIM 检查订单审核信息推送服务商接口", notes = "CPIM 检查订单审核信息推送服务商数据")
    PushApiResponse checkOrderAuditInfoPush(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        CheckOrderAuditPushReq checkOrderAuditPushReq = JSON.parseObject(req.toJSONString(), CheckOrderAuditPushReq.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(checkOrderAuditPushReq);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return dmjServiceProvidersService.checkOrderAuditInfoPush(checkOrderAuditPushReq);
    }

    @PostMapping("/pushCheckOrderCancel")
    @ApiOperation(value = "CPIM 用户取消检查订单后推送取消信息至服务商", notes = "CPIM 用户取消检查订单后推送取消信息至服务商")
    PushApiResponse pushCheckOrderCancel(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderCancel pushCheckOrderCancel = JSON.parseObject(req.toJSONString(), PushCheckOrderCancel.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCheckOrderCancel);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return dmjServiceProvidersService.checkOrderCancel(pushCheckOrderCancel);
    }

    @PostMapping("/pushCheckOrderClose")
    @ApiOperation(value = "CPIM 关闭检查订单后推送关闭订单信息", notes = "CPIM 关闭检查订单后推送关闭订单信息")
    PushApiResponse pushCheckOrderClose(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderClose pushCheckOrderClose = JSON.parseObject(req.toJSONString(), PushCheckOrderClose.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCheckOrderClose);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return dmjServiceProvidersService.checkOrderClose(pushCheckOrderClose);
    }

    @PostMapping("/pushCheckOrderSuspend")
    @ApiOperation(value = "CPIM 检查订单暂停信息推送服务商", notes = "CPIM 检查订单暂停信息推送服务商")
    PushApiResponse pushCheckOrderSuspend(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderSuspend pushCheckOrderSuspend = JSON.parseObject(req.toJSONString(), PushCheckOrderSuspend.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCheckOrderSuspend);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return dmjServiceProvidersService.checkOrderSuspend(pushCheckOrderSuspend);
    }

    @PostMapping("/pushCheckOrderRestore")
    @ApiOperation(value = "CPIM 检查订单恢复执行信息推送服务商", notes = "CPIM 检查订单恢复执行信息推送服务商")
    PushApiResponse pushCheckOrderRestore(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        if (!dmjServiceProvidersService.verificationOfSignatures(request, req)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderRestore pushCheckOrderRestore = JSON.parseObject(req.toJSONString(), PushCheckOrderRestore.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(pushCheckOrderRestore);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }
        // 业务调用
        return dmjServiceProvidersService.checkOrderRestore(pushCheckOrderRestore);
    }

    @PostMapping("/pushCheckOrder")
    @ApiOperation(value = "CPIM 推送检查订单基本信息给指定供应商接口", notes = "CPIM 推送检查订单基本信息给指定供应商接口")
    PushApiResponse pushCheckOrder(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        // 获取JSONObject包含集合的属性值
        JSONArray paramArray = req.getJSONArray("data");
        JSONArray tempArray = new JSONArray();
        for (int i = 0; i < paramArray.size(); i++) {
            Map<String, Object> tempMap = JSON.parseObject(JSON.toJSONString(paramArray.get(i)), Map.class);
            tempArray.add(new TreeMap<>(tempMap));
        }
        if (!dmjServiceProvidersService.verificationArrayDataOfSignatures(request, tempArray)) {
            return new PushApiResponse("鉴权失败");
        }
        PushCheckOrderBody paramBody = JSON.parseObject(req.toJSONString(), PushCheckOrderBody.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(paramBody);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }

        // 业务调用
        createDefaultLoginUser();
        Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder()
                .worderTypeId(8)
                .orderSouce("byd")
                .pushCheckOrderBody(paramBody).build(), "pushCheckOrder");
        return providerBusinessResult.getPushApiResponse();
    }

    // ==================== 2.14 和 2.15 接口 ====================

    @PostMapping("/pushRiskInfo")
    @ApiOperation(value = "CPIM 推送安装订单风险信息", notes = "CPIM 推送安装订单风险信息")
    PushApiResponse pushRiskInfo(HttpServletRequest request, @RequestBody JSONObject req) {
        // 验签
        // 获取JSONObject包含集合的属性值
        JSONArray paramArray = req.getJSONArray("data");
        JSONArray tempArray = new JSONArray();
        for (int i = 0; i < paramArray.size(); i++) {
            Map<String, Object> tempMap = JSON.parseObject(JSON.toJSONString(paramArray.get(i)), Map.class);
            tempArray.add(new TreeMap<>(tempMap));
        }
        if (!dmjServiceProvidersService.verificationArrayDataOfSignatures(request, tempArray)) {
            return new PushApiResponse("鉴权失败");
        }
        RiskParamBody paramBody= JSON.parseObject(req.toJSONString(), RiskParamBody.class);
        // 参数校验
        String msg = dmjServiceProvidersService.validateRequiredFields(paramBody);
        if (StringUtils.isNotBlank(msg)) {
            return new PushApiResponse(msg);
        }

        List<PushRiskInfo> data = paramBody.getData();
        // 业务调用
        return dmjServiceProvidersService.pushRiskInfo(data);
    }

}
