package com.bonc.rrs.workManager.controller;

import com.bonc.rrs.workManager.dao.BrandMapper;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.workManager.service.WordService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sys/word")
public class WordController {
    @Autowired
    SysFilesService sysFilesService;
    @Autowired(required = false)
    BrandMapper brandMapper;
    @Autowired
    WordService wordService;

    //校验是否有文件接口
    @RequestMapping("/findByValue")
    public R findByValue(String worderNo, Integer purpose) {
        //判断文件是否存在
        String name = (purpose == 2 ? "勘测报告-word" : "安装报告-word");
        Boolean aBoolean = sysFilesService.fingByValue(worderNo, name);
        if (!aBoolean) {
            return R.error("该工单已有报告文件,确认要生成/上传文件吗？");
        }
        return R.ok();
    }

//    *
//     * 一键生成
//    @RequestMapping("/generation")
//    public R generation(String worderNo,Integer purpose) throws Exception {
//        String name=(purpose==2?"勘测报告-word":"安装报告-word");
//        List<Map<String, Object>> images = sysFilesService.getUrl(worderNo, purpose);
//        //勘测
//        if(purpose==2){
//            R substitute = wordService.substitute(images);
//            //上传文件到oss
//            File files = new File(WordConfigs.getReportPath());
//            R uploadurvey = wordService.uploadurvey(files, images, worderNo,name);
//            return uploadurvey;
//        }else{
//            //安装
//            R substitute = wordService.install(images);
//            //上传文件到oss
//            File files = new File(WordConfigs.installPath());
//            R uploadurvey = wordService.uploadurvey(files, images, worderNo,name);
//            return uploadurvey;
//        }
//    }

    /**
     * 根据工单查品牌
     */
    @RequestMapping("/getBrandCarID")
    public Integer getBrandCarID(String worderNo){
        Integer id = brandMapper.selectBrandCarID(worderNo);
        return id;
    }
}
