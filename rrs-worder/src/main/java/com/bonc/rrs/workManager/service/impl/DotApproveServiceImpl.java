package com.bonc.rrs.workManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.entity.BalanceFileEntity;
import com.bonc.rrs.balanceprocess.entity.DrivingPermitApproveEntity;
import com.bonc.rrs.balanceprocess.entity.DrivingPermitEntity;
import com.bonc.rrs.balanceprocess.service.SerialNoVersionService;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.worder.dao.WorderAuditRecordDao;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.worder.entity.WorderAuditRecordEntity;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.entity.vo.*;
import com.bonc.rrs.workManager.enums.DotApproveStatusEnum;
import com.bonc.rrs.workManager.enums.DotApproveTypeEnum;
import com.bonc.rrs.workManager.service.*;
import com.ctc.wstx.util.StringUtil;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.MapUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.MagrAreaBrandMapper;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.vo.*;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserRoleService;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 网点审批
 * @Author: liujunpeng
 * @Date: 2023/7/25 14:02
 * @Version: 1.0
 */
@Service
@AllArgsConstructor
@Log4j2
public class DotApproveServiceImpl extends ServiceImpl<DotApproveInfoMapper, DotApproveInfo> implements DotApproveService {

    final SysUserRoleService sysUserRoleService;

    final SerialNoVersionService serialNoVersionService;

    final DotApproveDiffService dotApproveDiffService;

    final UserRoleAreaPermissionsService userRoleAreaPermissionsService;

    final DotAreaService dotAreaService;

    final WorkMsgService workMsgService;

    final SysUserDao sysUserDao;

    final DotInfoMapper dotInfoMapper;

    final DotContactsMapper dotContactsMapper;

    final BrandDao brandDao;

    final SysDictionaryDetailDao sysDictionaryDetailDao;

    final SysFilesMapper sysFilesMapper;

    final WorderAuditRecordDao worderAuditRecordDao;

    final MagrAreaBrandMapper magrAreaBrandMapper;

    final DotAreaMapper dotAreaMapper;


    @Override
    @Transactional
    public R updateInfoApplication(DotApproveInfoVo dotApproveInfoVo) throws InvocationTargetException, IllegalAccessException {
        DotInfo dotInfo = dotApproveInfoVo.getDotInfo();
        //查询是否存在在途审批单
        LambdaQueryWrapper<DotApproveInfo> approveWrapper = Wrappers.lambdaQuery();
        approveWrapper.in(DotApproveInfo::getStatus, 0, 1, 2).eq(DotApproveInfo::getDeleteState, 0).eq(DotApproveInfo::getVCode, dotInfo.getvCode());
        Integer count = this.baseMapper.selectCount(approveWrapper);
        if (count > 0) {
            return R.error("该网点V码已经存在在途的审批单号");
        }

        DotInfo work = dotApproveInfoVo.getDotInfo();
        DotContacts dotContact = dotApproveInfoVo.getDotContacts();

        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }

        Integer dotId = work.getDotId();
        if (dotId != null) {
            // 校验唯一 contactsName网点管理员账号，dotCode网点编码，dotName网点名称
            Integer userCount = sysUserDao.selectCount(new QueryWrapper<SysUserEntity>().eq("username", dotContact.getContactsName()).ne("user_id", dotContact.getContactsId()).ne("status", 0));
            if (userCount > 0) {
                return R.error("该管理员账号已存在");
            }
            Integer userPhoneCount = sysUserDao.selectCount(new QueryWrapper<SysUserEntity>().eq("mobile", dotContact.getContactsPhone()).ne("user_id", dotContact.getContactsId()).ne("status", 0));
            if (userPhoneCount > 0) {
                return R.error("该管理员手机号已存在");
            }
            Integer dotNameCount = dotInfoMapper.selectCount(new QueryWrapper<DotInfo>().eq("dot_name", work.getDotName()).ne("dot_id", work.getDotId()).ne("is_delete", 1));
            if (dotNameCount > 0) {
                return R.error("该网点名称已存在");
            }
            Integer dotCodeCount = dotInfoMapper.selectCount(new QueryWrapper<DotInfo>().eq("dot_code", work.getDotCode()).ne("dot_id", work.getDotId()).ne("is_delete", 1));
            if (dotCodeCount > 0) {
                return R.error("该网点编码已存在");
            }
            Integer dotVCodeCount = dotInfoMapper.selectCount(new QueryWrapper<DotInfo>().eq("v_code", work.getvCode()).ne("dot_id", work.getDotId()).ne("is_delete", 1));
            if (dotVCodeCount > 0) {
                return R.error("该网点V码已存在");
            }
            List<AreaBrandGroupVo> areaBrandGroupList = dotApproveInfoVo.getAreaBrandGroupList();
            if (areaBrandGroupList == null || areaBrandGroupList.isEmpty()) {
                return R.error("区域品牌组不能为空");
            }
            if (StringUtils.isEmpty(work) || StringUtils.isEmpty(dotContact)) {
                return R.error("缺少相关参数");
            }
            long gCcount = areaBrandGroupList.stream().filter(item -> item.getGroupId() == null).count();
            if (gCcount > 0) {
                return R.error("修改失败，请刷新页面重新操作");
            }
        } else {
            // 校验唯一 contactsName网点管理员账号，dotCode网点编码，dotName网点名称
            List<SysUserEntity> list = sysUserDao.selectByMap(new MapUtils().put("username", dotContact.getContactsName()));
            if (list.size() > 0) {
                return R.error("该管理员账号已存在");
            }
            List<SysUserEntity> userList = sysUserDao.selectByMap(new MapUtils().put("mobile", dotContact.getContactsPhone()));
            if (userList.size() > 0) {
                return R.error("该管理员手机号已存在");
            }
            List<DotInfo> dotInfoList = dotInfoMapper.selectByMap(new MapUtils().put("dot_name", work.getDotName()));
            if (dotInfoList.size() > 0) {
                return R.error("该网点名称已存在");
            }
            List<DotInfo> dotInfoList2 = dotInfoMapper.selectByMap(new MapUtils().put("dot_code", work.getDotCode()));
            if (dotInfoList2.size() > 0) {
                return R.error("该网点编码已存在");
            }
            Integer dotVCodeCount = dotInfoMapper.selectCount(new QueryWrapper<DotInfo>().eq("v_code", work.getvCode()));
            if (dotVCodeCount > 0) {
                return R.error("该网点V码已存在");
            }
            List<AreaBrandGroupVo> areaBrandGroupList = dotApproveInfoVo.getAreaBrandGroupList();
            if (areaBrandGroupList == null || areaBrandGroupList.isEmpty()) {
                return R.error("区域品牌组不能为空");
            }
            long gCcount = areaBrandGroupList.stream().filter(item -> item.getGroupId() == null).count();
            if (gCcount > 0) {
                return R.error("修改失败，请刷新页面重新操作");
            }
        }
        //保存或者更改
        return saveOrUpdateDotApprove(dotApproveInfoVo);
    }

    @Override
    @Transactional
    public R statusApplication(DotStatusUpdateVo dotStatusUpdateVo) {
        //获取登陆信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        //角色集合
        List<Long> roleIdList = user.getRoleIdList();

        Integer id = dotStatusUpdateVo.getId();
        if (id == null) {
            return R.error("网点ID不能为空");
        }
        Integer status = dotStatusUpdateVo.getStatus();
        if (status == null) {
            return R.error("操作状态不能为空");
        }

        List<Integer> roles = roleIdList.stream().map(item -> Integer.parseInt(item.toString())).collect(Collectors.toList());
        //权限拦截
        if (status == -1 || status == 5) {
            if (!dotAreaService.checkUserRoleArea(id, Math.toIntExact(user.getUserId()), roles)) {
                return R.error("权限不足，网点存在权限以外的辐射区域无法删除！");
            }
        }

        //根据网点ID查询网点信息
        LambdaQueryWrapper<DotInfo> dotWrapper = Wrappers.lambdaQuery();
        dotWrapper.select(DotInfo::getDotId, DotInfo::getvCode, DotInfo::getDotState, DotInfo::getDotName, DotInfo::getDotArea, DotInfo::getDotCity, DotInfo::getDotDistrict, DotInfo::getDotAddress);
        dotWrapper.eq(DotInfo::getDotId, id);
        DotInfo dotInformation = dotInfoMapper.selectOne(dotWrapper);
        //查询网点联系人信息
        LambdaQueryWrapper<DotContacts> contactsWrapper = Wrappers.lambdaQuery();
        contactsWrapper.eq(DotContacts::getDotCode, dotInformation.getDotCode()).eq(DotContacts::getIsDelete, 0);
        List<DotContacts> dotContacts = dotContactsMapper.selectList(contactsWrapper);

        //查询是否存在在途审批单
        LambdaQueryWrapper<DotApproveInfo> approveWrapper = Wrappers.lambdaQuery();
        approveWrapper.in(DotApproveInfo::getStatus, 0, 1, 2).eq(DotApproveInfo::getDeleteState, 0).eq(DotApproveInfo::getVCode, dotInformation.getvCode());
        Integer count = this.baseMapper.selectCount(approveWrapper);
        if (count > 0) {
            return R.error("该网点V码已经存在在途的审批单号");
        }

        DotApproveInfo dotApproveInfo = new DotApproveInfo();
        //生成网点审批单号
        String companyInvoiceNo = serialNoVersionService.businessNoMaker('d');
        // 0：无数据变更，1：基本信息，2：区域品牌，3：新建，4：区域品牌和基础信息，5：删除，6：停工，7：冻结，8：撤点，9：复工
        int type = 0;
        switch (status) {
            case -1:
                type = 5;
                break;
            case 4:
                type = 6;
                break;
            case 3:
                type = 7;
                break;
            case 5:
                type = 8;
                break;
            case 1:
                type = 9;
                break;
        }
        if (!dotContacts.isEmpty()) {
            dotApproveInfo.setContactsName(dotContacts.get(0).getContactsName());
        }
        dotApproveInfo.setDotId(dotInformation.getDotId());
        dotApproveInfo.setDotCode(dotInformation.getDotCode());
        dotApproveInfo.setApproveNo(companyInvoiceNo);
        dotApproveInfo.setOperationType(dotStatusUpdateVo.getOperationType());
        dotApproveInfo.setVCode(dotInformation.getvCode())
                .setDotName(dotInformation.getDotName())
                .setDotArea(Integer.parseInt(dotInformation.getDotArea()))
                .setDotCity(Integer.parseInt(dotInformation.getDotCity()))
                .setDotDistrict(Integer.parseInt(dotInformation.getDotDistrict())).setDotAddress(dotInformation.getDotAddress())
                .setReason(dotStatusUpdateVo.getReason())
                .setFileId(dotStatusUpdateVo.getFileId())
                .setCreateUser(user.getEmployeeName())
                .setStatus(1)
                .setType(type);
        this.baseMapper.insert(dotApproveInfo);
        //网点状态
        DotApproveDiff dotApproveDiff = new DotApproveDiff();
        dotApproveDiff.setApproveId(dotApproveInfo.getId());
        dotApproveDiff.setType(1);
        dotApproveDiff.setName("网点状态");
        dotApproveDiff.setOldValue(getStatus(dotInformation.getDotState()));
        if (status == -1) {
            dotApproveDiff.setNewValue("删除");
        } else {
            dotApproveDiff.setNewValue(getStatus(status.toString()));
        }
        dotApproveDiffService.save(dotApproveDiff);
        return R.ok();
    }

    @Override
    public R listRecord(DotApproveListVo dotApproveListVo) {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        List<Long> roleIdList = user.getRoleIdList();
        //权限
        if (user.getUserId() == 1L) {
            //admin可以查看所有
        } else if (roleIdList.contains(4L)) {
            //网点管理员
            dotApproveListVo.setContactsName(user.getUsername());
        } else {
            //查询当前用户的区域
            List<Integer> areaIds = magrAreaBrandMapper.selectAreaIdByUserId(user.getUserId());
            if (areaIds == null || areaIds.isEmpty()) {
                return R.ok().put("list", new ArrayList<>()).put("totalCount", 0);
            }
            dotApproveListVo.setAreaIds(areaIds.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")));
        }

        IPage<DotApproveInfo> page = this.baseMapper.selectListRecord(new Page<>(dotApproveListVo.getPage(), dotApproveListVo.getPageSize()), dotApproveListVo);
        List<DotApproveInfo> records = page.getRecords();
        //查询对应的审批记录
        List<String> approveNos = records.stream().map(DotApproveInfo::getApproveNo).collect(Collectors.toList());
        if (!approveNos.isEmpty()) {
            LambdaQueryWrapper<WorderAuditRecordEntity> auditRecordWrapper = Wrappers.lambdaQuery();
            auditRecordWrapper.in(WorderAuditRecordEntity::getApplyNo, approveNos).eq(WorderAuditRecordEntity::getAuditType, 4);
            List<WorderAuditRecordEntity> worderAuditRecordEntities = worderAuditRecordDao.selectList(auditRecordWrapper);
            records.forEach(item -> setAuditInfo(item, worderAuditRecordEntities));
        }
        //翻译字段
        records.forEach(this::translateField);
        return R.ok().put("list", records).put("totalCount", page.getTotal());
    }

    @Override
    public R getApproveDetail(Integer approveId) {
        if (approveId == null) {
            return R.error("审批单详情查询失败");
        }
        //查询审批单
        DotApproveInfo dotApproveInfo = this.baseMapper.selectById(approveId);
        dotApproveInfo.setTypeName(DotApproveTypeEnum.getName(dotApproveInfo.getType()));
        dotApproveInfo.setStatusName(DotApproveStatusEnum.getName(dotApproveInfo.getStatus()));

        //获取审批单的附件信息
        if (dotApproveInfo.getFileId() != null) {
            LambdaQueryWrapper<BalanceFileEntity> fileWrapper = Wrappers.lambdaQuery();
            fileWrapper.eq(BalanceFileEntity::getFileId, dotApproveInfo.getFileId());
            BalanceFileEntity fileEntity = sysFilesMapper.selectOne(fileWrapper);
            dotApproveInfo.setFileUrl(fileEntity.getPath());
        }
        //查询差异数据
        LambdaQueryWrapper<DotApproveDiff> diffWrapper = Wrappers.lambdaQuery();
        diffWrapper.eq(DotApproveDiff::getApproveId, approveId).orderByAsc(DotApproveDiff::getOrderby);
        List<DotApproveDiff> dotApproveDiffs = dotApproveDiffService.getBaseMapper().selectList(diffWrapper);

        //查询审批记录
        if (StringUtils.isNotBlank(dotApproveInfo.getApproveNo())) {
            LambdaQueryWrapper<WorderAuditRecordEntity> auditRecordWrapper = Wrappers.lambdaQuery();
            auditRecordWrapper.eq(WorderAuditRecordEntity::getApplyNo, dotApproveInfo.getApproveNo()).eq(WorderAuditRecordEntity::getAuditType, 4);
            List<WorderAuditRecordEntity> worderAuditRecordEntities = worderAuditRecordDao.selectList(auditRecordWrapper);
            setAuditInfo(dotApproveInfo, worderAuditRecordEntities);
        }

        return R.ok().put("approveInfo", dotApproveInfo).put("approveDiff", dotApproveDiffs);
    }

    @Override
    public R getList(DotApproveListVo dotApproveListVo) {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        List<Long> roleIdList = user.getRoleIdList();
        //权限
        if (user.getUserId() == 1L) {
            //admin可以查看所有
        } else if (roleIdList.contains(4L)) {
            //网点管理员
            dotApproveListVo.setContactsName(user.getUsername());
        } else {
            //查询当前用户的区域
            List<Integer> areaIds = magrAreaBrandMapper.selectAreaIdByUserId(user.getUserId());
            if (areaIds == null || areaIds.isEmpty()) {
                return R.ok().put("list", new ArrayList<>()).put("totalCount", 0);
            }
            dotApproveListVo.setAreaIds(areaIds.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")));
        }
        IPage<DotApproveInfo> page = this.baseMapper.selectListPage(new Page<>(dotApproveListVo.getPage(), dotApproveListVo.getPageSize()), dotApproveListVo);
        List<DotApproveInfo> records = page.getRecords();
        //翻译字段
        records.forEach(this::translateField);
        return R.ok().put("list", records).put("totalCount", page.getTotal());
    }

    @Override
    @Transactional
    public R notPassed(DotApproveAuditVo dotApproveAuditVo) {
        List<Integer> approveIds = dotApproveAuditVo.getApproveIds();
        if (approveIds == null || approveIds.isEmpty()) {
            return R.error("缺号审批单号");
        }
        //获取登陆信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        //查询对应的审批单
        LambdaQueryWrapper<DotApproveInfo> approveInfoWrapper = Wrappers.lambdaQuery();
        approveInfoWrapper.in(DotApproveInfo::getId, approveIds);
        List<DotApproveInfo> dotApproveInfos = this.baseMapper.selectList(approveInfoWrapper);
        for (DotApproveInfo dotApproveInfo : dotApproveInfos) {
            if (dotApproveAuditVo.getStatus() == -1 && dotApproveInfo.getStatus() != 1) {
                return R.error("网点审批单：" + dotApproveInfo.getDotName() + "不在一审状态");
            }

            if (dotApproveAuditVo.getStatus() == -2 && dotApproveInfo.getStatus() != 2) {
                return R.error("网点审批单：" + dotApproveInfo.getDotName() + "不在二审状态");
            }
            //生成审批记录
            WorderAuditRecordEntity recordEntity = new WorderAuditRecordEntity();
            recordEntity.setApplyNo(dotApproveInfo.getApproveNo());
            recordEntity.setAuditType(4);
            recordEntity.setAuditUserId(user.getUserId().intValue());
            recordEntity.setReceiveUserId(user.getUserId().intValue());
            recordEntity.setAuditStatus(dotApproveAuditVo.getStatus());
            recordEntity.setNoPassReason(dotApproveAuditVo.getNotPassedReason());
            worderAuditRecordDao.insert(recordEntity);
            //更新审批单状态
            dotApproveInfo.setStatus(dotApproveAuditVo.getStatus());
            dotApproveInfo.setUpdateTime(LocalDateTime.now());
            this.baseMapper.updateById(dotApproveInfo);
        }
        return R.ok();
    }

    @Override
    @Transactional
    public R passed(DotApproveAuditVo dotApproveAuditVo) {
        List<Integer> approveIds = dotApproveAuditVo.getApproveIds();
        if (approveIds == null || approveIds.isEmpty()) {
            return R.error("缺号审批单号");
        }
        //获取登陆信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }

        //查询对应的审批单
        LambdaQueryWrapper<DotApproveInfo> approveInfoWrapper = Wrappers.lambdaQuery();
        approveInfoWrapper.in(DotApproveInfo::getId, approveIds);
        List<DotApproveInfo> dotApproveInfos = this.baseMapper.selectList(approveInfoWrapper);
        //一审通过
        if (dotApproveAuditVo.getStatus() == 1) {
            for (DotApproveInfo dotApproveInfo : dotApproveInfos) {
                if (dotApproveInfo.getStatus() != 1) {
                    return R.error("网点审批单：" + dotApproveInfo.getDotName() + "不在一审状态");
                }
                //生成审批记录
                WorderAuditRecordEntity recordEntity = new WorderAuditRecordEntity();
                recordEntity.setApplyNo(dotApproveInfo.getApproveNo());
                recordEntity.setAuditType(4);
                recordEntity.setAuditUserId(user.getUserId().intValue());
                recordEntity.setReceiveUserId(user.getUserId().intValue());
                recordEntity.setAuditStatus(dotApproveAuditVo.getStatus());
                worderAuditRecordDao.insert(recordEntity);
                //更新审批单状态
                dotApproveInfo.setUpdateTime(LocalDateTime.now());
                dotApproveInfo.setStatus(2);
                this.baseMapper.updateById(dotApproveInfo);
            }
        } else {
            //二审通过
            for (DotApproveInfo dotApproveInfo : dotApproveInfos) {

                //新增和编辑时校验
                //1：基本信息，2：区域品牌，3：新建，4：区域品牌和基础信息，5：删除，6：停工，7：冻结，8：撤点，9：复工
                if (dotApproveInfo.getType() == 3 || dotApproveInfo.getType() == 1 || dotApproveInfo.getType() == 4) {
                    //校验管理员账号
                    int userCount = this.baseMapper.checkRepeatContacts(dotApproveInfo.getId(), dotApproveInfo.getType());
                    if (userCount > 0) {
                        return R.error("网点：" + dotApproveInfo.getDotName() + "，该管理员账号已存在");
                    }

                    //校验管理员账号
                    int userPhoneCount = this.baseMapper.checkRepeatContactsPhone(dotApproveInfo.getId(), dotApproveInfo.getType());
                    if (userPhoneCount > 0) {
                        return R.error("网点：" + dotApproveInfo.getDotName() + "，该管理员手机号已存在");
                    }
                    //校验网点名称是否已经存在
                    int dotNameCount = this.baseMapper.checkRepeatDotName(dotApproveInfo.getId(), dotApproveInfo.getType());
                    if (dotNameCount > 0) {
                        return R.error("网点：" + dotApproveInfo.getDotName() + "，该网点名称已存在");
                    }

                    //校验网点编码是否已经存在
                    int dotCodeCount = this.baseMapper.checkRepeatDotCode(dotApproveInfo.getId(), dotApproveInfo.getType());
                    if (dotCodeCount > 0) {
                        return R.error("网点：" + dotApproveInfo.getDotName() + "，该网点编码已存在");
                    }

                    //校验网点V码是否已经存在
                    int dotVCodeCount = this.baseMapper.checkRepeatDotVCode(dotApproveInfo.getId(), dotApproveInfo.getType());
                    if (dotVCodeCount > 0) {
                        return R.error("网点：" + dotApproveInfo.getDotName() + "，该网点V码已存在");
                    }
                }

                if (dotApproveInfo.getStatus() != 2) {
                    return R.error("网点审批单：" + dotApproveInfo.getDotName() + "不在二审状态");
                }
                synchronizeData(dotApproveInfo);
                //生成审批记录
                WorderAuditRecordEntity recordEntity = new WorderAuditRecordEntity();
                recordEntity.setApplyNo(dotApproveInfo.getApproveNo());
                recordEntity.setAuditType(4);
                recordEntity.setAuditUserId(user.getUserId().intValue());
                recordEntity.setReceiveUserId(user.getUserId().intValue());
                recordEntity.setAuditStatus(2);
                worderAuditRecordDao.insert(recordEntity);
                //更新审批单状态
                dotApproveInfo.setUpdateTime(LocalDateTime.now());
                dotApproveInfo.setStatus(3);
                this.baseMapper.updateById(dotApproveInfo);
            }
        }
        return R.ok();
    }

    @Override
    public R queryApproveByDotId(Integer dotId) {
        if(dotId == null){
            return R.error("网点不能为空");
        }

        //获取登陆信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }

        //查询是否存在在途审批单
        LambdaQueryWrapper<DotApproveInfo> approveWrapper = Wrappers.lambdaQuery();
        approveWrapper.in(DotApproveInfo::getStatus, 0, 1, 2).eq(DotApproveInfo::getDeleteState, 0).eq(DotApproveInfo::getDotId, dotId);
        Integer count = this.baseMapper.selectCount(approveWrapper);
        if (count > 0) {
            return R.error(1,"当前网点已存在途审批单，无法编辑，确认是否只能查看？");
        }
        //查询当前网点是否存在该账户权限以外的区域
        List<Integer> roleIdList = user.getRoleIdList().stream().map(item -> item.intValue()).collect(Collectors.toList());
        //获取当前项目经理用户对应的区域
        List<String> userAreaIds = userRoleAreaPermissionsService.queryUserRoleCompetenceConfig("dotInformationRoleConfig", user.getUserId().intValue(), roleIdList);
        if(userAreaIds.isEmpty()){
            return R.ok();
        }
        //查询网点对应的区域
        LambdaQueryWrapper<DotArea> dotAreaWrapper = Wrappers.lambdaQuery();
        dotAreaWrapper.select(DotArea::getAreaId);
        dotAreaWrapper.eq(DotArea::getDotId, dotId).eq(DotArea::getIsDelete, 0);
        List<DotArea> dotAreas = dotAreaMapper.selectList(dotAreaWrapper);
        if(dotAreas != null && !dotAreas.isEmpty()){
            Set<Integer> areaIds = dotAreas.stream().map(DotArea::getAreaId).collect(Collectors.toSet());
            areaIds.removeIf(item -> userAreaIds.contains(item.toString()));
            if(!areaIds.isEmpty()){
                return R.error(1,"此网点服务范围包含操作人员权限范围外的跨多区域或品牌，需更高权限的人员处理，请确认只能查看？");
            }
        }
        return R.ok();
    }
    /**
     * 同步数据
     *
     * @param dotApproveInfo
     */
    private void synchronizeData(DotApproveInfo dotApproveInfo) {
        //新建
        if (dotApproveInfo.getType() == 3) {
            //同步网点信息
            this.baseMapper.insertDotInfo(dotApproveInfo.getId());
            //查询网点ID
            Integer dotId = this.baseMapper.selectDotIdByVCode(dotApproveInfo.getVCode());
            dotApproveInfo.setDotId(dotId);
            //根据审批单ID查询网点联系人审批表
            DotContactsApprove dotContactsApprove = this.baseMapper.selectDotContacts(dotApproveInfo.getId());
            //同步网点区域信息
            String salt = RandomStringUtils.randomAlphanumeric(20);
            String password = "admin";
            SysUserEntity sysUser = new SysUserEntity();
            sysUser.setUsername(dotContactsApprove.getContactsName());
            sysUser.setPassword(new Sha256Hash(password, salt).toHex());
            sysUser.setSalt(salt);
            sysUser.setStatus(1);
            sysUser.setRoleName("网点管理员");
            sysUser.setCreateTime(new Date());
            sysUser.setMobile(dotContactsApprove.getContactsPhone());
            sysUserDao.insert(sysUser);
            List<Long> rolelist = new ArrayList<>();
            rolelist.add((long) 4);
            sysUserRoleService.saveOrUpdate(sysUser.getUserId(), rolelist);
            //同步网点联系人信息
            this.baseMapper.insertDotContacts(dotApproveInfo.getId(), sysUser.getUserId());
            //同步网点品牌信息
            this.baseMapper.insertDotArea(dotApproveInfo.getId(), dotId);
            this.baseMapper.insertDotBrand(dotApproveInfo.getId(), dotId);
            //同步行驶证信息
            this.baseMapper.insertDrivingPermit(dotApproveInfo.getId(), dotId);
            this.baseMapper.insertDotBank(dotApproveInfo.getId(), dotId);
        } else if (dotApproveInfo.getType() == 1 || dotApproveInfo.getType() == 2 || dotApproveInfo.getType() == 4) {
            //同步网点信息
            this.baseMapper.updateDotInfo(dotApproveInfo.getId());
            //同步网点联系人信息
            //根据审批单ID查询网点联系人审批表
            DotContactsApprove dotContactsApprove = this.baseMapper.selectDotContacts(dotApproveInfo.getId());
            //修改sys_file 手机号
            sysUserDao.updateMobile(dotContactsApprove.getContactsPhone(), dotContactsApprove.getContactsName());
            this.baseMapper.updateDotContacts(dotApproveInfo.getId());
            dotInfoMapper.updateSysUserName(dotContactsApprove.getContactsId(), dotContactsApprove.getContactsName());
            if (dotApproveInfo.getDotId() != null) {
                //删除网点区域
                dotInfoMapper.deleteDotArea(dotApproveInfo.getDotId());
                //删除网点品牌
                dotInfoMapper.deleteDotBrand(dotApproveInfo.getDotId());
            }
            //同步网点区域品牌信息
            this.baseMapper.insertDotArea(dotApproveInfo.getId(), dotApproveInfo.getDotId());
            this.baseMapper.insertDotBrand(dotApproveInfo.getId(), dotApproveInfo.getDotId());
            //插入/更新行驶证
            dotInfoMapper.deleteDrivingPermitApproveStatus(dotApproveInfo.getDotId());
            dotInfoMapper.deleteDotBankStatus(dotApproveInfo.getDotId());
            this.baseMapper.insertDrivingPermit(dotApproveInfo.getId(), dotApproveInfo.getDotId());
            this.baseMapper.updateDrivingPermit(dotApproveInfo.getId(), dotApproveInfo.getDotId());
            this.baseMapper.insertDotBank(dotApproveInfo.getId(), dotApproveInfo.getDotId());
            this.baseMapper.updateDotBank(dotApproveInfo.getId(), dotApproveInfo.getDotId());
        } else if (dotApproveInfo.getType() == 5) {
            //删除网点
            Integer contactId = dotInfoMapper.selectContactId(dotApproveInfo.getDotId());
            // 删除网点
            //禁止网点物理删除改为修改is_delete
            dotInfoMapper.deleteFlagDot(dotApproveInfo.getDotId());
            if (!StringUtils.isEmpty(contactId)) {
                //禁止用户表物理删除，改为状态控制
                dotInfoMapper.updateSysContactState(contactId);
            }
        } else if (dotApproveInfo.getType() == 6 || dotApproveInfo.getType() == 7 || dotApproveInfo.getType() == 8 || dotApproveInfo.getType() == 9) {
            // 6：停工，7：冻结，8：撤点，9：复工
            String status = "";
            switch (dotApproveInfo.getType()) {
                case 6:
                    status = "4";
                    break;
                case 7:
                    status = "3";
                    break;
                case 8:
                    status = "5";
                    break;
                case 9:
                    status = "1";
                    break;
            }
            //其他状态变更
            dotInfoMapper.updataFlagDot(dotApproveInfo.getDotId(), status);
        }
    }

    /**
     * 读取对应的审核记录
     *
     * @param item
     * @param worderAuditRecordEntities
     */
    private void setAuditInfo(DotApproveInfo item, List<WorderAuditRecordEntity> worderAuditRecordEntities) {
        if (worderAuditRecordEntities == null || worderAuditRecordEntities.isEmpty()) {
            return;
        }
        Map<String, List<WorderAuditRecordEntity>> auditMaps = worderAuditRecordEntities.stream().collect(Collectors.groupingBy(WorderAuditRecordEntity::getApplyNo));
        List<WorderAuditRecordEntity> worderAuditRecords = auditMaps.get(item.getApproveNo());
        if (worderAuditRecords == null || worderAuditRecords.isEmpty()) {
            return;
        }
        for (WorderAuditRecordEntity record : worderAuditRecords) {
            if (record.getAuditStatus() == -1 || record.getAuditStatus() == 1) {
                item.setFirstApproveTime(record.getCreateTime());
                if (record.getAuditUserId() != null) {
                    item.setFirstApproveUser(sysUserDao.selectEmployeeUser(record.getAuditUserId()));
                }
            } else if (record.getAuditStatus() == -2 || record.getAuditStatus() == 2) {
                item.setSecondApproveTime(record.getCreateTime());
                if (record.getAuditUserId() != null) {
                    item.setSecondApproveUser(sysUserDao.selectEmployeeUser(record.getAuditUserId()));
                }
            }
            if (StringUtils.isNotBlank(record.getNoPassReason())) {
                item.setNotPassedReason(record.getNoPassReason());
            }
        }
    }

    /**
     * 翻译指定属性
     *
     * @param dotApproveInfo
     */
    private void translateField(DotApproveInfo dotApproveInfo) {
        //翻译修改分类
        //0：无数据变更，1：基本信息，2：区域品牌，3：新建，4：区域品牌和基础信息，5：删除，6：停工，7：冻结，8：撤点，9：复工
        dotApproveInfo.setTypeName(DotApproveTypeEnum.getName(dotApproveInfo.getType()));
        //翻译状态
        dotApproveInfo.setStatusName(DotApproveStatusEnum.getName(dotApproveInfo.getStatus()));
        //翻译地址
        Integer dotArea = dotApproveInfo.getDotArea();
        BizRegionEntity dotAreaRegionEntity = InitRegionUtil.REGION_ID_MAP.get(dotArea.longValue());
        Integer dotCity = dotApproveInfo.getDotCity();
        BizRegionEntity dotCityRegionEntity = InitRegionUtil.REGION_ID_MAP.get(dotCity.longValue());
        Integer dotDistrict = dotApproveInfo.getDotDistrict();
        BizRegionEntity dotDistrictRegionEntity = InitRegionUtil.REGION_ID_MAP.get(dotDistrict.longValue());
        dotApproveInfo.setDotAddress(dotAreaRegionEntity.getName() + dotCityRegionEntity.getName() + dotDistrictRegionEntity.getName() + dotApproveInfo.getDotAddress());
    }

    /**
     * 翻译网点状态
     *
     * @param dotState
     * @return 1.有效，2.无效，3.冻结，4.停工 5.撤点
     */
    private String getStatus(String dotState) {
        if (StringUtils.isBlank(dotState)) {
            return null;
        }
        String state = "";
        switch (dotState) {
            case "1":
                state = "有效";
                break;
            case "2":
                state = "无效";
                break;
            case "3":
                state = "冻结";
                break;
            case "4":
                state = "停工";
                break;
            case "5":
                state = "撤点";
                break;
        }
        return state;
    }

    /**
     * 保存或者更新对应审批表
     */
    private R saveOrUpdateDotApprove(DotApproveInfoVo dotApproveInfoVo) throws InvocationTargetException, IllegalAccessException {
        DotInfo work = dotApproveInfoVo.getDotInfo();
        DotContacts dotContact = dotApproveInfoVo.getDotContacts();
        List<AreaBrandGroupVo> areaBrandGroupList = dotApproveInfoVo.getAreaBrandGroupList();

        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登录超时，请重新登陆");
        }

        //网点银行信息
        List<BankGroupVo> bankGroupVoList = dotApproveInfoVo.getBankGroupList();
        Map<String, String> bankAccountMap = new HashMap<>();
        List<Integer> bankIds = new ArrayList<>();
        List<DotBankApprove> dotBankApproveEntities = new ArrayList<>();
        List<DotBankApprove> updateDotBank = new ArrayList<>();
        Integer accountNumber = 0;
        for (int i = 0; i < bankGroupVoList.size(); i++) {
            BankGroupVo bankGroupVo = bankGroupVoList.get(i);
            bankAccountMap.put(bankGroupVo.getBankAccount(), bankGroupVo.getBankAccount());
            if (i==0){
                dotApproveInfoVo.getDotInfo().setDotBank(bankGroupVo.getDotBank());
                dotApproveInfoVo.getDotInfo().setBankAccount(bankGroupVo.getBankAccount());
                dotApproveInfoVo.getDotInfo().setBankName(bankGroupVo.getBankName());
                dotApproveInfoVo.getDotInfo().setBankNumber(bankGroupVo.getBankNumber());
                dotApproveInfoVo.getDotInfo().setTaxNo(bankGroupVo.getTaxNo());
            }

            //校验开户行账号唯一
            if (bankGroupVo.getBankId() == null){
                //新增
                if (dotInfoMapper.selectBankAccount(bankGroupVo.getBankAccount()) > 0) {
                    return R.error("开户行账号已存在：" + bankGroupVo.getBankAccount());
                }
            }else{
                bankIds.add(bankGroupVo.getBankId());
                //修改
                if (dotInfoMapper.selectBankAccountById(bankGroupVo.getBankAccount(),bankGroupVo.getBankId()) > 0) {
                    return R.error("开户行账号已存在：" + bankGroupVo.getBankAccount());
                }
            }
            //遍历
            DotBankApprove dotBankApproveEntity = new DotBankApprove();
            dotBankApproveEntity.setBankAccount(bankGroupVo.getBankAccount());
            dotBankApproveEntity.setBankName(bankGroupVo.getBankName());
            dotBankApproveEntity.setBankNumber(bankGroupVo.getBankNumber());
            dotBankApproveEntity.setDotBank(bankGroupVo.getDotBank());
            dotBankApproveEntity.setTaxNo(bankGroupVo.getTaxNo());
            dotBankApproveEntities.add(dotBankApproveEntity);

            accountNumber ++;
        }

        if (bankAccountMap.size() != accountNumber) {
            return R.error("开户行账号不能重复");
        }

        //行驶证开始
        List<DrivingGroupVo> drivingGroupVoList = dotApproveInfoVo.getDrivingGroupList();
        List<Integer> drivingIds = new ArrayList<>();
        List<DrivingPermitApproveEntity> drivingPermitEntities = new ArrayList<>();
        List<DrivingPermitApproveEntity> updatedrivingPermit = new ArrayList<>();
        Map<String, String> carNoMap = new HashMap<>();
        Integer carNum = 0;
        if (drivingGroupVoList != null && drivingGroupVoList.size() > 0) {
            for (int i = 0; i < drivingGroupVoList.size(); i++) {
                DrivingGroupVo drivingGroupVo = drivingGroupVoList.get(i);
                List<DrivingInfoVo> drivingInfoVoList = drivingGroupVo.getDrivingInfoList();
                if (drivingInfoVoList != null && drivingInfoVoList.size() > 0) {
                    for (DrivingInfoVo drivingInfoVo : drivingInfoVoList) {
                        if (drivingInfoVo.getDrivingPermit() != null && !drivingInfoVo.getDrivingPermit().isEmpty() && drivingInfoVo.getDrivingPermit().size() > 0) {
                            if (StringUtils.isBlank(drivingInfoVo.getCarNo())) {
                                return R.error("行驶证车牌号不能为空");
                            }
                            if (StringUtils.isBlank(drivingInfoVo.getName())) {
                                return R.error("行驶证姓名不能为空");
                            }
                            DrivingPermitApproveEntity drivingPermitEntity = new DrivingPermitApproveEntity();
                            carNoMap.put(drivingInfoVo.getCarNo(), drivingInfoVo.getCarNo());
                            drivingPermitEntity.setName(drivingInfoVo.getName());
                            String carNo = drivingInfoVo.getCarNo();
                            drivingPermitEntity.setCarNo(carNo);
                            List<DrivingPermitVo> drivingPermitVoList = drivingInfoVo.getDrivingPermit();
                            StringBuffer sb = new StringBuffer();
                            int count = 1;
                            for (DrivingPermitVo drivingPermitVo : drivingPermitVoList) {
                                if (count > 1) {
                                    sb.append(",");
                                }
                                sb.append(drivingPermitVo.getUid());
                                count++;
                            }
                            drivingPermitEntity.setImg(sb.toString());
                            drivingPermitEntity.setType(Integer.valueOf(drivingGroupVo.getType()));
                            if (drivingInfoVo.getDrivingId() == null) {
                                drivingPermitEntities.add(drivingPermitEntity);
                                if (dotInfoMapper.selectCarNo(carNo) > 0) {
                                    return R.error("行驶证车牌号已存在：" + carNo);
                                }
                            } else {
                                drivingIds.add(drivingInfoVo.getDrivingId());
                                drivingPermitEntity.setDrivingId(drivingInfoVo.getDrivingId());
                                updatedrivingPermit.add(drivingPermitEntity);
                                if (dotInfoMapper.selectCarNoById(carNo, drivingInfoVo.getDrivingId()) > 0) {
                                    return R.error("行驶证车牌号已存在：" + carNo);
                                }
                            }
                            carNum++;
                        } else {
                            if (StringUtils.isNotBlank(drivingInfoVo.getCarNo()) || StringUtils.isNotBlank(drivingInfoVo.getName())) {
                                return R.error("行驶证需上传行驶证照片");
                            }
                        }
                    }
                }
            }
        }

        if (carNoMap.size() != carNum) {
            return R.error("行驶证车牌号不能重复");
        }

        //数据库初始化插入审批单
        DotApproveInfo dotApproveInfo = insertApproveInfo(dotApproveInfoVo, user);

        List<AreaDot> arealist = new ArrayList<>();
        List<BrandDot> brandDotList = new ArrayList<>();

        String code = workMsgService.getBranchCode(work.getBranchName());
        work.setBranchCode(code);

        //数据库插入网点基本信息审批表
        DotInfoApprove dotInfoApprove = new DotInfoApprove();
        dotInfoApprove.setApproveId(dotApproveInfo.getId());
        BeanUtils.copyProperties(dotInfoApprove, work);
        dotInfoApprove.setDotShortName(dotApproveInfo.getDotShortName());
        this.baseMapper.insertSelective(dotInfoApprove);

        //网点银行信息保存
        if (work.getDotId() == null){
            //新增
            for (DotBankApprove dotBankApproveEntity : dotBankApproveEntities) {
                dotBankApproveEntity.setApproveId(dotApproveInfo.getId());
            }

            if (dotBankApproveEntities.size() > 0){
                dotInfoMapper.addDotBankApprove(dotBankApproveEntities);
            }
        }else{
            //修改
            for (DotBankApprove dotBankApproveEntity : dotBankApproveEntities) {
                dotBankApproveEntity.setDotId(work.getDotId());
                dotBankApproveEntity.setApproveId(dotApproveInfo.getId());
            }
            for (DotBankApprove dotBankApproveEntity : updateDotBank) {
                dotBankApproveEntity.setDotId(work.getDotId());
                dotBankApproveEntity.setApproveId(dotApproveInfo.getId());
            }

            if(!updateDotBank.isEmpty()){
                dotInfoMapper.addDotBankApprove(updateDotBank);
            }

            dotInfoMapper.deleteDotBankApproveStatus(dotApproveInfo.getId());
            if (accountNumber > 0) {
                if (bankIds.size() > 0) {
                    dotInfoMapper.updateDotBankApproveStatus(dotApproveInfo.getId(), bankIds);
                }
                if (dotBankApproveEntities.size() > 0) {
                    //开始保存行驶证信息
                    dotInfoMapper.addDotBankApprove(dotBankApproveEntities);
                }
            }
        }



        //驾驶证保存和编辑逻辑
        if (work.getDotId() == null) {
            for (DrivingPermitApproveEntity drivingPermitEntity : drivingPermitEntities) {
                drivingPermitEntity.setApproveId(dotApproveInfo.getId());
            }
            if (drivingPermitEntities.size() > 0) {
                //开始保存行驶证信息
                dotInfoMapper.addDrivingPermitApprove(drivingPermitEntities);
//                dotInfoMapper.updateApproveCarNumById(dotInfoApprove.getId(),carNum);
            }
        } else {
            for (DrivingPermitApproveEntity drivingPermitEntity : drivingPermitEntities) {
                drivingPermitEntity.setApproveId(dotApproveInfo.getId());
                drivingPermitEntity.setDotId(work.getDotId());
            }
            for (DrivingPermitApproveEntity drivingPermitEntity : updatedrivingPermit) {
                drivingPermitEntity.setApproveId(dotApproveInfo.getId());
                drivingPermitEntity.setDotId(work.getDotId());
            }
            if(!updatedrivingPermit.isEmpty()){
                dotInfoMapper.addDrivingPermitApprove(updatedrivingPermit);
            }

            dotInfoMapper.deleteDrivingPermitApproveStatus(dotApproveInfo.getId());
            if (carNum > 0) {
                if (drivingIds.size() > 0) {
                    dotInfoMapper.updateDrivingPermitApproveStatus(dotApproveInfo.getId(), drivingIds);
                }
                if (drivingPermitEntities.size() > 0) {
                    //开始保存行驶证信息
                    dotInfoMapper.addDrivingPermitApprove(drivingPermitEntities);
                }
//                dotInfoMapper.updateApproveCarNumById(dotApproveInfo.getId(),carNum);
            }
        }

        //数据库插入网点联系人审批表
        DotContactsApprove dotContactsApprove = new DotContactsApprove();
        BeanUtils.copyProperties(dotContactsApprove, dotContact);
        dotContactsApprove.setApproveId(dotApproveInfo.getId());
        //添加网点联系人审批表
        this.baseMapper.addContact(dotContactsApprove);

        //插入区域品牌服务类型组审批表
        areaBrandGroupList.forEach(areaBrandGroupVo -> {
            List<Integer> regionLabelList = areaBrandGroupVo.getRegionLabelList();
            if (regionLabelList != null) {
                regionLabelList.forEach(areaId -> arealist.add(new AreaDot(areaId, work.getDotId(), areaBrandGroupVo.getGroupId())));
            }
            List<BrandGroupVo> brandGroups = areaBrandGroupVo.getBrandGroupList();
            if (brandGroups != null) {
                for (BrandGroupVo brandGroupVo : brandGroups) {

                    List<Integer> serviceTypeList = brandGroupVo.getServiceTypeList();
                    if (brandGroupVo.getServiceTypeList() == null || brandGroupVo.getServiceTypeList().isEmpty()) {
                        serviceTypeList = new ArrayList<Integer>() {{
                            add(IntegerEnum.ZERO.getValue());
                        }};
                    }

                    List<Integer> brandList = brandGroupVo.getBrandList();
                    if (brandList != null) {
                        for (Integer brandId : brandList) {
                            for (Integer serviceType : serviceTypeList) {
                                brandDotList.add(new BrandDot(work.getDotId(), brandId, serviceType, new Date(), areaBrandGroupVo.getGroupId(), brandGroupVo.getChildGroupId()));
                            }
                        }
                    }
                }
            }
        });
        //封装数据
        dotApproveInfoVo.setArealist(arealist);
        dotApproveInfoVo.setBrandList(brandDotList);
        //添加区域和网点关联数据一组一组的入库
        Map<Long, List<AreaDot>> groups = arealist.stream().collect(Collectors.groupingBy(AreaDot::getGroupId));
        groups.forEach((groupId, areaDots) -> this.baseMapper.addDotAreaInfo(dotApproveInfo.getId(), work.getDotId(), dotAreaService.areaInfoAddHander(areaDots, dotApproveInfoVo.getUserId(), dotApproveInfoVo.getRoleIdList(), work.getDotId() , groupId), groupId));
        //添加品牌审批表
        SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdfs.format(new Date());
        this.baseMapper.addDotBrandInfo(dotApproveInfo.getId(), work.getDotId(), dotApproveInfoVo.getBrandList(), format);
        //审批单类型默认无变更
        int type = 0;
        List<DotApproveDiff> diffList = new ArrayList<>();
        //比对网点基本数据和图片
        compareBasicData(diffList, dotInfoApprove);
        //对比网点联系人数据
        compareDotContacts(diffList, dotContactsApprove);
        if (!diffList.isEmpty()) {
            //变更基本信息
            type = 1;
        }
        //差异字段翻译
        translateDiffData(diffList);

        //对比网点银行信息
        boolean dotBank = compareDotBank(diffList, dotInfoApprove.getDotId(), dotApproveInfo.getId());
        if(dotBank){
            type = 1;
        }

        //对比行驶证信息
        boolean dotDrivingPermit = compareDotDrivingPermit(diffList, dotInfoApprove.getDotId(), dotApproveInfo.getId());
        if(dotDrivingPermit){
            type = 1;
        }
        //网点区域品牌服务类型组数据对比
        boolean areaBrandFlag = compareDotAreaBrandServiceTypeGroup(diffList, dotInfoApprove.getDotId(), dotApproveInfo.getId());

        //更新网点ID
        if (dotInfoApprove.getDotId() != null) {
            for (DotApproveDiff dotApproveDiff : diffList) {
                dotApproveDiff.setDotId(dotInfoApprove.getDotId());
            }
        }

        if (type == 0 && areaBrandFlag) {
            //只变更区域品牌
            type = 2;
        } else if (type == 1 && areaBrandFlag) {
            //基本信息和区域品牌都变更
            type = 4;
        }
        //判断是否为新建
        if (dotInfoApprove.getDotId() == null) {
            //网点新建
            type = 3;
        }
        //无差异删除审批单
        if (type == 0) {
            dotApproveInfo.setType(type);
            dotApproveInfo.setDeleteState(1);
            this.baseMapper.updateById(dotApproveInfo);
            return R.ok();
        }

        dotApproveDiffService.saveBatch(diffList);
        //更新审批单为一审，同时更新类型
        dotApproveInfo.setStatus(1);
        dotApproveInfo.setType(type);
        this.baseMapper.updateById(dotApproveInfo);
        return R.ok();
    }


    /**
     * 翻译差异数据
     *
     * @param diffList
     */
    private void translateDiffData(List<DotApproveDiff> diffList) {
        if (diffList == null || diffList.isEmpty()) {
            return;
        }
        for (DotApproveDiff dotApproveDiff : diffList) {
            translateDiffItem(dotApproveDiff);
        }
    }

    /**
     * 翻译属性
     *
     * @param dotApproveDiff
     */
    private void translateDiffItem(DotApproveDiff dotApproveDiff) {
        List<Map<String, Object>> translateList;
        if (StringUtils.isBlank(dotApproveDiff.getTranslate())) {
            return;
        }
        //如果是图片属性需要获取URL
        if ("image".equals(dotApproveDiff.getTranslate())) {
            if (StringUtils.isNotBlank(dotApproveDiff.getNewValue())) {
                dotApproveDiff.setNewValue(sysFilesMapper.selectSysFileInfo(Integer.parseInt(dotApproveDiff.getNewValue())));
            }

            if (StringUtils.isNotBlank(dotApproveDiff.getOldValue())) {
                dotApproveDiff.setOldValue(sysFilesMapper.selectSysFileInfo(Integer.parseInt(dotApproveDiff.getOldValue())));
            }
            return;
        }

        switch (dotApproveDiff.getTranslate()) {
            case "DotQuality":
                translateList = dotInfoMapper.selectDictDetaillist("net_properties");
                break;
            case "DotClass":
                translateList = dotInfoMapper.selectDictDetaillist("node_types");
                break;
            case "DotFeatures":
                translateList = dotInfoMapper.selectDictDetaillist("network_features");
                break;
            case "MarketLevel":
                translateList = dotInfoMapper.selectDictDetaillist("market_level");
                break;
            case "DegreeEducation":
                translateList = dotInfoMapper.selectDictDetaillist("standard_culture");
                break;
            case "CompanyQuality":
                translateList = dotInfoMapper.selectDictDetaillist("company_nature");
                break;
            case "TaxPoint":
                translateList = dotInfoMapper.selectDictDetaillist("tax_point");
                break;
            case "Aptitude":
                translateList = dotInfoMapper.selectDictDetaillist("aptitude");
                break;
            case "CommonTaxPer":
            case "IsAdvanceMoney":
                translateList = dotInfoMapper.selectDictDetaillist("average_taxpayer");
                break;
            case "DotSignOut":
                translateList = new ArrayList<>();
                Map<String, Object> temp1 = new HashMap<>();
                temp1.put("name", "允许强制签退");
                temp1.put("number", "1");
                translateList.add(temp1);
                Map<String, Object> temp2 = new HashMap<>();
                temp2.put("name", "允许正常签退");
                temp2.put("number", "2");
                translateList.add(temp2);
                Map<String, Object> temp3 = new HashMap<>();
                temp3.put("name", "允许有理由签退");
                temp3.put("number", "3");
                translateList.add(temp3);
                break;
            default:
                translateList = new ArrayList<>();
                break;
        }
        Optional<Map<String, Object>> oldItem = translateList.stream().filter(item -> item.get("number") != null && item.get("number").toString().equals(dotApproveDiff.getOldValue())).findFirst();
        oldItem.ifPresent(item -> dotApproveDiff.setOldValue(String.valueOf(item.get("name"))));

        Optional<Map<String, Object>> newItem = translateList.stream().filter(item -> item.get("number") != null && item.get("number").toString().equals(dotApproveDiff.getNewValue())).findFirst();
        newItem.ifPresent(item -> dotApproveDiff.setNewValue(String.valueOf(item.get("name"))));
    }


    /**
     * 网点银行信息对比
     *
     * @param diffList
     * @param dotId
     * @param approveId
     * @return
     */
    private boolean compareDotBank(List<DotApproveDiff> diffList, Integer dotId, Integer approveId) {
        List<DotBank> oldDotBankList = new ArrayList<>();
        List<DotBank> newDotBankList;
        boolean diff = false;
        if (approveId == null) {
            throw new RRException("获取不到审批单ID，审批单插入数据库失败！！！");
        }
        if (dotId != null) {
            oldDotBankList = this.baseMapper.selectOldDotBankGroups(dotId);
        }
        newDotBankList = this.baseMapper.selectNewDotBankGroups(approveId);
        //没有修改网点银行信息
        if (newDotBankList.isEmpty() && oldDotBankList.isEmpty()) {
            return false;
        }

        if (oldDotBankList.size() != newDotBankList.size()) {
            diff = true;
        } else if (compareDotBankItem(oldDotBankList, newDotBankList)) {
            //对比网点银行信息
            diff = true;
        }

        if (diff) {
            DotApproveDiff dotApproveDiff = new DotApproveDiff(approveId, 5, "网点银行组", getDotBankJsonData(oldDotBankList), getDotBankJsonData(newDotBankList), null, 1000);
            diffList.add(dotApproveDiff);
            return true;
        }
        return false;
    }


    /**
     * 行驶证信息对比
     *
     * @param diffList
     * @param dotId
     * @param approveId
     * @return
     */
    private boolean compareDotDrivingPermit(List<DotApproveDiff> diffList, Integer dotId, Integer approveId) {
        List<DrivingPermitEntity> oldDrivingPermit = new ArrayList<>();
        List<DrivingPermitEntity> newDrivingPermit;
        boolean diff = false;
        if (approveId == null) {
            throw new RRException("获取不到审批单ID，审批单插入数据库失败！！！");
        }
        if (dotId != null) {
            oldDrivingPermit = this.baseMapper.selectOldDrivingPermitGroups(dotId);
        }
        newDrivingPermit = this.baseMapper.selectNewDrivingPermitGroups(approveId);
        //没有修改驾驶证信息
        if (newDrivingPermit.isEmpty() && oldDrivingPermit.isEmpty()) {
            return false;
        }

        //根据行驶证类型分类
        Map<Integer, List<DrivingPermitEntity>> oldTypeGroups = oldDrivingPermit.stream().collect(Collectors.groupingBy(DrivingPermitEntity::getType));
        Map<Integer, List<DrivingPermitEntity>> newTypeGroups = newDrivingPermit.stream().collect(Collectors.groupingBy(DrivingPermitEntity::getType));

        if (oldDrivingPermit.size() != newDrivingPermit.size()) {
            diff = true;
        } else if (compareDotDrivingPermitItem(oldTypeGroups.get(1), newTypeGroups.get(1)) || compareDotDrivingPermitItem(oldTypeGroups.get(2), newTypeGroups.get(2)) || compareDotDrivingPermitItem(oldTypeGroups.get(3), newTypeGroups.get(3))) {
            //对比公司类型行驶证
            diff = true;
        }

        if (diff) {
            DotApproveDiff dotApproveDiff = new DotApproveDiff(approveId, 4, "行驶证组", getDrivingPermitJsonData(oldTypeGroups), getDrivingPermitJsonData(newTypeGroups), null, 1000);
            diffList.add(dotApproveDiff);
            return true;
        }
        return false;
    }

    private String getDrivingPermitJsonData(Map<Integer, List<DrivingPermitEntity>> typeGroups) {
        if (typeGroups == null || typeGroups.isEmpty()) {
            return null;
        }
        List<JSONObject> dataGroups = new ArrayList<>();
        addDrivingPermitJsonData(typeGroups, dataGroups, 1, "公司");
        addDrivingPermitJsonData(typeGroups, dataGroups, 2, "法人");
        addDrivingPermitJsonData(typeGroups, dataGroups, 3, "服务兵");
        return dataGroups.size() > 0 ? JSON.toJSONString(dataGroups) : null;
    }

    private void addDrivingPermitJsonData(Map<Integer, List<DrivingPermitEntity>> oldTypeGroups, List<JSONObject> dataGroups, Integer type, String name) {
        List<DrivingPermitEntity> drivingPermitEntities = oldTypeGroups.get(type);
        if (drivingPermitEntities != null && !drivingPermitEntities.isEmpty()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", name);
            List<JSONObject> childGroups = new ArrayList<>();
            for (DrivingPermitEntity item : drivingPermitEntities) {
                JSONObject childItem = new JSONObject();
                childItem.put("name", item.getName());
                childItem.put("carNo", item.getCarNo());
                if (StringUtils.isNotBlank(item.getImg())) {
                    List<JSONObject> imageGroups = new ArrayList<>();
                    String[] images = item.getImg().split(",");
                    for (int i = 0; i < images.length; i++) {
                        JSONObject imageItem = new JSONObject();
                        imageItem.put("uid", i + 1);
                        imageItem.put("name", "无图片内容");
                        imageItem.put("status", "done");
                        imageItem.put("url", sysFilesMapper.selectSysFileInfo(Integer.parseInt(images[i])));
                        imageGroups.add(imageItem);
                    }
                    childItem.put("imags", imageGroups);
                }
                childGroups.add(childItem);
            }
            jsonObject.put("childGroups", childGroups);
            dataGroups.add(jsonObject);
        }
    }

    private String getDotBankJsonData(List<DotBank> dotBankList) {
        if (dotBankList == null || dotBankList.isEmpty()) {
            return null;
        }
        List<JSONObject> dataGroups = new ArrayList<>();
        addDotBankJsonData(dotBankList, dataGroups);
        return dataGroups.size() > 0 ? JSON.toJSONString(dataGroups) : null;
    }

    private void addDotBankJsonData(List<DotBank> dotBankList, List<JSONObject> dataGroups) {
        if (dotBankList != null && !dotBankList.isEmpty()) {
            for (DotBank dotBank : dotBankList) {
                JSONObject jsonObject = new JSONObject();
                if (dotBank.getDotId()!=null){
                    jsonObject.put("bankId", dotBank.getId());
                }
                jsonObject.put("dotBank",dotBank.getDotBank());
                jsonObject.put("bankNumber",dotBank.getBankNumber());
                jsonObject.put("bankAccount",dotBank.getBankAccount());
                jsonObject.put("bankName",dotBank.getBankName());
                jsonObject.put("taxNo",dotBank.getTaxNo());

                dataGroups.add(jsonObject);
            }

        }
    }



    private boolean compareDotBankItem(List<DotBank> oldDotBankList, List<DotBank> newDotBankList) {
        if ((oldDotBankList == null || oldDotBankList.isEmpty()) && (newDotBankList == null || newDotBankList.isEmpty())) {
            return false;
        }
        if (oldDotBankList == null && newDotBankList.size() > 0) {
            return true;
        }
        if (newDotBankList == null && oldDotBankList.size() > 0) {
            return true;
        }
        if (oldDotBankList.size() != newDotBankList.size()) {
            return true;
        }

        //获取户头
        String oldBankNames = oldDotBankList.stream().map(DotBank::getBankName).sorted().collect(Collectors.joining(","));
        String newBankNames = newDotBankList.stream().map(DotBank::getBankName).sorted().collect(Collectors.joining(","));
        if (!oldBankNames.equals(newBankNames)) {
            return true;
        }

        //获取开户行行号
        String oldBankNumbers = oldDotBankList.stream().map(DotBank::getBankNumber).sorted().collect(Collectors.joining(","));
        String newBankNumbers = newDotBankList.stream().map(DotBank::getBankNumber).sorted().collect(Collectors.joining(","));
        if (!oldBankNumbers.equals(newBankNumbers)) {
            return true;
        }

        //获取开户行名称
        String oldDotBanks = oldDotBankList.stream().map(DotBank::getDotBank).sorted().collect(Collectors.joining(","));
        String newDotBanks = newDotBankList.stream().map(DotBank::getDotBank).sorted().collect(Collectors.joining(","));
        if (!oldDotBanks.equals(newDotBanks)) {
            return true;
        }

        //获取开户行账号
        String oldBankAccounts = oldDotBankList.stream().map(DotBank::getBankAccount).sorted().collect(Collectors.joining(","));
        String newBankAccounts = newDotBankList.stream().map(DotBank::getBankAccount).sorted().collect(Collectors.joining(","));
        if (!oldBankAccounts.equals(newBankAccounts)) {
            return true;
        }

        //获取税号
        String oldTaxNos = oldDotBankList.stream().map(DotBank::getTaxNo).sorted().collect(Collectors.joining(","));
        String newTaxNos = newDotBankList.stream().map(DotBank::getTaxNo).sorted().collect(Collectors.joining(","));
        if (!oldTaxNos.equals(newTaxNos)) {
            return true;
        }


        return false;
    }


    private boolean compareDotDrivingPermitItem(List<DrivingPermitEntity> oldDrivingPermits, List<DrivingPermitEntity> newDrivingPermits) {
        if ((oldDrivingPermits == null || oldDrivingPermits.isEmpty()) && (newDrivingPermits == null || newDrivingPermits.isEmpty())) {
            return false;
        }
        if (oldDrivingPermits == null && newDrivingPermits.size() > 0) {
            return true;
        }
        if (newDrivingPermits == null && oldDrivingPermits.size() > 0) {
            return true;
        }
        if (oldDrivingPermits.size() != newDrivingPermits.size()) {
            return true;
        }
        //获取姓名
        String oldNames = oldDrivingPermits.stream().map(DrivingPermitEntity::getName).sorted().collect(Collectors.joining(","));
        String newNames = newDrivingPermits.stream().map(DrivingPermitEntity::getName).sorted().collect(Collectors.joining(","));
        if (!oldNames.equals(newNames)) {
            return true;
        }

        //获取车牌号
        String oldCarNos = oldDrivingPermits.stream().map(DrivingPermitEntity::getCarNo).sorted().collect(Collectors.joining(","));
        String newCarNos = newDrivingPermits.stream().map(DrivingPermitEntity::getCarNo).sorted().collect(Collectors.joining(","));
        if (!oldCarNos.equals(newCarNos)) {
            return true;
        }

        //获取图片
        String oldImgs = oldDrivingPermits.stream().map(DrivingPermitEntity::getImg).sorted().collect(Collectors.joining(","));
        String newImgs = newDrivingPermits.stream().map(DrivingPermitEntity::getImg).sorted().collect(Collectors.joining(","));
        if (!oldImgs.equals(newImgs)) {
            return true;
        }
        return false;
    }

    /**
     * 网点区域品牌服务类型组
     */
    private boolean compareDotAreaBrandServiceTypeGroup(List<DotApproveDiff> diffList, Integer dotId, Integer approveId) {
        List<DotAreaBrandGroupsVo> oldAreaBrandGroups = new ArrayList<>();
        List<DotAreaBrandGroupsVo> newAreaBrandGroups;
        boolean diff = false;
        if (approveId == null) {
            throw new RRException("获取不到审批单ID，审批单插入数据库失败！！！");
        }
        if (dotId != null) {
            oldAreaBrandGroups = this.baseMapper.selectOldDotBrandGroups(dotId);
        }
        newAreaBrandGroups = this.baseMapper.selectNewDotBrandGroups(approveId);

        Map<Long, List<DotAreaBrandGroupsVo>> oldGroups = oldAreaBrandGroups.stream().collect(Collectors.groupingBy(DotAreaBrandGroupsVo::getGroupId));
        Map<Long, List<DotAreaBrandGroupsVo>> newGroups = newAreaBrandGroups.stream().collect(Collectors.groupingBy(DotAreaBrandGroupsVo::getGroupId));

        Set<Long> oldGroupIds = oldGroups.keySet();
        Set<Long> newGroupIds = newGroups.keySet();
        if (oldGroupIds.size() != newGroupIds.size()) {
            diff = true;
        } else {
            HashSet<Long> tempSets = new HashSet<>(newGroupIds);
            tempSets.addAll(oldGroupIds);
            tempSets.removeAll(newGroupIds);
            if (!tempSets.isEmpty()) {
                diff = true;
            }
        }
        if (!diff) {
            for (Map.Entry<Long, List<DotAreaBrandGroupsVo>> entry : newGroups.entrySet()) {
                Long key = entry.getKey();
                List<DotAreaBrandGroupsVo> value = entry.getValue();
                Set<Long> newAreaIds = value.stream().map(DotAreaBrandGroupsVo::getAreaId).collect(Collectors.toSet());

                List<DotAreaBrandGroupsVo> oldDotAreaBrandGroupsVos = oldGroups.get(key);
                Set<Long> oldAreaIds = oldDotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getAreaId).collect(Collectors.toSet());

                //获取区域
                String newRegionNames = getRegionNames(newAreaIds);
                String oldRegionNames = getRegionNames(oldAreaIds);

                if (!newRegionNames.equals(oldRegionNames)) {
                    diff = true;
                    break;
                }
                //对比品牌服务类型组
                Map<Long, List<DotAreaBrandGroupsVo>> newChildGroups = value.stream().collect(Collectors.groupingBy(DotAreaBrandGroupsVo::getChildGroupId));
                Map<Long, List<DotAreaBrandGroupsVo>> oldChildGroups = oldDotAreaBrandGroupsVos.stream().collect(Collectors.groupingBy(DotAreaBrandGroupsVo::getChildGroupId));

                List<Long> newChildKeys = new ArrayList<>(newChildGroups.keySet());
                Collections.sort(newChildKeys);
                List<Long> oldChildKeys = new ArrayList<>(oldChildGroups.keySet());
                Collections.sort(oldChildKeys);

                String newChildKeysStr = newChildKeys.stream().map(Object::toString).collect(Collectors.joining());
                String oldChildKeysStr = oldChildKeys.stream().map(Object::toString).collect(Collectors.joining());

                if (!newChildKeysStr.equals(oldChildKeysStr)) {
                    diff = true;
                    break;
                }
                if (diffBrandServiceTypeGroups(newChildGroups, oldChildGroups)) {
                    diff = true;
                    break;
                }
            }
        }
        if (diff) {
            //查询所有品牌信息
            LambdaQueryWrapper<BrandEntity> brandWrapper = Wrappers.lambdaQuery();
            brandWrapper.select(BrandEntity::getId, BrandEntity::getBrandName);
            List<BrandEntity> brandEntities = brandDao.selectList(brandWrapper);
            Map<Integer, String> brandMap = brandEntities.stream().collect(Collectors.toMap(BrandEntity::getId, BrandEntity::getBrandName, (val1, val2) -> val1));
            //查询服务类型字典信息
            List<SysDictionaryDetailEntity> list = sysDictionaryDetailDao.selectDetailByNumbers("service_type");
            Map<String, String> serviceTypeMap = list.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
            String oldJsonData = getGroupToJsonData(oldGroups, brandMap, serviceTypeMap);

            String newJsonData = getGroupToJsonData(newGroups, brandMap, serviceTypeMap);

            diffList.add(new DotApproveDiff(approveId, 3, "区域品牌组", oldJsonData, newJsonData, null, 999));
            return true;
        }
        return false;
    }

    private String getGroupToJsonData(Map<Long, List<DotAreaBrandGroupsVo>> groups, Map<Integer, String> brandMap, Map<String, String> serviceTypeMap) {
        if (groups != null && !groups.isEmpty()) {
            List<JSONObject> result = new ArrayList<>();
            List<Long> groupIds = new ArrayList<>(groups.keySet());
            Collections.sort(groupIds);
            for (Long groupId : groupIds) {
                JSONObject jsonObject = new JSONObject();
                List<DotAreaBrandGroupsVo> dotAreaBrandGroupsVos = groups.get(groupId);
                Set<Long> areaIds = dotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getAreaId).collect(Collectors.toSet());
                String newRegionNames = getRegionNames(areaIds);
                jsonObject.put("areas", newRegionNames);
                Map<Long, List<DotAreaBrandGroupsVo>> childGroups = dotAreaBrandGroupsVos.stream().collect(Collectors.groupingBy(DotAreaBrandGroupsVo::getChildGroupId));
                getGroupToJsonData(jsonObject, childGroups, brandMap, serviceTypeMap);
                result.add(jsonObject);
            }
            if (!result.isEmpty()) {
                return JSON.toJSONString(result);
            }
        }
        return null;
    }

    private void getGroupToJsonData(JSONObject jsonObject, Map<Long, List<DotAreaBrandGroupsVo>> childGroups, Map<Integer, String> brandMap, Map<String, String> serviceTypeMap) {
        if (childGroups != null && !childGroups.isEmpty()) {
            List<Long> childGroupIds = new ArrayList<>(childGroups.keySet());
            Collections.sort(childGroupIds);
            List<JSONObject> result = new ArrayList<>();

            for (Long groupId : childGroupIds) {
                JSONObject childJsonObject = new JSONObject();
                List<DotAreaBrandGroupsVo> dotAreaBrandGroupsVos = childGroups.get(groupId);
                //获取品牌集合并且从小到大排序
                List<Long> brandIds = new ArrayList<>(dotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getBrandId).collect(Collectors.toSet()));
                Collections.sort(brandIds);
                //翻译品牌ID
                List<String> finalBrandNames = new ArrayList<>();
                brandIds.forEach(item -> finalBrandNames.add(brandMap.get(Integer.parseInt(item.toString()))));
                childJsonObject.put("brands", String.join(",", finalBrandNames));

                //获取服务类型从小到大排序
                List<Integer> serviceTypeIds = new ArrayList<>(dotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getServiceType).collect(Collectors.toSet()));
                Collections.sort(serviceTypeIds);

                List<String> finalServiceTypes = new ArrayList<>();
                serviceTypeIds.forEach(item -> finalServiceTypes.add(serviceTypeMap.get(item.toString())));
                childJsonObject.put("serviceTypes", String.join(",", finalServiceTypes));
                result.add(childJsonObject);
            }
            jsonObject.put("childGroups", result);
        }
    }


    /**
     * 品牌服务类型组对比
     *
     * @param newChildGroups
     * @param oldChildGroups
     * @return
     */
    private boolean diffBrandServiceTypeGroups(Map<Long, List<DotAreaBrandGroupsVo>> newChildGroups, Map<Long, List<DotAreaBrandGroupsVo>> oldChildGroups) {
        Set<Long> oldGroupIds = oldChildGroups.keySet();
        Set<Long> newGroupIds = newChildGroups.keySet();
        if (oldGroupIds.size() != newGroupIds.size()) {
            return true;
        } else {
            HashSet<Long> tempSets = new HashSet<>(newGroupIds);
            tempSets.addAll(oldGroupIds);
            tempSets.removeAll(newGroupIds);
            if (!tempSets.isEmpty()) {
                return true;
            }
        }

        for (Map.Entry<Long, List<DotAreaBrandGroupsVo>> entry : newChildGroups.entrySet()) {
            Long key = entry.getKey();
            List<DotAreaBrandGroupsVo> value = entry.getValue();

            List<DotAreaBrandGroupsVo> oldDotAreaBrandGroupsVos = oldChildGroups.get(key);

            //获取品牌
            List<Long> newBrandIds = new ArrayList<>(value.stream().map(DotAreaBrandGroupsVo::getBrandId).collect(Collectors.toSet()));
            Collections.sort(newBrandIds);
            List<Long> oldBrandIds = new ArrayList<>(oldDotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getBrandId).collect(Collectors.toSet()));
            Collections.sort(oldBrandIds);

            String newChildKeysStr = newBrandIds.stream().map(Object::toString).collect(Collectors.joining());
            String oldChildKeysStr = oldBrandIds.stream().map(Object::toString).collect(Collectors.joining());

            if (!newChildKeysStr.equals(oldChildKeysStr)) {
                return true;
            }

            //获取服务类型
            List<Integer> newServiceTypeIds = new ArrayList<>(value.stream().map(DotAreaBrandGroupsVo::getServiceType).collect(Collectors.toSet()));
            Collections.sort(newServiceTypeIds);
            List<Integer> oldServiceTypeIds = new ArrayList<>(oldDotAreaBrandGroupsVos.stream().map(DotAreaBrandGroupsVo::getServiceType).collect(Collectors.toSet()));
            Collections.sort(oldServiceTypeIds);

            String newServiceTypeIdsStr = newServiceTypeIds.stream().map(Object::toString).collect(Collectors.joining());
            String oldServiceTypeIdsStr = oldServiceTypeIds.stream().map(Object::toString).collect(Collectors.joining());

            if (!newServiceTypeIdsStr.equals(oldServiceTypeIdsStr)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理区域数据
     *
     * @param newAreaIds
     * @return
     */
    private String getRegionNames(Set<Long> newAreaIds) {
        if (newAreaIds == null || newAreaIds.isEmpty()) {
            return "";
        }
        List<String> names = new ArrayList<>();
        Set<Long> result = new HashSet<>();
        List<BizRegionEntity> list = new ArrayList<>();
        newAreaIds.forEach(areaId -> list.add(InitRegionUtil.REGION_ID_MAP.get(areaId)));

        //获取省份，地市，区县
        List<BizRegionEntity> provinceList = list.stream().filter(item -> item.getType() == 1).collect(Collectors.toList());
        List<BizRegionEntity> cityeList = list.stream().filter(item -> item.getType() == 2).collect(Collectors.toList());
        List<BizRegionEntity> disList = list.stream().filter(item -> item.getType() == 3).collect(Collectors.toList());

        Set<Long> provinceIds = new HashSet<>();
        for (BizRegionEntity provinceRegion : provinceList) {
            long count = cityeList.stream().filter(item -> provinceRegion.getId().equals(item.getPid())).count();
            if (count > 0) {
                int size = InitRegionUtil.REGION_PID_MAP.get(provinceRegion.getId()).size();
                if (count == size) {
                    result.add(provinceRegion.getId());
                    provinceIds.add(provinceRegion.getId());
                }
            } else {
                result.add(provinceRegion.getId());
                provinceIds.add(provinceRegion.getId());
            }
        }

        Set<Long> cityIds = new HashSet<>();
        for (BizRegionEntity cityRegion : cityeList) {
            long count = disList.stream().filter(item -> cityRegion.getId().equals(item.getPid())).count();
            if (count > 0) {
                int size = InitRegionUtil.REGION_PID_MAP.get(cityRegion.getId()).size();
                if (count == size && !provinceIds.contains(cityRegion.getPid())) {
                    result.add(cityRegion.getId());
                    cityIds.add(cityRegion.getId());
                }
            } else if (!provinceIds.contains(cityRegion.getPid())) {
                result.add(cityRegion.getId());
                cityIds.add(cityRegion.getId());
            }
        }

        for (BizRegionEntity disRegion : disList) {
            BizRegionEntity cityEntity = InitRegionUtil.REGION_ID_MAP.get(disRegion.getPid());
            if (!cityIds.contains(cityEntity.getId()) && !provinceIds.contains(cityEntity.getPid())) {
                result.add(disRegion.getId());
            }
        }
        //有小到大排序
        List<Long> ids = new ArrayList<>(result);
        Collections.sort(ids);
        //翻译名称
        ids.forEach(item -> names.add(InitRegionUtil.REGION_ID_MAP.get(item).getName()));
        return String.join(",", names);
    }


    /**
     * 网点管理员信息对比
     *
     * @param diffList
     * @param dotContactsApprove
     */
    private void compareDotContacts(List<DotApproveDiff> diffList, DotContactsApprove dotContactsApprove) {
        Integer contactsId = dotContactsApprove.getContactsId();
        DotContacts dotContacts = null;

        Integer approveId = dotContactsApprove.getApproveId();
        if (approveId == null) {
            throw new RRException("获取不到审批单ID，审批单插入数据库失败！！！");
        }
        if (contactsId != null) {
            LambdaQueryWrapper<DotContacts> dotContactsWrapper = Wrappers.lambdaQuery();
            dotContactsWrapper.eq(DotContacts::getContactsId, contactsId);
            dotContacts = dotContactsMapper.selectOne(dotContactsWrapper);
        }

        if (dotContacts == null) {
            dotContacts = new DotContacts();
        }

        compareAttr(approveId, diffList, dotContacts.getContactsName(), dotContactsApprove.getContactsName(), "管理员账号", 1, 45, null);
        compareAttr(approveId, diffList, dotContacts.getContactsPhone(), dotContactsApprove.getContactsPhone(), "管理员手机号", 1, 46, null);
        compareAttr(approveId, diffList, dotContacts.getContactsEmail(), dotContactsApprove.getContactsEmail(), "管理员邮箱", 1, 47, null);
    }

    /**
     * 基本数据类加图片
     *
     * @param diffList
     * @param dotInfoApprove
     */
    private void compareBasicData(List<DotApproveDiff> diffList, DotInfoApprove dotInfoApprove) {
        Integer dotId = dotInfoApprove.getDotId();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DotInfo dotInfo = new DotInfo();
        Integer approveId = dotInfoApprove.getApproveId();
        if (approveId == null) {
            throw new RRException("获取不到审批单ID，审批单插入数据库失败！！！");
        }
        if (dotId != null) {
            LambdaQueryWrapper<DotInfo> dotInfoWrapper = Wrappers.lambdaQuery();
            dotInfoWrapper.eq(DotInfo::getDotId, dotId);
            dotInfo = dotInfoMapper.selectOne(dotInfoWrapper);
        }
        compareAttr(approveId, diffList, dotInfo.getvCode(), dotInfoApprove.getvCode(), "网点v码", 1, 1, null);
        compareAttr(approveId, diffList, dotInfo.getBranchName(), dotInfoApprove.getBranchName(), "工贸名称", 1, 2, null);
        compareAttr(approveId, diffList, dotInfo.getDotQuality(), dotInfoApprove.getDotQuality(), "网点性质", 1, 3, "DotQuality");
        compareAttr(approveId, diffList, dotInfo.getDotName(), dotInfoApprove.getDotName(), "网点名称", 1, 4, null);
        compareAttr(approveId, diffList, dotInfo.getDotPostcode(), dotInfoApprove.getDotPostcode(), "网点邮编", 1, 5, null);
        compareAttr(approveId, diffList, dotInfo.getDotClass(), dotInfoApprove.getDotClass(), "网点种类", 1, 6, "DotClass");
        String addr = "";
        if (dotInfo.getDotArea() != null && dotInfo.getDotCity() != null && dotInfo.getDotDistrict() != null && dotInfo.getDotAddress() != null) {
            BizRegionEntity bfDotArera = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfo.getDotArea()));
            BizRegionEntity bfDotCity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfo.getDotCity()));
            BizRegionEntity bfDistrict = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfo.getDotDistrict()));
            addr = bfDotArera.getName() + bfDotCity.getName() + bfDistrict.getName() + dotInfo.getDotAddress();
        }
        BizRegionEntity afDotArera = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoApprove.getDotArea()));
        BizRegionEntity afDotCity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoApprove.getDotCity()));
        BizRegionEntity afDistrict = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoApprove.getDotDistrict()));
        compareAttr(approveId, diffList, addr, afDotArera.getName() + afDotCity.getName() + afDistrict.getName() + dotInfoApprove.getDotAddress(), "网点地址", 1, 7, null);
        compareAttr(approveId, diffList, dotInfo.getDotFeatures(), dotInfoApprove.getDotFeatures(), "网点特性", 1, 8, "DotFeatures");
        compareAttr(approveId, diffList, dotInfo.getCustomerCode(), dotInfoApprove.getCustomerCode(), "客户编码", 1, 9, null);
        compareAttr(approveId, diffList, dotInfo.getDotCode(), dotInfoApprove.getDotCode(), "网点编码", 1, 10, null);
        compareAttr(approveId, diffList, dotInfo.getMarketLevel(), dotInfoApprove.getMarketLevel(), "市场级别", 1, 11, "MarketLevel");
        compareAttr(approveId, diffList, dotInfo.getDotCreateTime() != null ? format.format(dotInfo.getDotCreateTime()) : "", dotInfoApprove.getDotCreateTime() != null ? format.format(dotInfoApprove.getDotCreateTime()) : "", "建点时间", 1, 12, null);
        compareAttr(approveId, diffList, dotInfo.getDotCreatePer(), dotInfoApprove.getDotCreatePer(), "建点人", 1, 13, null);
        compareAttr(approveId, diffList, dotInfo.getLegalPersonName(), dotInfoApprove.getLegalPersonName(), "法人姓名", 1, 14, null);
        compareAttr(approveId, diffList, dotInfo.getLegalPersonSex(), dotInfoApprove.getLegalPersonSex(), "法人性别", 1, 15, null);
        compareAttr(approveId, diffList, dotInfo.getDegreeEducation(), dotInfoApprove.getDegreeEducation(), "文化程度", 1, 16, "DegreeEducation");
        String bfTele = "";
        String bfPhone = "";
        if (dotInfo.getTelephone() != null) {
            String[] bfTelephone = dotInfo.getTelephone().split("-");
            bfTele = bfTelephone.length > 0 ? bfTelephone[0] : "";
            bfPhone = bfTelephone.length > 1 ? bfTelephone[1] : "";
        }
        String[] afTelephone = dotInfoApprove.getTelephone().split("-");
        compareAttr(approveId, diffList, bfTele, afTelephone.length > 0 ? afTelephone[0] : "", "区号", 1, 17, null);
        compareAttr(approveId, diffList, bfPhone, afTelephone.length > 1 ? afTelephone[1] : "", "电话", 1, 18, null);
        compareAttr(approveId, diffList, dotInfo.getStorageSpare(), dotInfoApprove.getStorageSpare(), "备件库面积", 1, 19, null);
        compareAttr(approveId, diffList, dotInfo.getVehiclesNums(), dotInfoApprove.getVehiclesNums(), "交通工具数量", 1, 20, null);
        compareAttr(approveId, diffList, dotInfo.getContractEnd() != null ? format.format(dotInfo.getContractEnd()) : null, dotInfoApprove.getContractEnd() != null ? format.format(dotInfoApprove.getContractEnd()) : "", "合同结束日期", 1, 21, null);
        compareAttr(approveId, diffList, dotInfo.getContactName(), dotInfoApprove.getContactName(), "经理姓名", 1, 22, null);
        compareAttr(approveId, diffList, dotInfo.getDotTelephone(), dotInfoApprove.getDotTelephone(), "经理手机号", 1, 23, null);
        compareAttr(approveId, diffList, dotInfo.getDotEmail(), dotInfoApprove.getDotEmail(), "网点邮箱", 1, 24, null);
        compareAttr(approveId, diffList, dotInfo.getDotBank(), dotInfoApprove.getDotBank(), "开户行名称", 1, 25, null);
        compareAttr(approveId, diffList, dotInfo.getBankName(), dotInfoApprove.getBankName(), "户头（开户名称）", 1, 26, null);
        compareAttr(approveId, diffList, dotInfo.getBankNumber(), dotInfoApprove.getBankNumber(), "开户行行号", 1, 27, null);
        compareAttr(approveId, diffList, dotInfo.getBankAccount(), dotInfoApprove.getBankAccount(), "开户行账号", 1, 28, null);
        compareAttr(approveId, diffList, dotInfo.getTaxNo(), dotInfoApprove.getTaxNo(), "税号", 1, 29, null);
        compareAttr(approveId, diffList, dotInfo.getCompanyQuality(), dotInfoApprove.getCompanyQuality(), "企业性质", 1, 30, "CompanyQuality");
        compareAttr(approveId, diffList, dotInfo.getRegistFund(), dotInfoApprove.getRegistFund(), "注册资金", 1, 31, null);
        compareAttr(approveId, diffList, dotInfo.getBuseRegistTime() != null ? format.format(dotInfo.getBuseRegistTime()) : null, format.format(dotInfoApprove.getBuseRegistTime()), "工商登记时间", 1, 32, null);
        compareAttr(approveId, diffList, dotInfo.getTaxRegistTime() != null ? format.format(dotInfo.getTaxRegistTime()) : null, format.format(dotInfoApprove.getTaxRegistTime()), "税务登记时间", 1, 33, null);
        compareAttr(approveId, diffList, dotInfo.getTaxDeadline() != null ? format.format(dotInfo.getTaxDeadline()) : null, format.format(dotInfoApprove.getTaxDeadline()), "税务有效截止时间", 1, 34, null);
        compareAttr(approveId, diffList, dotInfo.getIndustryDeadline() != null ? format.format(dotInfo.getIndustryDeadline()) : null, format.format(dotInfoApprove.getIndustryDeadline()), "工商有限截止时间", 1, 35, null);
        compareAttr(approveId, diffList, dotInfo.getBuseRegistNumber(), dotInfoApprove.getBuseRegistNumber(), "工商注册号", 1, 36, null);
        compareAttr(approveId, diffList, dotInfo.getApproveTime() != null ? format.format(dotInfo.getApproveTime()) : null, format.format(dotInfoApprove.getApproveTime()), "认证时间", 1, 37, null);
        compareAttr(approveId, diffList, dotInfo.getTaxPoint(), dotInfoApprove.getTaxPoint(), "网点税点", 1, 38, "TaxPoint");
        compareAttr(approveId, diffList, dotInfo.getAptitude(), dotInfoApprove.getAptitude(), "资质", 1, 39, "Aptitude");
        compareAttr(approveId, diffList, dotInfo.getCommonTaxPer(), dotInfoApprove.getCommonTaxPer(), "是否一般纳税人", 1, 40, "CommonTaxPer");
        compareAttr(approveId, diffList, dotInfo.getSmallCarRays(), dotInfoApprove.getSmallCarRays(), "车小鳐数量", 1, 41, null);
        compareAttr(approveId, diffList, dotInfo.getCommonCarNum(), dotInfoApprove.getCommonCarNum(), "普通车辆数量", 1, 42, null);
        compareAttr(approveId, diffList, dotInfo.getDotState() != null ? "1".equals(dotInfo.getDotState()) ? "有效" : "无效" : "", dotInfoApprove.getDotState() != null ? "1".equals(dotInfoApprove.getDotState()) ? "有效" : "无效" : "", "网点状态", 1, 43, null);
        compareAttr(approveId, diffList, dotInfo.getDotSignOut(), dotInfoApprove.getDotSignOut(), "服务兵签退权限", 1, 44, "DotSignOut");
        compareAttr(approveId, diffList, dotInfo.getIsAdvanceMoney() != null ? dotInfo.getIsAdvanceMoney() == 1 ? "是" : "否" : null, dotInfo.getIsAdvanceMoney() != null ? dotInfoApprove.getIsAdvanceMoney() == 1 ? "是" : "否" : null, "是否可垫资", 1, 48, null);
        //图片类
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getBuseLicenseUrl()) ? dotInfo.getBuseLicenseUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getBuseLicenseUrl()) ? dotInfoApprove.getBuseLicenseUrl() : null, "营业执照", 2, 49, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getOfficeSpaceUrl()) ? dotInfo.getOfficeSpaceUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getOfficeSpaceUrl()) ? dotInfoApprove.getOfficeSpaceUrl() : null, "办公场地面积图片", 2, 50, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getSparesSpaceUrl()) ? dotInfo.getSparesSpaceUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getSparesSpaceUrl()) ? dotInfoApprove.getSparesSpaceUrl() : null, "备件库面积图片", 2, 51, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getDoorHeadUrl()) ? dotInfo.getDoorHeadUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getDoorHeadUrl()) ? dotInfoApprove.getDoorHeadUrl() : null, "门头（铜牌）", 2, 52, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getVehicleUrl()) ? dotInfo.getVehicleUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getVehicleUrl()) ? dotInfoApprove.getVehicleUrl() : null, "交通工具", 2, 53, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getServiceAidUrl()) ? dotInfo.getServiceAidUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getServiceAidUrl()) ? dotInfoApprove.getServiceAidUrl() : null, "服务工具", 2, 54, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getConsumableUrl()) ? dotInfo.getConsumableUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getConsumableUrl()) ? dotInfoApprove.getConsumableUrl() : null, "耗材", 2, 55, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getBasicDataUrl()) ? dotInfo.getBasicDataUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getBasicDataUrl()) ? dotInfoApprove.getBasicDataUrl() : null, "建点基础数据申请表", 2, 56, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getHouseContractUrl()) ? dotInfo.getHouseContractUrl() : null, StringUtils.isNotBlank(dotInfoApprove.getHouseContractUrl()) ? dotInfoApprove.getHouseContractUrl() : null, "房屋合同", 2, 57, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getCorporateIdCard()) ? dotInfo.getCorporateIdCard() : null, StringUtils.isNotBlank(dotInfoApprove.getCorporateIdCard()) ? dotInfoApprove.getCorporateIdCard() : null, "法人身份证", 2, 58, "image");
        compareAttr(approveId, diffList, StringUtils.isNotBlank(dotInfo.getElectLicense()) ? dotInfo.getElectLicense() : null, StringUtils.isNotBlank(dotInfoApprove.getElectLicense()) ? dotInfoApprove.getElectLicense() : null, "电工证", 2, 59, "image");
    }

    /**
     * 字段对比并且封装有差异的保存进list
     *
     * @param approveId
     * @param diffList
     * @param oldValue
     * @param newValue
     * @param name
     * @param type
     * @param orderby
     * @param translate
     */
    private void compareAttr(Integer approveId, List<DotApproveDiff> diffList, Object oldValue, Object newValue, String name, Integer type, Integer orderby, String translate) {
        if (oldValue == null && newValue != null) {
            diffList.add(new DotApproveDiff(approveId, type, name, "", newValue.toString(), translate, orderby));
        } else if (oldValue != null && newValue == null) {
            diffList.add(new DotApproveDiff(approveId, type, name, oldValue.toString(), "", translate, orderby));
        } else if (!Objects.equals(oldValue, newValue)) {
            diffList.add(new DotApproveDiff(approveId, type, name, oldValue.toString(), newValue.toString(), translate, orderby));
        }
    }

    /**
     * 插入审批单记录
     *
     * @return
     */
    private DotApproveInfo insertApproveInfo(DotApproveInfoVo dotApproveInfoVo, SysUserEntity user) {
        String name = StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername();
        DotInfo dotInfo = dotApproveInfoVo.getDotInfo();
        DotContacts dotContacts = dotApproveInfoVo.getDotContacts();
        DotApproveInfo dotApproveInfo = new DotApproveInfo();
        dotApproveInfo.setDotId(dotInfo.getDotId());
        dotApproveInfo.setDotCode(dotInfo.getDotCode());
        dotApproveInfo.setDotShortName(dotInfo.getDotShortName());
        dotApproveInfo.setOperationType(dotApproveInfoVo.getOperationType());
        //生成网点审批单号
        String companyInvoiceNo = serialNoVersionService.businessNoMaker('d');
        if (dotInfo.getDotId() != null) {
            //根据网点ID查询网点信息
            LambdaQueryWrapper<DotInfo> dotWrapper = Wrappers.lambdaQuery();
            dotWrapper.select(DotInfo::getDotId, DotInfo::getvCode, DotInfo::getDotName, DotInfo::getDotArea, DotInfo::getDotCity, DotInfo::getDotDistrict, DotInfo::getDotAddress);
            dotWrapper.eq(DotInfo::getDotId, dotInfo.getDotId());
            DotInfo dotInformation = dotInfoMapper.selectOne(dotWrapper);
            dotApproveInfo.setVCode(dotInformation.getvCode()).setApproveNo(companyInvoiceNo).setDotName(dotInformation.getDotName()).setDotArea(Integer.parseInt(dotInformation.getDotArea())).setDotCity(Integer.parseInt(dotInformation.getDotCity())).setDotDistrict(Integer.parseInt(dotInformation.getDotDistrict())).setDotAddress(dotInformation.getDotAddress()).setReason(dotApproveInfoVo.getReason()).setFileId(dotApproveInfoVo.getFileId()).setCreateUser(name);
        } else {
            dotApproveInfo.setVCode(dotInfo.getvCode()).setApproveNo(companyInvoiceNo).setDotName(dotInfo.getDotName()).setDotArea(Integer.parseInt(dotInfo.getDotArea())).setDotCity(Integer.parseInt(dotInfo.getDotCity())).setDotDistrict(Integer.parseInt(dotInfo.getDotDistrict())).setDotAddress(dotInfo.getDotAddress()).setReason(dotApproveInfoVo.getReason()).setFileId(dotApproveInfoVo.getFileId()).setCreateUser(name);
        }
        dotApproveInfo.setContactsName(dotContacts.getContactsName());
        this.save(dotApproveInfo);
        return dotApproveInfo;
    }
}
