package com.bonc.rrs.workManager.controller;

import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.workManager.dao.AutoSendMapper;
import com.bonc.rrs.workManager.service.AutoSendService;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @author: MinJunping
 * @date: 2020/03/26
 */
@Slf4j
@Controller
@RequestMapping(value = "/auto/sendorder")
public class AutoSendController {

    @Autowired(required = false)
    private AutoSendService autoSendService;

    @Autowired(required = false)
    private AutoSendMapper autoSendMapper;

    /**
     * 自动派单给服务经理和网点
     * @param username
     * @param worderNo
     * @return
     */
    @RequestMapping(value = "updatainfo", method = {RequestMethod.GET, RequestMethod.POST},produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint
    public Results autoSendorder(@RequestParam(value = "username") String username,
                                 @RequestParam(value = "worderNo") String worderNo,
                                 @RequestParam(value = "conveyWorderNo", defaultValue = "") String conveyWorderNo){
        if(StringUtils.isNotBlank(conveyWorderNo)){
            //安装勘测工单自动派单逻辑
            Boolean flag = autoSendService.autoSendInstallToConveyDot(username, worderNo, conveyWorderNo);
            if(flag){
                return Results.message(0,"success",null);
            }
        }

        username = username.replace(" ","");
        worderNo = worderNo.replace(" ","");

        return autoSendService.autoSendMagrDot(username, worderNo);

    }

    /**
     * 添加或者更新星级评分接口
     * @param dotStar
     * @return
     */
    @RequestMapping(value = "updatadotstar",method = {RequestMethod.POST, RequestMethod.GET}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results updateNewStarController(@RequestParam(value = "dotStar") String dotStar){

        return autoSendService.updateNewStarRecord(dotStar);

    }

    /**
     * 重置网点周期内接单总计数和重置总的周期总工单数和周期开始时间
     * @return
     */
    @RequestMapping(value = "resetperiodsum",method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results resetPeriodSum(){

        int index = autoSendMapper.resetCountTime();

        int nums = autoSendMapper.updateDotInformation();

        if (index > 0 && nums > 0){
            return Results.message(0,"success",null);
        }else {
            return Results.message(110,"重置失败",null);
        }

    }

    /**
     * 更新自动派单状态
     * @param status
     * @return
     */
    @RequestMapping(value = "updateautosendstate",method = {RequestMethod.GET,RequestMethod.POST},produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results updateAutoSendState(@RequestParam(value = "state") Integer status){

        try {
            int index = autoSendMapper.updateAutoSendStatus("Auto_Send_Single",status);
            if (index > 0){
                return Results.message(0,"success",null);
            }else {
                return Results.message(1,"更新失败",null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return Results.message(110,"接口异常",null);
        }

    }

    /**
     * 获取自动工单状态
     * @return
     */
    @RequestMapping(value = "getautoworderstate",method = {RequestMethod.GET,RequestMethod.POST},produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getAutoWorderState(){

        try {
            int state = autoSendMapper.selectAutoSendStatus("Auto_Send_Single");
            return Results.message(0,"success",state);
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }

    }

//    @RequestMapping(value = "select", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Results getAll(@RequestParam(value = "score") Integer score){
//        Map<String, Object> map = autoSendMapper.selectStarMaxMin(score);
//        return Results.message("0","success",map);
//    }
}
