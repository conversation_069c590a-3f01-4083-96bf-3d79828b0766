package com.bonc.rrs.workManager.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.supervise.dto.SuperviseInfoExport;
import com.bonc.rrs.supervise.vo.SuperviseQueryListParam;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.worder.dao.BizRegionDao;
import com.bonc.rrs.worder.dao.DotInformationDao;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.entity.vo.*;
import com.bonc.rrs.workManager.service.BrandDotRealScoreService;
import com.bonc.rrs.workManager.service.BrandDotScoreService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2023/6/5 15:22
 * @Version: 1.0
 */
@Service
@AllArgsConstructor
@Log4j2
public class BrandDotScoreServiceImpl extends ServiceImpl<BrandDotScoreMapper, BrandDotScoreBean> implements BrandDotScoreService {

    final BrandDotRealScoreService brandDotRealScoreService;

    final BrandDotScoreMapper brandDotScoreMapper;

    final BrandPerEvaluationMapper brandPerEvaluationMapper;

    final ScoreSummaryMapper scoreSummaryMapper;

    final SysDictionaryDetailDao sysDictionaryDetailDao;

    final BrandDao brandDao;

    final BizRegionDao bizRegionDao;

    final DotDispatchMapper dotDispatchMapper;

    final PerformanceTargerMapper performanceTargerMapper;

    final DotInformationDao dotInformationDao;

    final RrsScoreSummaryDetailMapper rrsScoreSummaryDetailMapper;


    @Override
    public R getDotScoreList(DotScoreQueryVo param) {
        try {
            //获取当前登陆用户
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            if (user == null) {
                return R.error("登陆信息超时，请重新登陆");
            }
            //校验周期和起止时间必须填一个
            if (StringUtils.isBlank(param.getCycle()) && StringUtils.isBlank(param.getStartDate()) && StringUtils.isBlank(param.getEndDate())) {
                return R.error("请选择评分周期或者起止时间范围");
            }

            //查询字典
            List<SysDictionaryDetailEntity> performanceRole = sysDictionaryDetailDao.selectDetailByNumbers("performance_role");
            Map<String, String> scoreRoleMap = performanceRole.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (v1, v2) -> v2));
            List<Long> roleIdList = user.getRoleIdList();
            //运营、大区、项目经理，网点，超级管理员，其他角色默认不展示（即使配置菜单权限也无法查看数据）
            if (!isRole(scoreRoleMap.get("ZB"), roleIdList)
                    && !isRole(scoreRoleMap.get("DQ"), roleIdList)
                    && !isRole(scoreRoleMap.get("XMJL"), roleIdList)
                    && !isRole(scoreRoleMap.get("WD"), roleIdList)
                    && !roleIdList.contains(1L)) {
                return R.ok().put("list", null);
            }
            //查询条件区域级别，0：所有，1：地市级别，2：省份级别，3：大区级别
            int flags;
            if (param.getCity() != null) {
                flags = 1;
            } else if (param.getProvince() != null) {
                flags = 2;
            } else if (StringUtils.isNotBlank(param.getRegione())) {
                flags = 3;
            } else {
                flags = 0;
            }

            // 如果起止时间不等于空，走实时视图查询
            if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
                List<Map<String, Object>> realList = queryScoreReal(param, user, flags);
                return R.ok().put("list", realList);
            }

            List<Map<String, Object>> list = queryScoreByRole(param, user, flags);
            //查询条件到地市级别，需要额外查询地市下各网点的派单比例
            if (flags == 1 && StringUtils.isNotBlank(param.getCycle())) {
                //设置网点派单比例信息
                setDotProportion(list, param);
            }
            //如果是网点管理员角色进入并且展示网点的指标时，只展示当前网点名称，其他网点均展示xxxx
            if (isRole(scoreRoleMap.get("WD"), roleIdList) && (flags == 1 || flags == 2)) {
                desDotName(list, user);
            }

            return R.ok().put("list", list);
        } catch (Exception e) {
            log.error("网点业绩查询出现异常！！", e);
            return R.error("查询出现异常,请联系管理员");
        }
    }

    /**
     * 如果网点管理员权限，对网点名称进行加密处理，只展示自己的网点
     *
     * @param list
     * @param user
     */
    private void desDotName(List<Map<String, Object>> list, SysUserEntity user) {
        //根据网点管理员查询对应网点
        String dotName = dotInformationDao.selectDotByUserName(user.getUsername());
        dotName = Optional.ofNullable(dotName).orElse("");
        for (Map<String, Object> item : list) {
            Object obj = item.get("scoreInfo");
            String type = (String) item.get("type");
            if (obj != null) {
                List<ScoreVo> itemScoreList = (List<ScoreVo>) obj;
                Set<String> uniqueStrings = new HashSet<>();
                for (ScoreVo scoreVo : itemScoreList) {
                    if (!dotName.contains(scoreVo.getName()) && !"平均值".equals(scoreVo.getName())) {
                        String replacedStr = scoreVo.getName().replaceAll(".", "x");
                        while (uniqueStrings.contains(replacedStr)) {
                            if ("Bar".equals(type)) {
                                replacedStr = " " + replacedStr;
                            } else {
                                replacedStr += " ";
                            }
                        }
                        uniqueStrings.add(replacedStr);
                        scoreVo.setName(replacedStr);
                    }
                }
            }
        }
    }

    /**
     * 设置网点比例
     *
     * @param list
     */
    private void setDotProportion(List<Map<String, Object>> list, DotScoreQueryVo param) {
        if(param.getBrandIds().size() > 1){
            return;
        }
        //根据，周期，品牌，地市查询网点评分表
        List<BrandDotScoreBean> brandDotScoreBeans = brandDotScoreMapper.queryList(param);
        if (brandDotScoreBeans.isEmpty()) {
            return;
        }
        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("title", "网点派单比例");
        tempMap.put("flags", 1);
        tempMap.put("unit", "百分比");
        tempMap.put("numType", 0);
        tempMap.put("type", "Pie");
        int dotNum = brandDotScoreBeans.size();
        Integer brandId = param.getBrandIds().get(0);
        if (brandDotScoreBeans.size() > 1 && brandDotScoreBeans.size() <= 12) {
            //如果是上海特斯拉的需要定制比例
            String ratio;
            if (brandId == 14 && param.getCity() == 105L) {
                ratio = dotDispatchMapper.selectDispatchRatio(dotNum, param.getCity().longValue(), brandId);
            } else {
                //根据网点数量和品牌获取匹配的网点规则比例 x:x:x...
                ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, brandId);
                if (StringUtils.isBlank(ratio)) {
                    ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, -1);
                }
            }
            // 如果没有派单比例直接返回不展示比例
            if (StringUtils.isBlank(ratio)) {
                return;
            }
            //把查询的评分进行由大到小排序
            List<ScoreVo> scoreVos = new ArrayList<>();
            brandDotScoreBeans.forEach(item -> {
                ScoreVo scoreVo = new ScoreVo();
                if (item.getManualScore().compareTo(BigDecimal.ZERO) > 0) {
                    scoreVo.setScore(item.getManualScore());
                } else {
                    scoreVo.setScore(item.getScore());
                }
                scoreVo.setName(item.getDotName());
                scoreVos.add(scoreVo);
            });
            //根据评分排序
            scoreVos.sort((item1, item2) -> item2.getScore().compareTo(item1.getScore()));
            //比例调整如果评分一致，比例也需要保持一致
            String[] numArray = ratio.split(":");
            //如果有网点评分一样，以最高比例计算
            for (int i = 0; i < scoreVos.size() - 1; i++) {
                ScoreVo scoreVo = scoreVos.get(i);
                Float current = Float.valueOf(scoreVo.getScore().toString());
                ScoreVo nextScoreVo = scoreVos.get(i + 1);
                Float next = Float.valueOf(nextScoreVo.getScore().toString());
                if (Math.abs(current - next) == 0) {
                    numArray[i + 1] = numArray[i];
                }
            }
            //计算网点比例之和
            for (int i = 0; i < numArray.length; i++) {
                ScoreVo scoreVo = scoreVos.get(i);
                scoreVo.setValue(new BigDecimal(numArray[i]));
            }
            tempMap.put("scoreInfo", scoreVos);
        } else if (brandDotScoreBeans.size() == 1) {
            BrandDotScoreBean brandDotScoreBean = brandDotScoreBeans.get(0);
            List<ScoreVo> scoreVos = new ArrayList<>();
            ScoreVo scoreVo = new ScoreVo();
            scoreVo.setValue(BigDecimal.ONE);
            scoreVo.setName(brandDotScoreBean.getDotName());
            scoreVos.add(scoreVo);
            tempMap.put("scoreInfo", scoreVos);
        } else {
            return;
        }
        list.add(tempMap);
    }

    /**
     * 查询实时评分数据
     *
     * @param param
     * @param user
     * @param flags
     * @return
     */
    private List<Map<String, Object>> queryScoreReal(DotScoreQueryVo param, SysUserEntity user, int flags) {
        List<Map<String, Object>> result = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //起始和结束日期减去30天
        LocalDate startDate = LocalDate.parse(param.getStartDate(), formatter);
        LocalDate endDate = LocalDate.parse(param.getEndDate(), formatter);
        //如果是理想或者特斯拉 T-30, 潜力品牌T-10
        if (param.getBrandIds().get(0) == 14 || param.getBrandIds().get(0) == 18) {
            startDate = startDate.minusDays(30);
            endDate = endDate.minusDays(30);
        } else {
            startDate = startDate.minusDays(10);
            endDate = endDate.minusDays(10);
        }

        param.setStartDate(startDate.format(formatter));
        param.setEndDate(endDate.format(formatter));

        //查询当前用户是否配置区域
//        List<Long> roleIdList = user.getRoleIdList();
//        Set<Long> areaIds = new HashSet<>();
//        if (roleIdList.contains(4L)) {
//            areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
//        }

        //如果是查询省份或者地市级别查询关联的所有网点信息
        List<DotInfoVo> dotIds;
        if (flags == 1) {
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getCity().toString()));
            dotIds = this.baseMapper.queryDotIdsByArea(bizRegionEntity.getRegcode(), param.getBrandIds());
        } else if (flags == 2) {
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getProvince().toString()));
            dotIds = this.baseMapper.queryDotIdsByArea(bizRegionEntity.getRegcode(), param.getBrandIds());
        } else {
            dotIds = new ArrayList<>();
        }
        // 查询实时指标
        List<ScoreVo> list = rrsScoreSummaryDetailMapper.queryScoreReal(param, null, flags);
        //分组并且返回前端结果
        Map<String, List<ScoreVo>> collect = list.stream().collect(Collectors.groupingBy(ScoreVo::getTitle));
        collect.forEach((k, v) -> {
            if (v != null && !v.isEmpty()) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("title", k);
                tempMap.put("flags", flags);
                tempMap.put("unit", v.get(0).getUnit());
                tempMap.put("numType", v.get(0).getNumType());
                tempMap.put("target", null);
                //添加权限范围内但是没有单子的区域
                tempMap.put("type", "Bar");
                //计算并且添加平均值
                tempMap.put("scoreInfo", calculateAve(v, dotIds));
                result.add(tempMap);
            }
        });

        return result;
    }

    /**
     * 查询评分数据
     *
     * @param param
     * @return
     */
    private List<Map<String, Object>> queryScoreByRole(DotScoreQueryVo param, SysUserEntity user, int flags) {
        List<Map<String, Object>> result = new ArrayList<>();

        //查询当前用户是否配置区域
//        Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
        param.setBrand(param.getBrandIds().get(0));
        List<ScoreVo> list = scoreSummaryMapper.queryScoreByRole(param, flags, null);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Integer, BigDecimal> targetMaps;
        //如果查询条件有省份和地市时，需要查询对应指标的目标值
        if ((flags == 1 || flags == 2) && param.getBrandIds().size() == 1) {
            Integer province = param.getProvince();
            Set<Integer> indicatorIds = list.stream().map(ScoreVo::getIndicatorId).collect(Collectors.toSet());
            //查询目标值
            LambdaQueryWrapper<PerformanceTargerEntity> performanceTargerWrapper = Wrappers.lambdaQuery();
            performanceTargerWrapper.eq(PerformanceTargerEntity::getCycle, param.getCycle())
                    .eq(PerformanceTargerEntity::getBrandId, param.getBrandIds().get(0))
                    .eq(PerformanceTargerEntity::getProvinceId, province)
                    .in(PerformanceTargerEntity::getIndicatorId, indicatorIds).eq(PerformanceTargerEntity::getDeleteState, 0);
            List<PerformanceTargerEntity> performanceTargerEntities = performanceTargerMapper.selectList(performanceTargerWrapper);
            if (!performanceTargerEntities.isEmpty()) {
                targetMaps = performanceTargerEntities.stream().collect(Collectors.toMap(PerformanceTargerEntity::getIndicatorId, PerformanceTargerEntity::getTarget, (val1, val2) -> val1));
            } else {
                targetMaps = null;
            }
        } else {
            targetMaps = null;
        }

        //如果是查询省份或者地市级别查询关联的所有网点信息
        List<DotInfoVo> dotIds;
        if (flags == 1) {
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getCity().toString()));
            dotIds = this.baseMapper.queryDotIdsByArea(bizRegionEntity.getRegcode(), param.getBrandIds());
        } else if (flags == 2) {
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getProvince().toString()));
            dotIds = this.baseMapper.queryDotIdsByArea(bizRegionEntity.getRegcode(), param.getBrandIds());
        } else {
            dotIds = new ArrayList<>();
        }

        Map<String, List<ScoreVo>> collect = list.stream().collect(Collectors.groupingBy(ScoreVo::getTitle));
        collect.forEach((k, v) -> {
            if (v != null && !v.isEmpty()) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("title", k);
                tempMap.put("flags", flags);
                tempMap.put("unit", v.get(0).getUnit());
                tempMap.put("numType", v.get(0).getNumType());
                tempMap.put("target", targetMaps != null ? targetMaps.get(v.get(0).getIndicatorId()) : null);
                //添加权限范围内但是没有单子的区域
                tempMap.put("type", "Bar");
                //计算并且添加平均值
                tempMap.put("scoreInfo", calculateAve(v, dotIds));
                result.add(tempMap);
            }
        });
        return result;
    }

    /**
     * 计算并且加入平均值
     *
     * @param list
     * @return
     */
    private List<ScoreVo> calculateAve(List<ScoreVo> list, List<DotInfoVo> dotIds) {
        BigDecimal totalScore = list.stream().map(ScoreVo::getTotalScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal total = list.stream().map(ScoreVo::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal divide = totalScore.divide(total, 4, RoundingMode.HALF_UP);
        ScoreVo scoreVo = list.get(0);
        //补充网点信息
        if (dotIds != null && !dotIds.isEmpty()) {
            Map<Integer, String> dotMap = dotIds.stream().collect(Collectors.toMap(DotInfoVo::getDotId, DotInfoVo::getDotName, (val1, val2) -> val1));
            Set<Integer> dotList = list.stream().map(ScoreVo::getDotId).collect(Collectors.toSet());
            if (!dotList.isEmpty()) {
                dotList.forEach(dotMap::remove);
                for (Map.Entry<Integer, String> entry : dotMap.entrySet()) {
                    list.add(new ScoreVo(scoreVo.getTitle(), scoreVo.getUnit(), scoreVo.getNumType(), entry.getValue().substring(0, 6), BigDecimal.ZERO));
                }
            }
        }
        //排序
        list.sort(Comparator.comparing(ScoreVo::getName));
        //第一条加入平均值
        list.add(0, new ScoreVo(scoreVo.getTitle(), scoreVo.getUnit(), scoreVo.getNumType(), "平均值", divide));
        return list;
    }

    @Override
    public List<DotBrandScoreExport> selectExtportList(DotScoreQueryVo param) {
        //获取当前登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            throw new RRException("登陆失效，请重新登陆");
        }

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            throw new RRException("请选择起止时间导出");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        //起始和结束日期减去30天
        LocalDate startDate = LocalDate.parse(param.getStartDate(), formatter);
        LocalDate endDate = LocalDate.parse(param.getEndDate(), formatter);
        //如果是理想或者特斯拉 T-30, 潜力品牌T-10
        if (param.getBrandIds().get(0) == 14 || param.getBrandIds().get(0) == 18) {
            startDate = startDate.minusDays(30);
            endDate = endDate.minusDays(30);
        } else {
            startDate = startDate.minusDays(10);
            endDate = endDate.minusDays(10);
        }

        param.setStartDate(startDate.format(formatter));
        param.setEndDate(endDate.format(formatter));

        //查询当前用户是否配置区域
//        Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
        //只导实时数据
        return queryDotBrandScoreByReal(param, null);
    }

    private List<DotBrandScoreExport> queryDotBrandScoreByReal(DotScoreQueryVo param, Long userId) {
        //查询当前用户是否配置区域
//        Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(userId);
        return rrsScoreSummaryDetailMapper.queryScoreExport(param, null);
    }


    @Override
    public void implementBrandDotScoreTask() {
        //查询存储过程执行顺序表
        LambdaQueryWrapper<BrandPerEvaluation> evaluationWrapper = Wrappers.lambdaQuery();
        evaluationWrapper.eq(BrandPerEvaluation::getDeleteState, 0).orderByAsc(BrandPerEvaluation::getExeSort);
        List<BrandPerEvaluation> brandPerEvaluations = brandPerEvaluationMapper.selectList(evaluationWrapper);
        if (brandPerEvaluations == null || brandPerEvaluations.isEmpty()) {
            log.info("未查询到需要执行的存储过程");
            return;
        }
        //已执行的存储过程
        HashSet<String> executedProcedure = new HashSet<>();

//        for (BrandPerEvaluation brandPerEvaluation : brandPerEvaluations) {
//            if (!executedProcedure.contains(brandPerEvaluation.getProduceName())) {
//                log.info("开始执行存储过程，名称：{}，描述：{}", brandPerEvaluation.getProduceName(), brandPerEvaluation.getProduceDesc());
//                brandPerEvaluationMapper.callProcedure(brandPerEvaluation.getProduceName());
//                log.info("存储过程执行完成，名称：{}，描述：{}", brandPerEvaluation.getProduceName(), brandPerEvaluation.getProduceDesc());
//                executedProcedure.add(brandPerEvaluation.getProduceName());
//            }
//        }
        log.info("抽取数据的存储过程执行完成{}", executedProcedure);
        LocalDate currentDate = LocalDate.now();
        LocalDate previousMonth = currentDate.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formattedDate = previousMonth.format(formatter);
        //查询抽取的数据
        List<ScoreSummary> scoreSummaries = scoreSummaryMapper.statisticsCycleList(formattedDate);
        if (scoreSummaries == null || scoreSummaries.isEmpty()) {
            log.info("周期：{} ,未查询到存储过程抽取的数据!", formattedDate);
            return;
        }
        //转成Map
        Map<String, BigDecimal> scoreMaps = scoreSummaries.stream().collect(Collectors.toMap(item -> item.getBrandId() + "-" + item.getDotId() + "-" + item.getCity(), ScoreSummary::getScore, (val1, val2) -> val1));

        //查询本次周期的导入数据
        LambdaQueryWrapper<BrandDotScoreBean> brandDotScoreWrapper = Wrappers.lambdaQuery();
        brandDotScoreWrapper.eq(BrandDotScoreBean::getCycle, formattedDate).eq(BrandDotScoreBean::getDeleteState, 0);
        List<BrandDotScoreBean> brandDotScoreBeans = brandDotScoreMapper.selectList(brandDotScoreWrapper);

        //查询数据字典获取业绩(KPI)50%+服务能力20%+增值业绩20%+管理者关键任务评分10%+扣分项权重
        List<SysDictionaryDetailEntity> scoringWeight = sysDictionaryDetailDao.selectDetailByNumbers("scoring_weight");
        Map<String, BigDecimal> weightMap = scoringWeight.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, item -> new BigDecimal(item.getDetailName()), (val1, val2) -> val1));

        //获取当前数据字典配置的服务兵上限
        List<SysDictionaryDetailEntity> attendantSendCeiling = sysDictionaryDetailDao.selectDetailByNumbers("attendant_sendCeiling");
        String detailNumber = attendantSendCeiling.get(0).getDetailNumber();
        Integer sendCeiling = Integer.parseInt(detailNumber);

        //网点的服务能力得分
        Map<Integer, BigDecimal> serviceAbilityMaps = new HashMap<>();

        //更新数据
        List<BrandDotScoreBean> updateData = new ArrayList<>();

        for (BrandDotScoreBean brandDotScoreBean : brandDotScoreBeans) {
            //获取业绩评分
            String key = brandDotScoreBean.getBrandId() + "-" + brandDotScoreBean.getDotId() + "-" + brandDotScoreBean.getCity();
            BigDecimal performanceScore = scoreMaps.get(key);
            //如果map中没有业绩评分默认赋值为0
            if (performanceScore == null) {
                performanceScore = BigDecimal.ZERO;
            } else {
                //如果map中有业绩评分剔除当前key
                scoreMaps.remove(key);
            }
            //获取业绩评分乘以权重
            BigDecimal performance = performanceScore.multiply(weightMap.get("1")).setScale(2, RoundingMode.HALF_UP);
            //获取服务能力
            BigDecimal serviceAbility = serviceAbilityMaps.get(brandDotScoreBean.getDotId());
            if (serviceAbility == null) {
                serviceAbility = getServiceAbility(brandDotScoreBean.getDotId(), sendCeiling, weightMap.get("2"));
                serviceAbilityMaps.put(brandDotScoreBean.getDotId(), serviceAbility);
            }
            BigDecimal addedPerformance = brandDotScoreBean.getAddedPerformance().multiply(weightMap.get("3")).setScale(2, RoundingMode.HALF_UP);
            BigDecimal missionCritical = brandDotScoreBean.getMissionCritical().multiply(weightMap.get("4")).setScale(2, RoundingMode.HALF_UP);
            //计算最终评分
            BigDecimal score = performance.add(serviceAbility).add(addedPerformance).add(missionCritical).add(brandDotScoreBean.getDeduct()).setScale(2, RoundingMode.HALF_UP);
            //重新赋值
            brandDotScoreBean.setPerformance(performance);
            brandDotScoreBean.setServiceAbility(serviceAbility);
            //最终评分不能为负数最低为1
            brandDotScoreBean.setScore(score.compareTo(BigDecimal.ZERO) > 0 ? score : BigDecimal.ONE);
            updateData.add(brandDotScoreBean);
            //每500条批量提交一次更新
            if (updateData.size() >= 500) {
                this.updateBatchById(updateData);
                updateData = new ArrayList<>();
            }
        }
        if (!updateData.isEmpty()) {
            this.updateBatchById(updateData);
        }

        //插入业绩评分数据，此部分数据为未导入的数据
        if (!scoreMaps.isEmpty()) {
            //需要插入的数据集合
            List<BrandDotScoreBean> insertData = new ArrayList<>();
            //查询品牌表
            List<BrandEntity> brandEntities = brandDao.selectList(new QueryWrapper<>());
            Map<Integer, String> brandMap = brandEntities.stream().collect(Collectors.toMap(BrandEntity::getId, BrandEntity::getBrandName, (val1, val2) -> val1));

            for (Map.Entry<String, BigDecimal> entry : scoreMaps.entrySet()) {
                String k = entry.getKey();
                BigDecimal v = entry.getValue();
                if (v == null) {
                    v = BigDecimal.ZERO;
                }

                String[] split = k.split("-");
                //获取业绩评分乘以权重
                BigDecimal performance = v.multiply(weightMap.get("1")).setScale(2, RoundingMode.HALF_UP);
                //获取服务能力
                BigDecimal serviceAbility = serviceAbilityMaps.get(Integer.parseInt(split[1]));
                if (serviceAbility == null) {
                    serviceAbility = getServiceAbility(Integer.parseInt(split[1]), sendCeiling, weightMap.get("2"));
                    serviceAbilityMaps.put(Integer.parseInt(split[1]), serviceAbility);
                }
                //计算最终评分
                BigDecimal score = performance.add(serviceAbility).setScale(2, RoundingMode.HALF_UP);
                Integer brandId = Integer.parseInt(split[0]);
                insertData.add(BrandDotScoreBean.builder()
                        .brandId(brandId)
                        .brandName(brandMap.get(brandId))
                        .cycle(formattedDate)
                        .city(Integer.parseInt(split[2]))
                        .dotId(Integer.parseInt(split[1]))
                        .performance(performance)
                        .serviceAbility(serviceAbility)
                        .score(score).build());
                if (insertData.size() >= 500) {
                    this.saveBatch(insertData);
                    insertData = new ArrayList<>();
                }
            }

            if (!insertData.isEmpty()) {
                this.saveBatch(insertData);
            }
        }
    }

    @Override
    public R getRegionList(Integer brandId) {
        //获取当前登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        //角色集合
        List<Long> roleIdList = user.getRoleIdList();
        Map<String, List<BizRegionEntity>> regions;
        //总部运营和admin获取所有大区
        String defaultRegione = null;
        Long defaultProvince = null;
        try {
            //查询字典
            List<SysDictionaryDetailEntity> performanceRole = sysDictionaryDetailDao.selectDetailByNumbers("performance_role");
            Map<String, String> scoreRoleMap = performanceRole.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (v1, v2) -> v2));
            String roles = scoreRoleMap.get("ZB");

            //查询当前用户是否配置区域
            Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
            if (isRole(scoreRoleMap.get("WD"), roleIdList)) {
                //查询当前配置的区域下的区域
                List<BizRegionEntity> bizRegionEntities = bizRegionDao.getProvinceByDotUser(user.getUserId());
                if (brandId == 18) {
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getLxRegion));
                }else if (brandId == 14){
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getTslRegion));
                }else{
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getRegione));
                }
            }else if (areaIds.isEmpty()) {
                regions = getAllRegion(brandId);
            } else {
                //查询当前配置的区域下的区域
                List<BizRegionEntity> bizRegionEntities = bizRegionDao.getProvinceByUser(user.getUserId());
                if (brandId == 18) {
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getLxRegion));
                }else if (brandId == 14){
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getTslRegion));
                }else{
                    regions = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getRegione));
                }
            }
            //如果没有大区信息返回错误提示前端
            if (regions.isEmpty()) {
                return R.error("未查询到区域信息，请检查区域配置");
            }
            Set<String> regioneSet = regions.keySet();
            //区域总监角色默认选择一个大区
            if (isRole(roles, roleIdList)) {
            } else if (isRole(scoreRoleMap.get("DQ"), roleIdList)) {
                defaultRegione = regioneSet.stream().findFirst().orElse(null);
            } else if (isRole(scoreRoleMap.get("XMJL"), roleIdList) || isRole(scoreRoleMap.get("WD"), roleIdList)) {
                //项目经理默认选择一个大区和省
                defaultRegione = regioneSet.stream().findFirst().orElse(null);
                List<BizRegionEntity> bizRegionEntities = regions.get(defaultRegione);
                defaultProvince = bizRegionEntities.get(0).getId();
            }
        } catch (Exception e) {
            log.error("初始化大区数据出现异常!", e);
            return R.error("获取区域数据失败");
        }
        return Objects.requireNonNull(Objects.requireNonNull(R.ok().put("regions", regions)).put("defaultRegione", defaultRegione)).put("defaultProvince", defaultProvince);
    }

    private boolean isRole(String roles, List<Long> roleIdList) {
        for (String role : roles.split(",")) {
            if (roleIdList.contains(Long.parseLong(role))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public R getCityList(Integer province) {
        if (province == null) {
            return R.error("省份不能为空");
        }
        //获取当前登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        Long defaultCity = null;
        //根据省份获取下面的地市信息
        List<BizRegionEntity> resultRegionEntities = new ArrayList<>();
        try {
            // 获取角色列表
            List<Long> roleIdList = user.getRoleIdList();
            // 获取账户配置的区域信息
            Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
            // 获取当前省份下的地市
            List<BizRegionEntity> bizRegionEntities = InitRegionUtil.REGION_PID_MAP.get(province.longValue());
            // 如果区域账户区域为空，并且角色不是网点角色返回所有地市
            if (areaIds.isEmpty() && !roleIdList.contains(4L)) {
                return R.ok().put("cityList", bizRegionEntities);
            }
            //查询账户辐射的区域信息
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(province.longValue());
            List<BizRegionEntity> list;
            if (roleIdList.contains(4L)) {
                list = bizRegionDao.getListByDotUserAndRegCode(user.getUserId(), bizRegionEntity.getRegcode());
            } else {
                list = bizRegionDao.getListByUserAndRegCode(user.getUserId(), bizRegionEntity.getRegcode());
            }

            if (!list.isEmpty()) {
                long count = list.stream().filter(item -> item.getId() == province.longValue()).count();
                if (count < 1) {
                    Set<Long> cityIds = new HashSet<>();
                    list.forEach(item -> {
                        if (item.getType() == 2) {
                            cityIds.add(item.getId());
                        } else if (item.getType() == 3) {
                            cityIds.add(item.getPid());
                        }
                    });
                    resultRegionEntities = bizRegionEntities.stream().filter(item -> cityIds.contains(item.getId())).collect(Collectors.toList());
                } else {
                    resultRegionEntities = bizRegionEntities;
                }
            }
            if (roleIdList.contains(4L) && !resultRegionEntities.isEmpty()) {
                BizRegionEntity bizRegion = resultRegionEntities.get(0);
                defaultCity = bizRegion.getId();
            }
        } catch (Exception e) {
            log.error("获取地市列表异常", e);
            return R.error("获取地市列表失败");
        }
        return R.ok().put("cityList", resultRegionEntities).put("defaultCity", defaultCity);
    }

    @Override
    public R getBrandList() {
        //获取当前登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        List<Long> roleIdList = user.getRoleIdList();
        List<BrandEntity> brandEntities;
        try {
            if (roleIdList.contains(4L)) {
                //网点管理员查询网点关联的品牌
                brandEntities = brandDao.selectListByDotUser(user.getUserId());
            } else {
                //角色集合
                brandEntities = brandDao.selectListByUser(user.getUserId());
            }
            if (brandEntities.isEmpty()) {
                return R.error("当前账户权限未查询到品牌信息");
            }
        } catch (Exception e) {
            log.error("查询品牌信息异常", e);
            return R.error("查询品牌信息失败");
        }
        Integer defaultBrand;
        long countLx = brandEntities.stream().filter(item -> item.getId().equals(18)).count();
        long countTsl = brandEntities.stream().filter(item -> item.getId().equals(14)).count();
        if (countLx > 0) {
            defaultBrand = 18;
        } else if (countTsl > 0) {
            defaultBrand = 14;
        } else {
            defaultBrand = brandEntities.get(0).getId();
        }
        return Objects.requireNonNull(R.ok().put("brandList", brandEntities)).put("defaultBrand", defaultBrand);
    }

    @Override
    public IPage<BrandDotScoreBean> selectPageByDot(Page<BrandDotScoreBean> page, Integer region, Integer dotId, String queryFormattedDate) {
        return brandDotScoreMapper.selectPageByDot(page, region, dotId, queryFormattedDate);
    }

    @Override
    public R getDotTables(DotScoreTableVo param) {
        List<JSONObject> result = new ArrayList<>();
        // 分页查询业绩评分表
        if(StringUtils.isNotBlank(param.getRegionId())){
            BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getRegionId()));
            if(bizRegionEntity.getType() == 1){
                List<BizRegionEntity> bizRegionEntities = InitRegionUtil.REGION_PID_MAP.get(Long.parseLong(param.getRegionId()));
                // 转为逗号隔开
                String ids = bizRegionEntities.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.joining(", "));
                param.setRegionId(ids);
            }else if (bizRegionEntity.getType() == 3){
                param.setRegionId(String.valueOf(bizRegionEntity.getPid()));
            }
        }
        IPage<Map<String, Object>> list = brandDotScoreMapper.selectPageTables(new Page<>(param.getPage(), param.getPageSize()), param);
        List<Map<String, Object>> records = list.getRecords();
        if (records.isEmpty()) {
            return R.ok().put("list", new ArrayList<>()).put("total", 0);
        }
        QueryWrapper<BrandEntity> brandEntityQueryWrapper = new QueryWrapper<>();
        brandEntityQueryWrapper.orderByDesc("id");
        // 查询所有品牌
        List<BrandEntity> brandEntities = brandDao.selectList(brandEntityQueryWrapper);
        // 根据records查询所有对应的品牌数据
        List<BrandDotScoreBean> brandDotScoreBeans = brandDotScoreMapper.selectListByRecords(records);
        // 查询所有品牌对应的业绩评分
        Map<String, List<BrandDotScoreBean>> brandScores = brandDotScoreBeans.stream().collect(Collectors.groupingBy(item -> item.getCycle() + "-" + item.getDotId() + "-" + item.getCity()));
        for (Map<String, Object> record : records) {
            JSONObject itemJson = new JSONObject();
            itemJson.put("dotName", record.get("dot_name"));
            String city = record.get("city").toString();
            if (StringUtils.isNotBlank(city)) {
                BizRegionEntity cityRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(city));
                BizRegionEntity proRegionEntity = InitRegionUtil.REGION_ID_MAP.get(cityRegionEntity.getPid());
                itemJson.put("regionName", proRegionEntity.getName() + cityRegionEntity.getName());
            }
            setTitleBrand(itemJson, brandEntities, record, brandScores);
            result.add(itemJson);
        }
        return R.ok().put("list", result).put("total", list.getTotal());
    }

    @Override
    public void export(DotScoreTableVo param, HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            if(StringUtils.isNotBlank(param.getRegionId())){
                BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(param.getRegionId()));
                if(bizRegionEntity.getType() == 1){
                    List<BizRegionEntity> bizRegionEntities = InitRegionUtil.REGION_PID_MAP.get(Long.parseLong(param.getRegionId()));
                    // 转为逗号隔开
                    String ids = bizRegionEntities.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.joining(", "));
                    param.setRegionId(ids);
                }else if (bizRegionEntity.getType() == 3){
                    param.setRegionId(String.valueOf(bizRegionEntity.getPid()));
                }
            }
            String filename = new String("网点业绩评分".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xls");
            excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
            //查询导出数据
            List<BrandDotScoreExport> brandDotScoreExports = brandDotScoreMapper.queryBrandDotScoreReport(param);
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "网点业绩评分").head(BrandDotScoreExport.class).build();
            excelWriter.write(brandDotScoreExports, writeSheet);
        } catch (Exception e) {
            log.error("导出网点业绩评分出现异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    private void setTitleBrand(JSONObject itemJson, List<BrandEntity> brandEntities, Map<String, Object> record, Map<String, List<BrandDotScoreBean>> brandScores) {
        if (brandEntities == null || record == null || brandScores == null) {
            brandEntities.forEach(item -> itemJson.put("title-" + item.getBrandName(), 0));
        }
        String key = record.get("cycle") + "-" + record.get("dot_id") + "-" + record.get("city");
        List<BrandDotScoreBean> brandDotScoreBeans = brandScores.get(key);
        if (brandDotScoreBeans == null || brandDotScoreBeans.isEmpty()) {
            brandEntities.forEach(item -> itemJson.put("title-" + item.getBrandName(), 0));
        }
        Map<Integer, BrandDotScoreBean> brandMap = brandDotScoreBeans.stream().collect(Collectors.toMap(BrandDotScoreBean::getBrandId, item -> item, (val1, val2) -> val2));
        for (BrandEntity brandEntity : brandEntities) {
            BrandDotScoreBean brandDotScoreBean = brandMap.get(brandEntity.getId());
            if(brandDotScoreBean != null){
                itemJson.put("title-" + brandEntity.getId(), brandDotScoreBean.getScore());
                Map<String, BigDecimal> detail = new HashMap<>();
                detail.put("performance", brandDotScoreBean.getPerformance());
                detail.put("serviceAbility", brandDotScoreBean.getServiceAbility());
                detail.put("addedPerformance", brandDotScoreBean.getAddedPerformance());
                detail.put("missionCritical", brandDotScoreBean.getMissionCritical());
                detail.put("deduct", brandDotScoreBean.getDeduct());
                detail.put("manualScore", brandDotScoreBean.getManualScore());
                detail.put("score", brandDotScoreBean.getScore());
                detail.put("manualScore", brandDotScoreBean.getManualScore());
                itemJson.put("title-" + brandEntity.getId() + "-detail", detail);
            }else{
                itemJson.put("title-" + brandEntity.getId(), 0);
                Map<String, BigDecimal> detail = new HashMap<>();
                detail.put("performance", BigDecimal.ZERO);
                detail.put("serviceAbility", BigDecimal.ZERO);
                detail.put("addedPerformance", BigDecimal.ZERO);
                detail.put("missionCritical", BigDecimal.ZERO);
                detail.put("deduct", BigDecimal.ZERO);
                detail.put("manualScore", BigDecimal.ZERO);
                detail.put("score", BigDecimal.ZERO);
                detail.put("manualScore", BigDecimal.ZERO);
                itemJson.put("title-" + brandEntity.getId() + "-detail", detail);
            }
        }
    }

    /**
     * 获取所有大区下省份并且分组
     *
     * @return
     */
    public Map<String, List<BizRegionEntity>> getAllRegion(Integer brandId) {
        LambdaQueryWrapper<BizRegionEntity> bizRegionWrapper = Wrappers.lambdaQuery();
        bizRegionWrapper.select(BizRegionEntity::getRegione, BizRegionEntity::getLxRegion, BizRegionEntity::getTslRegion, BizRegionEntity::getId, BizRegionEntity::getName)
                .eq(BizRegionEntity::getType, 1);
        List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(bizRegionWrapper);
        if(brandId == 18){
            return bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getLxRegion));
        }else if(brandId == 14){
            return bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getTslRegion));
        }else{
            return bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getRegione));
        }
    }

    /**
     * 获取网点服务能力
     *
     * @param dotId
     * @param sendCeiling
     * @return
     */
    private BigDecimal getServiceAbility(Integer dotId, Integer sendCeiling, BigDecimal weight) {
        // 查询网点服务能力M ，M=等于 上月单量/（30*3）
        BigDecimal m = brandDotScoreMapper.getTargetServiceAbility(dotId, sendCeiling);
        // 查询网点实际服务能力S， S=系统实际有效服务能力，
        //（1车+1组服务兵）为1 组有效服务能力（取小）
        List<Map<String, Object>> sList = brandDotScoreMapper.getActualServiceAbility(dotId);
        Map<String, Object> sMap = sList.get(0);
        Object commonCarNum = sMap.get("common_car_num");
        Object attendantNum = sMap.get("attendant_num");
        if (commonCarNum == null || attendantNum == null || m == null) {
            return BigDecimal.ZERO;
        }
        int nCommonCarNum = (Integer) commonCarNum;
        Long nAttendantNum = (Long) attendantNum;
        int s = Math.min(nAttendantNum.intValue(), nCommonCarNum);
        if (s == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = m.divide(BigDecimal.valueOf(s), RoundingMode.HALF_UP);
        result = result.compareTo(BigDecimal.ONE) >= 0 ? BigDecimal.ONE : result;
        weight = weight.multiply(new BigDecimal(100));
        return result.multiply(weight).setScale(2, RoundingMode.HALF_UP);
    }
}
