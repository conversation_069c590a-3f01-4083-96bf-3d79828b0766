package com.bonc.rrs.workManager.controller;


import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 服务兵派单量记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
@RestController
@RequestMapping("/attendant/sends/record")
public class AttendantSendsRecordController {
    @Autowired(required = false)
    AttendantSendsRecordService attendantSendsRecordService;
    /**
     * 校验当前服务兵单量是否溢出
     */
    @Warning("校验当前服务兵单量是否溢出")
    @RequestMapping(value = "checkoutAttSends",method = {RequestMethod.GET,RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint("校验当前服务兵单量是否溢出")
    public Results checkoutAttSends(String affectedUserId){
        return attendantSendsRecordService.checkoutAttSends(affectedUserId);
    }

}
