package com.bonc.rrs.workManager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderInformationSub;
import com.bonc.rrs.worder.service.WorderInformationSubService;
import com.bonc.rrs.workManager.constant.AttSendConstant;
import com.bonc.rrs.workManager.constant.CodeEnums;
import com.bonc.rrs.workManager.dao.AttendantSendsRecordMapper;
import com.bonc.rrs.workManager.dao.SendWorderRecordMapper;
import com.bonc.rrs.workManager.entity.AttendantSendsRecord;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.lenmoncore.common.validator.Assert;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 服务兵派单量记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
@Service
public class AttendantSendsRecordServiceImpl extends ServiceImpl<AttendantSendsRecordMapper, AttendantSendsRecord> implements AttendantSendsRecordService {
    @Autowired(required = false)
    AttendantSendsRecordMapper attendantSendsRecordMapper;
    @Autowired(required = false)
    SendWorderRecordMapper sendWorderRecordMapper;
    @Autowired(required = false)
    WorderInformationSubService worderInformationSubService;
    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    private SendWorderRecordService sendWorderRecordService;

    //溢出量校验
    @Override
    public Results checkoutAttSends(String affectedUserId) {
        Assert.isNull(affectedUserId, "缺少服务兵ID");
        if (!StringUtils.isEmpty(affectedUserId)) {
            //查询当前服务兵阀值开关是否打开
            List<Map<String, Object>> thresholdSwitchList = attendantSendsRecordMapper.findByOverflow(AttSendConstant.THRESHOLD_SWITCH);
            Map<String, Object> thresholdSwitchMap = thresholdSwitchList.get(0);
            boolean thresholdSwitch = thresholdSwitchMap.get("detail_number") != null && "1".equals(thresholdSwitchMap.get("detail_number").toString());
            //查服务兵派单阀门
            List<Map<String, Object>> byOverflow = attendantSendsRecordMapper.findByOverflow(AttSendConstant.OVER_FLOW);
            //通过服务斌ID和当前日期查attendant_sends_record溢出量是否溢出
            List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordMapper.listAttendantNum(Long.valueOf(affectedUserId), LocalDate.now());
            if (attendantSendsRecords.size() > 0) {
                if (attendantSendsRecords.get(0).getSendNum() >= Integer.parseInt(byOverflow.get(0).get("detail_number").toString())) {
                    Assert.isFalse(thresholdSwitch, "服务兵派单达到阀值！");
                    return Results.message(1, "服务人员当日最大服务能力为" + byOverflow.get(0).get("detail_number").toString() + "单，目前已派" + byOverflow.get(0).get("detail_number").toString() + "单，是否继续派工");
                }
            }
        }
        return Results.message(0, "");
    }

    //派单记录表
    @Override
    public void SendOrderRecord(OperationRecord work, Long serviceId) throws ParseException {
        //获取当前用户信息
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        //查询状态为0 派单不计数
        QueryWrapper<WorderInformationSub> queryWrapper = new QueryWrapper();
        queryWrapper.eq("worder_id", work.getWorderId());
        List<WorderInformationSub> worderInformationSubs = worderInformationSubService.list(queryWrapper);
        if (worderInformationSubs.size()>0 && worderInformationSubs.get(0).getDotCountState() !=null && worderInformationSubs.get(0).getDotCountState() == 0) {
            //添加派单记录表
            Integer integer = saveSendWorder(work, user, 0L, work.getTypeSendOrder());
        } else {
            //查字典中溢出量阀值
            List<Map<String, Object>> byOverflow = attendantSendsRecordMapper.findByOverflow(AttSendConstant.OVER_FLOW);
            //2 手动派单
            if (Objects.equals(work.getTypeSendOrder(), Integer.valueOf(CodeEnums.SEND_ORDER.getDesc()))) {
                //是否溢出标识
                long WhetherOverflow = 0L;
                //查询当前服务兵今天有没有单
                List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordMapper.listAttendantNum(work.getAffectedUserId(), LocalDate.now());
                // 今天有单 直接增加派单量 派单量>=2 就加溢出量
                if (attendantSendsRecords.size() > 0) {
                    //派单量>=2
                    if (attendantSendsRecords.get(0).getSendNum() >= Integer.parseInt(byOverflow.get(0).get("detail_number").toString())) {
                        //添加溢出量+1 派单量+1
                        attendantSendsRecordMapper.updateOverflow(work.getAffectedUserId(), attendantSendsRecords.get(0).getOverflow() + 1, attendantSendsRecords.get(0).getSendNum() + 1, LocalDate.now());
                        WhetherOverflow = 1L;
                    } else {
                        //添加派单量+1
                        attendantSendsRecordMapper.updateSendNum(work.getAffectedUserId(), attendantSendsRecords.get(0).getSendNum() + 1, LocalDate.now());
                    }
                }
                //没有 添加服务兵派单量表  加派单量
                else {
                    Integer integer = saveAttSend(work, user);
                    saveSendWorder(work, user, WhetherOverflow, work.getTypeSendOrder());
                    return;
                }
                //添加派单记录表
                Integer integer = saveSendWorder(work, user, WhetherOverflow, work.getTypeSendOrder());
                return;
            }
            //3 手动改派
            if (Objects.equals(work.getTypeSendOrder(), Integer.valueOf(CodeEnums.ALTER_SEND_ORDER.getDesc()))) {
                //获取当前服务兵ID
                List<AttendantSendsRecord> beforeSendOrder = attendantSendsRecordMapper.listAttendantNum(serviceId, LocalDate.now());
                //并且是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", work.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).ge("create_time", LocalDate.now()));

                if (!beforeSendOrder.isEmpty() && !sendWorderRecords.isEmpty()) {
                    //通过今天的日期和服务兵ID如果查询到了单子 就把当前服务兵派单量减一 没有的话就继续往下执行
                    //派单量>=2
                    if (beforeSendOrder.get(0).getOverflow() > 0) {
                        //溢出量-1
                        attendantSendsRecordMapper.updateOverflow(serviceId, beforeSendOrder.get(0).getOverflow() - 1, beforeSendOrder.get(0).getSendNum() - 1, LocalDate.now());
                    } else {
                        if (beforeSendOrder.get(0).getSendNum() > 0) {
                            //派单量-1
                            attendantSendsRecordMapper.updateSendNum(serviceId, beforeSendOrder.get(0).getSendNum() - 1, LocalDate.now());
                        }
                    }
                }
                //改派到的服务兵派单量+1 先判断这个派单员是否已经有单子 有的话 修改 没有的话 添加
                List<AttendantSendsRecord> NowSengOrder = attendantSendsRecordMapper.listAttendantNum(work.getAffectedUserId(), LocalDate.now());
                long WhetherOverflow = 0L;
                if (NowSengOrder.size() > 0) {
                    //派单量>=2
                    if (NowSengOrder.get(0).getSendNum() >= Integer.parseInt(byOverflow.get(0).get("detail_number").toString())) {
                        //添加溢出量+1
                        attendantSendsRecordMapper.updateOverflow(work.getAffectedUserId(), NowSengOrder.get(0).getOverflow() + 1, NowSengOrder.get(0).getSendNum() + 1, LocalDate.now());
                        WhetherOverflow = 1L;
                    } else {
                        //添加派单量+1
                        attendantSendsRecordMapper.updateSendNum(work.getAffectedUserId(), NowSengOrder.get(0).getSendNum() + 1, LocalDate.now());
                    }
                } else {
                    Integer integer = saveAttSend(work, user);
                }
                //添加派单记录表
                Integer integer = saveSendWorder(work, user, WhetherOverflow, work.getTypeSendOrder());
            }
        }

    }

    @Override
    public Integer getDotNextAttendanSendCount(Integer dotId) {
        return attendantSendsRecordMapper.getDotNextAttendanSendCount(dotId);
    }

    //添加服务兵派单量表
    public Integer saveAttSend(OperationRecord work, SysUserEntity user) {
        AttendantSendsRecord attendant = new AttendantSendsRecord();
        attendant.setServiceId(work.getAffectedUserId());
        attendant.setPresentDate(LocalDate.now());
        attendant.setSendNum(1L);
        attendant.setCreateTime(LocalDateTime.now());
        attendant.setCreateUser(user.getUserId());
        attendant.setUpdateTime(LocalDateTime.now());
        attendant.setUpdateUser(user.getUserId());
        return attendantSendsRecordMapper.insert(attendant);
    }

    //添加派单记录表
    public Integer saveSendWorder(OperationRecord work, SysUserEntity user, Long overFlow, Integer typeSendOrder) {
        SendWorderRecord sendWorderRecord = new SendWorderRecord();
        //通过工单ID获取区域ID
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectById(work.getWorderId());
        if (worderInformationEntity != null) {
            sendWorderRecord.setAreaId(worderInformationEntity.getAreaId().longValue());
        }
        sendWorderRecord.setWorderId(work.getWorderId());
        sendWorderRecord.setSendWorderType(3L);
        sendWorderRecord.setSendWorderUser(user.getUserId());
        sendWorderRecord.setAcceptWorderUser(work.getAffectedUserId());
        sendWorderRecord.setOperationType(typeSendOrder == 2 ? 2L : 3L);
        sendWorderRecord.setSendWorderOverflow(overFlow);
        sendWorderRecord.setCreateTime(LocalDateTime.now());
        sendWorderRecord.setOperationUser(user.getUserId());
        return sendWorderRecordMapper.insert(sendWorderRecord);
    }
}
