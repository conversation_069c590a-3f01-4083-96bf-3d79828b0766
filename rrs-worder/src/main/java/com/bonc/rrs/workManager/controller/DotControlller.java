package com.bonc.rrs.workManager.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.entity.DrivingPermitEntity;
import com.bonc.rrs.util.ExcelInputUtil;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dao.BizRegionDao;
import com.bonc.rrs.worder.dao.WorderTemplateRegionDao;
import com.bonc.rrs.worder.entity.BizAttendantEntity;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.entity.dto.BrandDotScoreExcelProperty;
import com.bonc.rrs.workManager.listener.BrandDotScoreExcelListener;
import com.bonc.rrs.workManager.service.BrandDotScoreService;
import com.bonc.rrs.workManager.service.DotAreaService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.workManager.service.WorkMsgService;
import com.bonc.rrs.wsdlproperties.OssProperties;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.export.properties.DotExport;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.MapUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.vo.*;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserRoleService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/*
 * @author: minJunping
 * @date: 2020/03/17
 * */
@Controller
@RequestMapping(value = "/dot/information")
@Slf4j
public class DotControlller extends ServiceImpl<SysUserDao, SysUserEntity> {

    @Autowired
    private WorkMsgService workMsgService;

    @Autowired(required = false)
    private DotInfoMapper dotInfoMapper;

    @Autowired(required = false)
    private DrivingPermitMapper drivingPermitMapper;

    @Autowired(required = false)
    private DotBankMapper dotBankMapper;

    @Autowired(required = false)
    private DotContactsMapper dotContactsMapper;

    @Autowired(required = false)
    private SysFilesService sysFileService;

    @Autowired
    private BrandDotScoreService brandDotScoreService;

    @Autowired(required = false)
    private SysFilesMapper sysFileMapper;

    @Autowired(required = false)
    private SysUserRoleService sysUserRoleService;

    @Autowired(required = false)
    private BizRegionMapper bizRegionMapper;

    @Autowired(required = false)
    private DotAreaService dotAreaService;
    @Autowired(required = false)
    private SysUserService sysUserService;
    @Autowired(required = false)
    private BizRegionDao bizRegionDao;
    @Autowired(required = false)
    private WorderTemplateRegionDao worderTemplateRegionDao;
    @Autowired(required = false)
    DotExport dotExport;
    @Autowired(required = false)
    private SysUserDao sysUserDao;
    @Autowired(required = false)
    private SysDictionaryDetailService sysDictionaryDetailService;

    @Autowired(required = false)
    private BrandDao brandDao;

    @Autowired(required = false)
    private RedisTemplate<String, String> redisTemplate;

    private static final String ENDPOINT = "oss-cn-shanghai.aliyuncs.com";

    private static final String ACCESS_KEY_ID = "LTAI4FpavfCDPFoBXsW564ps";

    private static final String ACCESS_KEY_SECRET = "******************************";

    @RequestMapping("/export")
    @ResponseBody
    public void export(HttpServletResponse response) {
        // 导出数据
        List<DotInfo> exportList = dotInfoMapper.export();
        List<String> dotList = dotExport.getDotList();
        HashMap<String, String> dotMap = dotExport.getDot();
        // 创建文档
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建表单
        HSSFSheet dotSheet = wb.createSheet("网点信息");
        // 设置字体
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setFontName("黑体");
        HSSFCellStyle cellTitleStyle = wb.createCellStyle();
        cellTitleStyle.setFont(font);
        cellTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 创建一行
        HSSFRow dotNameRow = dotSheet.createRow(0);
        dotNameRow.setHeightInPoints((short) 25);
        // 列名
        for (int i = 0, len = dotList.size(); i < len; i++) {
            String key = dotList.get(i);
            HSSFCell cell = dotNameRow.createCell(i);
            cell.setCellValue(dotMap.get(key));
            cell.setCellStyle(cellTitleStyle);
        }
        // 列值
        for (int i = 0, len = exportList.size(); i < len; i++) {
            DotInfo dotInfo = exportList.get(i);
            HSSFRow dotValueRow = dotSheet.createRow(i + 1);
            dotValueRow.setHeightInPoints((short) 20);
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(dotInfo));
            for (int j = 0, len2 = dotList.size(); j < len2; j++) {
                String key = dotList.get(j);
                dotValueRow.createCell(j).setCellValue(jsonObject.getString(key));
            }
        }
        for (int i = 0, len = dotList.size(); i < len; i++) {
            dotSheet.autoSizeColumn(i);
        }
        //输出Excel文件
        OutputStream output = null;
        try {
            output = response.getOutputStream();
            response.reset();
            String filename = new String("网点信息".getBytes("gbk"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment; filename=" + filename + ".xls");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            wb.write(output);
            output.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("/downloadDriving")
    @ResponseBody
    public void downloadDriving(HttpServletResponse response, @RequestBody Map<String,Object> param) throws IOException {
        OutputStream outputStream = null;
        try {
            //获取文档对象
            Workbook workbook = new SXSSFWorkbook();
            //获取sheet
            Sheet sheet = workbook.createSheet();

            String[] titles = {"网点名称", "行驶证类型", "行驶证持有人姓名", "车牌号", "行驶证照片"};
            Row titleRow = sheet.createRow(0);//在工作薄创建第一行
            // 图片字节数组
            byte[] imgByte = null;
            for (int i = 0; i < titles.length; i++) {
                Cell cell = titleRow.createCell(i);//给第一列  创建多个单元格  第1列
                cell.setCellValue(titles[i]);//表头
            }
            List<DrivingExport> drivingInfos = dotInfoMapper.selectDotDriving(param);
            drivingInfos.forEach(drivingExport -> {
                String [] imgIds = drivingExport.getImg().split(",");
                StringBuffer sb = new StringBuffer();
                switch (drivingExport.getType()){
                    case "1":
                        drivingExport.setType("公司");
                        break;
                    case "2":
                        drivingExport.setType("法人");
                        break;
                    case "3":
                        drivingExport.setType("服务兵");
                        break;
                }
                for (String imgId : imgIds) {
                    List<SysFileEntity> sysFileEntities = sysFileMapper.listSysFiles(imgId);
                    String hz=sysFileEntities.get(0).getNewName().substring(sysFileEntities.get(0).getNewName().length() - 3);
                    drivingExport.getImgs().add(sysFileEntities.get(0).getPath());
                    drivingExport.getHz().add(hz);
                }
                drivingExport.setImg(sb.toString());
            });
            int rowNum = 1;
            for (DrivingExport drivingInfo : drivingInfos) {
                Row row = sheet.createRow(rowNum);//创建行
                row.setHeightInPoints((short) 70);
                Cell cell;
                for (int i = 0; i < titles.length; i++) {
                    cell = row.createCell(i);
                    // 设置列宽
                    sheet.setColumnWidth(i, 256 * 25);
                    //找到图片列
                    switch (i){
                        case 0:
                            cell.setCellValue(drivingInfo.getDotName());
                            break;
                        case 1:
                            cell.setCellValue(drivingInfo.getType());
                            break;
                        case 2:
                            cell.setCellValue(drivingInfo.getName());
                            break;
                        case 3:
                            cell.setCellValue(drivingInfo.getCarNo());
                            break;
                        case 4:
                            List<String> url= drivingInfo.getImgs();
                            List<String> hz= drivingInfo.getHz();
                            int count = 1;
                            for (int j = 0; j < url.size(); j++) {
                                imgByte=getFileStream(url.get(j));
                                if (imgByte != null) {
                                    int addPicture;
                                    // 图片存在即输出图片
                                    //判断图片后缀
                                    if("jpg".equals(hz.get(j))){
                                        addPicture = workbook.addPicture(imgByte, workbook.PICTURE_TYPE_JPEG);
                                    }else {
                                        addPicture = workbook.addPicture(imgByte, workbook.PICTURE_TYPE_PNG);
                                    }
                                    Drawing drawing = sheet.createDrawingPatriarch();
                                    sheet.setColumnWidth(i+count-1, 256 * 25);
                                    //图片位置 j是列，rowNum是行
                                    ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, i+count-1,rowNum, i+count,rowNum +1);
                                    Picture picture = drawing.createPicture(anchor, addPicture);
                                }
                                count++;
                            }
                            break;
                    }
                }
                rowNum++;
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    System.currentTimeMillis() + ".txt");
            outputStream = response.getOutputStream();
            //输出excel
            workbook.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outputStream) {
                outputStream.close();
            }
        }
    }


    /**
     * 得到文件流
     * @param url
     * @return
     */
    public static byte[] getFileStream(String url){
        try {
            URL httpUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection)httpUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            InputStream inStream = conn.getInputStream();//通过输入流获取图片数据
            byte[] btImg = readInputStream(inStream);//得到图片的二进制数据
            return btImg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从输入流中获取数据
     * @param inStream 输入流
     * @return
     * @throws Exception
     */
    public static byte[] readInputStream(InputStream inStream) throws Exception{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while( (len=inStream.read(buffer)) != -1 ){
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }

    @RequestMapping("/download")
    @ResponseBody
    public void download(HttpServletResponse response, @RequestBody Map<String,Object> param) {
        ExcelWriter excelWriter = null;
        try {
            //获取登陆信息
            String filename = new String("网点信息".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xls");
            excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
            QueryWrapper<SysDictionaryDetailEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dictionary_id","62");
            List<SysDictionaryDetailEntity> list = sysDictionaryDetailService.list(queryWrapper);
            List<DotInfoExport> dotInfos = dotInfoMapper.selectDot(param);
            dotInfos.forEach(dotInfoExport -> {
                if(StringUtils.isNotBlank(dotInfoExport.getDotArea()) && StringUtils.isNotBlank(dotInfoExport.getDotCity()) && StringUtils.isNotBlank(dotInfoExport.getDotDistrict())){
                    dotInfoExport.setDotArea(InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoExport.getDotArea())).getName());
                    dotInfoExport.setDotCity(InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoExport.getDotCity())).getName());
                    dotInfoExport.setDotDistrict(InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(dotInfoExport.getDotDistrict())).getName());
                    Optional<String> detailName = list.stream()
                            .filter(item -> item.getDetailNumber().equals(dotInfoExport.getDotFeatures()))
                            .findFirst()
                            .map(SysDictionaryDetailEntity::getDetailName);
                    dotInfoExport.setDotFeatures(detailName.orElseGet(() -> {
                        // 执行复杂的逻辑，例如日志记录或计算默认值
                        return "";
                    }));

                }
            });
            WriteSheet writeSheet1 = EasyExcelFactory.writerSheet(0, "网点基本信息").head(DotInfoExport.class).build();
            excelWriter.write(dotInfos, writeSheet1);

//            List<DotAreaBrand> areaBrands = dotInfoMapper.selectDotAreaBrand(param);
//            //翻译服务类型,查询字典
//            List<SysDictionaryDetailEntity> serviceTypes = sysDictionaryDetailService.selectDetailByNumber("service_type");
//            Map<String, String> serviceTypeMap = serviceTypes.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName));
//            areaBrands.forEach(item -> {
//                item.setServiceTypeName(serviceTypeMap.get(item.getServiceTypeName()));
//            });

//            WriteSheet writeSheet2 = EasyExcelFactory.writerSheet(1, "网点辐射区域品牌信息").head(DotAreaBrand.class).build();
//            excelWriter.write(areaBrands, writeSheet2);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 下载评分导入模版
     * @param response
     * @param param
     */
    @RequestMapping("/downloadModel")
    @ResponseBody
    public void downloadModel(HttpServletResponse response,@RequestParam Map<String,Object> param) {
        ExcelWriter excelWriter = null;
        try {

            String filename = new String("评分导入模版信息".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xlsx");

            //读取OSS的模版
            OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(OssProperties.bucketName, "model/importScoreModel.xlsx"));
            InputStream modelInputStream = ossObject.getObjectContent();

            //填充网点信息数据
            LambdaQueryWrapper<DotInfo> dotInfoQueryWrapper = Wrappers.lambdaQuery();
            dotInfoQueryWrapper.select(DotInfo::getDotId, DotInfo::getDotName)
                    .eq(DotInfo::getIsDelete, 0)
                    .eq(DotInfo::getDotState, "1")
                    .orderByDesc(DotInfo::getCreateTime);
            List<DotInfo> dotInfos = dotInfoMapper.selectList(dotInfoQueryWrapper);

            List<Map<String, Object>> mapList = dotInfos.stream()
                    .map(item -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("dot_name", item.getDotName());
                        map.put("dot_id", item.getDotId());
                        return map;
                    })
                    .collect(Collectors.toList());

            //填充品牌信息
            LambdaQueryWrapper<BrandEntity> brandWrapper = Wrappers.lambdaQuery();
            brandWrapper.select(BrandEntity::getId, BrandEntity::getBrandName);
            List<Map<String, Object>> brnads = brandDao.selectMaps(brandWrapper);

            // 这里 会填充到第一个sheet， 然后文件流会自动关闭
            excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(modelInputStream).build();
            WriteSheet writeSheet1 = EasyExcel.writerSheet(1).build();
            excelWriter.fill(mapList, writeSheet1);
            WriteSheet writeSheet2 = EasyExcel.writerSheet(2).build();
            excelWriter.fill(brnads, writeSheet2);
            ossClient.shutdown();
        } catch (Exception e) {
            log.error("模版下载出现异常",e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 网点评分导入
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/uploadScoreModel")
    @ResponseBody
    public R uploadScoreModel(@RequestParam("fileName") MultipartFile file) throws IOException {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formattedDate = currentDate.format(formatter);
        try {
            if(redisTemplate.hasKey("importScore:" + formattedDate)){
                return R.error("周期："+formattedDate+",正在执行导入，请稍后");
            }else{
                redisTemplate.opsForValue().set("importScore:" + formattedDate, "1", 600, TimeUnit.SECONDS);
            }
            //获取文件名
            String fileName = file.getOriginalFilename();
            //获取文件的后缀名为xlsx
            assert fileName != null;
            String fileXlsx = fileName.substring(fileName.length() - 5);
            String fileXls = fileName.substring(fileName.length() - 4);
            //校验文件扩展名
            //如果不是excel文件
            if (!(fileXlsx.equals(".xlsx") || fileXls.equals(".xls"))) {
                return R.error().put("msg", "文件类型不正确！");
            }

            List<String> failureDatas = new ArrayList<>();
            //上传文件到OSS
            OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            DateTimeFormatter nowTime = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedNowTime = currentDate.format(nowTime);
            String[] split = fileName.split("\\.");
            String newFile = split[0] + formattedNowTime + "." + split[1];
            PutObjectResult putObjectResult = ossClient.putObject(OssProperties.bucketName, newFile, file.getInputStream());

            Date expiration = new Date(new Date().getTime() + 3600l * 1000 * 24 * 365 * 100);
            URL url = ossClient.generatePresignedUrl(OssProperties.bucketName, newFile, expiration);
            // 关闭OSSClient
            ossClient.shutdown();
            //保存表
            SysFileEntity sysFileEntity = new SysFileEntity();
            sysFileEntity.setObjType(15);
            sysFileEntity.setObjValue(15);
            sysFileEntity.setOldName(fileName);
            sysFileEntity.setNewName(newFile);
            sysFileEntity.setPath(url.toString());
            sysFileService.saveSysFile(sysFileEntity);

            //清除当前周期的数据
            LambdaUpdateWrapper<BrandDotScoreBean> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(BrandDotScoreBean::getCycle, formattedDate);
            updateWrapper.set(true, BrandDotScoreBean::getDeleteState, 1);
            brandDotScoreService.getBaseMapper().update(null, updateWrapper);
            //读取文件并且更新表
            EasyExcel.read(file.getInputStream(), BrandDotScoreExcelProperty.class, new BrandDotScoreExcelListener(brandDotScoreService, failureDatas, new HashSet<>())).sheet().doRead();
            if (failureDatas.size() > 0) {
                return R.error().put("failureDatas", failureDatas);
            }
        } catch (Exception e) {
            log.error("网点评分导入异常", e);
            return R.error("导入失败，请联系管理员");
        }finally {
            redisTemplate.delete("importScore:" + formattedDate);
        }
        return R.ok();
    }

    /**
     * 网点评分导入
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/getScoreImportFile")
    @ResponseBody
    public R getScoreImportFile() {
        return sysFileService.getScoreImportFile();
    }

    /**
     *
     * @param file
     * @return
     * @throws IOException
     */
    @GetMapping("/getBrandDotScoreInfoByDot")
    @ResponseBody
    public R getBrandDotScoreInfoByDot(@RequestParam("dotId") Integer dotId,@RequestParam("regionId") String regionId,@RequestParam("page") Long page,@RequestParam("pageSize") Long pageSize) {
        if (dotId == null) {
            return R.error("网点ID不能为空");
        }
        Integer region = null;
        if(!StringUtils.isBlank(regionId) && !"undefined".equals(regionId)){
            region = Integer.parseInt(regionId);
        }
        //获取当前周期
        LocalDate currentDate = LocalDate.now();
        LocalDate previousMonth = currentDate.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月");
        String formattedDate = previousMonth.format(formatter);

        DateTimeFormatter queryFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String queryFormattedDate = previousMonth.format(queryFormatter);
        IPage<BrandDotScoreBean> list = brandDotScoreService.selectPageByDot(new Page<>(page, pageSize),region,dotId,queryFormattedDate);
        return R.ok().put("cycle", formattedDate).put("brandDotScoreList", list.getRecords()).put("total",list.getTotal());
    }


    /*
     * 网点信息添加
     * 添加用户表管理员信息
     * 添加管理员信息表
     * */
    @RequestMapping(value = "add", method = {RequestMethod.GET, RequestMethod.POST}, produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @Transactional
    public Results addMangerController(@RequestBody DotAddInfo dotAddInfo) {
//        System.out.println("code"+dotAddInfo.getDotInfo().getBranchCode()+"name"+dotAddInfo.getDotInfo().getBranchName());
        String code = workMsgService.getBranchCode(dotAddInfo.getDotInfo().getBranchName());
        dotAddInfo.getDotInfo().setBranchCode(code);
        try {
            if(StringUtils.isBlank(dotAddInfo.getReason())){
                return Results.message(10, "请填写原因", null);
            }

            DotInfo work = dotAddInfo.getDotInfo();
            DotContacts dotContact = dotAddInfo.getDotContacts();

            // 校验唯一 contactsName网点管理员账号，dotCode网点编码，dotName网点名称
            List<SysUserEntity> list = this.baseMapper.selectByMap(new MapUtils().put("username", dotContact.getContactsName()));
            if (list.size() > 0) {
                return Results.message(10, "该管理员账号已存在", null);
            }
            List<SysUserEntity> userList = this.baseMapper.selectByMap(new MapUtils().put("mobile", dotContact.getContactsPhone()));
            if (userList.size() > 0) {
                return Results.message(11, "该管理员手机号已存在", null);
            }
            List<DotInfo> dotInfoList = dotInfoMapper.selectByMap(new MapUtils().put("dot_name", work.getDotName()));
            if (dotInfoList.size() > 0) {
                return Results.message(12, "该网点名称已存在", null);
            }
            List<DotInfo> dotInfoList2 = dotInfoMapper.selectByMap(new MapUtils().put("dot_code", work.getDotCode()));
            if (dotInfoList2.size() > 0) {
                return Results.message(13, "该网点编码已存在", null);
            }

            List<AreaBrandGroupVo> areaBrandGroupList = dotAddInfo.getAreaBrandGroupList();
            if(areaBrandGroupList == null || areaBrandGroupList.isEmpty()){
                return Results.message(14, "区域品牌组为必填选项", null);
            }

            long gCount = areaBrandGroupList.stream().filter(item -> item.getGroupId() == null).count();
            if(gCount > 0){
                return Results.message(14, "修改失败，请刷新页面重新操作", null);
            }

            if (!StringUtils.isEmpty(work) && !StringUtils.isEmpty(dotContact)) {

                //行驶证开始
                List<DrivingGroupVo> drivingGroupVoList = dotAddInfo.getDrivingGroupList();
                List<DrivingPermitEntity> drivingPermitEntities = new ArrayList<>();
                Map<String,String> carNoMap = new HashMap<>();
                Integer carNum = 0;
                if (drivingGroupVoList!=null&&drivingGroupVoList.size()>0){
                    for (int i = 0; i < drivingGroupVoList.size(); i++) {
                        DrivingGroupVo drivingGroupVo = drivingGroupVoList.get(i);
                        List<DrivingInfoVo> drivingInfoVoList = drivingGroupVo.getDrivingInfoList();
                        if (drivingInfoVoList!=null&&drivingInfoVoList.size()>0){
                            for (DrivingInfoVo drivingInfoVo : drivingInfoVoList) {
                                if (drivingInfoVo.getDrivingPermit()!=null&&!drivingInfoVo.getDrivingPermit().isEmpty()&&drivingInfoVo.getDrivingPermit().size()>0){
                                    DrivingPermitEntity drivingPermitEntity = new DrivingPermitEntity();
                                    if (StringUtils.isBlank(drivingInfoVo.getCarNo())) {
                                        return Results.message(15, "行驶证车牌号不能为空", null);
                                    }
                                    if (StringUtils.isBlank(drivingInfoVo.getName())){
                                        return Results.message(15, "行驶证姓名不能为空", null);
                                    }
                                    carNoMap.put(drivingInfoVo.getCarNo(),drivingInfoVo.getCarNo());
                                    drivingPermitEntity.setName(drivingInfoVo.getName());
                                    String carNo = drivingInfoVo.getCarNo();
                                    drivingPermitEntity.setCarNo(carNo);
                                    List<DrivingPermitVo> drivingPermitVoList = drivingInfoVo.getDrivingPermit();
                                    StringBuffer sb = new StringBuffer();
                                    int count = 1;
                                    for (DrivingPermitVo drivingPermitVo : drivingPermitVoList) {
                                        if (count>1){
                                            sb.append(",");
                                        }
                                        sb.append(drivingPermitVo.getUid());
                                        count++;
                                    }
                                    drivingPermitEntity.setImg(sb.toString());
                                    drivingPermitEntity.setType(Integer.valueOf(drivingGroupVo.getType()));
                                    if (dotInfoMapper.selectCarNo(carNo)>0){
                                        return Results.message(15,"行驶证车牌号已存在"+carNo,null);
                                    }
                                    drivingPermitEntities.add(drivingPermitEntity);
                                    carNum++;
                                }else{
                                    if (StringUtils.isNotBlank(drivingInfoVo.getCarNo())||StringUtils.isNotBlank(drivingInfoVo.getName())){
                                        return Results.message(17,"行驶证需上传行驶证照片",null);
                                    }
                                }
                            }
                        }
                    }
                }
                if (carNoMap.size()!=carNum){
                    return Results.message(16,"行驶证车牌号不能重复",null);
                }

                int index = dotInfoMapper.insertSelective(work);
                for (DrivingPermitEntity drivingPermitEntity : drivingPermitEntities) {
                    drivingPermitEntity.setDotId(work.getDotId());
                }
                if (drivingPermitEntities.size()>0){
                    //开始保存行驶证信息
                    log.info("=============保存行驶证信息开始=============");
                    int drivingNum = dotInfoMapper.addDrivingPermit(drivingPermitEntities);
                    dotInfoMapper.updateCarNumByDotId(work.getDotId(),carNum);
                }
                //行驶证结束

                String salt = RandomStringUtils.randomAlphanumeric(20);
                String password = "admin";
                SysUserEntity sysUser = new SysUserEntity();
                sysUser.setUsername(dotContact.getContactsName());
                sysUser.setPassword(new Sha256Hash(password, salt).toHex());
                sysUser.setSalt(salt);
                sysUser.setStatus(1);
                sysUser.setRoleName("网点管理员");
                sysUser.setCreateTime(new Date());
                sysUser.setMobile(dotContact.getContactsPhone());
                this.save(sysUser);
                List<Long> rolelist = new ArrayList<>();
                rolelist.add((long) 4);
                sysUserRoleService.saveOrUpdate(sysUser.getUserId(), rolelist);
                dotContact.setContactsId((Integer.valueOf(sysUser.getUserId().toString())));
                int num = dotInfoMapper.addContact(dotContact);
//                int member = dotContactsMapper.addManagerRoleId(sysUser.getUserId(),4);

                //重新组装区域品牌组数据
//                List<SysDictionaryDetailEntity> serviceTypelist = sysDictionaryDetailService.selectDetailByNumber("service_type");
//                List<Integer> allServiceTypeList = serviceTypelist.stream()
//                        .filter(item -> item != null && org.apache.commons.lang.StringUtils.isNotBlank(item.getDetailNumber()))
//                        .map(item -> Integer.parseInt(item.getDetailNumber())).collect(Collectors.toList());

                List<AreaDot> arealist = new ArrayList<>();
                List<BrandDot> brandDotList = new ArrayList<>();
                areaBrandGroupList.forEach(areaBrandGroupVo -> {
                    List<Integer> regionLabelList = areaBrandGroupVo.getRegionLabelList();
                    if(regionLabelList != null){
                        regionLabelList.forEach(areaId -> arealist.add(new AreaDot(areaId, work.getDotId(), areaBrandGroupVo.getGroupId())));
                    }
                    List<BrandGroupVo> brandGroups = areaBrandGroupVo.getBrandGroupList();
                    if (brandGroups != null) {
                        for (BrandGroupVo brandGroupVo : brandGroups) {

                            List<Integer> serviceTypeList = brandGroupVo.getServiceTypeList();
                            if (brandGroupVo.getServiceTypeList() == null || brandGroupVo.getServiceTypeList().isEmpty()) {
                                serviceTypeList = new ArrayList<Integer>(){{add(IntegerEnum.ZERO.getValue());}};
                            }

                            List<Integer> brandList = brandGroupVo.getBrandList();
                            if (brandList != null) {
                                for (Integer brandId : brandList) {

                                    for (Integer serviceType : serviceTypeList) {

                                        brandDotList.add(new BrandDot(work.getDotId(),
                                                brandId,
                                                serviceType,
                                                new Date(),
                                                areaBrandGroupVo.getGroupId(),
                                                brandGroupVo.getChildGroupId()));
                                    }
                                }
                            }

                        }

                    }
                });
                //封装数据
                dotAddInfo.setArealist(arealist);
                dotAddInfo.setBrandList(brandDotList);
                //添加区域和网点关联数据一组一组的入库
                Map<Long, List<AreaDot>> groups = arealist.stream().collect(Collectors.groupingBy(AreaDot::getGroupId));
                groups.forEach((groupId,areaDots) -> {
                    dotInfoMapper.addDotAreaInfo(work.getDotId(), dotAreaService.areaInfoAddHander(areaDots,dotAddInfo.getUserId(),dotAddInfo.getRoleIdList(),work.getDotId(),groupId),groupId);
                });

                //添加品牌
                SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = sdfs.format(new Date());
                int brandNum = dotInfoMapper.addDotBrandInfo(work.getDotId(), dotAddInfo.getBrandList(), format);

                if (index > 0 && num > 0 && brandNum>0) {
                    return Results.message(0, "success", null);
                } else {
                    return Results.message(10, "添加失败", null);
                }
            } else {
                return Results.message(100, "添加信息参数不能为空", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Results.message(110, "接口异常或者参数错误或者有参数为空", null);
        }

    }

    /*
     * 图片上传
     * */
    @RequestMapping("/imageUpload")
    @ResponseBody
    public Results imageUpload(@RequestParam("fileName") MultipartFile file) throws IOException {

        if (!StringUtils.isEmpty(file)) {
            Map<String, String> urlMap = FileUtils.getUrl(file);

            return Results.message(0, "success", urlMap);
        } else {
            return Results.message(10, "参数不能为空", null);
        }

    }

    /*
     * 网点条件查询
     * */
    @RequestMapping(value = "selectcondition", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R selectWorkController(@RequestParam(value = "nameCode") String nameCode,
                                  @RequestParam(value = "contactName") String contactName,
                                  @RequestParam(value = "vCode") String vCode,
                                  @RequestParam(value = "dotState", required = false) String dotState,
                                  @RequestParam(value = "star", required = false, defaultValue = "") String wdXj,
                                  @RequestParam(value = "roleIds", required = false, defaultValue = "") String roleIds,
                                  @RequestParam(value = "userId", required = false) Integer userId,
                                  @RequestParam(value = "currentIndex") Integer currentIndex,
                                  @RequestParam(value = "pageSize") Integer pageSize) throws Exception {

        int index = currentIndex;
        currentIndex = currentIndex - 1;
        currentIndex = currentIndex * pageSize;
        List<Integer> roleIdList = new ArrayList<>();
        ;
        if (StringUtils.isNotBlank(roleIds)) {
            for (String roleId : roleIds.split(",")) {
                roleIdList.add(Integer.valueOf(roleId));
            }
        }
        return workMsgService.selectDotInfo(nameCode, contactName, vCode, wdXj, currentIndex, pageSize, index, dotState, roleIdList, userId);

    }

    /*
     * 修改网点信息回显修改页面
     * */
    @RequestMapping(value = "workentity", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R getWorkEntityController(@RequestParam(value = "id") Integer id,
                                     @RequestParam(value = "roleIds", required = false, defaultValue = "") String roleIds,
                                     @RequestParam(value = "userId", required = false) Integer userId) {

        if (!StringUtils.isEmpty(id)) {

            DotInfo dotInfo = dotInfoMapper.selectByPrimaryKey(id);
            if (StringUtils.isNotBlank(dotInfo.getBuseLicenseUrl())) {
                dotInfo.setBuseLicenseId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getBuseLicenseUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getOfficeSpaceUrl())) {
                dotInfo.setOfficeSpaceId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getOfficeSpaceUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getSparesSpaceUrl())) {
                dotInfo.setSparesSpaceId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getSparesSpaceUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getDoorHeadUrl())) {
                dotInfo.setDoorHeadId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getDoorHeadUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getVehicleUrl())) {
                dotInfo.setVehicleId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getVehicleUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getServiceAidUrl())) {
                dotInfo.setServiceAidId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getServiceAidUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getConsumableUrl())) {
                dotInfo.setConsumableId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getConsumableUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getBasicDataUrl())) {
                dotInfo.setBasicDataId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getBasicDataUrl())));
            }
            if (StringUtils.isNotBlank(dotInfo.getHouseContractUrl())) {
                dotInfo.setHouseContractId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getHouseContractUrl())));
            }
//                Thread.sleep(1 * 1000);
            if (StringUtils.isNotBlank(dotInfo.getCorporateIdCard())) {
                dotInfo.setCorporateIdCardId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getCorporateIdCard())));
            }
            if (StringUtils.isNotBlank(dotInfo.getElectLicense())) {
                dotInfo.setElectLicenseId(sysFileMapper.selectSysFileInfo(Integer.valueOf(dotInfo.getElectLicense())));
            }


            String dotCode = dotInfoMapper.selectDotCodeById(id);

            List<DotContacts> lst = dotContactsMapper.selectByPrimaryKey(dotCode);
            DotContacts dotContacts = null;
            if (lst.size() > 0) {
                dotContacts = lst.get(0);
            }
            List<Map<String, Object>> dotInfoContract = new ArrayList<>();

            Map<String, Object> objectMap = new HashMap<>();

            objectMap.put("dotinfo", dotInfo);

            objectMap.put("dotContract", dotContacts);

            dotInfoContract.add(objectMap);

            Map dotAreaInfo = new HashMap();
            dotAreaInfo.put("dotAreaId", dotInfo.getDotArea());
            if (StringUtils.isNotBlank(dotInfo.getDotArea())) {
                dotAreaInfo.put("dotArea", bizRegionMapper.selectAreaName(Integer.valueOf(dotInfo.getDotArea())));
            }

            dotAreaInfo.put("dotCityId", dotInfo.getDotCity());
            if (StringUtils.isNotBlank(dotInfo.getDotCity())) {
                dotAreaInfo.put("dotCity", bizRegionMapper.selectAreaName(Integer.valueOf(dotInfo.getDotCity())));
            }

            dotAreaInfo.put("dotDistrictId", dotInfo.getDotDistrict());
            if (StringUtils.isNotBlank(dotInfo.getDotDistrict())) {
                dotAreaInfo.put("dotDistrict", bizRegionMapper.selectAreaName(Integer.valueOf(dotInfo.getDotDistrict())));
            }

            List<Integer> roleIdList = new ArrayList<>();
            ;
            if (StringUtils.isNotBlank(roleIds)) {
                for (String roleId : roleIds.split(",")) {
                    roleIdList.add(Integer.valueOf(roleId));
                }
            }

//            List<SysDictionaryDetailEntity> serviceTypelist = sysUserService.getServiceTypelist();
//            List<Integer> allServiceTypeList = serviceTypelist.stream()
//                    .filter(item -> item != null && org.apache.commons.lang.StringUtils.isNotBlank(item.getDetailNumber()))
//                    .map(item -> Integer.parseInt(item.getDetailNumber())).collect(Collectors.toList());

            List<AreaBrandGroupVo> areaBrandGroupVoList = new ArrayList<>();
            //获取网点关联的区域信息
            List<Map<String, String>> arealist = dotAreaService.selectDotAllAreas(dotInfo.getDotId(), userId, roleIdList);
            //按照组ID分组
            Map<String, List<Map<String, String>>> areaGroups = arealist.stream().collect(Collectors.groupingBy(item -> item.get("groupId")));
            //获取网点品牌信息
            List<Map<String, Object>> byBrands = dotInfoMapper.findByBrand(dotInfo.getDotId());
            //品牌按照组ID分组
            Map<String, List<Map<String, Object>>> brandGroups = byBrands.stream().collect(Collectors.groupingBy(item -> item.get("groupId").toString()));

            brandGroups.forEach((k, v) -> {
                AreaBrandGroupVo areaBrandGroupVo = new AreaBrandGroupVo();
                areaBrandGroupVo.setGroupId(Long.valueOf(k));

                List<Map<String, String>> areaGroup = areaGroups.get(k);
                if (areaGroup != null) {
                    List<Integer> regionLabelList = areaGroup.stream().map(item -> Integer.parseInt(item.get("areaId"))).collect(Collectors.toList());
                    areaBrandGroupVo.setRegionLabelList(regionLabelList);
                }

                Map<String, List<Map<String, Object>>> brandChildGroups = v.stream().collect(Collectors.groupingBy(item -> item.get("childGroupId").toString()));

                List<BrandGroupVo> brandGroupList = new ArrayList<>();

                brandChildGroups.forEach((ck, cv) -> {

                    BrandGroupVo brandGroupVo = new BrandGroupVo();
                    brandGroupVo.setChildGroupId(Long.valueOf(ck));

                    brandGroupVo.setServiceTypeList(new ArrayList<>(cv.stream().filter(item -> item.get("serviceType") != null).map(item -> Integer.parseInt(item.get("serviceType").toString())).collect(Collectors.toSet())));
                    brandGroupVo.setBrandList(new ArrayList<>(cv.stream().filter(item -> item.get("brandId") != null).map(item -> Integer.parseInt(item.get("brandId").toString())).collect(Collectors.toSet())));

                    brandGroupList.add(brandGroupVo);

                });

                areaBrandGroupVo.setBrandGroupList(brandGroupList);
                areaBrandGroupVoList.add(areaBrandGroupVo);
            });

            //组装行驶证信息
            List<DrivingGroupVo> drivingGroupVoList = new ArrayList<>();
            boolean driving = getDriving(drivingGroupVoList,dotInfo);
            //组装行驶证信息结束
            //组装网点银行信息
            List<BankGroupVo> bankGroupVoList = new ArrayList<>();
            getBank(bankGroupVoList,dotInfo);
            //组装网点银行信息结束
//            if (brandGroups != null && !brandGroups.isEmpty()) {
//                brandGroups.forEach((groupId , brnadList) -> {
//                    List<Map<String, String>> areaMaps = areaGroups.get(groupId.toString());
//                    if (areaMaps != null && !areaMaps.isEmpty()) {
//                        ArrayList<Integer> areaIds = new ArrayList<>(areaMaps.stream().filter(item -> item.get("areaId") != null).map(item -> Integer.parseInt(item.get("areaId").toString())).collect(Collectors.toSet()));
////                        areaBrandGroupVoList.add(new AreaBrandGroupVo(Long.parseLong(groupId.toString()),new ArrayList<>(brnadList.stream().filter(item -> item.get("brandId") != null).map(item -> Integer.parseInt(item.get("brandId").toString())).collect(Collectors.toSet())),areaIds));
//                    }else{
//                        areaBrandGroupVoList.add(new AreaBrandGroupVo(Long.parseLong(groupId.toString()),new ArrayList<>(brnadList.stream().filter(item -> item.get("brandId") != null).map(item -> Integer.parseInt(item.get("brandId").toString())).collect(Collectors.toSet())),new ArrayList<>()));
//                    }
//                });
//            }

            //QueryWrapper<BrandDot> brandDotQueryWrapper = new QueryWrapper<>();
            //brandDotQueryWrapper.eq("dot_id", dotInfo.getDotId());
            //List<BrandEntity> brandEntities = brandDao.selectList(brandDotQueryWrapper);

            //组装区域和品牌

            return R.ok().put("dotInfoContract", dotInfoContract)
                    .put("arealist", arealist).put("dotAreaInfo", dotAreaInfo).put("brandList",byBrands).put("areaBrandGroupList",areaBrandGroupVoList).put("drivingGroupVoList",drivingGroupVoList).put("driving",driving).put("bankGroupVoList",bankGroupVoList);
        } else {
            return R.ok();
        }
//        }catch (Exception e){
//            return Results.message(110,"接口异常",null);
//        }
    }

    public boolean getDriving(List<DrivingGroupVo> drivingGroupVoList,DotInfo dotInfo){
        List<DrivingPermitEntity> drivingPermitEntityList = new ArrayList<>();
        drivingPermitEntityList = drivingPermitMapper.selectList(new QueryWrapper<DrivingPermitEntity>().eq("dot_id",dotInfo.getDotId()).eq("is_delete","0"));
        boolean driving = true;
        DrivingGroupVo group1 = new DrivingGroupVo();
        group1.setType("1");
        group1.setNick("公司");
        DrivingGroupVo group2 = new DrivingGroupVo();
        group2.setType("2");
        group2.setNick("法人");
        DrivingGroupVo group3 = new DrivingGroupVo();
        group3.setType("3");
        group3.setNick("服务兵");
        if (drivingPermitEntityList.size()>0){
            for (DrivingPermitEntity drivingPermitEntity : drivingPermitEntityList) {
                DrivingInfoVo drivingInfoVo = new DrivingInfoVo();
                drivingInfoVo.setDrivingId(drivingPermitEntity.getId());
                drivingInfoVo.setName(drivingPermitEntity.getName());
                drivingInfoVo.setCarNo(drivingPermitEntity.getCarNo());
                List<DrivingPermitVo> list = new ArrayList<>();
                String [] imgs = drivingPermitEntity.getImg().split(",");
                for (String img : imgs) {
                    DrivingPermitVo drivingPermitVo = new DrivingPermitVo();
                    String path = sysFileMapper.selectSysFileInfo(Integer.valueOf(img));
                    drivingPermitVo.setUrl(path);
                    drivingPermitVo.setUid(img);
                    list.add(drivingPermitVo);
                }
                drivingInfoVo.setDrivingPermit(list);

                if (drivingPermitEntity.getType()==1){
                    //是公司
                    group1.getDrivingInfoList().add(drivingInfoVo);
                }else if(drivingPermitEntity.getType()==2){
                    //是法人
                    group2.getDrivingInfoList().add(drivingInfoVo);
                }else if(drivingPermitEntity.getType()==3){
                    //是服务兵
                    group3.getDrivingInfoList().add(drivingInfoVo);
                }
            }
        }

        if (group1.getDrivingInfoList().size()>0){
            drivingGroupVoList.add(group1);
        }else{
            List<DrivingInfoVo> drivingInfoVoList = new ArrayList<>();
            DrivingInfoVo drivingInfoVo = new DrivingInfoVo();
            List<DrivingPermitVo> drivingPermitVos = new ArrayList<>();
            drivingInfoVo.setDrivingPermit(drivingPermitVos);
            drivingInfoVoList.add(drivingInfoVo);
            group1.setDrivingInfoList(drivingInfoVoList);
            drivingGroupVoList.add(group1);
        }
        if (group2.getDrivingInfoList().size()>0){
            drivingGroupVoList.add(group2);
        }else{
            List<DrivingInfoVo> drivingInfoVoList = new ArrayList<>();
            DrivingInfoVo drivingInfoVo = new DrivingInfoVo();
            List<DrivingPermitVo> drivingPermitVos = new ArrayList<>();
            drivingInfoVo.setDrivingPermit(drivingPermitVos);
            drivingInfoVoList.add(drivingInfoVo);
            group2.setDrivingInfoList(drivingInfoVoList);
            drivingGroupVoList.add(group2);
        }
        if (group3.getDrivingInfoList().size()>0){
            drivingGroupVoList.add(group3);
        }else{
            List<DrivingInfoVo> drivingInfoVoList = new ArrayList<>();
            DrivingInfoVo drivingInfoVo = new DrivingInfoVo();
            List<DrivingPermitVo> drivingPermitVos = new ArrayList<>();
            drivingInfoVo.setDrivingPermit(drivingPermitVos);
            drivingInfoVoList.add(drivingInfoVo);
            group3.setDrivingInfoList(drivingInfoVoList);
            drivingGroupVoList.add(group3);
        }
        if (drivingPermitEntityList.size()==0){
            driving = false;
        }
        return driving;
    }

    public void getBank(List<BankGroupVo> bankGroupVoList,DotInfo dotInfo){
        List<DotBank> bankEntityList = new ArrayList<>();
        bankEntityList = dotBankMapper.selectList(new QueryWrapper<DotBank>().eq("dot_id",dotInfo.getDotId()).eq("is_delete","0"));

        if (bankEntityList.size()>0){
            for (int i = 0; i < bankEntityList.size(); i++) {
                DotBank dotBankEntity = bankEntityList.get(i);
                BankGroupVo bankGroupVo = new BankGroupVo();
                bankGroupVo.setGroupId(Long.valueOf(i));
                bankGroupVo.setBankId(dotBankEntity.getId());
                bankGroupVo.setBankAccount(dotBankEntity.getBankAccount());
                bankGroupVo.setBankName(dotBankEntity.getBankName());
                bankGroupVo.setBankNumber(dotBankEntity.getBankNumber());
                bankGroupVo.setDotBank(dotBankEntity.getDotBank());
                bankGroupVo.setTaxNo(dotBankEntity.getTaxNo());
                bankGroupVoList.add(bankGroupVo);
            }
        }else{
            BankGroupVo bankGroupVo = new BankGroupVo();
            bankGroupVo.setGroupId(1l);
            bankGroupVo.setTaxNo("");
            bankGroupVo.setDotBank("");
            bankGroupVo.setBankNumber("");
            bankGroupVo.setBankAccount("");
            bankGroupVo.setBankName("");
            bankGroupVoList.add(bankGroupVo);
        }
    }
    /**
     * 判断是否全部服务类型
     * @param allServiceTypeList
     * @param serviceTypeList
     * @return
     */
    private Boolean isAllServiceType(List<Integer> allServiceTypeList, List<Integer> serviceTypeList) {
        Boolean flag = true;
        for (Integer serviceType : allServiceTypeList) {
            if (!serviceTypeList.contains(serviceType)) {
                flag = false;
            }
        }
        return flag;
    }

//    @RequestParam(value = "dotInfo") String dotInfo,
//    @RequestParam(value = "dotContacts") String dotContacts,
//    @RequestBody() List<AreaDot> arealist

    /*
     * 修改网点信息
     * */
    @RequestMapping(value = "updataentity", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @Transactional
    public Results upDataEntity(@RequestBody DotAddInfo dotAddInfo) {
        try{
            //        System.out.println("code"+dotAddInfo.getDotInfo().getBranchCode()+"name"+dotAddInfo.getDotInfo().getBranchName());
            String code = workMsgService.getBranchCode(dotAddInfo.getDotInfo().getBranchName());
            dotAddInfo.getDotInfo().setBranchCode(code);

            if(StringUtils.isBlank(dotAddInfo.getReason())){
                return Results.message(10, "请填写原因", null);
            }

            if (!StringUtils.isEmpty(dotAddInfo)) {
                DotInfo work = dotAddInfo.getDotInfo();
                DotContacts dotContact = dotAddInfo.getDotContacts();
                // 校验唯一 contactsName网点管理员账号，dotCode网点编码，dotName网点名称
                List<SysUserEntity> sysUserList = this.baseMapper.selectList(new QueryWrapper<SysUserEntity>()
                        .eq("username", dotContact.getContactsName()).ne("user_id", dotContact.getContactsId())
                        .ne("status",0)
                );
                if (sysUserList.size() > 0) {
                    return Results.message(10, "该管理员账号已存在", null);
                }
                sysUserList = this.baseMapper.selectList(new QueryWrapper<SysUserEntity>()
                        .eq("mobile", dotContact.getContactsPhone())
                        .ne("user_id", dotContact.getContactsId())
                        .ne("status",0)
                );
                if (sysUserList.size() > 0) {
                    return Results.message(11, "该管理员手机号已存在", null);
                }
                List<DotInfo> dotList = dotInfoMapper.selectList(new QueryWrapper<DotInfo>()
                        .eq("dot_name", work.getDotName()).ne("dot_id", work.getDotId())
                        .ne("is_delete",1)
                );
                if (dotList.size() > 0) {
                    return Results.message(12, "该网点名称已存在", null);
                }
                dotList = dotInfoMapper.selectList(new QueryWrapper<DotInfo>()
                        .eq("dot_code", work.getDotCode()).ne("dot_id", work.getDotId())
                        .ne("is_delete",1)
                );
                if (dotList.size() > 0) {
                    return Results.message(13, "该网点编码已存在", null);
                }

                List<AreaBrandGroupVo> areaBrandGroupList = dotAddInfo.getAreaBrandGroupList();
                if (areaBrandGroupList == null || areaBrandGroupList.isEmpty()) {
                    return Results.message(14, "区域品牌组不能为空", null);
                }

            long gCcount = areaBrandGroupList.stream().filter(item -> item.getGroupId() == null).count();
            if(gCcount > 0){
                return Results.message(14, "修改失败，请刷新页面重新操作", null);
            }


                //行驶证开始
                List<DrivingGroupVo> drivingGroupVoList = dotAddInfo.getDrivingGroupList();
                List<Integer> drivingIds = new ArrayList<>();
                List<DrivingPermitEntity> drivingPermitEntities = new ArrayList<>();
                List<DrivingPermitEntity> updatedrivingPermit = new ArrayList<>();
                Map<String, String> carNoMap = new HashMap<>();
                Integer carNum = 0;
                if (drivingGroupVoList != null && drivingGroupVoList.size() > 0) {
                    for (int i = 0; i < drivingGroupVoList.size(); i++) {
                        DrivingGroupVo drivingGroupVo = drivingGroupVoList.get(i);
                        List<DrivingInfoVo> drivingInfoVoList = drivingGroupVo.getDrivingInfoList();
                        if (drivingInfoVoList != null && drivingInfoVoList.size() > 0) {
                            for (DrivingInfoVo drivingInfoVo : drivingInfoVoList) {
                                if (drivingInfoVo.getDrivingPermit() != null && !drivingInfoVo.getDrivingPermit().isEmpty() && drivingInfoVo.getDrivingPermit().size() > 0) {
                                    if (StringUtils.isBlank(drivingInfoVo.getCarNo())) {
                                        return Results.message(15, "行驶证车牌号不能为空", null);
                                    }
                                    if (StringUtils.isBlank(drivingInfoVo.getName())) {
                                        return Results.message(15, "行驶证姓名不能为空", null);
                                    }
                                    DrivingPermitEntity drivingPermitEntity = new DrivingPermitEntity();
                                    carNoMap.put(drivingInfoVo.getCarNo(), drivingInfoVo.getCarNo());
                                    drivingPermitEntity.setName(drivingInfoVo.getName());
                                    String carNo = drivingInfoVo.getCarNo();
                                    drivingPermitEntity.setCarNo(carNo);
                                    List<DrivingPermitVo> drivingPermitVoList = drivingInfoVo.getDrivingPermit();
                                    StringBuffer sb = new StringBuffer();
                                    int count = 1;
                                    for (DrivingPermitVo drivingPermitVo : drivingPermitVoList) {
                                        if (count > 1) {
                                            sb.append(",");
                                        }
                                        sb.append(drivingPermitVo.getUid());
                                        count++;
                                    }
                                    drivingPermitEntity.setImg(sb.toString());
                                    drivingPermitEntity.setType(Integer.valueOf(drivingGroupVo.getType()));
                                    if (drivingInfoVo.getDrivingId() == null) {
                                        drivingPermitEntities.add(drivingPermitEntity);
                                        if (dotInfoMapper.selectCarNo(carNo) > 0) {
                                            return Results.message(15, "行驶证车牌号已存在：" + carNo, null);
                                        }
                                    } else {
                                        drivingIds.add(drivingInfoVo.getDrivingId());
                                        drivingPermitEntity.setId(drivingInfoVo.getDrivingId());
                                        updatedrivingPermit.add(drivingPermitEntity);
                                        if (dotInfoMapper.selectCarNoById(carNo, drivingInfoVo.getDrivingId()) > 0) {
                                            return Results.message(15, "行驶证车牌号已存在：" + carNo, null);
                                        }
                                    }
                                    carNum++;
                                } else {
                                    if (StringUtils.isNotBlank(drivingInfoVo.getCarNo()) || StringUtils.isNotBlank(drivingInfoVo.getName())) {
                                        return Results.message(17, "行驶证需上传行驶证照片", null);
                                    }
                                }
                            }
                        }
                    }
                }
                if (carNoMap.size()!=carNum){
                    return Results.message(16, "行驶证车牌号不能重复", null);
                }

                // 修改网点基本信息
                int index = dotInfoMapper.updateByPrimaryKeySelective(work);

                for (DrivingPermitEntity drivingPermitEntity : drivingPermitEntities) {
                    drivingPermitEntity.setDotId(work.getDotId());
                }
                for (DrivingPermitEntity drivingPermitEntity : updatedrivingPermit) {
                    dotInfoMapper.updateDrivingPermit(drivingPermitEntity);
                }

                dotInfoMapper.deleteDrivingPermitStatus(work.getDotId());
                if (carNum>0){
                    if (drivingIds.size()>0){
                        dotInfoMapper.updateDrivingPermitStatus(work.getDotId(),drivingIds);
                    }
                    if (drivingPermitEntities.size()>0){
                        //开始保存行驶证信息
                        log.info("=============保存行驶证信息开始=============");
                        int drivingNum = dotInfoMapper.addDrivingPermit(drivingPermitEntities);
                    }
                    dotInfoMapper.updateCarNumByDotId(work.getDotId(),carNum);
                }

                //行驶证结束



//            List<SysDictionaryDetailEntity> serviceTypelist = sysDictionaryDetailService.selectDetailByNumber("service_type");
//            List<Integer> allServiceTypeList = serviceTypelist.stream()
//                    .filter(item -> item != null && org.apache.commons.lang.StringUtils.isNotBlank(item.getDetailNumber()))
//                    .map(item -> Integer.parseInt(item.getDetailNumber())).collect(Collectors.toList());
                //获取品牌组的品牌list
                List<BrandDot> brandDotList = new ArrayList<>();
                //获取品牌组的区域list
                List<AreaDot> areaDotList = new ArrayList<>();

                areaBrandGroupList.forEach(areaBrandGroupVo -> {
                    List<Integer> regionLabelList = areaBrandGroupVo.getRegionLabelList();
                    if(regionLabelList != null){
                        regionLabelList.forEach(areaId -> areaDotList.add(new AreaDot(areaId,work.getDotId(),areaBrandGroupVo.getGroupId())));
                    }
//                List<Integer> brandList = areaBrandGroupVo.getBrandList();
//                if (brandList != null) {
//                    brandList.forEach(brandId -> brandDotList.add(new BrandDot(work.getDotId(),brandId,areaBrandGroupVo.getGroupId(),new Date())));
//                }
                    List<BrandGroupVo> brandGroups = areaBrandGroupVo.getBrandGroupList();
                    if (brandGroups != null) {
                        for (BrandGroupVo brandGroupVo : brandGroups) {

                            List<Integer> serviceTypeList = brandGroupVo.getServiceTypeList();
                            if (brandGroupVo.getServiceTypeList() == null || brandGroupVo.getServiceTypeList().isEmpty()) {
                                serviceTypeList = new ArrayList<Integer>(){{add(IntegerEnum.ZERO.getValue());}};
                            }

                            List<Integer> brandList = brandGroupVo.getBrandList();
                            if (brandList != null) {
                                for (Integer brandId : brandList) {

                                    for (Integer serviceType : serviceTypeList) {

                                        brandDotList.add(new BrandDot(work.getDotId(),
                                                brandId,
                                                serviceType,
                                                new Date(),
                                                areaBrandGroupVo.getGroupId(),
                                                brandGroupVo.getChildGroupId()));
                                    }
                                }
                            }

                        }

                    }
                });
                //封装数据
                dotAddInfo.setArealist(areaDotList);
                dotAddInfo.setBrandList(brandDotList);
                //添加区域和网点关联数据一组一组的入库

                //循环遍历插入网点区域组
                //清除绑定区域关系表
//            Map<Long, List<AreaDot>> areaGroups = areaDotList.stream().collect(Collectors.groupingBy(AreaDot::getGroupId));
//            areaGroups.forEach((groupId,areaDots) -> {
//                dotAreaService.updateDotAreaList(work.getDotId(), areaDots, dotAddInfo.getUserId(), dotAddInfo.getRoleIdList(),groupId);
//            });
                //删除网点区域
                Integer integer = dotInfoMapper.deleteDotArea(work.getDotId());
                Map<Long, List<AreaDot>> groups = areaDotList.stream().collect(Collectors.groupingBy(AreaDot::getGroupId));
                groups.forEach((groupId,areaDots) -> {
                    dotInfoMapper.addDotAreaInfo(work.getDotId(), dotAreaService.areaInfoAddHander(areaDots,dotAddInfo.getUserId(),dotAddInfo.getRoleIdList(),work.getDotId(),groupId),groupId);
                });

                //删除网点品牌
                dotInfoMapper.deleteDotBrand(work.getDotId());
                SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = sdfs.format(new Date());

                //添加网点品牌
                dotInfoMapper.addDotBrandInfo(work.getDotId(),brandDotList,format);

                //修改sys_file 手机号
                sysUserDao.updateMobile(dotContact.getContactsPhone(),dotContact.getContactsName());



                // 删除过去的网点辐射区
//            dotAreaService.deleteByDotId(work.getDotId());
//            // 保存新的辐射区
//            List<AreaDot> arealist = dotAddInfo.getArealist();
//            List<DotArea> dotAreaList = arealist.stream().map(item -> {
//                item.getAreaId();
//                return new DotArea(null, item.getAreaId(), work.getDotId());
//            }).collect(Collectors.toList());
//            boolean saveDotArea = dotAreaService.saveBatch(dotAreaList);
                // 获得之前网点辐射区域ID列表
//            List<Integer> dotAreaIdlist = dotInfoMapper.selectDotAreaId(work.getDotId());
//            List<DotArea> mapList = new ArrayList<>();
//            for (int i = 0; i < dotAddInfo.getArealist().size(); i++) {
//                DotArea dotArea = new DotArea(dotAreaIdlist.get(i),dotAddInfo.getArealist().get(i).getAreaId());
//                mapList.add(dotArea);
//            }
//            int ber = dotInfoMapper.updateDotAreaAreaId(mapList,work.getDotId());
                int num = dotContactsMapper.updateByPrimaryKeySelective(dotContact);
                dotInfoMapper.updateSysUserName(dotContact.getContactsId(), dotContact.getContactsName());
                if (index > 0 && num > 0) {
                    return Results.message(0, "success", null);
                } else {
                    return Results.message(10, "无相关id数据", null);
                }
            } else {
                return Results.message(110, "接口异常", null);
            }

        }catch (Exception e){
            log.error("网点修改失败",e);
            return Results.message(110, "修改失败", null);
        }
    }

    /*
     * 更新网点状态
     * */
    @RequestMapping(value = "dotupdateflag", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results workFreezecontroller(@RequestParam(value = "id") Integer id,
                                        @RequestParam(value = "status") String status,
                                        @RequestParam(value = "roleIds", required = false, defaultValue = "") String roleIds,
                                        @RequestParam(value = "userId", required = false) Integer userId) {
        try {
            return Results.message(10, "请填写原因", null);
//            if (!StringUtils.isEmpty(id) && !StringUtils.isEmpty(status)) {
//                status = status.replace(" ", "");
//                if (StringUtils.isNotBlank(roleIds) && userId != null && !StringUtils.isEmpty(id) && "5".equals(status)) {
//                    List<Integer> roleIdList = new ArrayList<>();
//                    if (StringUtils.isNotBlank(roleIds)) {
//                        for (String roleId : roleIds.split(",")) {
//                            roleIdList.add(Integer.valueOf(roleId));
//                        }
//                    }
//                    if (!dotAreaService.checkUserRoleArea(id, userId, roleIdList)) {
//                        return Results.message(110, "权限不足，网点存在权限以外的辐射区域无法撤点！", null);
//                    }
//                }
//
//                int index = dotInfoMapper.updataFlagDot(id, status);
//                if (index > 0) {
//                    return Results.message(0, "网点状态更新成功", null);
//                } else {
//                    return Results.message(10, "没有相关id数据", null);
//                }
//            } else {
//                return Results.message(100, "参数不能为空", null);
//            }
        } catch (Exception e) {
            return Results.message(110, "解冻冻结接口异常", null);
        }
    }

    /*
     * 网点星级评分
     * */
    @RequestMapping(value = "dotgradflag", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results workGradcontroller(@RequestParam(value = "id") Integer id,
                                      @RequestParam(value = "dotStar") String dotStar,
                                      @RequestParam(value = "dotScore") String dotScore) {
        try {
            if (!StringUtils.isEmpty(id) && !StringUtils.isEmpty(dotStar) && !StringUtils.isEmpty(dotScore)) {
                dotStar = dotStar.replace(" ", "");
                dotScore = dotScore.replace(" ", "");
                int index = dotInfoMapper.updataScoreDot(id, dotStar, dotScore);
                if (index > 0) {
                    return Results.message(0, "网点评分更新成功", null);
                } else {
                    return Results.message(10, "没有相关id数据", null);
                }
            } else {
                return Results.message(100, "参数不能为空", null);
            }
        } catch (Exception e) {
            return Results.message(110, "撤点恢复网点接口异常", null);
        }

    }

    /*
     * 网点信息模拟删除
     * */
    @RequestMapping(value = "deleteworkentity", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results deleteWOrkEntity(@RequestParam(value = "id") Integer id,
                                    @RequestParam(value = "roleIds", required = false, defaultValue = "") String roleIds,
                                    @RequestParam(value = "userId", required = false) Integer userId) {
        try {
            return Results.message(10, "请填写原因", null);

//            if (StringUtils.isNotBlank(roleIds) && userId != null && !StringUtils.isEmpty(id)) {
//                List<Integer> roleIdList = new ArrayList<>();
//                if (StringUtils.isNotBlank(roleIds)) {
//                    for (String roleId : roleIds.split(",")) {
//                        roleIdList.add(Integer.valueOf(roleId));
//                    }
//                }
//                if (!dotAreaService.checkUserRoleArea(id, userId, roleIdList)) {
//                    return Results.message(110, "权限不足，网点存在权限以外的辐射区域无法删除！", null);
//                }
//            }
//
//            if (!StringUtils.isEmpty(id)) {
//                Integer contactId = dotInfoMapper.selectContactId(id);
//                // 删除网点
//                //禁止网点物理删除改为修改is_delete
//                int index = dotInfoMapper.deleteFlagDot(id);
//                if (!StringUtils.isEmpty(contactId)) {
//                    // 删除网点管理员账号
////                    sysUserService.deleteById(Long.valueOf(contactId));
//
//                    //禁止用户表物理删除，改为状态控制
//                    int nums = dotInfoMapper.updateSysContactState(contactId);
//                }
//                //删除网点区域
////                dotAreaService.deleteByDotId(id);
//                if (index > 0) {
//                    return Results.message(0, "网点模拟删除成功", null);
//                } else {
//                    return Results.message(10, "无相关id数据", null);
//                }
//            } else {
//                return Results.message(100, "参数id不能为空", null);
//            }
        } catch (Exception e) {
            e.printStackTrace();
            return Results.message(110, "删除网点接口异常", null);
        }
    }

    /*
     * 网点行政区三级获取信息
     * */
    @RequestMapping(value = "getarealist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getAllArea(@RequestParam(value = "pid") Integer pid) {
        return workMsgService.getArealist(pid);
    }

    /*
     * 工贸信息查询
     * */
    @RequestMapping(value = "getindustryinfo", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getIndustryInfo(@RequestParam(value = "name") String name) {
        return workMsgService.getIndustryName(name);
    }

    @RequestMapping(value = "getfileidcontroller", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getFileIdController(@RequestParam(value = "ObjValue") Integer ObjValue,
                                       @RequestParam(value = "ObjType") Integer ObjType,
                                       @RequestParam(value = "Filelist") MultipartFile[] Filelist) throws IOException {

        return sysFileService.batchAddFilelist(ObjValue, ObjType, Filelist);

    }

    /**
     * 根据地区编码模糊查询地区信息
     *
     * @param code
     * @return
     */
    @RequestMapping(value = "getbizregionlist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getBizRegionlist(@RequestParam(value = "code") String code) {

        try {
            if (!StringUtils.isEmpty(code)) {
                List<Map<String, String>> regionlist = bizRegionMapper.selectRegionlist(code);
                return Results.message(0, "success", regionlist);
            } else {
                return Results.message(1, "参数不能为空", null);
            }
        } catch (Exception e) {
            return Results.message(110, "模糊查询地区接口异常", null);
        }

    }

//    @RequestMapping(value = "addArea",method = {RequestMethod.GET,RequestMethod.POST},produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Results addArealist(@RequestBody() List<AreaDot> arealist){
//        int index = dotInfoMapper.addDotAreaInfo(1,arealist);
//        if (index > 0){
//            return Results.message("0","success",null);
//        }else {
//            return Results.message("1","失败",null);
//        }
//    }

    /**
     * 获取网点字典信息
     *
     * @return
     */
    @RequestMapping(value = "getdictionlist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R getDictionlist() {

        try {
            List<Map<String, Object>> net = dotInfoMapper.selectDictDetaillist("net_properties");
            List<Map<String, Object>> node = dotInfoMapper.selectDictDetaillist("node_types");
            List<Map<String, Object>> features = dotInfoMapper.selectDictDetaillist("network_features");
            List<Map<String, Object>> culture = dotInfoMapper.selectDictDetaillist("standard_culture");
            List<Map<String, Object>> company = dotInfoMapper.selectDictDetaillist("company_nature");
            List<Map<String, Object>> taxEnd = dotInfoMapper.selectDictDetaillist("tax_end");
            List<Map<String, Object>> industry = dotInfoMapper.selectDictDetaillist("industry_end");
            List<Map<String, Object>> point = dotInfoMapper.selectDictDetaillist("tax_point");
            List<Map<String, Object>> aptitude = dotInfoMapper.selectDictDetaillist("aptitude");
            List<Map<String, Object>> taxpayer = dotInfoMapper.selectDictDetaillist("average_taxpayer");
            List<Map<String, Object>> marketLevel = dotInfoMapper.selectDictDetaillist("market_level");
            return R.ok().put("net", net).put("node", node).put("features", features).put("culture", culture).put("company", company).
                    put("taxEnd", taxEnd).put("industry", industry).put("point", point).put("aptitude", aptitude).put("taxpayer", taxpayer)
                    .put("marketLevel", marketLevel);
        } catch (Exception e) {
            return R.error("网点添加字典信息接口异常");
        }

    }

    @RequestMapping("/listdot")
    @ResponseBody
    public R listWorderAreaDot(String areaId) {
        BizRegionEntity bizRegionEntity = bizRegionDao.selectById(areaId);
        String code = bizRegionEntity.getRegcode();
        String regcode = "(" + code.substring(0, 3);
        if (code.length() > 3) {
            regcode += ", ";
            regcode += code.substring(0, 6);
        }
        if (code.length() > 6) {
            regcode += ", ";
            regcode += code.substring(0, 9);
        }
        regcode += ")";
        return R.ok().put("bizRegion", bizRegionEntity).putList(dotInfoMapper.selectWorderAreaDot(regcode));
    }

    @RequestMapping("/input")
    @ResponseBody
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public R input(@RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        System.out.println(fileName);
        boolean notNull = false;
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            System.out.println("上传文件格式不正确");
            return R.error("上传文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        Date date = new Date();

        List<DotAddInfo> dotAddInfoList = new ArrayList<>();

        String salt = RandomStringUtils.randomAlphanumeric(20);
        String password = "admin";
        SysUserEntity sysUser = new SysUserEntity();
        sysUser.setPassword(new Sha256Hash(password, salt).toHex());
        sysUser.setSalt(salt);
        sysUser.setStatus(1);
        sysUser.setRoleName("网点管理员");
        sysUser.setCreateTime(date);
        InputStream is = null;
        try {
            is = file.getInputStream();
            Workbook wb = null;
            if (isExcel2003) {
                wb = new HSSFWorkbook(is);
            } else {
                wb = new XSSFWorkbook(is);
            }
            Sheet sheet = wb.getSheetAt(0);
            if (sheet != null) {
                notNull = true;
            }
            for (int i = 2, len = sheet.getPhysicalNumberOfRows(); i <= len; i++) {
                BizAttendantEntity excelInput = new BizAttendantEntity();
                Row row = sheet.getRow(i);
                // 排除空行
                if (row == null) {
                    continue;
                }
                // 是否存在非空cell
                boolean flag = false;
                for (int j = 0, len2 = row.getPhysicalNumberOfCells(); j < len2; j++) {
                    Cell cell = row.getCell(j);

                    if (StringUtils.isNotBlank(cell.toString())) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    continue;
                }
                // 获取行信息
                DotAddInfo dotAddInfo = getCells(row);
                dotAddInfoList.add(dotAddInfo);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        for (int i = 0, len = dotAddInfoList.size(); i < len; i++) {
            DotAddInfo dotAddInfo = dotAddInfoList.get(i);
            DotInfo work = dotAddInfo.getDotInfo();
            DotContacts dotContact = dotAddInfo.getDotContacts();
            sysUser.setMobile(dotContact.getContactsPhone());
            sysUser.setUsername(dotContact.getContactsName());
            // 添加网点信息
            int index = dotInfoMapper.insertSelective(work);
            // 添加管理员账号信息
            this.save(sysUser);
            List<Long> rolelist = new ArrayList<>();
            rolelist.add((long) 4);
            // 添加管理员账号角色信息
            sysUserRoleService.saveOrUpdate(sysUser.getUserId(), rolelist);
            dotContact.setContactsId((Integer.valueOf(sysUser.getUserId().toString())));
            // 添加网点管理员信息
            int num = dotInfoMapper.addContact(dotContact);
            // 添加网点辐射区域
            int ns = dotInfoMapper.addDotAreaInfo(work.getDotId(), dotAddInfo.getArealist(), 0L);

        }
        return R.ok().putList("success");
    }


    public DotAddInfo getCells(Row row) {
        DotAddInfo dotAddInfo = new DotAddInfo();
        DotInfo dotInfo = new DotInfo();
        DotContacts dotContacts = new DotContacts();
        int i = 1;
        // 工贸名称
        String branchName = ExcelInputUtil.getCellValue(row, i++);
        log.info("工贸名称: [" + branchName + "]");
        dotInfo.setBranchName(branchName);
        // 网点性质
//        String dotQuality = ExcelInputUtil.getCellValue(row, i++);
//        log.info("网点性质: [" + dotQuality + "]");
//        dotInfo.setDotQuality(dotQuality);
        //网点特性
        String dotFeatures = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点特性: [" + dotFeatures + "]");
        dotInfo.setDotFeatures(dotFeatures);
        // 网点名称
        String dotName = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点名称: [" + dotName + "]");
        dotInfo.setDotName(dotName);
        // 网点邮编
        String dotPostcode = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点邮编: [" + dotPostcode + "]");
        dotInfo.setDotPostcode(dotPostcode);
        //网点地址
        String dotAddress = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点地址: [" + dotAddress + "]");
        dotInfo.setDotAddress(dotAddress);
        //网点种类
        String dotClass = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点种类: [" + dotClass + "]");
        dotInfo.setDotClass(dotClass);
        //客户编码
        String customerCode = ExcelInputUtil.getCellValue(row, i++);
        log.info("客户编码: [" + customerCode + "]");
        dotInfo.setCustomerCode(customerCode);
        //网点编码
        String dotCode = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点编码: [" + dotCode + "]");
        dotInfo.setDotCode(dotCode);
        //市场级别
        String marketLevel = ExcelInputUtil.getCellValue(row, i++);
        log.info("市场级别: [" + marketLevel + "]");
        dotInfo.setMarketLevel(marketLevel);
        //建点时间
        String dotCreateTime = ExcelInputUtil.getCellValue(row, i++);
        log.info("建点时间: [" + dotCreateTime + "]");
        try {
            dotInfo.setDotCreateTime(DateUtils.stringToDate(dotCreateTime, DateUtils.DATE_PATTERN));
        } catch (Exception e) {
            dotInfo.setDotCreateTime(null);
        }
        //建点人
        String dotCreatePer = ExcelInputUtil.getCellValue(row, i++);
        log.info("建点人: [" + dotCreatePer + "]");
        dotInfo.setDotCreatePer(dotCreatePer);
        //法人姓名
        String legalPersonName = ExcelInputUtil.getCellValue(row, i++);
        log.info("法人姓名: [" + legalPersonName + "]");
        dotInfo.setLegalPersonName(legalPersonName);
        //法人性别
        String legalPersonSex = ExcelInputUtil.getCellValue(row, i++);
        log.info("法人性别: [" + legalPersonSex + "]");
        dotInfo.setLegalPersonSex(legalPersonSex);
        //文化程度
        String degreeEducation = ExcelInputUtil.getCellValue(row, i++);
        log.info("文化程度: [" + degreeEducation + "]");
        dotInfo.setDegreeEducation(degreeEducation);
        //区号
        String areaCode = ExcelInputUtil.getCellValue(row, i++);
        log.info("区号: [" + areaCode + "]");
        //电话
        String telCode = ExcelInputUtil.getCellValue(row, i++);
        log.info("电话: [" + telCode + "]");
        //电话
        String telephone = areaCode + "-" + telCode;
        log.info("电话: [" + telephone + "]");
        dotInfo.setTelephone(telephone);
        //备件库面积（平方米）
        String storageSpare = ExcelInputUtil.getCellValue(row, i++);
        log.info("备件库面积: [" + storageSpare + "]");
        dotInfo.setStorageSpare(storageSpare);
        //交通工具数量
        String vehiclesNums = ExcelInputUtil.getCellValue(row, i++);
        log.info("交通工具数量: [" + vehiclesNums + "]");
        if (StringUtils.isNotBlank(vehiclesNums)) {
//            dotInfo.setVehiclesNums(Integer.parseInt(vehiclesNums));
        }
        //合同结束日期
        String contractEnd = ExcelInputUtil.getCellValue(row, i++);
        log.info("合同结束日期: [" + contractEnd + "]");
        try {
            dotInfo.setContractEnd(DateUtils.stringToDate(contractEnd, DateUtils.DATE_PATTERN));
        } catch (Exception e) {
            dotInfo.setContractEnd(null);
        }
        //经理姓名
        String contactName = ExcelInputUtil.getCellValue(row, i++);
        log.info("经理姓名: [" + contactName + "]");
        dotInfo.setContactName(contactName);
        //经理手机号
        String dotTelephone = ExcelInputUtil.getCellValue(row, i++);
        log.info("经理手机号: [" + dotTelephone + "]");
        dotInfo.setDotTelephone(dotTelephone);
        //网点邮箱
        String dotEmail = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点邮箱: [" + dotEmail + "]");
        dotInfo.setDotEmail(dotEmail);
        //开户行姓名
        String dotBank = ExcelInputUtil.getCellValue(row, i++);
        log.info("开户行姓名: [" + dotBank + "]");
        dotInfo.setDotBank(dotBank);
        //户头
        String bankName = ExcelInputUtil.getCellValue(row, i++);
        log.info("户头: [" + bankName + "]");
        dotInfo.setBankName(bankName);
        //开户行行号
        String bankNumber = ExcelInputUtil.getCellValue(row, i++);
        log.info("开户行行号: [" + bankNumber + "]");
        dotInfo.setBankNumber(bankNumber);
        //开户行帐号
        String bankAccount = ExcelInputUtil.getCellValue(row, i++);
        log.info("开户行帐号: [" + bankAccount + "]");
        dotInfo.setBankAccount(bankAccount);
        //税号
        String taxNo = ExcelInputUtil.getCellValue(row, i++);
        log.info("税号: [" + taxNo + "]");
        dotInfo.setTaxNo(taxNo);
        //企业性质
        String companyQuality = ExcelInputUtil.getCellValue(row, i++);
        log.info("企业性质: [" + companyQuality + "]");
        dotInfo.setCompanyQuality(companyQuality);
        //注册资金（万）
        String registFund = ExcelInputUtil.getCellValue(row, i++);
        log.info("注册资金: [" + registFund + "]");
        dotInfo.setRegistFund(registFund);
        //工商登记时间
        Date buseRegistTime = ExcelInputUtil.getDate(row, i++);
        log.info("工商登记时间: [" + buseRegistTime.toString() + "]");
        dotInfo.setBuseRegistTime(buseRegistTime);
        //税务登记时间
        Date taxRegistTime = ExcelInputUtil.getDate(row, i++);
        log.info("税务登记时间: [" + taxRegistTime.toString() + "]");
        dotInfo.setTaxRegistTime(taxRegistTime);
        //税务有效截止时间
        String taxDeadline = ExcelInputUtil.getCellValue(row, i++);
        Date taxDeadlineDate = null;
        try {
            taxDeadlineDate = DateUtils.stringToDate(ExcelInputUtil.filterDate(taxDeadline), DateUtils.DATE_PATTERN);
            taxDeadline = DateUtils.format(taxDeadlineDate);
        } catch (Exception e) {
            log.error("税务有效截止时间有误");
            taxDeadline = null;
        }
        dotInfo.setTaxDeadline(taxDeadlineDate);
        log.info("税务有效截止时间: [" + taxDeadline + "]");
        //工商有限截止时间
        String industryDeadline = ExcelInputUtil.getCellValue(row, i++);
        Date industryDeadlineDate = null;
        try {
            industryDeadlineDate = DateUtils.stringToDate(ExcelInputUtil.filterDate(industryDeadline), DateUtils.DATE_PATTERN);
            industryDeadline = DateUtils.format(industryDeadlineDate);
        } catch (Exception e) {
            log.error("工商有限截止时间有误");
            industryDeadline = null;
        }
        dotInfo.setIndustryDeadline(industryDeadlineDate);
        log.info("工商有限截止时间: [" + industryDeadline + "]");
        //工商注册号
        String buseRegistNumber = ExcelInputUtil.getCellValue(row, i++);
        log.info("工商注册号: [" + buseRegistNumber + "]");
        dotInfo.setBuseRegistNumber(buseRegistNumber);
        //认证时间
        String approveTime = ExcelInputUtil.getCellValue(row, i++);
        log.info("认证时间: [" + approveTime + "]");
        dotInfo.setApproveTime(DateUtils.stringToDate(approveTime, DateUtils.DATE_PATTERN));
        //网点税点
        String taxPoint = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点税点: [" + taxPoint + "]");
        dotInfo.setTaxPoint(taxPoint);
        //资质
        String aptitude = ExcelInputUtil.getCellValue(row, i++);
        log.info("资质: [" + aptitude + "]");
        dotInfo.setAptitude(aptitude);
        //是否一般纳税人
        String commonTaxPer = ExcelInputUtil.getCellValue(row, i++);
        log.info("是否一般纳税人: [" + commonTaxPer + "]");
        dotInfo.setCommonTaxPer(commonTaxPer);
        //车小鳐数量
        String smallCarRays = ExcelInputUtil.getCellValue(row, i++);
        log.info("车小鳐数量: [" + smallCarRays + "]");
        if (StringUtils.isNotBlank(smallCarRays)) {
            dotInfo.setSmallCarRays(Integer.parseInt(smallCarRays));
        }
        //普通车辆数量
        String commonCarNum = ExcelInputUtil.getCellValue(row, i++);
        log.info("普通车辆数量: [" + commonCarNum + "]");
        if (StringUtils.isNotBlank(commonCarNum)) {
            dotInfo.setCommonCarNum(Integer.parseInt(commonCarNum));
        }
        //网点状态
        String dotState = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点状态: [" + dotState + "]");
        dotInfo.setDotState(dotState);
        //服务兵签退权限
        String dotSignOut = ExcelInputUtil.getCellValue(row, i++);
        log.info("服务兵签退权限: [" + dotSignOut + "]");
        if (StringUtils.isNotBlank(dotSignOut)) {
            dotInfo.setDotSignOut(Integer.parseInt(dotSignOut));
        }
        //管理员账号
        String contactsName = ExcelInputUtil.getCellValue(row, i++);
        log.info("管理员账号: [" + contactsName + "]");
        dotContacts.setContactsName(contactsName);
        //管理员手机号
        String contactsPhone = ExcelInputUtil.getCellValue(row, i++);
        log.info("管理员手机号: [" + contactsPhone + "]");
        dotContacts.setContactsPhone(contactsPhone);
        //管理员邮箱
        String contactsEmail = ExcelInputUtil.getCellValue(row, i++);
        log.info("管理员邮箱: [" + contactsEmail + "]");
        dotContacts.setContactsEmail(contactsEmail);
        dotContacts.setDotCode(dotCode);

        //辐射区域
        String area = ExcelInputUtil.getCellValue(row, i++);
//        log.info("辐射区域: [" + arealist + "]");
        String idCard = ExcelInputUtil.getCellValue(row, i++);
        log.info("身份证: [" + idCard + "]");
        dotInfo.setCorporateIdCard(idCard);
        String dotVCode = ExcelInputUtil.getCellValue(row, i++);
        log.info("网点V码: [" + dotVCode + "]");
        dotInfo.setvCode(dotVCode);
        List<Integer> regionIds = workMsgService.listIndustryRegionIds(branchName);
        List<AreaDot> arealist = new ArrayList<>();
        regionIds.stream().filter(item -> null != item).map(item -> {
            arealist.add(new AreaDot(item, null));
            return null;
        }).collect(Collectors.toList());
        dotAddInfo.setArealist(arealist);
        dotAddInfo.setDotContacts(dotContacts);
        dotAddInfo.setDotInfo(dotInfo);
        return dotAddInfo;
    }

    public static void main(String[] args) {
        DotControlller dotControlller = new DotControlller();
        dotControlller.testxx();
    }

    public R testxx() {
        File file = new File("/Users/<USER>/Desktop/网点导入数据补充20200805.xls");
        String fileName = file.getName();
        System.out.println(fileName);
        boolean notNull = false;
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            System.out.println("上传文件格式不正确");
            return R.error("上传文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        Date date = new Date();

        List<DotAddInfo> dotAddInfoList = new ArrayList<>();

        String salt = RandomStringUtils.randomAlphanumeric(20);
        String password = "admin";
        SysUserEntity sysUser = new SysUserEntity();
        sysUser.setPassword(new Sha256Hash(password, salt).toHex());
        sysUser.setSalt(salt);
        sysUser.setStatus(1);
        sysUser.setRoleName("网点管理员");
        sysUser.setCreateTime(date);
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            Workbook wb = null;
            if (isExcel2003) {
                wb = new HSSFWorkbook(is);
            } else {
                wb = new XSSFWorkbook(is);
            }
            Sheet sheet = wb.getSheetAt(0);
            if (sheet != null) {
                notNull = true;
            }
            for (int i = 2, len = sheet.getPhysicalNumberOfRows(); i <= len; i++) {
                BizAttendantEntity excelInput = new BizAttendantEntity();
                Row row = sheet.getRow(i);
                // 排除空行
                if (row == null) {
                    continue;
                }
                // 是否存在非空cell
                boolean flag = false;
                for (int j = 0, len2 = row.getPhysicalNumberOfCells(); j < len2; j++) {
                    Cell cell = row.getCell(j);

                    if (StringUtils.isNotBlank(cell.toString())) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    continue;
                }
                // 获取行信息
                DotAddInfo dotAddInfo = getCells(row);
                dotAddInfoList.add(dotAddInfo);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        for (int i = 0, len = dotAddInfoList.size(); i < len; i++) {
            DotAddInfo dotAddInfo = dotAddInfoList.get(i);
            DotInfo work = dotAddInfo.getDotInfo();
            DotContacts dotContact = dotAddInfo.getDotContacts();
            sysUser.setMobile(dotContact.getContactsPhone());
            sysUser.setUsername(dotContact.getContactsName());


        }
        return R.ok().putList("success");
    }


}
