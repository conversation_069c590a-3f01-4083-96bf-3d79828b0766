package com.bonc.rrs.workManager.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bonc.rrs.workManager.entity.PerformanceTargerEntity;
import com.bonc.rrs.workManager.entity.dto.PerformanceTargerExcelProperty;
import com.bonc.rrs.workManager.service.PerformanceTargerService;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 网点评分导入处理类
 * @Author: liujunpeng
 * @Date: 2023/8/28 15:18
 * @Version: 1.0
 */
@AllArgsConstructor
@Log4j2
public class PerformanceTargerExcelListener extends AnalysisEventListener<PerformanceTargerExcelProperty> {

    private PerformanceTargerService performanceTargerService;

    private Integer userId;

    private List<String> failureDatas;

    private Set<String> keys;

    @Override
    public void invoke(PerformanceTargerExcelProperty data, AnalysisContext context) {

        if (StringUtils.isNotBlank(data.getCycle())) {
            // 定义正则表达式模式
            String regexPattern = "\\d{4}-\\d{2}";
            // 创建 Pattern 对象
            Pattern pattern = Pattern.compile(regexPattern);
            // 使用 Pattern 对象进行匹配
            Matcher matcher = pattern.matcher(data.getCycle());
            if (!matcher.matches()) {
                failureDatas.add("序号：" + data.getSerialNumber() + ",周期不合法");
                return;
            }
        } else {
            failureDatas.add("序号：" + data.getSerialNumber() + ",周期不能为空");
            return;
        }

        int indicatorId;
        try {
            indicatorId = Integer.parseInt(data.getIndicatorId());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",指标不合法");
            return;
        }
        int brandId;
        try {
            brandId = Integer.parseInt(data.getBrandId());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",品牌不合法");
            return;
        }
        int provinceId;
        try {
            provinceId = Integer.parseInt(data.getProvinceId());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",省份不合法");
            return;
        }

        BigDecimal target;
        if (StringUtils.isNotBlank(data.getTarget())) {
            try {
                target = new BigDecimal(data.getTarget());
            } catch (Exception e) {
                failureDatas.add("序号：" + data.getSerialNumber() + ",目标值不合法");
                return;
            }
        } else {
            failureDatas.add("序号：" + data.getSerialNumber() + ",目标值不能为空");
            return;
        }

        try {
            //先删除
            UpdateWrapper<PerformanceTargerEntity> performanceWrapper = new UpdateWrapper<>();
            performanceWrapper.set("delete_state", 1);
            performanceWrapper.eq("cycle", data.getCycle())
                    .eq("brand_id", data.getBrandId())
                    .eq("province_id", data.getProvinceId())
                    .eq("indicator_id", data.getIndicatorId());
            //先删除在新增
            performanceTargerService.getBaseMapper().update(null, performanceWrapper);
            //插入
            PerformanceTargerEntity entity = new PerformanceTargerEntity();
            entity.setCycle(data.getCycle());
            entity.setBrandId(brandId);
            entity.setProvinceId(provinceId);
            entity.setIndicatorId(indicatorId);
            entity.setTarget(target);
            entity.setCreateUser(userId);
            performanceTargerService.save(entity);
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",入库失败：" + e.getMessage());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadWorkbookHolder readWorkbookHolder = context.readWorkbookHolder();
        InputStream inputStream = readWorkbookHolder.getInputStream();
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
