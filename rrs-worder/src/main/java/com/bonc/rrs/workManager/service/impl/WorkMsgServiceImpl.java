
package com.bonc.rrs.workManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.supervise.entity.SuperviseInfomation;
import com.bonc.rrs.supervise.entity.SuperviseOperationRecord;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.WorderTemplateDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.service.*;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
* @author: minJunping
* @date: 2020/03/01
* */
@Service
@Log4j2
public class WorkMsgServiceImpl implements WorkMsgService {

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    @Autowired(required = false)
    private DotInfoMapper dotInfoMapper;

    @Autowired(required = false)
    private BizRegionMapper bizRegionMapper;

    @Autowired(required = false)
    private SendOrdersMapper sendOrdersMapper;

    @Autowired(required = false)
    private SysFilesService sysFilesService;
    @Autowired(required = false)
    private SendOrderService sendOrderService;
    @Autowired
    private UserRoleAreaPermissionsService userRoleAreaPermissions;
    @Autowired
    private FlowCommon flowCommon;

    @Autowired
    private SendWorderRecordService sendWorderRecordService;

    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired(required = false)
    private AttendantSendsRecordMapper attendantSendsRecordMapper;

    @Autowired
    private WorderTemplateDao worderTemplateDao;



    public String checkcountname(String countname)
    {
        String p = "[\u4e00-\u9fa5]";
        Matcher m = Pattern.compile(p).matcher(countname);
        if (m.find()) {
            return "1";
        }
        return "2";
    }

    /*
     * 派单权限控制
     * 已经派单控制
     * 可选服务经理回显
     * 常用服务经理回显
     * 客服经理派单给服务经理
     * */
    @Override
    public Results selectMangerService(String username, String worderno) {
        try {
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(worderno)){
                WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no",worderno));

                Integer areaId = worder.getAreaId();
                int nums = worder.getWorderExecStatus();
                if (nums == 18) {
                    return Results.message(1, "本用户已经派单给服务经理了", null);
                }

                //根据工单号查询工单模版
                WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worder.getTemplateId());

                //获取品牌
                Integer brandId = sendOrdersMapper.selectBrandIdByWorderNo(worderno);
                //根据工单的areaId筛选覆盖此区域的服务经理
                List<Integer> userIdlist = sendOrderService.selectManager(areaId,brandId,template.getServiceTypeEnum(),null);
                if (userIdlist.size() != 0){
                    List<Map<String, String>> mapList = workMsgDao.selectMangerlist(userIdlist);
                    List<Map<String, String>> maps = workMsgDao.selectContactlist(username);
                    Map<String, List<Map<String, String>>> stringListMap = new HashMap<>();
                    stringListMap.put("newmanager", mapList);
                    stringListMap.put("historymanager", maps);
                    return Results.message(0, "success", stringListMap);
                }else {
                    return Results.message(3,"查不到相关辐射区域的服务经理或者辐射区域网格未启用",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            log.error(e);
            return Results.message(110,"接口异常",null);
        }

    }

    /**
     * 获取用户
     *
     * @return
     */
    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }


    /*
     * 派单权限控制
     * 已经派单控制
     * 可选服务经理回显
     * 常用服务经理回显
     * 客服经理派单给服务经理
     * */
    @Override
    public Results selectUpdateMangerService(String username, String worderno) {
        try {
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(worderno)){
                WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no",worderno));
                Integer userId = worder.getPmId();
                Integer areaId = worder.getAreaId();
                int nums = worder.getWorderExecStatus();
                if (nums == 17) {
                    return Results.message(1, "已结单不可改派服务经理", null);
                }
                //获取品牌
                Integer brandId = sendOrdersMapper.selectBrandIdByWorderNo(worderno);

                //根据工单号查询工单模版
                WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worder.getTemplateId());

                //根据工单的areaId筛选覆盖此区域的服务经理
                List<Integer> userIdlist = sendOrderService.selectManager(areaId,brandId,template.getServiceTypeEnum(),userId);
                if (userIdlist.size() != 0){
                    List<Map<String, String>> mapList = workMsgDao.selectMangerlist(userIdlist);
                    List<Map<String, String>> maps = workMsgDao.selectContactlist(username);
                    Map<String, List<Map<String, String>>> stringListMap = new HashMap<>();
                    stringListMap.put("newmanager", mapList);
                    stringListMap.put("historymanager", maps);
                    return Results.message(0, "success", stringListMap);
                }else {
                    return Results.message(3,"未查到其他项目经理",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            log.error("接口异常",e);
            return Results.message(110,"接口异常",null);
        }

    }

    /*
     * 可选服务经理回显
     * 常用服务经理回显
     * 改派
     * */
    @Override
    public Results getupdateUserNameService(String username, String worderno) {
        try {
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(worderno)){
                WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no",worderno));
                Integer userId = worder.getPmId();
                Integer nums = sendOrdersMapper.selectMangerId(worderno);
                if (nums == 17) {
                    return Results.message(1, "已结单不可改派服务经理", null);
                }

                //根据工单号查询工单模版
                WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worder.getTemplateId());

                //获取品牌
                Integer brandId = sendOrdersMapper.selectBrandIdByWorderNo(worderno);
                //根据工单的品牌查找服务经理
                List<Integer> userIdlist = sendOrderService.selectManagerByBrand(brandId,userId,template.getServiceTypeEnum());
                if (userIdlist.size() != 0){
                    List<Map<String, String>> mapList = workMsgDao.selectMangerlist(userIdlist);
                    List<Map<String, String>> maps = workMsgDao.selectContactlist(username);
                    Map<String, List<Map<String, String>>> stringListMap = new HashMap<>();
                    stringListMap.put("newmanager", mapList);
                    stringListMap.put("historymanager", maps);
                    return Results.message(0, "success", stringListMap);
                }else {
                    return Results.message(3,"未查到其他项目经理",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            log.error(e);
            return Results.message(110,"接口异常",null);
        }

    }


    /*
     * 添加派单操作记录
     * 更新工单状态，工单项目组信息
     * 添加服务经理至常用经理表中
     * 客服经理派单给服务经理
     * */
    @Override
    @Transactional
    public Results insertOperation(String worderRecord) {
            OperationRecord work = JSONObject.parseObject(worderRecord, OperationRecord.class);
//            SysUserEntity sysUserEntity = SysUserUtil.getUser();
//            work.setOperationUser(sysUserEntity.getUsername());
//            work.setUserId(sysUserEntity.getUserId());
            if (!StringUtils.isEmpty(work)){
                WorderInformationEntity worderInformation = worderInformationService.getBaseMapper().selectById(work.getWorderId());
                    if (worderInformation.getWorderStatus()==0&&StringUtils.isEmpty(worderInformation.getPmId())){
//                    String username = work.getOperationUser().replace(" ","");
//                    String affectUser = work.getAffectedUser().replace(" ", "");
//                    int numsUpdate = 0;
//                    int members = workMsgDao.getContactCount(username,affectUser);
//                    if (members > 0){
//                        Map<String, Long> member = workMsgDao.getContactNum(username,affectUser);
//                        numsUpdate = workMsgDao.updataContactNum(username, member.get("nums") +  1, affectUser);
//                    }else{
//                        numsUpdate = workMsgDao.addContactAffect(username,affectUser,work.getAffectedUserId());
//                    }

                    if(worderInformation!=null && worderInformation.getServiceId()!=null){
                        List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", worderInformation.getWorderId()).eq("delete_state", 0).eq("accept_worder_user", worderInformation.getServiceId()).eq("send_worder_type",3).ge("create_time", LocalDate.now()));
                        if(sendWorderRecords != null && !sendWorderRecords.isEmpty()){
                            //获取服务兵派单量
                            List<AttendantSendsRecord> beforeSendOrder = attendantSendsRecordMapper.listAttendantNum(worderInformation.getServiceId().longValue(), LocalDate.now());
                            if (beforeSendOrder.get(0).getOverflow() > 0) {
                                //溢出量-1
                                attendantSendsRecordMapper.updateOverflow(worderInformation.getServiceId().longValue(), beforeSendOrder.get(0).getOverflow() - 1, beforeSendOrder.get(0).getSendNum() - 1, LocalDate.now());
                            } else {
                                if (beforeSendOrder.get(0).getSendNum() > 0) {
                                    //派单量-1
                                    attendantSendsRecordMapper.updateSendNum(worderInformation.getServiceId().longValue(), beforeSendOrder.get(0).getSendNum() - 1, LocalDate.now());
                                }
                            }
                        }
                    }

                    int nums = 0;
                    // 判断是否配置工作流
                    if (flowCommon.hasFlowByWorderNo(work.getWorderNo())) {
                        // 调用流程
                        ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(work.getWorderNo(), FlowConstant.ProcessCode.AssignProjMng, FlowConstant.ProcessStatus.Y);
                        // 流程调用失败直接返回
                        if(!"0".equals(executeFlowResultPo.getCode())){
                            return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(),null);
                        }
                        nums = workMsgDao.flowUpdataOrderMager(work.getWorderNo(), work.getAffectedUser(), work.getAffectedUserId());
                    } else {
                        nums = workMsgDao.updataOrderMager(work.getWorderNo(), work.getAffectedUser(),"待服务经理派单给网点", work.getAffectedUserId(),18);
                    }

//                    System.out.println(nums);
//                    int nums = workMsgDao.updataOrderMager("SSH2019-09-300007", "11111", 1);

                    int index = workMsgDao.insertOperation(work);

                    System.out.println(index);
                    if (index > 0 && nums > 0){
                        //保存服务经理派单记录
                        //判断是派单还是改派
                        SendWorderRecord sendWorderRecord = new SendWorderRecord()
                                .setWorderId(work.getWorderId())
                                .setSendWorderType(1L)
                                .setSendWorderUser(work.getUserId())
                                .setAcceptWorderUser(work.getAffectedUserId())
                                .setOperationType(worderInformation.getPmId() != null ? 3L : 2L)
                                .setOperationUser(work.getUserId())
                                .setCreateTime(LocalDateTime.now())
                                .setRemark(work.getRecord());
                        sendWorderRecordService.save(sendWorderRecord);
                        return Results.message(0, "success",null).putWorderNo(work.getWorderNo())
                                .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_MANAGER)
                                .putWorderExecStatus(FieldConstant.WAIT_BRANCH_SEND_ORDER);
                    }else {
                        return Results.message(10, "添加失败",null);
                    }
                }else{
                    return Results.message(10,"已分配项目经理",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
    }

    /*
     * 添加派单操作记录
     * 更新工单状态，工单项目组信息
     * 添加服务经理至常用经理表中
     * 客服经理派单给服务经理
     * */
    @Override
    @Transactional
    public Results updateOperation(String worderRecord) {
        SysUserEntity sysUser = getUser();
        OperationRecord work = JSONObject.parseObject(worderRecord, OperationRecord.class);
        if (!StringUtils.isEmpty(work)){
            int nums = 0;
            nums = workMsgDao.updataManager(work.getWorderNo(), work.getAffectedUser(),"待服务经理派单给网点", work.getAffectedUserId(),18);
            List<SuperviseInfomation> superviseInfomationList = workMsgDao.getSuperviseByWorderNo(work.getWorderNo());
            for (SuperviseInfomation superviseInfomation : superviseInfomationList) {
                //转移督办单 转移来电记录 记录操作记录
                workMsgDao.userSuperviseTransfer(Integer.valueOf(work.getAffectedUserId().toString()),superviseInfomation.getDutyPeo(),work.getWorderNo());
                workMsgDao.userSuperviseTeleTransfer(Integer.valueOf(work.getAffectedUserId().toString()),superviseInfomation.getDutyPeo(),work.getWorderNo());
                SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
                superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
                superviseOperationRecord.setRelatedPeo(superviseInfomation.getRelatedPeo());
                superviseOperationRecord.setDutyPeo(work.getAffectedUserId().toString());
                superviseOperationRecord.setOptrationContent("督办单转移");
                superviseOperationRecord.setUserId(Integer.valueOf(sysUser.getUserId().toString()));
                superviseOperationRecord.setUserName(sysUser.getUsername());
                workMsgDao.insertSuperviseOperationRecord(superviseOperationRecord);
            }
            return Results.message(0,"sucess",null);
        }else {
            return Results.message(100,"参数不能为空",null);
        }
    }


    /*
    * 网点分页条件查询
    * */
    @Override
    public R selectDotInfo(String nameCode, String contactName, String vCode,String dotStar, Integer currentIndex,
                           Integer pageSize,Integer index,String dotState,List<Integer> roleIds,Integer userId) throws Exception {
        try {


            String code = "";
            if ("".equals(contactName)){
                contactName = null;
            }
            if ("".equals(dotStar)){
                dotStar = null;
            }
            if ("".equals(vCode)){
            vCode = null;
             }
            if (!StringUtils.isEmpty(nameCode)){
                code = this.checkcountname(nameCode);
            }
            //用户角色区域权限查询
//            List<String> areaIds = userRoleAreaPermissions.queryUserRoleCompetenceConfig("dotInformationRoleConfig",userId,roleIds);
            List<SelectDot> PreRegistInfos = dotInfoMapper.selectDotInfo(code,nameCode,contactName,vCode,dotStar,currentIndex,userId,pageSize,dotState);
            Integer total = dotInfoMapper.getDotInfoCount(code,nameCode,contactName,vCode,dotStar,dotState,userId);
            return R.ok().put("data",PreRegistInfos).put("total",total).put("pageSize",pageSize).put("pageIndex",index);
        }catch (Exception e){
            e.printStackTrace();
            return R.error("网点查询接口异常");
        }

    }

    /*
    * 行政区三级查询
    * */
    @Override
    public Results getArealist(Integer pid) {

        try {
            if (!StringUtils.isEmpty(pid)){
                List<BizRegion> bizRegionList = bizRegionMapper.selectArealist(pid);
                if (!StringUtils.isEmpty(bizRegionList)){
                    return Results.message(0,"success",bizRegionList);
                }else {
                    return Results.message(10,"没有相关pid参数",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"行政区查询接口异常",null);
        }

    }

    /*
    * 工贸信息查询
    * */
    @Override
    public Results getIndustryName(String name) {

        try {
            if (name.equals("")){
                name = null;
            }
            List<Map<String,String>> mapList = workMsgDao.selectIndustryName(name);
            if (mapList.size() != 0) {
                return Results.message(0, "success", mapList);
            }else {
                return Results.message(10,"没有查询到相关参数",null);
            }
        }catch (Exception e){
            return Results.message(110,"工贸信息查询接口异常",null);
        }
    }

    /**
     * 查询工贸编码
     * @param branchName 工贸名称
     * @return 工贸编码
     */
    @Override
    public String getBranchCode(String branchName){
       return workMsgDao.selectBranchCode(branchName);
    }
    @Override
    public Results saveFilePath(Integer id, String contractEnd, MultipartFile file) {

        try {
            if (!StringUtils.isEmpty(id) && !StringUtils.isEmpty(contractEnd) && !StringUtils.isEmpty(file)) {
                String oldName = file.getOriginalFilename();
                Map<String, String> urlMap = FileUtils.getUrl(file);
                String path = urlMap.get("url");
                String name = urlMap.get("name");
                String md5Hex =urlMap.get("md5Str");
                Integer fileId = sysFilesService.saveFilePath(path,name,oldName,md5Hex);
                int index = workMsgDao.updateFilePath(id,fileId.toString(),contractEnd);
                if (index > 0){
                    return Results.message(0,"success",null);
                }else {
                    return Results.message(10,"文件上传失败",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"合同文件上传接口异常",null);
        }

    }

    @Override
    public List<Integer> listIndustryRegionIds(String brachName) {
        return workMsgDao.listIndustryRegionIds(brachName);
    }

}
