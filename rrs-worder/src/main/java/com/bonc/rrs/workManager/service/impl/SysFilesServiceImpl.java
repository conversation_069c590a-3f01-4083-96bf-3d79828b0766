package com.bonc.rrs.workManager.service.impl;

import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.entity.FileEntity;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.utils.R;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Log4j2
public class SysFilesServiceImpl implements SysFilesService {

    @Autowired(required = false)
    private SysFilesMapper sysFileMapper;

    @Override
    public Integer saveSysFilePath(String path, String name) {
        return saveSysFilePath(path, name);
    }

    @Override
    public Integer saveSysFilePath(String path, String name, String oldName,String md5Str) {
        SysFileEntity sysFileEntity = new SysFileEntity();
        sysFileEntity.setPath(path);
        sysFileEntity.setNewName(name);
        sysFileEntity.setOldName(oldName);
        sysFileEntity.setFileCode(md5Str);
        return saveSysFile(sysFileEntity);
    }

    @Override
    public Integer saveFilePath(String path,String name,String oldName,String md5Str) {
        SysFileEntity sysFileEntity = new SysFileEntity();
        sysFileEntity.setPath(path);
        sysFileEntity.setNewName(name);
        sysFileEntity.setOldName(oldName);
        sysFileEntity.setFileCode(md5Str);
        return saveSysFile(sysFileEntity);
    }
    @Override
    public Integer saveFile(String path, String name, String oldName,Integer objValue,String md5Str) {
        SysFileEntity sysFileEntity = new SysFileEntity();
        sysFileEntity.setPath(path);
        sysFileEntity.setNewName(name);
        sysFileEntity.setOldName(oldName);
        sysFileEntity.setObjValue(objValue);
        sysFileEntity.setFileCode(md5Str);
        Integer num = sysFileMapper.saveFile(sysFileEntity);
        return num;
    }

    @Override
    public Integer saveSysFile(SysFileEntity sysFile) {
        Integer num = sysFileMapper.saveSysFile(sysFile);
        return sysFile.getFileId();
    }

    @Override
    public Results batchAddFilelist(Integer ObjValue, Integer ObjType, MultipartFile[] Filelist) throws IOException {

        List<FileEntity> filelist = new ArrayList<>();

        for (int i = 0; i < Filelist.length; i++) {
            FileEntity fileEntity = new FileEntity();
            MultipartFile file = Filelist[i];
            Map<String,String> fileInfoMap = FileUtils.getUrl(file,i);
            fileEntity.setFileName(fileInfoMap.get("name"));
            fileEntity.setUrl(fileInfoMap.get("url"));
            fileEntity.setFileCode(fileInfoMap.get("md5Str"));
            fileEntity.setObjType(ObjType);
            fileEntity.setObjValue(ObjValue);
            String md5Str = DigestUtils.md5Hex(file.getInputStream());
            fileEntity.setFileCode(md5Str);
            filelist.add(fileEntity);
        }

        sysFileMapper.addFilelist(filelist);
        List<Integer> idList = new ArrayList<>();
        for (FileEntity fileEntity : filelist) {
            idList.add(fileEntity.getId());
        }

        return Results.message(0,"success",idList);

    }

    @Override
    public SysFileEntity getSysFileById(String fileId) {
        List<SysFileEntity> sysFiles = sysFileMapper.listSysFiles(fileId);
        if (sysFiles.size() > 0) {
            return sysFiles.get(0);
        }
        return null;
    }
    /**
     * 通过工单id查询utl
     * @return
     */
    public List<Map<String, Object>>  getUrl(String worderNo,Integer purpose){
        List<Map<String, Object>> url = sysFileMapper.getUrl(worderNo,purpose);
        return url;
    }

    public Boolean fingByValue(String worderNo,String name){
        SysFileEntity sysFileEntity = sysFileMapper.fingByValue(worderNo,name);
        if(sysFileEntity.getFileId()==null){
            return true;
        }else{
            return false;
        }
    }

    public List<SysFileEntity> getAll(){
        return sysFileMapper.getAll();
    }

    public Integer updateCode(SysFileEntity sysFileEntity){
        return sysFileMapper.updateCode(sysFileEntity);
    }

    @Override
    public R getScoreImportFile() {
        try {
            SysFileEntity fileEntity = sysFileMapper.getScoreImportFile();
            return R.ok().put("data", fileEntity);
        } catch (Exception e) {
            log.error("查询评分上传文件异常", e);
            return R.error("加载文件失败");
        }
    }

    @Override
    public List<SysFileEntity> getSysFileByIds(String fileIds) {
        return sysFileMapper.getSysFileByIds(fileIds);
    }

    @Override
    public String getPathByName(String name) {
        return sysFileMapper.getPathByName(name);
    }
}