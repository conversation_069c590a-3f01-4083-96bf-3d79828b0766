package com.bonc.rrs.workManager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.OssFileZipUtil;
import com.bonc.rrs.util.ThreadUtil;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.vo.ExtFileVo;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.workManager.dao.AutidOrderMapper;
import com.bonc.rrs.workManager.dao.BrandMapper;
import com.bonc.rrs.workManager.dao.SysDoloadMapper;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.entity.SysDoloadEntity;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysDoloadService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.SneakyThrows;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipOutputStream;

@Service
public class SysDoloadServiceImpl implements SysDoloadService {
    @Autowired(required = false)
    SysDoloadEntity sysDoloadEntity;
    @Autowired(required = false)
    SysFilesMapper sysFilesMapper;
    @Autowired(required = false)
    SysDoloadMapper sysDoloadMapper;
    @Autowired(required = false)
    AutidOrderMapper autidOrderMapper;
    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    SysFilesService sysFilesService;
    @Autowired(required = false)
    BrandMapper brandMapper;
    @Autowired(required = false)
    SysDoloadService sysDoloadService;

    @SneakyThrows
    @Override
    public Long saveSysDoload(String worderNo , Integer purpose, Integer fieldId, Integer fieldType,SysUserEntity user,Integer state) {
        if(state==1){
            SysDoloadEntity sysDoloadEntity = new SysDoloadEntity();
             if(fieldType==3) {
                String companyOrderNumber = "";
                List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectList(new QueryWrapper<WorderInformationEntity>()
                        .eq("worder_no", worderNo));
                if (worderInformationEntities.size() > IntegerEnum.ZERO.getValue()) {
                    companyOrderNumber = worderInformationEntities.get(IntegerEnum.ZERO.getValue()).getCompanyOrderNumber();
                }
                String zipFileName = worderNo + "###" + companyOrderNumber + "###" + autidOrderMapper.getClientName(worderNo)
                        + (purpose == 2 ? "勘测" : purpose == 3 ? "安装" : "") + ".zip";
                String name = zipFileName;
                sysDoloadEntity.setName(name);
                sysDoloadEntity.setUid(user.getUserId());
                sysDoloadEntity.setState(0L);
                sysDoloadEntity.setDownType(2L);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String format = sdf.format(new Date());
                Date date = sdf.parse(format);
                sysDoloadEntity.setApplyTime(date);
                sysDoloadMapper.saveSysDoload(sysDoloadEntity);
            }
            return sysDoloadEntity.getId();
        } else{
            SysDoloadEntity sysDoloadEntity = new SysDoloadEntity();
            String name=(purpose==2?"勘测报告-word":"安装报告-word");
            //根据工单号获取品牌名称和车企单号
            List<Map<String, String>> maps = brandMapper.selectBrandCarName(worderNo);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateString = sdf.format(new Date());
            String oldName="";
            if(name.equals("勘测报告-word")){
                oldName=worderNo+"###"+ maps.get(0).get("company_order_number")+"###" + "勘测报告" +"###"+ dateString + "自动.docx";
            }else{
                oldName=worderNo+"###"+ maps.get(0).get("company_order_number") +"###" +"安装报告" +"###"+dateString + "自动.docx";
            }
            sysDoloadEntity.setName(oldName);
            sysDoloadEntity.setUid(user.getUserId());
            sysDoloadEntity.setState(0L);
            sysDoloadEntity.setDownType(purpose==2?1L:0L);
            SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdfs.format(new Date());
            Date date = sdfs.parse(format);
            sysDoloadEntity.setApplyTime(date);
            sysDoloadMapper.saveSysDoload(sysDoloadEntity);
            return sysDoloadEntity.getId();
        }
    }

    @Override
    public String down(Integer id) {
        String url = sysDoloadMapper.getUrl(id);
        return url;
    }

    /**
     * 下载时间
     * @param
     * @return
     */
    @Override
    public Integer updateTime(Integer id) throws ParseException {
        //根据id 修改时间
        SysDoloadEntity sysDoloadEntity = new SysDoloadEntity();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdf.format(new Date());
        Date date = sdf.parse(format);
        sysDoloadEntity.setDownTime(date);
        sysDoloadEntity.setId(id.longValue());
        return sysDoloadMapper.updateTime(sysDoloadEntity);
    }
    /**
     * 加入队列
     */

    @Override
    public R queue(String worderNo, Integer purpose, Integer fieldId, Integer fieldType, Integer state) {
        Map<String,Object> map = new HashMap<>();
        ConcurrentLinkedQueue<Map<String,Object>> list = ThreadUtil.queue;
        //获取用户名
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        SysDoloadEntity sysDoloadEntity = new SysDoloadEntity();
        Long id = sysDoloadService.saveSysDoload(worderNo, purpose, fieldId, fieldType, user,state);
        if(id!=0L){
            map.put("worderNo",worderNo);
            map.put("purpose",purpose);
            map.put("fieldId",fieldId);
            map.put("fieldType",fieldType);
            map.put("user",user);
            map.put("id",id);
            map.put("state",state);
            list.add(map);
            return R.ok();
        }
        return R.error();
    }

}
