package com.bonc.rrs.workManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bonc.rrs.baidumap.dao.DotSendsRecordMapper;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.byd.domain.PushBranchRecord;
import com.bonc.rrs.byd.domain.PushOrderBody;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.honeywell.entity.UpdateDispatchRequest;
import com.bonc.rrs.honeywell.service.HoneywellBizService;
import com.bonc.rrs.pay.manage.AsyncManager;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.BizRegionDao;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderRemarkLogDao;
import com.bonc.rrs.worder.dao.WorderTemplateDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.TechnicianLevelDto;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.workManager.async.WorkManagerAsyncFactory;
import com.bonc.rrs.workManager.constant.AttSendConstant;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.entity.vo.DotAddedMaterialTypeRealStockVo;
import com.bonc.rrs.workManager.service.*;
import com.bonc.rrs.xk.service.XkApiService;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.constant.WorderStatusEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.lenmoncore.common.validator.ValidatorUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryService;
import lombok.extern.log4j.Log4j2;
import org.activiti.engine.impl.util.CollectionUtil;
import org.apache.http.HttpStatus;
import org.apache.shiro.SecurityUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class SendOrderServiceImp implements SendOrderService {

    @Autowired(required = false)
    private SendOrdersMapper sendOrdersMapper;

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    @Autowired(required = false)
    private BizRegionMapper bizRegionMapper;
    @Autowired(required = false)
    private BizRegionDao bizRegionDao;
    @Autowired(required = false)
    private DotInfoMapper dotInfoMapper;
    @Autowired
    private FlowCommon flowCommon;
    @Autowired(required = false)
    private WorderTypeService worderTypeService;

    @Autowired(required = false)
    private ICaApiService caApiService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private AttendantSendsRecordService attendantSendsRecordService;
    @Autowired(required = false)
    private AttendantSendsRecordMapper attendantSendsRecordMapper;
    @Autowired(required = false)
    private DotInformationService dotInformationService;
    @Autowired(required = false)
    private SysDictionaryService sysDictionaryService;
    @Autowired(required = false)
    private BizAttendantService bizAttendantService;
    @Autowired(required = false)
    private DotAreaService dotAreaService;

    @Autowired
    private SendWorderRecordService sendWorderRecordService;
    @Autowired(required = false)
    private WorderInformationSubService worderInformationSubService;
    @Autowired
    private WorderRemarkLogDao worderRemarkLogDao;
    @Autowired(required = false)
    private DotSendsRecordMapper dotSendsRecordMapper;
    @Autowired(required = false)
    private WorderTemplateDao worderTemplateDao;
    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;
    @Autowired
    private ProviderBusinessService providerBusinessService;
    @Autowired
    private XkApiService xkApiService;
    @Autowired
    private WorderIntfMessageService worderIntfMessageService;
    @Autowired
    private HoneywellBizService honeywellBizService;
    @Autowired
    private WorderOperationRecordService worderOperationRecordService;
    @Autowired
    private SysFilesService sysFilesService;

    /*
     * 服务经理派单给网点中的网点信息查询
     * */
    @Override
    public Results getDotInfolist(String worderNo, String username, Integer type) {

        try {
            if (!StringUtils.isEmpty(worderNo)) {
                WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no",
                        worderNo));
                Integer dotSendId = null;
                Integer areaId = worder.getAreaId();
                //查询工单对应品牌ID
                Integer brandId = sendOrdersMapper.selectBrandIdByWorderNo(worderNo);

                //根据工单号查询工单模版
                WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worder.getTemplateId());

                if (type == 0) {
                    int nums = worder.getWorderExecStatus();
                    if (nums == 19) {
                        return Results.message(1, "本用户已经派单给网点", null);
                    }
                } else {
                    dotSendId = worder.getDotId();
                }

                if (areaId != null) {

                    //获取该工单的区县信息
                    BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
                    if (bizRegionEntity == null) {
                        return Results.message(10, "该工单未查询到区县信息", null);
                    }

                    //获取工单的地市信息
                    BizRegionEntity cityBizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(bizRegionEntity.getPid());
                    if (cityBizRegionEntity == null) {
                        return Results.message(10, "该工单未查询到地市信息", null);
                    }

                    //筛选辐射区域,品牌和服务类型，覆盖此工单的网点信息
                    List<Integer> dotIds = selectDotByAreas(areaId, brandId, template.getServiceTypeEnum(), dotSendId);
                    if (dotIds == null || dotIds.isEmpty()) {
                        return Results.message(10, "相关工单辐射区域和品牌没有网点或者区域网格未启用", null);
                    }

                    //剔除不符合的网点
                    List<DotArea> dotAreas = dotAreaService.selectListByDotsBrand(dotIds, brandId, template.getServiceTypeEnum());

                    if (dotAreas != null && !dotAreas.isEmpty()) {
                        //根据网点分组
                        Map<Integer, List<DotArea>> dotAreaGroupMap = dotAreas.stream().collect(Collectors.groupingBy(DotArea::getDotId));
                        //遍历Map
                        for (Map.Entry<Integer, List<DotArea>> entry : dotAreaGroupMap.entrySet()) {
                            List<DotArea> dotAreaList = entry.getValue();
                            // 判断一: 拿工单区域ID和该网点下关联的区域ID进行匹配。
                            long count = dotAreaList.stream().filter(item -> item.getAreaId() == areaId.intValue()).count();
                            if (count > 0) {
                                continue;
                            }

                            List<BizRegionEntity> rizRegionList = new ArrayList<>();
                            for (DotArea dotArea : dotAreaList) {
                                rizRegionList.add(InitRegionUtil.REGION_ID_MAP.get(dotArea.getAreaId().longValue()));
                            }
                            //市
                            long cityCount = rizRegionList.stream().filter(item -> cityBizRegionEntity.getId().equals(item.getId())).count();
                            //县
                            long countyCount =
                                    rizRegionList.stream().filter(item -> (item.getId() != areaId.longValue() && cityBizRegionEntity.getId().equals(item.getPid()) && item.getType() == 3)).count();

                            if (cityCount > 0 && countyCount > 0) {
                                dotIds.remove(entry.getKey());
                            }
                        }
                    }

                    if (dotIds.isEmpty()) {
                        return Results.message(10, "相关工单辐射区域和品牌没有网点或者区域网格未启用", null);
                    }

                    List<DotInfo> dotIdlist = dotIds.stream().map(dotId -> {
                        DotInfo dotInfo = new DotInfo();
                        dotInfo.setDotId(dotId);
                        return dotInfo;
                    }).collect(Collectors.toList());
                    String areaName = bizRegionMapper.selectAreaName(areaId);

                    List<Map<String, String>> mapList = sendOrdersMapper.selectDotInfo(dotIdlist);

                    for (Map<String, String> map : mapList) {
                        map.replace("radiateArea", areaName);
                    }

                    if (mapList.size() != 0) {
                        return Results.message(0, "success", mapList);
                    } else {
                        return Results.message(10, "相关工单辐射区域没有网点", null);
                    }
                } else {
                    return Results.message(1, "工单辐射区域不能为空", null);
                }
            } else {
                return Results.message(100, "工单编号参数不能为空", null);
            }
        } catch (Exception e) {
            log.error("网点信息查询接口异常", e);
            return Results.message(110, "网点信息查询接口异常", null);
        }

    }

    private List<Integer> selectDotByAreas(Integer areaId, Integer brandId, Integer serviceType, Integer dotId) {
        String regcode = handleCode(areaId);
        return dotInfoMapper.selectWorderAreaDotByRegcode(regcode, brandId, serviceType, dotId);
    }

    //手动派单根据工单的areaId筛选覆盖此区域的服务经理
    @Override
    public List<Integer> selectManager(Integer areaId) {
        String regcode = handleCode(areaId);
        List<Integer> managerId = dotInfoMapper.selectManagerArea(regcode);
        return managerId;
    }

    @Override
    public List<Integer> selectManager(Integer areaId, Integer brandId) {
        String regcode = handleCode(areaId);
        return dotInfoMapper.selectListWorderAreaManager(regcode, brandId);
    }

    @Override
    public List<Integer> selectManager(Integer areaId, Integer brandId, Integer serviceType, Integer userId) {
        String regcode = handleCode(areaId);
        List<Integer> managerId = dotInfoMapper.updateManagerArea(regcode, brandId, serviceType, userId);
        return managerId;
    }

    //自动派单根据工单的areaId和品牌筛选覆盖此区域的服务经理
    @Override
    public List<Integer> getManager(Integer areaId, Integer brandId, Integer serviceType) {
        String regcode = handleCode(areaId);
        return dotInfoMapper.selectWorderAreaManager(regcode, brandId, serviceType);
    }

    @Override
    public List<Integer> selectManagerByBrand(Integer brandId, Integer userId, Integer serviceType) {
        List<Integer> managerId = dotInfoMapper.updateManagerBrand(brandId, userId, serviceType);
        return managerId;
    }

    //根据工单的areaId筛选覆盖此区域的网点
    @Override
    public List<WorderAreaDotEntity> selectDot(Integer areaId) {
        String regcode = handleCode(areaId);
        List<WorderAreaDotEntity> worderArea = dotInfoMapper.selectWorderAreaDot(regcode);
        return worderArea;
    }

    @Override
    public List<Integer> selectDot(String countyRegcode, Integer brandId, Integer serviceType) {
        //区县的regcode
        String cityRegcode = countyRegcode.substring(0, 6);
        //省份
        String provinceRegcode = countyRegcode.substring(0, 3);
        return dotInfoMapper.selectWorderAreaDotByBrand(provinceRegcode, cityRegcode, brandId, serviceType);
    }

    //处理区域编码
    public String handleCode(Integer areaId) {
        BizRegionEntity bizRegionEntity = bizRegionDao.selectById(areaId);
        String code = bizRegionEntity.getRegcode();
        String regcode = "(" + code.substring(0, 3);
        if (code.length() > 3) {
            regcode += ", ";
            regcode += code.substring(0, 6);
        }
        if (code.length() > 6) {
            regcode += ", ";
            regcode += code.substring(0, 9);
        }
        regcode += ")";
        return regcode;
    }

    /**
     * 校验网点增值物料库存
     *
     * @param templateId
     * @param dotId
     * @param worderId
     * @return
     */
    @Override
    public R checkAddedMaterialStock(Integer templateId, Integer dotId, Integer worderId) {
        R rs = R.ok();
        List<WorderInformationAttributeEntity> worderInformationAttributeList = new ArrayList<>();
        // 查询模板配置的增值物料类型
        List<WorderTemplateAddedMaterialTypeEntity> worderTemplateAddedMaterialTypeList =
                dotInfoMapper.queryTemplateAddedMaterialTypeByTemplateId(templateId);
        if (worderTemplateAddedMaterialTypeList != null && worderTemplateAddedMaterialTypeList.size() > 0) {
            List<Integer> addedMaterialTypeIdList =
                    worderTemplateAddedMaterialTypeList.stream().map(WorderTemplateAddedMaterialTypeEntity::getMaterielTypeId).collect(Collectors.toList());

            Integer threshold = dotInfoMapper.getAddMaterialStockThreshold();

            // 查询网点下增值物料仓位对应的增值物料库存
            List<DotAddedMaterialTypeRealStockVo> dotAddedMaterialTypeRealStockVoList = dotInfoMapper.queryDotAddedMaterialTypeRealStock(dotId,
                    addedMaterialTypeIdList);
            if (dotAddedMaterialTypeRealStockVoList == null || dotAddedMaterialTypeRealStockVoList.size() == 0 || (dotAddedMaterialTypeRealStockVoList.size() > 0 && dotAddedMaterialTypeRealStockVoList.get(0) == null)) {

                rs = R.error("未查询到网点下增值物料库存，请核查！");

                String dotName = dotInfoMapper.getDotNameByDotId(dotId);

                rs.put("worderId", worderId);
                rs.put("threshold", threshold);
                rs.put("dotId", dotId);
                rs.put("dotName", dotName);
                rs.put("materielTypeName", worderTemplateAddedMaterialTypeList.get(0).getMaterielTypeName());
                rs.put("total", 0);
                return rs;
            }
            rs.put("worderId", worderId);
            rs.put("threshold", threshold);
            Map<Integer, List<DotAddedMaterialTypeRealStockVo>> dotAddedMaterialTypeRealStockVoMap =
                    dotAddedMaterialTypeRealStockVoList.stream().collect(Collectors.groupingBy(DotAddedMaterialTypeRealStockVo::getMaterielTypeId,
                            Collectors.toList()));
            Date now = new Date();

            for (WorderTemplateAddedMaterialTypeEntity worderTemplateAddedMaterialType : worderTemplateAddedMaterialTypeList) {

                if (!dotAddedMaterialTypeRealStockVoMap.containsKey(worderTemplateAddedMaterialType.getMaterielTypeId())) {
                    String dotName = dotInfoMapper.getDotNameByDotId(dotId);
                    rs.put("code", HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    rs.put("msg", "网点下增值物料[" + worderTemplateAddedMaterialType.getMaterielTypeName() + "]库存不足，请核查！");
                    rs.put("dotId", dotId);
                    rs.put("dotName", dotName);
                    rs.put("materielTypeName", worderTemplateAddedMaterialType.getMaterielTypeName());
                    rs.put("total", 0);
                    return rs;
                }

                DotAddedMaterialTypeRealStockVo dotAddedMaterialTypeRealStockVo =
                        dotAddedMaterialTypeRealStockVoMap.get(worderTemplateAddedMaterialType.getMaterielTypeId()).get(0);

                // 判断网点库存是否小于阈值
                if (dotAddedMaterialTypeRealStockVo.getGoodsTotal() < threshold) {
                    rs.put("dotId", dotId);
                    rs.put("dotName", dotAddedMaterialTypeRealStockVo.getDotName());
                    rs.put("materielTypeName", worderTemplateAddedMaterialType.getMaterielTypeName());
                    rs.put("total", dotAddedMaterialTypeRealStockVo.getGoodsTotal());
                }

                if (dotAddedMaterialTypeRealStockVo.getGoodsTotal() < worderTemplateAddedMaterialType.getUsedNum()) {
                    rs.put("code", HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    rs.put("msg", "网点下增值物料[" + worderTemplateAddedMaterialType.getMaterielTypeName() + "]库存不足，请核查！");
                    return rs;
                }

                WorderInformationAttributeEntity worderInformationAttribute = new WorderInformationAttributeEntity();

                worderInformationAttribute.setWorderId(worderId);
                worderInformationAttribute.setAttribute("addedMaterialType");
                worderInformationAttribute.setAttributeCode(worderTemplateAddedMaterialType.getMaterielTypeId() + "");
                worderInformationAttribute.setAttributeName(worderTemplateAddedMaterialType.getMaterielTypeName());
                worderInformationAttribute.setAttributeValue(worderTemplateAddedMaterialType.getUsedNum() + "");
                worderInformationAttribute.setIsDelete(IntegerEnum.ZERO.getValue());
                worderInformationAttribute.setCreateTime(now);

                worderInformationAttributeList.add(worderInformationAttribute);
            }
        }
        return rs.put("data", worderInformationAttributeList);
    }

    /**
     * 服务经理派单给网点更新工单状态
     * 添加派代消息记录表
     *
     * @param worderRecord
     * @return
     */
    @Override
    public Results insertDotOrderInfo(String worderRecord) {
        OperationRecord work = JSONObject.parseObject(worderRecord, OperationRecord.class);
        if (null == work.getType()) {
            work.setType(3);
        }
        if (StringUtils.isBlank(work.getTitle())) {
            work.setTitle("");
        }

        if (StringUtils.isEmpty(work)) {
            saveWorderRemark(work.getWorderNo(), work.getTitle() + work.getWorderNo() + "网点手动派单失败", "失败原因：参数不能为空", work.getUserId());
            return Results.message(100, "参数不能为空", null);
        }

        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_no", work.getWorderNo());

        if (work.getWorderStatus() != null && work.getWorderExecStatus() != null) {
            wrapper.eq("worder_exec_status", work.getWorderExecStatus());
            wrapper.eq("worder_status", work.getWorderStatus());
        }
        WorderInformationEntity worder = worderInformationService.getOne(wrapper);
        if (worder == null) {
            return Results.message(10, "当前工单已经不在此状态，请刷新页面", null);
        }

        Integer audoSend = sendOrdersMapper.selectAutoSendByWorderNo(worder.getWorderId());
        if (audoSend == 0) {
            return Results.message(10, "派单原因不能为空", null);
        }

        return ((SendOrderService) AopContext.currentProxy()).insertDotOrderInfo2(work);
    }

    @Override
    @Transactional
    public Results insertDotOrderInfo2(OperationRecord work) {
        if (null == work.getType()) {
            work.setType(3);
        }
        if (StringUtils.isBlank(work.getTitle())) {
            work.setTitle("");
        }

        if (StringUtils.isEmpty(work)) {
            saveWorderRemark(work.getWorderNo(), work.getTitle() + work.getWorderNo() + "网点手动派单失败", "失败原因：参数不能为空", work.getUserId());
            return Results.message(100, "参数不能为空", null);
        }

        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_no", work.getWorderNo());
        WorderInformationEntity informationEntity = worderInformationService.getOne(wrapper);

        Integer oldDotId = informationEntity.getDotId();
        Integer affectedUserId = work.getAffectedUserId().intValue();

        Integer templateId = informationEntity.getTemplateId();

        //校验网点品牌是否符合
        //获取工单的品牌
        Integer brandId = worderInformationService.selectBrandByTemp(informationEntity.getWorderId());
        //查询网点和品牌关联表
        List<Integer> brandIds = worderInformationService.selectBrandsByDot(affectedUserId);
        if (brandIds != null && !brandIds.isEmpty() && !brandIds.contains(brandId)) {
            return Results.message(10, "当前网点品牌不符合！", null);
        }

        if (oldDotId != null && affectedUserId.equals(oldDotId)) {
            return Results.message(10, "改派网点与当前网点一致，请核查！", null);
        } else {

            // 增值物料预占 -- start
            List<WorderInformationAttributeEntity> worderInformationAttributeList = null;

            Integer addedMaterialTypePreempCount = dotInfoMapper.queryAddedMaterialTypePreempCountByWorderId(informationEntity.getWorderId());

            if (addedMaterialTypePreempCount == 0) {
                R checkR = checkAddedMaterialStock(templateId, affectedUserId, informationEntity.getWorderId());

                // 有预警库存进行短信预警
                if (checkR.containsKey("total")) {
                    // 异步发送预警短信
                    AsyncManager.instance().execute(WorkManagerAsyncFactory.addMaterialStockThreshold(checkR));
                }

                if ((Integer) checkR.get("code") != 0) {
                    saveWorderRemark(work.getWorderNo(), work.getWorderNo() + "网点手动派单失败", "失败原因：添加失败或者没有相关工单编号信息" + checkR.get("msg"),
                            work.getUserId());
                    return Results.message(10, (String) checkR.get("msg"), null);
                }
                worderInformationAttributeList = (List<WorderInformationAttributeEntity>) checkR.get("data");
            }

            // 增值物料预占 -- end
            int index = workMsgDao.insertOperation(work);
            int nums = 0;
            if (flowCommon.hasFlowByWorderNo(work.getWorderNo())) {
                //调用网点已接单流程
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(work.getWorderNo(), FlowConstant.ProcessCode.AssignDot,
                        FlowConstant.ProcessStatus.Y, affectedUserId);
                // 流程调用失败直接返回
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                }
                nums = sendOrdersMapper.updateOrder(work.getWorderNo(), work.getAffectedUser(), work.getAffectedUserId());
            } else {
                nums = sendOrdersMapper.updataOrderInfo(work.getWorderNo(), work.getAffectedUser(), "网点已接单", work.getAffectedUserId(), 1);
            }
            //改派网点时，如果已派单服务兵并且是当天派服务兵的 服务兵的派单数量要减1
            operateAttendantCount(informationEntity);
            //添加工单备注表信息
            if (oldDotId == null) {
                saveWorderRemark(work.getWorderNo(), work.getTitle() + work.getWorderNo() + "主状态：分配中，子状态：网点手动派单成功", work.getWorderNo() +
                        "成功手动派单给网点：" + work.getAffectedUser(), work.getUserId());
            } else {
                saveWorderRemark(work.getWorderNo(), work.getTitle() + work.getWorderNo() + "主状态：分配中，子状态：网点手动改派成功", work.getWorderNo() +
                        "成功手动改派给网点：" + work.getAffectedUser(), work.getUserId());
            }
            //原派单数量-1 现派单数量+1
            updateCount(oldDotId, affectedUserId, work.getWorderId());
            if (index > 0 && nums > 0) {

                // 保存增值物料
                if (worderInformationAttributeList != null && worderInformationAttributeList.size() > 0) {
                    if (oldDotId != null) {
                        // 释放原来库存
                        worderInformationAttributeDao.update(null, new UpdateWrapper<WorderInformationAttributeEntity>()
                                .eq("worder_id", informationEntity.getWorderId())
                                .eq("attribute", "addedMaterialType")
                                .set("is_delete", 1));
                    }
                    worderInformationAttributeDao.insertBatch(worderInformationAttributeList);
                }

                LocalDateTime dispatchTime = LocalDateTime.now();
                //保存网点派单记录
                SendWorderRecord sendWorderRecord = new SendWorderRecord()
                        .setWorderId(informationEntity.getWorderId().longValue())
                        .setSendWorderType(2L)
                        .setSendWorderUser(work.getUserId())
                        .setAcceptWorderUser(work.getAffectedUserId())
                        .setOperationType(informationEntity.getDotId() != null ? 3L : 2L)
                        .setOperationUser(work.getUserId())
                        .setCreateTime(dispatchTime)
                        .setRemark(work.getRecord());
                sendWorderRecordService.save(sendWorderRecord);

                // 比亚迪推送工单派单给 825 上海飞隼科技有限公司 转单给小咖
                if (825 == affectedUserId
                        && providerBusinessService.checkBydOrderByWorderId(informationEntity.getWorderId())
                        && informationEntity.getWorderTypeId() == 5) {
                    String data = worderIntfMessageService.getDataByWorderId(informationEntity.getWorderId());
                    if (StringUtils.isNotBlank(data)) {
                        log.info("比亚迪推送工单派单给飞隼转单小咖平台");
                        PushOrderBody pushOrderBody = JSON.parseObject(data, PushOrderBody.class);
                        PushApiResponse pushApiResponse = xkApiService.pushOrder(pushOrderBody);
                        if (ConstantPool.SUCCESS.equals(pushApiResponse.getMessage())) {
                            saveWorderAttribute(informationEntity.getWorderId(), "Transfer-Order", "转单标识", "1", "TransferOrder");
                            worderIntfMessageService.transferWorder(informationEntity.getWorderId(), 1);
                        } else {
                            log.info("比亚迪推送工单派单给飞隼转单小咖平台失败:" + pushApiResponse.getMessage());
                        }
                    }
                }

                /*providerBusinessService.callBusiness(
                        () -> {
                            DotInformationEntity dotInfo = dotInformationService.getInfoById(affectedUserId);
                            PushBranchRecord pushBranchRecord = new PushBranchRecord();
                            pushBranchRecord.setOrderCode(informationEntity.getCompanyOrderNumber());
                            pushBranchRecord.setIssuedBranchTime(DateUtils.parseLocalDateTimeSting(dispatchTime));
                            pushBranchRecord.setBranchCode(dotInfo.getVCode());
                            pushBranchRecord.setBranchName(dotInfo.getDotShortName());
                            return BusinessProcessPo.builder().pushBranchRecord(pushBranchRecord).build();
                        },
                        informationEntity.getWorderId(),
                        "pushBranchRecord"
                );*/

                return Results.message(0, "success", null)
                        .putWorderNo(work.getWorderNo())
                        .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_BRANCH)
                        .putWorderExecStatus(FieldConstant.WAIT_MANAGER_SEND_ORDER);
            } else {
                saveWorderRemark(work.getWorderNo(), work.getTitle() + work.getWorderNo() + "主状态：分配中，子状态：网点手动派单失败", "失败原因：添加失败或者没有相关工单编号信息",
                        work.getUserId());
                return Results.message(10, "添加失败或者没有相关工单编号信息", null);
            }
        }
    }

    private void saveWorderAttribute(Integer worderId,
                                     String attributeCode,
                                     String attributeName,
                                     String attributeValue,
                                     String attribute) {
        WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
        entity.setWorderId(worderId);
        entity.setAttributeCode(attributeCode);
        entity.setAttributeName(attributeName);
        entity.setAttributeValue(attributeValue);
        entity.setAttribute(attribute);
        worderInformationAttributeDao.insert(entity);
    }

    /**
     * 校验网点上限
     *
     * @param dotId
     * @return
     */
    @Override
    public Results checkoutDotSends(String dotId) {
        //获取配置的服务兵上限的数量
        String ceiling = sysDictionaryService.selectDictionaryByNumber("attendant_sendCeiling");
        //获取网点下服务兵数量
        Integer count = bizAttendantService.getBaseMapper().selectCount(new QueryWrapper<BizAttendantEntity>().eq("dot_id", dotId).eq(
                "attendant_state", 1));
        //获取网点上线
        Integer dotCeiling = Integer.parseInt(ceiling) * count;
        QueryWrapper<DotSendsRecord> wrappers = new QueryWrapper<>();
        wrappers.eq("dot_id", dotId)
                .eq("present_date", LocalDate.now());
        List<DotSendsRecord> dotSendsRecords = dotSendsRecordMapper.selectList(wrappers);
        if (dotSendsRecords.size() > 0 && dotSendsRecords.get(0).getSendNum() != null && dotSendsRecords.get(0).getSendNum() >= dotCeiling) {
            return Results.message(10, "该网点当日最大服务能力为" + dotCeiling + "单,目前已派" + dotCeiling + "单,是否继续派工", null);
        }
        return Results.message(0, "");
    }

    /**
     * 改派网点时，如果已派单服务兵并且是当天派服务兵的 服务兵的派单数量要减1
     *
     * @param informationEntity
     */
    private void operateAttendantCount(WorderInformationEntity informationEntity) {

        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        //获取服务兵ID
        Integer serviceId = informationEntity.getServiceId();
        Optional.ofNullable(serviceId).ifPresent(id -> {
            //查询当前服务兵是否当天派单的
            List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq(
                    "worder_id", informationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 3).eq("accept_worder_user",
                    serviceId).ge("create_time", LocalDate.now()));
            if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                //查询当前服务兵派单数据
                AttendantSendsRecord attendantSendsRecord =
                        attendantSendsRecordService.getBaseMapper().selectOne(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()));
                //更新服务兵派单数量-1
                if (attendantSendsRecord.getSendNum() > 0) {
                    attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                }
                if (attendantSendsRecord.getOverflow() > 0) {
                    attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                }
                attendantSendsRecord.setUpdateTime(LocalDateTime.now());
                attendantSendsRecord.setUpdateUser(user != null ? user.getUserId() : null);
                attendantSendsRecordService.getBaseMapper().updateById(attendantSendsRecord);
            }
        });
    }

    public void saveWorderRemark(String worderNo, String title, String content, Long userId) {
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity();
        worderRemarkLogEntity.setWorderNo(worderNo);
        worderRemarkLogEntity.setTitle(title);
        worderRemarkLogEntity.setContent(content);
        worderRemarkLogEntity.setCreateTime(new Date());
        worderRemarkLogEntity.setUserId(userId);
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }

    private void updateCount(Integer dotId, Integer affectedUserId, Long worderId) {
        //判断是否打标
        QueryWrapper<WorderInformationSub> queryWrapper = new QueryWrapper();
        queryWrapper.eq("worder_id", worderId);
        List<WorderInformationSub> worderInformationSubs = worderInformationSubService.list(queryWrapper);
        //改派清除标识
        if (dotId != null) {
            worderInformationSubService.updateState(worderId);
        }
        //如果有打标 改派时候只需要现网点+1
        if (worderInformationSubs.size() > 0 && worderInformationSubs.get(0).getDotCountState() != null && worderInformationSubs.get(0).getDotCountState() == 0) {
            //改派时 现有网点+1
            if (dotId != null) {
                updateDotCount(affectedUserId, 0);
            }
        } else {
            //改派
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords =
                        sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id",
                                worderId.intValue()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge(
                                        "create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id",
                            dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }
            //派单 现网点数量+1
            updateDotCount(affectedUserId, 1);
        }
    }

    public void updateDotCount(Integer affectedUserId, Integer presentDate) {
        QueryWrapper<DotInformationEntity> present = new QueryWrapper<>();
        present.eq("dot_id", affectedUserId);
        DotInformationEntity dotInformationEntity1 = dotInformationService.getOne(present);
        //现网点派单数量+1
        dotInformationEntity1.setCount(dotInformationEntity1.getCount() + 1);
        dotInformationEntity1.setDotId(affectedUserId);
        dotInformationService.updateById(dotInformationEntity1);
        //如果没有打标
        if (presentDate == 1) {
            //查询当日网点派单数量表是否有派单
            List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id",
                    affectedUserId).eq("present_date", LocalDate.now()));
            //当日网点派单数量表有派单 +1
            if (CollectionUtil.isNotEmpty(dotSendsRecordList)) {
                updateDotSend(dotSendsRecordList.get(0).getSendNum() + 1, dotSendsRecordList.get(0).getId());
            } else {
                DotSendsRecord dotSendsRecord = new DotSendsRecord()
                        .setSendNum(1)
                        .setDotId(affectedUserId)
                        .setPresentDate(LocalDate.now());
                dotSendsRecordMapper.insert(dotSendsRecord);
            }//当日网点没有派单 添加一条记录
        }

    }

    //修改网点派单数量表
    public void updateDotSend(Integer sendNum, Integer id) {
        DotSendsRecord dotSendsRecord = new DotSendsRecord()
                .setSendNum(sendNum)
                .setId(id);
        dotSendsRecordMapper.updateById(dotSendsRecord);
    }

    /*
     * 派单给服务兵
     * 查询相关服务兵信息
     * */
    @Override
    public Results getServiceInfo(String worderNo, String username, Integer type) {

        try {
            if (!StringUtils.isEmpty(worderNo)) {
                if (type == 0) {
                    int nums = sendOrdersMapper.selectMangerId(worderNo);
                    if (nums == 2) {
                        return Results.message(1, "本用户已经派单给服务兵了", null);
                    }
                }
                Integer dotId = sendOrdersMapper.selectWorderServiceId(worderNo);
                String technicianLevel = sendOrdersMapper.selectBrandTechnicianLevelByWorderNo(worderNo);
                List<String> technicianLevelList;
                if (technicianLevel == null || technicianLevel.indexOf("0") >= 0) {
                    List<TechnicianLevelDto> technicianLevelDtoList = bizAttendantService.getTechnicianLevelList("pc");
                    technicianLevelList = technicianLevelDtoList.stream().map(TechnicianLevelDto::getId).collect(Collectors.toList());
                } else {
                    technicianLevelList = Arrays.asList(technicianLevel.split(","));
                }
                List<Map<String, String>> servicelist = sendOrdersMapper.selectServiceArmlist(dotId, technicianLevelList);
                List<Map<String, String>> serviceMap = new ArrayList<>();
                for (Map<String, String> map : servicelist) {
                    map.put("star", null);
                    serviceMap.add(map);
                }
                if (servicelist.size() != 0) {
                    return Results.message(0, "success", serviceMap);
                } else {
                    return Results.message(10, "查无相关信息", null);
                }
            } else {
                return Results.message(100, "参数不能未空", null);
            }
        } catch (Exception e) {
            log.error("服务兵信息查询接口异常", e);
            return Results.message(110, "服务兵信息查询接口异常", null);
        }

    }

    /**
     * 网点派单给服务兵，更新工单状态和状态值
     * 添加操作记录
     *
     * @param worderRecord
     * @return
     */
    @Override
    @Transactional
    public Results upDateOrderinsertOperate(String worderRecord) throws ParseException {
        if (!StringUtils.isEmpty(worderRecord)) {
            OperationRecord work = JSONObject.parseObject(worderRecord, OperationRecord.class);
            //获取工单的服务兵ID
            Long byServiceId = attendantSendsRecordMapper.findByServiceId(work.getWorderId());
            //校验服务兵是否已经派过单了
            if (work.getTypeSendOrder() == 2 && byServiceId != null) {
                log.error("=============当前服务兵：{}=已经派过单了============", byServiceId);
                return Results.message(100, "该工单服务兵已经派完单，请勿重复派单！", null);
            }
            //改派情况下不允许改派当前服务兵
            if (work.getTypeSendOrder() == 3 && byServiceId != null && byServiceId.equals(work.getAffectedUserId())) {
                log.error("=============当前服务兵：{}=改派相同服务兵============", byServiceId);
                return Results.message(100, "请不要改派，该工单相同的服务兵！", null);
            }

            //需要在进行校验服务兵阀值
            List<Map<String, Object>> thresholdSwitchList = attendantSendsRecordMapper.findByOverflow(AttSendConstant.THRESHOLD_SWITCH);
            Map<String, Object> thresholdSwitchMap = thresholdSwitchList.get(0);
            boolean thresholdSwitch =
                    thresholdSwitchMap.get("detail_number") != null && "1".equals(thresholdSwitchMap.get("detail_number").toString());
            //查服务兵派单阀门
            List<Map<String, Object>> byOverflow = attendantSendsRecordMapper.findByOverflow(AttSendConstant.OVER_FLOW);
            //通过服务斌ID和当前日期查attendant_sends_record溢出量是否溢出
            List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordMapper.listAttendantNum(work.getAffectedUserId(), LocalDate.now());
            if (!attendantSendsRecords.isEmpty()) {
                if (attendantSendsRecords.get(0).getSendNum() >= Integer.parseInt(byOverflow.get(0).get("detail_number").toString()) && thresholdSwitch) {
                    log.error("=============服务兵：{}=派单超过阀值============", work.getAffectedUserId());
                    return Results.message(100, "服务兵派单达到阀值！", null);
                }
            }

            int index = workMsgDao.insertOperation(work);
            int nums = 0;
            if (flowCommon.hasFlowByWorderNo(work.getWorderNo())) {
                // 调用待勘测预约
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(work.getWorderNo(), FlowConstant.ProcessCode.AssignSoldier,
                        FlowConstant.ProcessStatus.Y, work.getAffectedUserId());
                // 流程调用失败直接返回
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                }
                nums = sendOrdersMapper.updateServiceInfo(work.getWorderNo(), work.getAffectedUser(), work.getAffectedUserId());
            } else {
                nums = sendOrdersMapper.updateOrderServiceInfo(work.getWorderNo(), work.getAffectedUser(), "服务兵待勘测预约", work.getAffectedUserId(), 2,
                        1, "勘测中");
            }
            if (index > 0 && nums > 0) {
                WorderInfoEntity byWorderId = worderInformationService.getByWorderId(work.getWorderId().intValue());
                //派单记录
                attendantSendsRecordService.SendOrderRecord(work, byServiceId);
                R r = worderTypeService.checkWorderTypeByWorderNo(work.getWorderNo(), WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER);
                Integer worderExecStatus = (Integer) r.get(WarningConstant.WORDER_EXEC_STATUS);
                worderExecStatus = worderExecStatus != null ? worderExecStatus : FieldConstant.CONVEY_NOT_APPOINT;
                BizAttendantEntity bizAttendantEntity = bizAttendantService.getById(work.getAffectedUserId());
                //调用长安接口
                try {
                    CaApiResponse response = caApiService.pushServiceTime(byWorderId.getWorderNo(), byWorderId.getCompanyOrderNumber());
                    if (!response.getSuccess()) {
                        throw new RRException(response.getMessage());
                    }
                    CaApiResponse response2 = caApiService.pushAmmeterStatus(byWorderId.getWorderNo(), byWorderId.getCompanyOrderNumber(), 1);
                    if (!response2.getSuccess()) {
                        throw new RRException(response2.getMessage());
                    }
                    CaApiResponse response1 = caApiService.pushTakeOrders(byWorderId.getWorderNo(), byWorderId.getCompanyOrderNumber(),
                            bizAttendantEntity.getName(), bizAttendantEntity.getContact());
                    if (!response1.getSuccess()) {
                        throw new RRException(response1.getMessage());
                    }

                } catch (Exception e) {
                    throw new RRException(e.getMessage());
                }
                // 调用霍尼韦尔接口
                long dispatchTime = System.currentTimeMillis();
                honeywellBizService.updateDispatch(
                        () -> {
                            UpdateDispatchRequest updateDispatchRequest = new UpdateDispatchRequest();
                            updateDispatchRequest.setOrderCode(byWorderId.getCompanyOrderNumber());
                            updateDispatchRequest.setUpdateTime(dispatchTime);
                            updateDispatchRequest.setJobCode(byWorderId.getWorderNo());
                            updateDispatchRequest.setDispatchTime(dispatchTime);
                            updateDispatchRequest.setServiceStation(byWorderId.getCandidateBranch());
                            updateDispatchRequest.setDispatchStatus(String.valueOf(byWorderId.getWorderStatus()));
                            updateDispatchRequest.setDispatchStatusName(WorderStatusEnum.getNameFromCode(byWorderId.getWorderStatus()));
                            updateDispatchRequest.setInstaller(bizAttendantEntity.getName());
                            updateDispatchRequest.setContactMobile(bizAttendantEntity.getContact());
                            updateDispatchRequest.setRemark("");
                            return updateDispatchRequest;
                        },
                        byWorderId.getCompanyOrderNumber()
                );

                Result result = providerBusinessService.callBusiness(
                        () -> {
                            DotInformationEntity dotInfo = dotInformationService.getInfoById(byWorderId.getDotId());
                            PushBranchRecord pushBranchRecord = new PushBranchRecord();
                            pushBranchRecord.setOrderCode(byWorderId.getCompanyOrderNumber());
                            WorderOperationRecord record = worderOperationRecordService.lambdaQuery()
                                    .select(WorderOperationRecord::getCreateTime)
                                    .eq(WorderOperationRecord::getWorderNo, work.getWorderNo())
                                    .eq(WorderOperationRecord::getType, 3)
                                    .eq(WorderOperationRecord::getAffectedUserId, dotInfo.getDotId())
                                    .orderByDesc(WorderOperationRecord::getId)
                                    .last("limit 1")
                                    .one();
                            //②	增加必传字段手机号、电工证编号、电工证照片、电工证有效期开始时间、电工证有效期结束时间
                            pushBranchRecord.setIssuedBranchTime(DateUtils.format(record.getCreateTime()));
                            pushBranchRecord.setBranchCode(dotInfo.getVCode());
                            pushBranchRecord.setBranchName(dotInfo.getDotName());
                            pushBranchRecord.setPersonnelNumber(bizAttendantEntity.getContact());
                            pushBranchRecord.setPersonnelName(work.getAffectedUser());
                            pushBranchRecord.setPersonnelPhoneNumber(bizAttendantEntity.getContact());
                            //电工证编号
                            pushBranchRecord.setElectricianCertificateNumber(bizAttendantEntity.getElectricianCertificateNumber());
                            //电工证照片
                            String electricianCertificate = bizAttendantEntity.getElectricianCertificate();
                            if(StringUtils.isNotBlank(electricianCertificate)){
                                String[] fileIds = electricianCertificate.split(",");
                                SysFileEntity sysFile = sysFilesService.getSysFileById(fileIds[0]);
                                if (sysFile!=null) {
                                    try {
                                        String imageUrl = FileUtils.copyImage(sysFile.getNewName());
                                        pushBranchRecord.setElectricianCertificateImage(imageUrl);
                                    } catch (Exception e) {
                                        throw new RRException("电工证图片转换异常");
                                    }
                                }
                            }
                            //电工证有效期开始时间、电工证有效期结束时间
                            pushBranchRecord.setExpirationDateBegin(bizAttendantEntity.getElectricianEffectiveStart());
                            pushBranchRecord.setExpirationDateEnd(bizAttendantEntity.getElectricianEffectiveEnd());

                            ValidatorUtils.validateEntity(pushBranchRecord);

                            return BusinessProcessPo.builder().worderTypeId(byWorderId.getWorderTypeId()).pushBranchRecord(pushBranchRecord).build();
                        },
                        byWorderId.getWorderId(),
                        "pushBranchRecord"
                );

                if (result.getCode()!=0) {
                    throw new RRException("调用比亚迪上传网点信息接口报错"+result.getMsg());
                }

                return Results.message(0, "success", null)
                        .putWorderNo(work.getWorderNo())
                        .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER)
                        .putWorderExecStatus(worderExecStatus);
            } else {
                return Results.message(10, "更新添加失败", null);
            }
        } else {
            return Results.message(100, "参数不能为空", null);
        }

    }

    @Override
    @Warning("派单给服务兵")
    @Transactional(rollbackFor = {Exception.class})
    public Results apportionToAttendant(OperationRecord worderRecord) {
        if (!ObjectUtils.isEmpty(worderRecord)) {
            int index = workMsgDao.insertOperation(worderRecord);
            int nums = sendOrdersMapper.updateOrderServiceInfo(worderRecord.getWorderNo(), worderRecord.getAffectedUser(), "服务兵待勘测预约",
                    worderRecord.getAffectedUserId(), 2, 1, "勘测中");
            if (index > 0 && nums > 0) {
                R r = worderTypeService.checkWorderTypeByWorderNo(worderRecord.getWorderNo(), WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER);
                Integer worderExecStatus = (Integer) r.get(WarningConstant.WORDER_EXEC_STATUS);
                worderExecStatus = worderExecStatus != null ? worderExecStatus : FieldConstant.CONVEY_NOT_APPOINT;
                return Results.message(0, "success", null)
                        .putWorderNo(worderRecord.getWorderNo())
                        .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_SOLDIER)
                        .putWorderExecStatus(worderExecStatus);
            } else {
                return Results.message(10, "更新添加失败", null);
            }
        } else {
            return Results.message(100, "参数不能为空", null);
        }
    }

    @Override
    @Warning("派单给网点")
    @Transactional(rollbackFor = {Exception.class})
    public Results apportionToDot(OperationRecord worderRecord) {
        if (!ObjectUtils.isEmpty(worderRecord)) {
            int index = workMsgDao.insertOperation(worderRecord);
            int nums = sendOrdersMapper.updataOrderInfo(worderRecord.getWorderNo(), worderRecord.getAffectedUser(), "网点已接单",
                    worderRecord.getAffectedUserId(), 1);
            if (index > 0 && nums > 0) {
                return Results.message(0, "success", null)
                        .putWorderNo(worderRecord.getWorderNo())
                        .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_BRANCH)
                        .putWorderExecStatus(FieldConstant.WAIT_MANAGER_SEND_ORDER);
            } else {
                return Results.message(10, "添加失败或者没有相关工单编号信息", null);
            }
        } else {
            return Results.message(100, "参数不能为空", null);
        }
    }

}
