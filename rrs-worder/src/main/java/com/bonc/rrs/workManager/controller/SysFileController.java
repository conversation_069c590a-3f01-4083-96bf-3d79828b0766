package com.bonc.rrs.workManager.controller;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.wsdlproperties.OssProperties;
import com.qiniu.util.IOUtils;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StreamUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.*;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by zhangyibo on 2020-03-31 14:40
 */

@RestController
@RequestMapping("/sys/file")
@Slf4j
public class SysFileController {

    @Autowired
    SysFilesService sysFilesService;

    private final String JFIF = "jfif";

    @RequestMapping("/upload")
    @ResponseBody
    @CrossOrigin
    public R imageUpload(@RequestParam("fileName") MultipartFile file) throws IOException {
        long size = file.getSize();
        if (!StringUtils.isEmpty(file) && size > 0) {
            String oldName = file.getOriginalFilename();
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            if (suffix.equals(JFIF)){
                InputStream inputStream = file.getInputStream();
                File newFile = new File(file.getOriginalFilename().replace(JFIF,"jpg"));
                convertJfifToJpg(inputStream,newFile);
                inputStream.close();
                MultipartFile newMultipartFile = convert(newFile);
                Map<String, String> urlMap = FileUtils.getUrl(newMultipartFile);
                String path = urlMap.get("url");
                String name = urlMap.get("name");
                String md5Hex =urlMap.get("md5Str");
                Integer fileId = sysFilesService.saveFilePath(path,name,oldName,md5Hex);
                return R.ok().put("fileId", fileId).put("url", path).put("fileName",oldName).put("newName", name);
            }
            Map<String, String> urlMap = FileUtils.getUrl(file);
            String path = urlMap.get("url");
            String name = urlMap.get("name");
            String md5Hex =urlMap.get("md5Str");
            Integer fileId = sysFilesService.saveFilePath(path,name,oldName,md5Hex);
            return R.ok().put("fileId", fileId).put("url", path).put("fileName",oldName).put("newName", name);
        }else {
            return R.error("文件不能为空");
        }
    }

    public static MultipartFile convert(File file) throws IOException {
        FileInputStream input = new FileInputStream(file);

        // 创建MockMultipartFile对象，用于模拟MultipartFile
        MultipartFile multipartFile = new MockMultipartFile(
                file.getName(), // 文件原始名
                file.getName(), // 保存的文件名，可以自定义
                Files.probeContentType(file.toPath()), // 文件类型，根据文件内容自动判断
                input // 文件输入流
        );
        input.close();
        return multipartFile;
    }

    public void convertJfifToJpg(InputStream inputFile,File outPutFile)throws IOException{
        BufferedImage image = ImageIO.read(inputFile);
        // 将图像写为JPG格式
        ImageIO.write(image, "jpg", outPutFile);
    }

    /**
     * 获取oss缩略图
     * @param name
     */
    @RequestMapping("/getOssThumbnailOrBank")
    public void getOssThumbnailOrBank(String name, String bucketName, HttpServletResponse response){
        OSSClient ossClient = FileUtils.create();
        String style = "image/resize,m_fixed,w_100,h_100";
        GetObjectRequest request;
        InputStream in = null;
        OutputStream output = null;
        byte[] bytes;

        boolean found = false;
        try {
            found = ossClient.doesObjectExist(bucketName, name);
        } catch (Exception ignored) {}
        if(found){
            response.setHeader("Content-disposition", "attachment; filename="+name);
            response.setContentType(FileUtils.getContentType(name.split("\\.")[IntegerEnum.ONE.getValue()]));
            try {
                request = new GetObjectRequest(bucketName, name);
                request.setProcess(style);
                OSSObject ossObject = ossClient.getObject(request);
                in = ossObject.getObjectContent();
                bytes = StreamUtils.readInputStream(in);
                output = response.getOutputStream();
                output.write(bytes);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                ossClient.shutdown();
                try {
                    in.close();
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }else{
            try {
                // 根据文件名查询表
                String imageUrl = sysFilesService.getPathByName(name);
                if(StringUtils.isBlank(imageUrl)){
                    return;
                }
                // 创建一个 URL 对象
                URL url = new URL(imageUrl);
                // 打开连接
                URLConnection urlConnection = url.openConnection();
                // 获取输入流
                in = urlConnection.getInputStream();
                bytes = StreamUtils.readInputStream(in);
                output = response.getOutputStream();
                output.write(bytes);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    in.close();
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取oss缩略图
     * 暂时未使用
     * @param name
     */
    @RequestMapping("/getOssThumbnail")
    public void getOssThumbnail(String name, HttpServletResponse response){
        response.setHeader("Content-disposition", "attachment; filename="+name);
        response.setContentType(FileUtils.getContentType(name.split("\\.")[IntegerEnum.ONE.getValue()]));
        OSSClient ossClient = FileUtils.create();
        String style = "image/resize,m_fixed,w_100,h_100";
        GetObjectRequest request = null;
        InputStream in = null;
        OutputStream output = null;
        HttpURLConnection conn = null;
        byte[] bytes;
        try {
            boolean found = ossClient.doesObjectExist(OssProperties.bucketName, name);
            if(found){
                request = new GetObjectRequest(OssProperties.bucketName, name);
                request.setProcess(style);
                OSSObject ossObject = ossClient.getObject(request);
                in = ossObject.getObjectContent();
            }else{
//                in = new FileInputStream("http://ah-rrs.oss-cn-shanghai.aliyuncs.com/20200714074514384.jpg?Expires=4748327114&OSSAccessKeyId=LTAI4FpavfCDPFoBXsW564ps&Signature=QmW78mcsYcv5hsv4hUndwrUvZYE%3D");
                String doGetUrl = "http://ah-rrs.oss-cn-shanghai.aliyuncs.com/20200714074514384" +
                        ".jpg?Expires=4748327114&OSSAccessKeyId=LTAI4FpavfCDPFoBXsW564ps&Signature=QmW78mcsYcv5hsv4hUndwrUvZYE%3D";
                URL url = new URL(doGetUrl);
                conn = (HttpURLConnection) url.openConnection();
                conn.setRequestProperty("accept", "*/*");
                conn.setRequestProperty("connection", "Keep-Alive");
                conn.setRequestProperty("Charset", "UTF-8");
                conn.setUseCaches(false);
                conn.setRequestMethod("GET");
//            conn.setDoOutput(true);
//            conn.setDoInput(true);
                conn.setConnectTimeout(30000);
                conn.setReadTimeout(60000);
                in = conn.getInputStream();
            }
            bytes = StreamUtils.readInputStream(in);
            output = response.getOutputStream();
            output.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
            if(null != conn){
                conn.disconnect();
            }
            try {
                in.close();
                output.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

//    public static void main(String[] args) {
//        String[] split = "20201022111012411.jpg".split("\\.");
//        System.out.println(split.length);
//        String contentType = FileUtils.getContentType(split[1]);
//        System.out.println(contentType);
//    }
}
