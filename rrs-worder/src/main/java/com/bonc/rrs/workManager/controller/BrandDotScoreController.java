package com.bonc.rrs.workManager.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.bonc.rrs.workManager.entity.vo.DotBrandScoreExport;
import com.bonc.rrs.workManager.entity.vo.DotScoreQueryVo;
import com.bonc.rrs.workManager.entity.vo.DotScoreTableVo;
import com.bonc.rrs.workManager.service.BrandDotScoreService;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Description: 网点业绩评分Controller
 * @Author: liujunpeng
 * @Date: 2023/6/6 16:51
 * @Version: 1.0
 */
@RestController
@RequestMapping(value = "/dot/score")
@AllArgsConstructor
public class BrandDotScoreController {

    final BrandDotScoreService brandDotScoreService;

    @PostMapping("/list")
    @ApiOperation(value = "查询网点业绩评分接口", notes = "查询网点业绩评分接口")
    private R list(@Validated @RequestBody  DotScoreQueryVo param){
        return brandDotScoreService.getDotScoreList(param);
    }


    @PostMapping("/tables")
    @ApiOperation(value = "查询网点业绩评分列表接口", notes = "查询网点业绩评分列表接口")
    private R tables(@Validated @RequestBody DotScoreTableVo param){
        return brandDotScoreService.getDotTables(param);
    }

    @ApiOperation("业绩评分导出")
    @RequestMapping(value = "export", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    void export(@RequestBody DotScoreTableVo param, HttpServletResponse response) {
        brandDotScoreService.export(param, response);
    }


    @GetMapping("/getBrandList")
    @ApiOperation(value = "根据角色获取品牌信息", notes = "根据角色获取品牌信息")
    private R getBrandList(){
        return brandDotScoreService.getBrandList();
    }

    @GetMapping("/getRegionList")
    @ApiOperation(value = "根据角色获取大区信息", notes = "根据角色获取大区信息")
    private R getRegionList(@RequestParam("brandId") Integer brandId){
        return brandDotScoreService.getRegionList(brandId);
    }

    @GetMapping("/getCityList")
    @ApiOperation(value = "根据角色获取地市信息", notes = "根据角色获取地市信息")
    private R getCityList(@RequestParam Integer province){
        return brandDotScoreService.getCityList(province);
    }

    @PostMapping("/downloadDotScore")
    @ApiOperation(value = "导出网点评分列表", notes = "导出网点评分列表")
    private void downloadDotScoreList(HttpServletResponse response, @RequestBody DotScoreQueryVo param){
        ExcelWriter excelWriter = null;
        try {
            String filename = new String("网点评分列表".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xls");
            excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
            //查询导出数据
            List<DotBrandScoreExport> dotBrandScoreExports = brandDotScoreService.selectExtportList(param);
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "网点业绩评分信息").head(DotBrandScoreExport.class).build();
            excelWriter.write(dotBrandScoreExports, writeSheet);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }
}
