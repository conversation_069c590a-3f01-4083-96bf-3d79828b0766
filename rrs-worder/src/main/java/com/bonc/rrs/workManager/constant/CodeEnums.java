package com.bonc.rrs.workManager.constant;

public enum CodeEnums {
    OVER_FLOW("LUO001","服务人员当日最大服务能力为2单，目前已派2单，是否继续派工"),
    ALTER_SEND_ORDER("003","3"),
    SEND_ORDER("002","2"),
    SAVA_UPDATE("10","添加或更新失败");

    private String value;
    private String desc;

    private CodeEnums(String value, String desc) {
        this.setValue(value);
        this.setDesc(desc);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "[" + this.value + "]" + this.desc;
    }
}
