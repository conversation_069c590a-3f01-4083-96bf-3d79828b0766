package com.bonc.rrs.workManager.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.bonc.rrs.workManager.entity.vo.DotApproveAuditVo;
import com.bonc.rrs.workManager.entity.vo.DotApproveInfoVo;
import com.bonc.rrs.workManager.entity.vo.DotApproveListVo;
import com.bonc.rrs.workManager.entity.vo.DotStatusUpdateVo;
import com.bonc.rrs.workManager.service.DotApproveService;
import com.bonc.rrs.wsdlproperties.OssProperties;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;

/**
 * @Description: 网点审批流程
 * @Author: liujunpeng
 * @Date: 2023/7/25 13:55
 * @Version: 1.0
 */
@Controller
@RequestMapping("/dot/approve/")
@AllArgsConstructor
@Api(tags = "网点审批相关接口")
public class DotApproveController {

    final DotApproveService dotApproveService;


    private static final String ENDPOINT = "oss-cn-shanghai.aliyuncs.com";

    private static final String ACCESS_KEY_ID = "LTAI4FpavfCDPFoBXsW564ps";

    private static final String ACCESS_KEY_SECRET = "******************************";

    @ApiOperation("网点信息变更申请接口")
    @RequestMapping(value = "updateInfoApplication", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R updateInfoApplication(@Validated @RequestBody DotApproveInfoVo dotApproveInfoVo) throws InvocationTargetException, IllegalAccessException {
        return dotApproveService.updateInfoApplication(dotApproveInfoVo);
    }


    @ApiOperation("网点状态变更申请接口")
    @RequestMapping(value = "statusApplication", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R statusApplication(@Validated @RequestBody DotStatusUpdateVo dotStatusUpdateVo) {
        return dotApproveService.statusApplication(dotStatusUpdateVo);
    }

    @ApiOperation("网点审批记录列表查询接口")
    @RequestMapping(value = "listRecord", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R listRecord(@RequestBody DotApproveListVo dotApproveListVo) {
        return dotApproveService.listRecord(dotApproveListVo);
    }

    @ApiOperation("网点审批列表查询接口")
    @RequestMapping(value = "list", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R list(@RequestBody DotApproveListVo dotApproveListVo) {
        return dotApproveService.getList(dotApproveListVo);
    }

    @ApiOperation("网点审批单详情信息获取")
    @RequestMapping(value = "getApproveDetail", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R getApproveDetail(@RequestParam Integer approveId) {
        return dotApproveService.getApproveDetail(approveId);
    }

    @ApiOperation("网点审批不通过接口")
    @RequestMapping(value = "notPassed", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R notPassed(@Validated @RequestBody DotApproveAuditVo dotApproveAuditVo) {
        return dotApproveService.notPassed(dotApproveAuditVo);
    }

    @ApiOperation("网点审批通过接口")
    @RequestMapping(value = "passed", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R passed(@Validated @RequestBody DotApproveAuditVo dotApproveAuditVo) {
        return dotApproveService.passed(dotApproveAuditVo);
    }

    @ApiOperation("网点在途审批单查询")
    @RequestMapping(value = "queryApproveByDotId", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R updateInfoApplication(@RequestParam Integer dotId) {
        return dotApproveService.queryApproveByDotId(dotId);
    }

    /**
     * 下载网点审批附件模版
     * @param response
     * @param param
     */
    @RequestMapping("attachmentModel")
    @ResponseBody
    public void attachmentModel123(HttpServletResponse response,@RequestParam Map<String,Object> param) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");
            //读取OSS的模版
            OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(OssProperties.bucketName, "model/dotApproveModel.xlsx"));
            InputStream modelInputStream = ossObject.getObjectContent();
            // 这里 会填充到第一个sheet， 然后文件流会自动关闭
            excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(modelInputStream).build();
            ossClient.shutdown();
        } catch (Exception e) {
            throw new RRException("");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }
}
