package com.bonc.rrs.workManager.controller;

import com.aliyun.oss.OSSClient;
import com.bonc.rrs.workManager.service.WorkMsgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.bonc.rrs.util.Results;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/*
* @author: MinJunping
* @date: 2020/03/19
* */
@RequestMapping(path = "/files")
@RestController
public class FileController {

    @Autowired(required = false)
    private WorkMsgService workMsgService;

    /*
    * 合同文件上传
    * */
    @RequestMapping(value = "/upload")
    public Results emailUpload(@RequestParam(value = "fileName") MultipartFile file,
                               @RequestParam(value = "id") Integer id,
                               @RequestParam(value = "endTime") String endTime) {

        return workMsgService.saveFilePath(id,endTime,file);

    }

}


