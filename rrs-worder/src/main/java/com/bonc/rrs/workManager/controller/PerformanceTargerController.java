package com.bonc.rrs.workManager.controller;

import com.bonc.rrs.workManager.entity.PerformanceTargerEntity;
import com.bonc.rrs.workManager.entity.vo.PerformanceTargerVo;
import com.bonc.rrs.workManager.service.PerformanceTargerService;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 业绩指标目标值记录表;(performance_targer)表控制层
 * <AUTHOR> liujunpeng
 * @date : 2023-8-23
 */
@Api(tags = "业绩指标目标值记录表对象功能接口")
@AllArgsConstructor
@RestController
@RequestMapping("/performance/targer")
public class PerformanceTargerController{

    final PerformanceTargerService performanceTargerService;

    @ApiOperation("业绩指标目标值记录列表查询接口")
    @RequestMapping(value = "list", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R list(@RequestBody PerformanceTargerVo performanceTargerVo) {
        return performanceTargerService.selectListPage(performanceTargerVo);
    }

    @ApiOperation("查询当前账户权限下的省份")
    @RequestMapping(value = "queryProvinceList", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R queryProviceList() {
        return performanceTargerService.queryProviceList();
    }

    @ApiOperation("查询业绩指标")
    @RequestMapping(value = "queryIndicatorList", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R queryIndicatorList() {
        return performanceTargerService.queryIndicatorList();
    }

    @ApiOperation("查询详情")
    @RequestMapping(value = "queryDetailById", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R queryDetailById(@RequestParam Integer id) {
        return performanceTargerService.queryDetailById(id);
    }

    @ApiOperation("删除目标值")
    @RequestMapping(value = "deleteById", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R deleteById(@RequestParam Integer id) {
        return performanceTargerService.deleteById(id);
    }

    @ApiOperation("保存或者修改目标值")
    @RequestMapping(value = "saveOrUpdate", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R saveOrUpdate(@RequestBody PerformanceTargerEntity performanceTargerEntity) {
        return performanceTargerService.saveOrUpdateInfo(performanceTargerEntity);
    }

    /**
     * 目标值模版下载
     * @param response
     */
    @ApiOperation("目标值模版下载")
    @RequestMapping("/downloadModel")
    @ResponseBody
    public void downloadModel(HttpServletResponse response) {
        performanceTargerService.downloadModel(response);
    }

    /**
     * 目标值导入
     * @param file
     * @return
     * @throws IOException
     */
    @ApiOperation("目标值导入")
    @RequestMapping("/uploadModel")
    @ResponseBody
    public R uploadScoreModel(@RequestParam("fileName") MultipartFile file) {
        return performanceTargerService.uploadScoreModel(file);
    }
}