package com.bonc.rrs.workManager.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.baidumap.dao.DotSendsRecordMapper;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.service.GacePushService;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.common.IdempotentCheck;
import com.bonc.rrs.worder.constant.IdempotentConstant;
import com.bonc.rrs.worder.constant.RedisConstant;
import com.bonc.rrs.worder.dao.WorderExtFieldDao;
import com.bonc.rrs.worder.dao.WorderTemplateDao;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.IdempotentPo;
import com.bonc.rrs.worder.service.DotInformationService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderSendAuditService;
import com.bonc.rrs.worder.service.WorderTypeService;
import com.bonc.rrs.workManager.dao.RemarkLogMapper;
import com.bonc.rrs.workManager.dao.SendOrdersMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.AttendantSendsRecord;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.entity.vo.ApportionBatchVo;
import com.bonc.rrs.workManager.service.AttendantSendsRecordService;
import com.bonc.rrs.workManager.service.SendOrderService;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import com.bonc.rrs.workManager.service.WorkMsgService;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.impl.util.CollectionUtil;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/*
 * @author: minJunping
 * @date: 2020/03/12
 * */
@Slf4j
@Controller
@RequestMapping(value = "/apportion/manage")
public class ApportionController {

    @Autowired(required = false)
    private WorkMsgService workMsgService;

    @Autowired(required = false)
    private SendOrderService sendOrderService;

    @Autowired(required = false)
    private SendOrdersMapper sendOrdersMapper;

    @Autowired(required = false)
    private RemarkLogMapper remarkLogMapper;

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    @Autowired(required = false)
    private WorderTypeService worderTypeService;

    @Autowired(required = false)
    private WorderInformationService worderInformationService;

    @Autowired(required = false)
    private SendWorderRecordService sendWorderRecordService;

    @Autowired(required = false)
    private AttendantSendsRecordService attendantSendsRecordService;
    @Autowired
    private DotInformationService dotInformationService;

    @Resource
    private WorderExtFieldDao worderExtFieldDao;
    @Resource
    private WorderTemplateDao worderTemplateDao;
    @Resource
    private DotSendsRecordMapper dotSendsRecordMapper;
    @Autowired
    private WorderSendAuditService worderSendAuditService;
    @Autowired
    private IdempotentCheck idempotentCheck;

    @Autowired
    private GacePushService gacePushService;

    /*
     * 派单权限控制
     * 已经派单控制
     * 可选服务经理回显
     * 常用服务经理回显
     * */
    @RequestMapping(value = "getmanagerlist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getUserName(@RequestParam(value = "username") String username,
                               @RequestParam(value = "worderno") String worderno) {

        username = username.replace(" ", "");
        worderno = worderno.replace(" ", "");
        return workMsgService.selectMangerService(username, worderno);

    }

    /*
     * 派单权限控制
     * 已经派单控制
     * 可选服务经理回显
     * 常用服务经理回显
     * */
    @RequestMapping(value = "getupdatemanagerlist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getupdateUserName(@RequestParam(value = "username") String username,
                                     @RequestParam(value = "worderno") String worderno) {

        username = username.replace(" ", "");
        worderno = worderno.replace(" ", "");
        return workMsgService.selectUpdateMangerService(username, worderno);

    }


    /*
     * 派单权限控制
     * 已经派单控制
     * 可选服务经理回显
     * 常用服务经理回显
     * */
    @RequestMapping(value = "getupdateUserNameService", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getupdateUserNameService(@RequestParam(value = "username") String username,
                                            @RequestParam(value = "worderno") String worderno) {

        username = username.replace(" ", "");
        worderno = worderno.replace(" ", "");
        return workMsgService.getupdateUserNameService(username, worderno);


    }

    /*
     * 添加派单操作记录
     * 更新工单状态，工单项目组信息
     * 添加服务经理至常用经理表中
     * */
    @Warning(value = "派单给服务经理")
    @RequestMapping(value = "sendmanageroperation", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint(name = "派单给服务经理")
    public Results sendOperationController(@RequestParam(value = "operationRecord") String operationRecord) {

        return workMsgService.insertOperation(operationRecord);

    }

    /*
     * 添加派单操作记录
     * 更新工单状态，工单项目组信息
     * 添加服务经理至常用经理表中
     * */
    @Warning(value = "派单给服务经理")
    @RequestMapping(value = "updatemanageroperation", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint(name = "派单给服务经理")
    public Results updateOperationController(@RequestParam(value = "operationRecord") String operationRecord) {

        return workMsgService.updateOperation(operationRecord);

    }


    /*
     * 服务经理派单给网点
     * 查询相关的辐射网点
     * */
    @RequestMapping(value = "getdotinfolist", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getDotInfoController(@RequestParam(value = "worderNo") String worderNo,
                                        @RequestParam(value = "username") String username,
                                        @RequestParam(value = "type") Integer type) {
        worderNo.replace(" ", "");
        username.replace(" ", "");
        return sendOrderService.getDotInfolist(worderNo, username, type);
    }

    /*
     *服务经理派单给网点
     *工单信息表更新状态
     *添加操作记录
     * */
    @Warning(value = "派单给网点")
    @RequestMapping(value = "senddotinfo", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint(name = "派单给网点")
    public Results insertDotinfoController(@RequestParam(value = "worderRecord") String worderRecord) {
        return sendOrderService.insertDotOrderInfo(worderRecord);
    }
    /**
     * 批量改派网点审核
     *
     * @param companyOrderNumber
     * @param affectedUser
     * @return
     */
    @RequestMapping(value = "sendDotPointAuditCompanyOrderNumber", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    public Results sendDotPointAuditCompanyOrderNumber(@RequestParam(value = "companyOrderNumber") String companyOrderNumber, @RequestParam(value = "affectedUser") String affectedUser) {
        QueryWrapper<WorderInformationEntity> qw = new QueryWrapper<>();
        qw.eq("company_order_number", companyOrderNumber);
        WorderInformationEntity worderInfoDTO = worderInformationService.getBaseMapper().selectOne(qw);
        if (worderInfoDTO == null) {
            return Results.message(500, "工单号不存在");
        }
        QueryWrapper<DotInformationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dot_name", affectedUser);
        DotInformationEntity dotInformationEntity = dotInformationService.getBaseMapper().selectOne(queryWrapper);
        if (dotInformationEntity == null) {
            return Results.message(500, "网点不存在");
        }
        SysUserEntity currentUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        OperationRecord work = new OperationRecord();
        work.setWorderStatus(worderInfoDTO.getWorderStatus().toString());
        work.setWorderExecStatus(worderInfoDTO.getWorderExecStatus().toString());
        work.setUserId(currentUser.getUserId());
        work.setOperationUser(currentUser.getUsername());
        work.setAffectedUserId(dotInformationEntity.getDotId().longValue());
        work.setAffectedUser(affectedUser);
        work.setType(3);
        work.setWorderNo(worderInfoDTO.getWorderNo());
        work.setAcceptSend(worderInfoDTO.getWorderId());
        work.setCauseEnum(-1);
        work.setModifyCause("其他-批量改派");
        work.setSendType(2);
        return sendDotPointOperationRecord(work);
    }
    /**
     * 批量改派网点审核
     *
     * @param worderNo
     * @param affectedUser
     * @return
     */
    @RequestMapping(value = "sendDotPointAuditWorderNo", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    public Results sendDotPointAuditWorderNo(@RequestParam(value = "worderNo") String worderNo, @RequestParam(value = "affectedUser") String affectedUser) {
        QueryWrapper<WorderInformationEntity> qw = new QueryWrapper<>();
        qw.eq("worder_no", worderNo);
        WorderInformationEntity worderInfoDTO = worderInformationService.getBaseMapper().selectOne(qw);
        if (worderInfoDTO == null) {
            return Results.message(500, "工单号不存在");
        }
        QueryWrapper<DotInformationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dot_name", affectedUser);
        DotInformationEntity dotInformationEntity = dotInformationService.getBaseMapper().selectOne(queryWrapper);
        if (dotInformationEntity == null) {
            return Results.message(500, "网点不存在");
        }
        SysUserEntity currentUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        OperationRecord work = new OperationRecord();
        work.setWorderStatus(worderInfoDTO.getWorderStatus().toString());
        work.setWorderExecStatus(worderInfoDTO.getWorderExecStatus().toString());
        work.setUserId(currentUser.getUserId());
        work.setOperationUser(currentUser.getUsername());
        work.setAffectedUserId(dotInformationEntity.getDotId().longValue());
        work.setAffectedUser(affectedUser);
        work.setType(3);
        work.setWorderNo(worderNo);
        work.setAcceptSend(worderInfoDTO.getWorderId());
        work.setCauseEnum(-1);
        work.setModifyCause("其他-批量改派");
        work.setSendType(2);
        return sendDotPointOperationRecord(work);
    }

    /*
     * 派单给网点提交审核
     * */
    @RequestMapping(value = "sendDotPointAudit", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    public Results sendDotPointAudit(@RequestParam(value = "worderRecord") String worderRecord) {

        OperationRecord work = JSONObject.parseObject(worderRecord, OperationRecord.class);

        return sendDotPointOperationRecord(work);

    }

    private Results sendDotPointOperationRecord(OperationRecord work) {

        String worderRecord = JSON.toJSONString(work);
        if (worderInformationService.checkTransferOrder(work.getWorderNo())) {
            return Results.message(10, "工单已转小咖平台，请先从小咖平台回传工单给News", null);
        }

        // 重复校验获取执行状态
        IdempotentPo idempotentPo = idempotentCheck.functionBegin(RedisConstant.Prefix.BUSINESS
                , "sendDotPointAudit"
                , worderRecord);

        // 首次执行完成将返回首次结果
        if (IdempotentConstant.Result.FINISH.equals(idempotentPo.getCode())) {
            return Results.message(10, "禁止重复提交", null);
            // 首次调用未执行完成放回重复调用
        } else if (!IdempotentConstant.Result.SUCCESS.equals(idempotentPo.getCode())) {
            return Results.message(10, "禁止重复提交", null);
        }

        Results r = worderSendAuditService.sendDotPointAudit(worderRecord);

        // 执行结束更新执行状态
        idempotentCheck.functionFinish(RedisConstant.Prefix.BUSINESS
                , "success"
                , "sendDotPointAudit"
                , worderRecord);
        return r;
    }

    /*
     * 派单给网点提交审核
     * */
    @RequestMapping(value = "queryAutoSendReason", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    public JSONObject queryAutoSendReason() {

        List<SysDictionaryDetailEntity> dictionaryDetailEntityList = worderSendAuditService.queryAutoSendReason();
        Map<String, String> dictionaryDetailEntityMap = dictionaryDetailEntityList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName));

        return (JSONObject) JSONObject.toJSON(R.ok().put("data", dictionaryDetailEntityList).put("dataMap", dictionaryDetailEntityMap));
    }

    /*
     *服务经理派单给网点
     *工单信息表更新状态
     *添加操作记录
     * */
    @Warning(value = "网点上限校验")
    @RequestMapping(value = "checkoutDotSends", method = {RequestMethod.GET, RequestMethod.POST}, name = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint(name = "网点上限校验")
    public Results checkoutDotSends(String affectedUserId) {
        return sendOrderService.checkoutDotSends(affectedUserId);
    }

    /**
     * 批量派单给网点
     *
     * @param apportionBatchVo
     * @return
     */
    @RequestMapping("apportionToDotBatch")
    @ResponseBody
    public Results apportionToDotBatch(@RequestBody ApportionBatchVo apportionBatchVo) {
        Results results = Results.message(0, "批量派单给网点成功", null);
        try {
            List<OperationRecord> worderRecords = apportionBatchVo.getWorderRecords();
            for (OperationRecord worderRecord : worderRecords) {
                sendOrderService.apportionToDot(worderRecord);
            }
        } catch (Exception e) {
            results = Results.message(500, "批量派单给网点失败", e);
            log.info("批量派单给网点失败: {}", e.getMessage());
        }
        return results;
    }

    /*
     * 派单给服务兵
     * 查询相关服务兵信息
     * */
    @RequestMapping(value = "getserviceinfo", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getServiceInfoController(@RequestParam(value = "worderNo") String worderNo,
                                            @RequestParam(value = "username") String username,
                                            @RequestParam(value = "type") Integer type) {

        worderNo.replace(" ", "");
        username.replace(" ", "");
        return sendOrderService.getServiceInfo(worderNo, username, type);

    }

    /*
     * 派单给服务兵
     * 添加操作记录
     * 更新工单状态
     * */
    @Warning("派单给服务兵")
    @RequestMapping(value = "updateserviceinfo", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @LogPrint(name = "服务兵派单接口")
    public Results updateServieInfoController(@RequestParam(value = "worderRecord") String worderRecord) throws ParseException {
        try {
            return sendOrderService.upDateOrderinsertOperate(worderRecord);
        } catch (RRException e) {
            log.error("派单给服务兵失败: {}", e.getMessage());
            return Results.message(500,e.getMessage());
        }
    }

    @RequestMapping("apportionToAttendantBatch")
    @ResponseBody
    public Results apportionToAttendantBatch(@RequestBody ApportionBatchVo apportionBatchVo) {
        Results results = Results.message(0, "批量派单给服务兵成功", null);
        try {
            List<OperationRecord> worderRecords = apportionBatchVo.getWorderRecords();
            for (OperationRecord worderRecord : worderRecords) {
                sendOrderService.apportionToAttendant(worderRecord);
            }
        } catch (Exception e) {
            results = Results.message(500, "批量派单给服务兵失败", e);
            log.info("批量派单给服务兵失败: {}", e.getMessage());
        }
        return results;
    }

    /**
     * 查询工单相关操作记录信息
     *
     * @param worderNo
     * @return
     */
    @RequestMapping(value = "getoperationrecord", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getOperationRecord(@RequestParam(value = "worderNo") String worderNo) {
        List<Map<String, String>> mapList = sendOrdersMapper.selectOperatRecord(worderNo);
        if (mapList.size() != 0) {
            return Results.message(0, "success", mapList);
        } else {
            return Results.message(100, "无法查到相关工单操作记录信息", null);
        }
    }

    /**
     * 勘测结单
     *
     * @param worderNo
     * @param title
     * @param content
     * @param userId
     * @param id
     * @param status
     * @param statusValue
     * @return
     */
    @RequestMapping(value = "updatewordersurveystatus", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R updateWorderSurveyStatus(@RequestParam(value = "worderNo") String worderNo,
                                      @RequestParam(value = "title") String title,
                                      @RequestParam(value = "content") String content,
                                      @RequestParam(value = "userId") Integer userId,
                                      @RequestParam(value = "id") Integer id,
                                      @RequestParam(value = "status") Integer status,
                                      @RequestParam(value = "statusValue") String statusValue,
                                      @RequestParam(value = "execStatus") Integer execStatus,
                                      @RequestParam(value = "execValue") String execValue,
                                      @RequestParam(value = "worderStatus") Integer worderStatus) {

        try {
            //查询工单信息
            WorderInformationEntity informationEntity = worderInformationService.getById(id);
            if (informationEntity != null) {
                if (null != informationEntity.getUserActualCost()) {
                    return R.error("用户已收费无法执行当前操作");
                }
            }

            Integer templateId = informationEntity.getTemplateId();
            WorderTemplateDto templateInfoById = worderTemplateDao.findTemplateInfoById(templateId);
            int brandId = Integer.parseInt(templateInfoById.getBrandId());

            if (worderStatus != null && worderStatus == 6 && brandId == 18) {
                WorderExtFieldEntity fieldEntity = worderExtFieldDao.selectOne(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", worderNo).eq("field_id", 1035));
                if (null != fieldEntity && StringUtils.isNotBlank(fieldEntity.getFieldValue())) {
                    String fieldValue = fieldEntity.getFieldValue();
                    if (worderExtFieldDao.updateFieldValue(fieldValue, 1035) < 1) {
                        return R.error("取消工单失败");
                    }
                }
            }

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            String dateStr = df.format(new Date());// new Date()为获取当前系统时间
            String username = workMsgDao.selectUserNameById(userId);
            String str = dateStr + " " + username;
            int index = remarkLogMapper.addOperationLog(worderNo, str + title, content, userId);
            int num = remarkLogMapper.updateWordeInfoState(id, status, statusValue, execStatus, execValue, worderStatus);
            if (index < 1 || num < 1) {
                return R.error("操作日志添加失败或工单信心修改失败！");
            }
            //判断当前工单是否存在服务兵并且是当天派单的，需要减一
            Integer serviceId = informationEntity.getServiceId();
            if (serviceId != null) {
                //查询当前服务兵是否是当天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", informationEntity.getWorderId()).eq("send_worder_type", 3).eq("accept_worder_user", serviceId).eq("delete_state", 0).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    List<AttendantSendsRecord> attendantSendsRecords = attendantSendsRecordService.getBaseMapper().selectList(new QueryWrapper<AttendantSendsRecord>().eq("service_id", serviceId).eq("present_date", LocalDate.now()).eq("delete_state", 0));
                    if (attendantSendsRecords != null && !attendantSendsRecords.isEmpty()) {
                        AttendantSendsRecord attendantSendsRecord = attendantSendsRecords.get(0);
                        if (attendantSendsRecord.getOverflow() > 0) {
                            attendantSendsRecord.setOverflow(attendantSendsRecord.getOverflow() - 1);
                        }
                        if (attendantSendsRecord.getSendNum() > 0) {
                            attendantSendsRecord.setSendNum(attendantSendsRecord.getSendNum() - 1);
                        }
                        attendantSendsRecord.setUpdateTime(LocalDateTime.now())
                                .setUpdateUser(userId != null ? userId.longValue() : null);
                        attendantSendsRecordService.updateById(attendantSendsRecord);
                    }
                }
            }
            Integer dotId = informationEntity.getDotId();
            if (dotId != null) {
                //判断当前网点是否今天派单的
                List<SendWorderRecord> sendWorderRecords = sendWorderRecordService.getBaseMapper().selectList(new QueryWrapper<SendWorderRecord>().eq("worder_id", informationEntity.getWorderId()).eq("delete_state", 0).eq("send_worder_type", 2).eq("accept_worder_user", dotId).ge("create_time", LocalDate.now()));
                if (sendWorderRecords != null && !sendWorderRecords.isEmpty()) {
                    //原网点派单数量-1
                    QueryWrapper<DotInformationEntity> before = new QueryWrapper<>();
                    before.eq("dot_id", dotId);
                    DotInformationEntity dotInformationEntity = dotInformationService.getOne(before);
                    if (dotInformationEntity != null && dotInformationEntity.getCount() > 0) {
                        dotInformationEntity.setCount(dotInformationEntity.getCount() - 1);
                        dotInformationService.updateById(dotInformationEntity);
                    }
                    //当天日期的原网点Id-1
                    List<DotSendsRecord> dotSendsRecordList = dotSendsRecordMapper.selectList(new QueryWrapper<DotSendsRecord>().eq("dot_id", dotId).eq("present_date", LocalDate.now()));
                    if (CollectionUtil.isNotEmpty(dotSendsRecordList) && dotSendsRecordList.get(0).getSendNum() != null && dotSendsRecordList.get(0).getSendNum() > 0) {
                        updateDotSend(dotSendsRecordList.get(0).getSendNum() - 1, dotSendsRecordList.get(0).getId());
                    }
                }
            }

            //广汽取消工单
            if (worderStatus != null && worderStatus == 6) {
                gacePushService.pushOrder(informationEntity.getWorderNo(), WorkOrderStatusEnum.CANCEL, content);
            }

            //如果分配中工单取消更新工单状态后直接返回成功
            if (informationEntity.getWorderStatus() == 0 || informationEntity.getWorderStatus() == 1) {
                return R.ok("处理成功");
            }
            //勘测物料以及后续结算处理
            R materielBalanceAccounts = worderInformationService.materielBalanceAccounts(id);
            //勘测结单  ---- 广汽取消工单
            if (worderStatus != null && worderStatus == 5) {
                gacePushService.pushOrder(informationEntity.getWorderNo(), WorkOrderStatusEnum.CANCEL, content);
            }
            return materielBalanceAccounts;
        } catch (Exception e) {
            return R.error("更新工单勘测结算接口异常");
        }
    }


    //修改网点派单数量表
    public void updateDotSend(Integer sendNum, Integer id) {
        DotSendsRecord dotSendsRecord = new DotSendsRecord()
                .setSendNum(sendNum)
                .setId(id);
        dotSendsRecordMapper.updateById(dotSendsRecord);
    }

}
