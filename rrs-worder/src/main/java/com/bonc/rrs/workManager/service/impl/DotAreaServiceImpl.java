package com.bonc.rrs.workManager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.baidumap.vo.GridListDataVo;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.dao.BizRegionMapper;
import com.bonc.rrs.workManager.dao.DotAreaMapper;
import com.bonc.rrs.workManager.entity.AreaDot;
import com.bonc.rrs.workManager.entity.DotArea;
import com.bonc.rrs.workManager.service.DotAreaService;
import com.bonc.rrs.workManager.service.UserRoleAreaPermissionsService;
import com.google.common.base.Joiner;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.MapUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DotAreaServiceImpl extends ServiceImpl<DotAreaMapper, DotArea> implements DotAreaService {

    private static final Logger log = LoggerFactory.getLogger(DotAreaServiceImpl.class);
    private final BizRegionMapper bizRegionMapper;

    private final UserRoleAreaPermissionsService userRoleAreaPermissionsService;

    private final DotAreaMapper dotAreaMapper;

    @Override
    public int deleteByDotId(Integer dotId) {
        return baseMapper.deleteByMap(new MapUtils().put("dot_id", dotId));
    }

    @Override
    public List<AreaDot> areaInfoAddHander(List<AreaDot> arealist, Integer userId, List<Integer> roleIdList, Integer dotId, Long groupId) {
        List<AreaDot> areaDots = areaInfoHander(arealist);
        //获取角色对应的区域id
        List<String> userAreaIds = userRoleAreaPermissionsService.queryUserRoleCompetenceConfig("dotInformationRoleConfig", userId, roleIdList);
        if (userAreaIds != null && userAreaIds.size() > 0) {
            List<Integer> areaIds = areaDots.stream().map(AreaDot::getAreaId).collect(Collectors.toList());
            areaIds.removeIf(areaId -> !userAreaIds.contains(areaId.toString()));
            if (areaIds.isEmpty()) {
                return areaDots;
            }
            //查询现有的区域关系
            List<AreaDot> dotAreaList = getBindDotArea(dotId, groupId, userAreaIds);
            List<BizRegionEntity> bizRegionEntities = bizRegionMapper.selectBatchIds(areaIds);
            //清除多余的省份和地市
            userRoleAreaPermissionsService.removeBizRegion(bizRegionEntities, userAreaIds, 1);
            List<AreaDot> userAreaDotList =
                    bizRegionEntities.stream().map(bizRegion -> new AreaDot(bizRegion.getId().intValue(), dotId)).collect(Collectors.toList());
            if (dotAreaList != null && !dotAreaList.isEmpty()) {
                userAreaDotList.addAll(dotAreaList);
            }
            return userAreaDotList;
        } else {
            return areaDots;
        }
    }

    private List<AreaDot> getBindDotArea(Integer dotId, Long groupId, List<String> userAreaIds) {
        //根据网点和组ID查询现有绑定的区域组
        LambdaQueryWrapper<DotArea> dotAreaWrapper = Wrappers.lambdaQuery();
        dotAreaWrapper.eq(DotArea::getDotId, dotId).eq(DotArea::getGroupId, groupId).eq(DotArea::getIsDelete, 0);
        List<DotArea> dotAreas = dotAreaMapper.selectList(dotAreaWrapper);
        if (dotAreas != null && !dotAreas.isEmpty()) {
            dotAreas.removeIf(item -> userAreaIds.contains(item.getAreaId().toString()));
            return dotAreas.stream().map(itemDotArea -> new AreaDot(itemDotArea.getAreaId(), dotId)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<DotArea> selectByDotValue(Integer dotId, Integer brandId, List<Integer> newLoginUserAreaIds, Integer type) {
        //拼接查询条件
        StringBuilder areaIdsWrapper = new StringBuilder();
        if (!newLoginUserAreaIds.isEmpty()) {
            if (newLoginUserAreaIds.size() > Constant.NUM_1000) {
                int toIndex = 0;
                while (toIndex < newLoginUserAreaIds.size()) {
                    List<Integer> subAreaIds = newLoginUserAreaIds.subList(toIndex, (toIndex + 500 >= newLoginUserAreaIds.size()) ?
                            newLoginUserAreaIds.size() - 1 : toIndex + 500);
                    if (subAreaIds.size() > 0) {
                        areaIdsWrapper.append("da.area_id in (").append(Joiner.on(",").join(subAreaIds)).append(")");
                    }
                    toIndex += 500;
                    if (toIndex < newLoginUserAreaIds.size()) {
                        areaIdsWrapper.append(" or ");
                    }
                }
            } else {
                areaIdsWrapper.append("da.area_id in (").append(Joiner.on(",").join(newLoginUserAreaIds)).append(")");
            }
        }
        String areaIds = "";
        if (areaIdsWrapper.toString().length() > 0) {
            areaIds = " ( " + areaIdsWrapper + " ) ";
        }
        return dotAreaMapper.selectByDotValue(dotId, brandId, areaIds, type);
    }

    @Override
    public Set<Integer> selectByAreaId(Collection<Integer> areaIds, GridListDataVo param) {
        return dotAreaMapper.selectByAreaId(areaIds, param);
    }

    @Override
    public List<DotArea> selectListByDotBrand(Integer dotId, Integer brandId) {
        return dotAreaMapper.selectListByDotBrand(dotId, brandId);
    }

    @Override
    public List<DotArea> selectListByDotsBrand(List<Integer> dotIds, Integer brandId, Integer serviceType) {
        return dotAreaMapper.selectListByDotsBrand(dotIds, brandId, serviceType);
    }

    @Override
    public List<AreaDot> areaInfoHander(List<AreaDot> arealist) {
        if (arealist == null || arealist.size() < 1) {
            return new ArrayList<>();
        }
        //区域id，set集合
        Set<Integer> areaIdSet = new HashSet<>();
        //过滤出区域不等于空的区域id集合
        List<Integer> areaIds = arealist.stream().map(AreaDot::getAreaId).filter(Objects::nonNull).collect(Collectors.toList());
        //获取对应所有区域的set集合
        userRoleAreaPermissionsService.traverseArea(areaIds, areaIdSet);
        //添加缺少的省份地市信息
        addRegion(areaIdSet, 3);
        //把所有的区域id集合包装成对象返回
        return areaIdSet.stream().map(areaId -> new AreaDot(areaId, null)).collect(Collectors.toList());
    }

    /**
     * 重新过滤数据把没有添加的省市信息添加上去
     *
     * @param areaIdSet
     * @param type
     */
    public void addRegion(Set<Integer> areaIdSet, Integer type) {
        if (type <= 1) {
            return;
        }
        List<BizRegionEntity> list =
                InitRegionUtil.REGIONS.stream().filter(region -> (areaIdSet.contains(region.getId().intValue()) && Objects.equals(type,
                        region.getType()))).collect(Collectors.toList());
        //根据pid分组
        Map<Long, Long> collect = list.stream().collect(Collectors.groupingBy(BizRegionEntity::getPid, Collectors.counting()));
        collect.forEach((k, v) -> {
            if (k != null && InitRegionUtil.REGION_PID_MAP.get(k).size() == v && !areaIdSet.contains(k.intValue())) {
                areaIdSet.add(k.intValue());
            }
        });
        addRegion(areaIdSet, type - 1);
    }

    @Override
    public boolean updateDotAreaList(Integer dotId, List<AreaDot> arealist, Integer userId, List<Integer> roleIdList, Long groupId) {
        //查询当前用户所对应所有区县信息
        List<String> userAreaIds = userRoleAreaPermissionsService.queryUserRoleCompetenceConfig("dotInformationRoleConfig", userId, roleIdList);
        if (userAreaIds.size() > 0) {
            Set<Integer> resultAreaIds = new HashSet<>();
            //查询当前网点关联的区域id
            Set<Integer> dotAreaIds = new HashSet<>();
            List<DotArea> dotAreas = baseMapper.selectList(new QueryWrapper<DotArea>().eq("dot_id", dotId).eq("is_delete", "0"));
            userRoleAreaPermissionsService.traverseArea(dotAreas.stream().map(DotArea::getAreaId).collect(Collectors.toList()), dotAreaIds);

            //前端传入区域
            List<AreaDot> areaDots = areaInfoHander(arealist);

            //获取网点中关联区域存在，但是角色区域权限以外的区域id
            dotAreaIds.forEach(areaId -> {
                if (!userAreaIds.contains(areaId.toString())) {
                    resultAreaIds.add(areaId);
                }
            });
            //获取前端传入的区域中在，当前角色区域权限的区域ID
            areaDots.forEach(areaDot -> {
                if (userAreaIds.contains(areaDot.getAreaId().toString())) {
                    resultAreaIds.add(areaDot.getAreaId());
                }
            });

            List<BizRegionEntity> bizRegionEntities = bizRegionMapper.selectBatchIds(resultAreaIds);
            //清除多余的省份和地市
            userRoleAreaPermissionsService.removeBizRegion(bizRegionEntities, userAreaIds, 1);

            //清除绑定区域关系表
            baseMapper.deleteByMap(new MapUtils().put("dot_id", dotId).put("group_id", groupId));

            List<DotArea> dotAreaList =
                    bizRegionEntities.stream().map(bizRegion -> new DotArea(null, bizRegion.getId().intValue(), dotId)).collect(Collectors.toList());
            dotAreaList.forEach(item -> item.setGroupId(groupId));
            return saveBatch(dotAreaList);
        } else {

            //清除绑定区域关系表
            baseMapper.deleteByMap(new MapUtils().put("dot_id", dotId).put("group_id", groupId));
            // 保存新的辐射区
            List<DotArea> dotAreaList = arealist.stream().map(item -> {
                item.getAreaId();
                return new DotArea(null, item.getAreaId(), dotId);
            }).collect(Collectors.toList());
            dotAreaList.forEach(item -> item.setGroupId(groupId));
            return saveBatch(dotAreaList);
        }
    }

    @Override
    public List<Map<String, String>> selectDotAllAreas(Integer dotId, Integer userId, List<Integer> roleIdList) {

        Map<Long, List<BizRegionEntity>> regionPidMap = InitRegionUtil.REGION_PID_MAP;

        List<Map<String, String>> result = new ArrayList<>();
        List<String> userAreaIds = userRoleAreaPermissionsService.queryUserRoleCompetenceConfig("dotInformationRoleConfig", userId, roleIdList);
        if (userAreaIds == null || userAreaIds.size() < 1) {
            List<Map<String, Object>> list = bizRegionMapper.selctDotFuSheInfo(dotId);
            List<Map<String, String>> results = new ArrayList<>();
            list.forEach(bizRegion -> {
                Map<String, String> map = new HashMap<>();
                Long areaId = Long.parseLong(bizRegion.get("areaId").toString());
                Long pid = InitRegionUtil.REGION_ID_MAP.get(areaId).getPid();
                if (pid != null && pid != 0) {
                    map.put("name", InitRegionUtil.REGION_ID_MAP.get(pid).getName() + bizRegion.get("name"));
                }
                map.put("groupId", bizRegion.get("groupId").toString());
                map.put("areaId", bizRegion.get("areaId").toString());
                results.add(map);
            });
            return results;
        }
        //返回结果
        Set<BizRegionEntity> set = new HashSet<>();
        //根据网点查询网点下绑定的区域
        List<BizRegionEntity> bizRegions = bizRegionMapper.selectListByDot(dotId);

        //把区域转成Map<key = id,value = pid>
        Map<Long, Long> dotAreaMap = bizRegions.stream().collect(Collectors.toMap(BizRegionEntity::getId, BizRegionEntity::getPid,
                (val1, val2) -> val1));
        //遍历网点下关联的区域
        bizRegions.forEach(bizRegion -> {
            Long groupId = bizRegion.getGroupId();
            switch (bizRegion.getType()) {
                case 1:
                    set.add(bizRegion);
                    if (!dotAreaMap.containsValue(bizRegion.getId())) {
                        List<BizRegionEntity> cityRegins = regionPidMap.get(bizRegion.getId());
                        cityRegins.forEach(cityRegin -> cityRegin.setGroupId(groupId));
                        set.addAll(cityRegins);
                        cityRegins.forEach(cityRegion -> {
                            List<BizRegionEntity> bizRegionEntities = regionPidMap.get(cityRegion.getId());
                            bizRegionEntities.forEach(item -> item.setGroupId(groupId));
                            set.addAll(bizRegionEntities);
                        });
                    }
                    break;
                case 2:
                    set.add(bizRegion);
                    if (!dotAreaMap.containsValue(bizRegion.getId())) {
                        List<BizRegionEntity> bizRegionEntities = regionPidMap.get(bizRegion.getId());
                        bizRegionEntities.forEach(item -> item.setGroupId(groupId));
                        set.addAll(bizRegionEntities);
                    }
                    break;
                case 3:
                    set.add(bizRegion);
                    bizRegion.setName(InitRegionUtil.REGION_ID_MAP.get(bizRegion.getPid()).getName() + bizRegion.getName());
                    break;
                default:
                    break;
            }
        });
        set.forEach(bizRegion -> {
            Map<String, String> map = new HashMap<>(2);
            map.put("areaId", bizRegion.getId().toString());
            map.put("name", bizRegion.getName());
            map.put("groupId", String.valueOf(bizRegion.getGroupId()));
            result.add(map);
        });
        return result;
    }

    @Override
    public boolean checkUserRoleArea(Integer id, Integer userId, List<Integer> roleIdList) {
        //获取当前角色权限区域
        List<String> userAreaIds = userRoleAreaPermissionsService.queryUserRoleCompetenceConfig("dotInformationRoleConfig", userId, roleIdList);
        if (userAreaIds == null || userAreaIds.size() < 1) {
            return true;
        }
        //获取网点辐射区域
        List<DotArea> dotAreas = baseMapper.selectList(new QueryWrapper<DotArea>().eq("dot_id", id));
        for (DotArea dotArea : dotAreas) {
            if (!userAreaIds.contains(dotArea.getAreaId().toString())) {
                return false;
            }
        }
        return true;
    }

}
