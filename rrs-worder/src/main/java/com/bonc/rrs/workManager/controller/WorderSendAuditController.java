package com.bonc.rrs.workManager.controller;

import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.service.WorderSendAuditService;
import com.bonc.rrs.workManager.entity.dto.WorderSendAuditSubmitDto;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 18:23
 */
@Slf4j
@Controller
@RequestMapping(value = "/send/audit")
public class WorderSendAuditController {

    @Autowired
    private WorderSendAuditService worderSendAuditService;

    @GetMapping("/page")
    @ResponseBody
    public R page(@RequestParam Map<String, Object> params){
        PageUtils page = worderSendAuditService.queryAutoSendList(params);
        return R.ok().put("page", page);
    }

    @GetMapping("/querySendAuditDetail")
    @ResponseBody
    public R querySendAuditDetail(Integer id){
        return R.ok().put("data", worderSendAuditService.querySendAuditDetail(id));
    }

    @Warning(value="派单给网点")
    @PostMapping("/submitSendAudit")
    @ResponseBody
    @LogPrint(name = "派单给网点")
    public Results submitSendAudit(@RequestBody WorderSendAuditSubmitDto worderSendAuditSubmitDto){
        return worderSendAuditService.submitSendAudit(worderSendAuditSubmitDto);
    }

    @PostMapping("/submitSendAuditBatch")
    @ResponseBody
    public Results submitSendAuditBatch(@RequestBody WorderSendAuditSubmitDto worderSendAuditSubmitDto){

        log.info("网点派单批量审批 ========== begin");
        if (worderSendAuditSubmitDto.getIds() == null || worderSendAuditSubmitDto.getIds().isEmpty()) {
            return Results.message(10, "请选择审核对象", null);
        }

        List<Integer> ids = worderSendAuditSubmitDto.getIds();

        List<String> errors = new ArrayList<>();

        Results result = new Results();
        result.setCode(0);
        result.setMsg("操作成功");
        for (Integer id : ids) {
            log.info("网点派单批量审批 ========== id:" + id);
            WorderSendAuditSubmitDto worderSendAuditSubmit = new WorderSendAuditSubmitDto();

            worderSendAuditSubmit.setId(id);
            worderSendAuditSubmit.setStatus(worderSendAuditSubmitDto.getStatus());
            worderSendAuditSubmit.setFailRemark(worderSendAuditSubmitDto.getFailRemark());
            Results results = worderSendAuditService.submitSendAudit(worderSendAuditSubmit);

            if (results.getCode() != 0) {
                log.info("网点派单批量审批 ========== id:" + id + "，操作失败:" + results.getMsg());
                result.setCode(10);
                String no = "id:" + id;
                if (StringUtils.isNotBlank(results.getWorderNo())) {
                    no = "工单:" + results.getWorderNo();
                }
                errors.add(no + "，" + results.getMsg());
            }
        }

        if (errors.size() > 0) {
            result.setMsg("部分操作失败");
            result.setData(errors);
        }
        log.info("网点派单批量审批 ========== end");
        return result;
    }
}
