package com.bonc.rrs.workManager.controller;

import com.bonc.rrs.workManager.dao.BrandMapper;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/brand/car")
public class BrandCarController {

    @Autowired(required = false)
    private BrandMapper brandMapper;

    @RequestMapping(value = "info", method = {RequestMethod.POST,RequestMethod.GET}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public R getBrandCarInfo(@RequestParam(value = "brandName") String brandName){

        if (brandName.equals("") ){
            brandName = null;
        }

        List<Map<String,String>> brandCarlist = brandMapper.selectBrandCarNamelist(brandName);

        return R.ok().put("data",brandCarlist);

    }
}
