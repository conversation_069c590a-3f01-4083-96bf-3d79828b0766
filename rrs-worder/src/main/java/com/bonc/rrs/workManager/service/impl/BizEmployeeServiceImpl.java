package com.bonc.rrs.workManager.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.entity.BizEmployeeEntity;
import com.bonc.rrs.workManager.dao.BizEmployeeMapper;
import com.bonc.rrs.workManager.dao.EmployeeMapper;
import com.bonc.rrs.workManager.entity.BizEmployee;
import com.bonc.rrs.workManager.entity.EmployeeEntity;
import com.bonc.rrs.workManager.service.BizEmployeeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
* @author: minJunping
* @date: 2020/03/03
* */

@Service
public class BizEmployeeServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements BizEmployeeService {

    @Autowired(required = false)
    private BizEmployeeMapper bizEmployeeMapper;

    @Autowired(required = false)
    private EmployeeMapper employeeMapper;

    @Autowired
    private SysDepartService departService;
    @Autowired
    private SysUserDao sysUserDao;

    public String checkcountname(String countname)
    {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(countname);
        if (m.find()) {
            return "1";
        }
        return "2";
    }

    @Override
    @Transactional
    public Results insertSelectiveService(EmployeeEntity bizEmployee) {

        Integer count = bizEmployeeMapper.selectCount(new QueryWrapper<BizEmployee>()
                .eq("contact", bizEmployee.getContact()));
        if (count > 0) {
            return Results.message(111, "手机号已存在");
        }
        try {
            if (!StringUtils.isEmpty(bizEmployee)){
                EmployeeEntity employeeEntity = employeeMapper.selectOne(new QueryWrapper<EmployeeEntity>()
                        .eq("name", bizEmployee.getName()));
//                if (employeeEntity!=null){
//                    return Results.message(01,"用户名已存在！",null);
//                }
                //验证身份证号码
                if ((StringUtils.isBlank(bizEmployee.getIdCard()))){
                    return Results.message(01,"身份证信息不能为空！",null);

                }
                boolean validCard = IdcardUtil.isValidCard(bizEmployee.getIdCard());
                if (!validCard){
                    return Results.message(01,"身份证号码格式不正确!",null);
                }
                //验证手机号码格式
                if ((StringUtils.isBlank(bizEmployee.getContact()))){
                    return Results.message(01,"手机号码不能为空！",null);

                }
                boolean isMobile = Validator.isMobile(bizEmployee.getContact());
                if (!isMobile){
                    return Results.message(01,"手机号码格式不正确！",null);
                }
                bizEmployee.setCreateTime(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

                if ("0".equals(bizEmployee.getGender())){
                    bizEmployee.setGenderValue(Constant.GENDERVALUE.MALE);
                }
                if ("1".equals(bizEmployee.getGender())){
                    bizEmployee.setGenderValue(Constant.GENDERVALUE.FEMALE);
                }

                count = employeeMapper.insert(bizEmployee);
                if (count > 0){
                    return Results.message(00,"添加成功",null);
                }else {
                    return Results.message(01,"添加失败",null);
                }
            }else {
                return Results.message(01,"参数不能为空",null);
            }

        }catch (Exception e){
            e.printStackTrace();
            return Results.message(01,"接口异常",null);
        }
    }



    @Override
    public PageInfo<BizEmployee> selectEmployeeByKey(String nameId, Integer pageSize, Integer currentIndex) throws Exception {

        try {
            String code = "";
            if (nameId==(null)){
                nameId = "";
            }
            if (!nameId.equals("")) {
                code = this.checkcountname(nameId);

            }else {
                code = "3";
            }

            if (!StringUtils.isEmpty(pageSize) && !StringUtils.isEmpty(currentIndex)){
                PageHelper.startPage(currentIndex, pageSize);
                List<BizEmployee> PreRegistInfos = bizEmployeeMapper.selectEmployee(code,nameId, pageSize, currentIndex);
                //加入员工部门信息
                PreRegistInfos.stream().forEach(bizEmployee ->
                    bizEmployee.setDepartName( departService.getBaseMapper().selectById(bizEmployee.getDepartmentId()).getDepartname())
                );
                PageInfo<BizEmployee> changeLists = new PageInfo<>(PreRegistInfos);
                return changeLists;
            }else {
                throw new Exception("参数不能为空");
            }
        }catch (Exception e){
            throw new Exception("接口异常");
        }
    }


    @Override
    public Results updateByKeyService(BizEmployee bizEmployee) {

        Integer count = bizEmployeeMapper.selectCount(new QueryWrapper<BizEmployee>()
                .eq("contact", bizEmployee.getContact())
                .ne("id", bizEmployee.getId()));
        if (count > 0) {
            return Results.message(111, "手机号已存在");
        }
        try {
            if (!StringUtils.isEmpty(bizEmployee)){
                int index = bizEmployeeMapper.updateByPrimaryKeySelective(bizEmployee);
                if (index > 0){
                    //获取userId
                    Long userId = sysUserDao.fingByUserId(bizEmployee.getId());
                    //修改手机号
                        sysUserDao.updateIdMobile(bizEmployee.getContact(),userId);
                    return Results.message(0,"success",null);
                }else {
                    return Results.message(10,"更新失败",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }
    }

    @Override
    public Results deleteByKey(String id) {

        try {
            if (!StringUtils.isEmpty(id)){
//                int index = bizEmployeeMapper.deleteByPrimaryKey(id);
                BizEmployee bizEmployee =bizEmployeeMapper.selectByPrimaryKey(id);
                bizEmployee.setIsdelete("1");
                int index = bizEmployeeMapper.updateByPrimaryKey(bizEmployee);
                if (index > 0){
                    return Results.message(0,"删除成功",null);
                }else {
                    return Results.message(10,"删除失败",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }
    }

}

