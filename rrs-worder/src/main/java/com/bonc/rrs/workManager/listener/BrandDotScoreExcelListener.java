package com.bonc.rrs.workManager.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.workManager.entity.BrandDotScoreBean;
import com.bonc.rrs.workManager.entity.dto.BrandDotScoreExcelProperty;
import com.bonc.rrs.workManager.service.BrandDotScoreService;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

/**
 * @Description: 网点评分导入处理类
 * @Author: liujunpeng
 * @Date: 2023/6/5 15:18
 * @Version: 1.0
 */
@AllArgsConstructor
@Log4j2
public class BrandDotScoreExcelListener extends AnalysisEventListener<BrandDotScoreExcelProperty> {

    private BrandDotScoreService brandDotScoreService;

    private List<String> failureDatas;

    private Set<String> keys;

    @Override
    public void invoke(BrandDotScoreExcelProperty data, AnalysisContext context) {
        int dotId;
        try {
            dotId = Integer.parseInt(data.getDotId());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",网点不合法");
            return;
        }
        int brandId;
        try {
            brandId = Integer.parseInt(data.getBrandId());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",品牌不合法");
            return;
        }
        int city;
        try {
            city = Integer.parseInt(data.getCity());
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",地市不合法");
            return;
        }
        //设置周期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formattedDate = currentDate.format(formatter);
        try {
            String key = formattedDate + "-" + brandId + "-" + dotId + "-" + city;

            BrandDotScoreBean brandDotScoreBean = new BrandDotScoreBean(formattedDate, city, brandId, data.getBrandName(), dotId, data.getAddedPerformance(), data.getMissionCritical(), data.getDeduct());

            if(keys.contains(key)){
                //先查询是否有重复记录
                LambdaQueryWrapper<BrandDotScoreBean> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper
                        .eq(BrandDotScoreBean::getCycle, formattedDate)
                        .eq(BrandDotScoreBean::getCity,city)
                        .eq(BrandDotScoreBean::getDotId, dotId)
                        .eq(BrandDotScoreBean::getBrandId, brandId)
                        .eq(BrandDotScoreBean::getDeleteState, 0);
                BrandDotScoreBean bean = brandDotScoreService.getBaseMapper().selectOne(queryWrapper);
                if(bean != null){
                    bean.setAddedPerformance(brandDotScoreBean.getAddedPerformance());
                    bean.setMissionCritical(brandDotScoreBean.getMissionCritical());
                    bean.setDeduct(brandDotScoreBean.getDeduct());
                    brandDotScoreService.getBaseMapper().updateById(bean);
                    return;
                }
            }
            keys.add(key);
            brandDotScoreService.getBaseMapper().insert(brandDotScoreBean);
        } catch (Exception e) {
            failureDatas.add("序号：" + data.getSerialNumber() + ",入库失败：" + e.getMessage());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadWorkbookHolder readWorkbookHolder = context.readWorkbookHolder();
        InputStream inputStream = readWorkbookHolder.getInputStream();
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
