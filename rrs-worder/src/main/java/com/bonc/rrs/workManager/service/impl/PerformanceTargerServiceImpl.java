package com.bonc.rrs.workManager.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.worder.dao.BizRegionDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.dao.BrandDotScoreMapper;
import com.bonc.rrs.workManager.dao.BrandPerEvaluationMapper;
import com.bonc.rrs.workManager.dao.PerformanceTargerMapper;
import com.bonc.rrs.workManager.entity.BrandPerEvaluation;
import com.bonc.rrs.workManager.entity.PerformanceTargerEntity;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.entity.dto.PerformanceTargerExcelProperty;
import com.bonc.rrs.workManager.entity.vo.PerformanceTargerVo;
import com.bonc.rrs.workManager.listener.PerformanceTargerExcelListener;
import com.bonc.rrs.workManager.service.PerformanceTargerService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.wsdlproperties.OssProperties;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 业绩指标目标值记录表;(performance_targer)表服务实现类
 *
 * <AUTHOR> liujunpeng
 * @date : 2023-8-23
 */
@Service
@AllArgsConstructor
@Log4j2
public class PerformanceTargerServiceImpl extends ServiceImpl<PerformanceTargerMapper, PerformanceTargerEntity> implements PerformanceTargerService {

    final BrandPerEvaluationMapper brandPerEvaluationMapper;

    final BizRegionDao bizRegionDao;

    final SysDictionaryDetailDao sysDictionaryDetailDao;

    final BrandDotScoreMapper brandDotScoreMapper;

    final BrandDao brandDao;

    private SysFilesService sysFileService;

    private WorderInformationDao worderInformationDao;

    private static final String ENDPOINT = "oss-cn-shanghai.aliyuncs.com";

    private static final String ACCESS_KEY_ID = "LTAI4FpavfCDPFoBXsW564ps";

    private static final String ACCESS_KEY_SECRET = "******************************";

    @Override
    public R selectListPage(PerformanceTargerVo performanceTargerVo) {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        //查询当前用户是否配置区域
        Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());

        // 如果是网点管理员角色，查询关联的网点
        List<Long> roleIdList = user.getRoleIdList();
        Integer dotId = null;
        if(roleIdList.contains(4L)){
            // 根据网点管理员查询网点ID
            dotId = worderInformationDao.getDotId(user.getUserId());
            areaIds = null;

        }
        IPage<PerformanceTargerEntity> page = this.baseMapper.selectListPage(new Page<>(performanceTargerVo.getPage(), performanceTargerVo.getPageSize()), performanceTargerVo, (areaIds == null || areaIds.isEmpty()) ? null : user.getUserId(), dotId);
        return Objects.requireNonNull(R.ok().put("list", page.getRecords())).put("totalCount", page.getTotal());
    }

    @Override
    public R queryProviceList() {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        List<BizRegionEntity> bizRegionEntities;
        //查询当前用户是否配置区域
        Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
        if (areaIds.isEmpty()) {
            bizRegionEntities = InitRegionUtil.REGION_PID_MAP.get(0L);
        } else {
            //查询当前配置的区域下的区域
            bizRegionEntities = bizRegionDao.getProvinceByUser(user.getUserId());
        }
        return R.ok().put("provinceList", bizRegionEntities);
    }

    @Override
    public R queryIndicatorList() {
        LambdaQueryWrapper<BrandPerEvaluation> brandPerEvaluationWrapper = Wrappers.lambdaQuery();
        brandPerEvaluationWrapper.select(BrandPerEvaluation::getId, BrandPerEvaluation::getIndicatorKey, BrandPerEvaluation::getIndicatorDesc);
        brandPerEvaluationWrapper.eq(BrandPerEvaluation::getDeleteState, 0);
        List<BrandPerEvaluation> brandPerEvaluations = brandPerEvaluationMapper.selectList(brandPerEvaluationWrapper);
        return R.ok().put("indicatorList", brandPerEvaluations);
    }

    @Override
    public R queryDetailById(Integer id) {
        PerformanceTargerEntity performanceTargerEntity = this.baseMapper.selectById(id);
        return R.ok().put("performanceTargerInfo", performanceTargerEntity);
    }

    @Transactional
    @Override
    public R saveOrUpdateInfo(PerformanceTargerEntity performanceTargerEntity) {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }

        //根据周期，品牌，省份，业绩指标查询是否存在重复
        LambdaQueryWrapper<PerformanceTargerEntity> performanceTargerWrapper = Wrappers.lambdaQuery();
        performanceTargerWrapper.eq(PerformanceTargerEntity::getCycle, performanceTargerEntity.getCycle())
                .eq(PerformanceTargerEntity::getBrandId, performanceTargerEntity.getBrandId())
                .eq(PerformanceTargerEntity::getProvinceId, performanceTargerEntity.getProvinceId())
                .eq(PerformanceTargerEntity::getIndicatorId, performanceTargerEntity.getIndicatorId())
                .eq(PerformanceTargerEntity::getDeleteState, 0);

        if (performanceTargerEntity.getId() == null) {
            performanceTargerEntity.setCreateUser(user.getUserId().intValue());
        } else {
            performanceTargerWrapper.ne(PerformanceTargerEntity::getId, performanceTargerEntity.getId());
        }

        //查询是否存在，相同周期，品牌，省份，指标是否存在重复目标值
        Integer count = this.baseMapper.selectCount(performanceTargerWrapper);
        if (count > 0) {
            return R.error("同一周期，品牌，省份，指标，目标值重复！");
        }
        //保存或者更新
        boolean res = this.saveOrUpdate(performanceTargerEntity);
        if (!res) {
            return R.error("操作失败");
        }
        return R.ok();
    }

    @Transactional
    @Override
    public R deleteById(Integer id) {
        PerformanceTargerEntity performanceTargerEntity = new PerformanceTargerEntity();
        performanceTargerEntity.setDeleteState(1);
        performanceTargerEntity.setId(id);
        this.updateById(performanceTargerEntity);
        return R.ok();
    }

    @Override
    public void downloadModel(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            String filename = new String("目标值导入模版信息".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xlsx");

            //读取OSS的模版
            OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(OssProperties.bucketName, "model/performanceTargerModel.xlsx"));
            InputStream modelInputStream = ossObject.getObjectContent();
            ossClient.shutdown();
            //获取登陆用户
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            List<Long> roleIdList = user.getRoleIdList();

            List<Map<String,Object>> bizRegionEntities;
            //查询当前用户是否配置区域
            Set<Long> areaIds = brandDotScoreMapper.selectAreaByUserId(user.getUserId());
            //理想指标ID
            Integer[] lxIndicator = {1,2,13,14};
            List<Integer> lxList = Arrays.asList(lxIndicator);
            //特斯拉指标ID
            Integer[] tslIndicator = {3,4,5,6,7,8};
            List<Integer> tslList = Arrays.asList(tslIndicator);
            //潜力品牌指标ID
            Integer[] otherIndicator = {9,10,11,12};
            List<Integer> otherList = Arrays.asList(otherIndicator);

            // 如果是网点角，需要根据网点区域品牌查询
            if(roleIdList.contains(4L)){
                Integer dotId = worderInformationDao.getDotId(user.getUserId());
                bizRegionEntities = this.baseMapper.getAreaAndBrandByDot(dotId);

            } else {
                //查询当前配置的区域下的区域
                bizRegionEntities = this.baseMapper.getAreaAndBrandByUser(user.getUserId());
            }

            //填充指标数据
            LambdaQueryWrapper<BrandPerEvaluation> brandPerEvaluationWrapper = Wrappers.lambdaQuery();
            brandPerEvaluationWrapper.select(BrandPerEvaluation::getId, BrandPerEvaluation::getIndicatorKey, BrandPerEvaluation::getIndicatorDesc);
            brandPerEvaluationWrapper.eq(BrandPerEvaluation::getDeleteState, 0);
            List<Map<String, Object>> brandPerEvaluations = brandPerEvaluationMapper.selectMaps(brandPerEvaluationWrapper);

            //填充品牌信息
            LambdaQueryWrapper<BrandEntity> brandWrapper = Wrappers.lambdaQuery();
            brandWrapper.select(BrandEntity::getId, BrandEntity::getBrandName);
            List<Map<String, Object>> brnads = brandDao.selectMaps(brandWrapper);

            int i = 1;
            // 填充sheet，指标，省份，品牌笛卡尔积
            List<Map<String, Object>> datas = new ArrayList<>();
            for (Map<String, Object> brandPerEvaluation : brandPerEvaluations){
                for (Map<String,Object> map : bizRegionEntities){
                    Integer brandId = (Integer) map.get("brand_id");
                    Integer indicatorId = (Integer) brandPerEvaluation.get("id");
                    if(brandId != 14 && tslList.contains(indicatorId)){
                        continue;
                    }
                    if(brandId != 18 && lxList.contains(indicatorId)){
                        continue;
                    }
                    if((brandId == 14 || brandId == 18) && otherList.contains(indicatorId)){
                        continue;
                    }
                    Map<String, Object> itemMap = new HashMap<>();
                    itemMap.put("number", i++);
                    itemMap.put("indicator_desc", brandPerEvaluation.get("indicator_desc"));
                    itemMap.put("indicator_id", brandPerEvaluation.get("id"));
                    itemMap.put("region_name", map.get("name"));
                    itemMap.put("region_id", map.get("id"));
                    itemMap.put("brand_name", map.get("brand_name"));
                    itemMap.put("brand_id", brandId);
                    datas.add(itemMap);
                }
            }

            // 这里 会填充到第一个sheet， 然后文件流会自动关闭
            excelWriter = EasyExcelFactory.write(response.getOutputStream()).withTemplate(modelInputStream).build();
            WriteSheet writeSheet0 = EasyExcelFactory.writerSheet(0).build();
            excelWriter.fill(datas, writeSheet0);
            WriteSheet writeSheet1 = EasyExcelFactory.writerSheet(1).build();
            excelWriter.fill(brandPerEvaluations, writeSheet1);
            WriteSheet writeSheet2 = EasyExcelFactory.writerSheet(2).build();
            excelWriter.fill(brnads, writeSheet2);
        } catch (Exception e) {
            log.error("下载模版失败",e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public R uploadScoreModel(MultipartFile file) {
        LocalDate currentDate = LocalDate.now();
        try {
            //获取文件名
            String fileName = file.getOriginalFilename();
            //获取文件的后缀名为xlsx
            assert fileName != null;
            String fileXlsx = fileName.substring(fileName.length() - 5);
            String fileXls = fileName.substring(fileName.length() - 4);
            //校验文件扩展名
            //如果不是excel文件
            if (!(fileXlsx.equals(".xlsx") || fileXls.equals(".xls"))) {
                return R.error().put("msg", "文件类型不正确！");
            }

            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            if (user == null) {
                return R.error("登陆超时，请重新登陆");
            }

            List<String> failureDatas = new ArrayList<>();
            //上传文件到OSS
            OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
            DateTimeFormatter nowTime = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedNowTime = currentDate.format(nowTime);
            String[] split = fileName.split("\\.");
            String newFile = split[0] + formattedNowTime + "." + split[1];
            ossClient.putObject(OssProperties.bucketName, newFile, file.getInputStream());

            Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24 * 365 * 100);
            URL url = ossClient.generatePresignedUrl(OssProperties.bucketName, newFile, expiration);
            // 关闭OSSClient
            ossClient.shutdown();
            //保存表
            SysFileEntity sysFileEntity = new SysFileEntity();
            sysFileEntity.setObjType(15);
            sysFileEntity.setObjValue(15);
            sysFileEntity.setOldName(fileName);
            sysFileEntity.setNewName(newFile);
            sysFileEntity.setPath(url.toString());
            sysFileService.saveSysFile(sysFileEntity);
            //读取文件并且更新表
            EasyExcelFactory.read(file.getInputStream(), PerformanceTargerExcelProperty.class, new PerformanceTargerExcelListener(this, user.getUserId().intValue(), failureDatas, new HashSet<>())).sheet().doRead();
            if (!failureDatas.isEmpty()) {
                return R.error().put("failureDatas", failureDatas);
            }
        } catch (Exception e) {
            log.error("目标值导入异常", e);
            return R.error("目标值导入失败，请联系管理员");
        }
        return R.ok();
    }
}