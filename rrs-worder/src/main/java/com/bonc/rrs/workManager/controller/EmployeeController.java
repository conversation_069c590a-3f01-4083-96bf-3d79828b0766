package com.bonc.rrs.workManager.controller;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.util.ExcelInputUtil;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.workManager.dao.BizEmployeeMapper;
import com.bonc.rrs.workManager.entity.BizEmployee;
import com.bonc.rrs.workManager.entity.EmployeeEntity;
import com.bonc.rrs.workManager.entity.dto.SysDeptDto;
import com.bonc.rrs.workManager.service.BizEmployeeService;
import com.bonc.rrs.workManager.service.EmployeeService;
import com.github.pagehelper.PageInfo;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDepartEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDepartService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/*
* 员工管理
* @author: minJunping
* @date: 2020/03/06
* */
@Controller
@RequestMapping(value = "/work/bizemployee")
@Slf4j
public class EmployeeController {

    @Autowired(required = false)
    private BizEmployeeService bizEmployeeService;
    @Autowired(required = false)
    private EmployeeService employeeService;

    @Autowired(required = false)
    private BizEmployeeMapper bizEmployeeMapper;
    @Autowired(required = false)
    SysDepartService sysDepartService;

    /*
    * 员工添加
    * */

    @PostMapping(value = "add")
    @ResponseBody
    public Results addEmployeeController(@RequestBody EmployeeEntity bizEmployee){
        try {
            return bizEmployeeService.insertSelectiveService(bizEmployee);
        } catch (Exception e) {
            e.printStackTrace();
            return Results.message(110,"添加失败",null);
        }
    }

    /*
    * 员工条件查询
    * */

    @RequestMapping(value = "selectlist",method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public PageInfo<BizEmployee> selectEmployeeEntity(@RequestParam(value = "nameId",required = false) String nameId,
                                                      @RequestParam(value = "currentIndex",required = false,defaultValue = "1") Integer currentIndex,
                                                      @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize) throws Exception {

        return bizEmployeeService.selectEmployeeByKey(nameId, pageSize, currentIndex);

    }

    /*
    * 员工更新
    * */

    @RequestMapping(value = "update",method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results updateEmployee(@RequestBody BizEmployee bizEmployeeEntity){

        return bizEmployeeService.updateByKeyService(bizEmployeeEntity);

    }

    /*
    * 员工删除
    * */

    @RequestMapping(value = "delete", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results deleteEmployeeByid(@RequestParam(value = "id") String id){
        return bizEmployeeService.deleteByKey(id);
    }

    /*
    * 员工修改回显数据
    * */

    @RequestMapping(value = "selectentity", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results selectEntity(@RequestParam(value = "id") String id){
        try {
            if (!StringUtils.isEmpty(id)){
                BizEmployee bizEmployee = bizEmployeeMapper.selectByPrimaryKey(id);
                return Results.message(0,"success",bizEmployee);
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }
    }

    /**
     * 日日顺部门信息查询
     * @return
     */
    @RequestMapping(value = "getdepartmen", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getDepartmentInfo(){

        List<Map<String,String>> mapList = bizEmployeeMapper.selectDepatInfo();
//        mapList.stream().map(map->{
//            List<Map<String, String>> list = bizEmployeeMapper.getDepartByParentId(map.get("id"));
//            if (list!=null&&list.size()<1){
//                map.put("isLeaf","1");
//            }else {
//                map.put("isLeaf","0");
//            }
//            return map;
//        }).collect(Collectors.toList());
        if (mapList.size() != 0) {
            return Results.message(0, "success", mapList);
        }else {
            return Results.message(100,"查无相关的部门信息",null);
        }

    }

    /**
     * 日日顺部门信息查询 第一级
     * @return
     */
    @RequestMapping(value = "getdepart", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results getDepart(){

        List<SysDeptDto> list = bizEmployeeMapper.getDeptInfo();
        list.stream().map(SysDeptDto->{
            List<SysDeptDto> deptList = bizEmployeeMapper.getDepartByParentId(SysDeptDto.getId()+"");
            if (deptList!=null&&deptList.size()<1){
                SysDeptDto.setIsLeaf("1");
            }else {
                SysDeptDto.setIsLeaf("0");
            }
            return SysDeptDto;
        }).collect(Collectors.toList());

        if (list.size() != 0) {
            return Results.message(0, "success", list);
        }else {
            return Results.message(100,"查无相关的部门信息",null);
        }

    }

    /**
     * 日日顺部门信息查询 根据父id
     * @return
     */
    @GetMapping (value = "getDepartByParentId")
    @ResponseBody
    public Results getDepartByParentId(String parentId) {

        List<SysDeptDto> list = bizEmployeeMapper.getDepartByParentId(parentId);
        list.stream().map(SysDeptDto -> {
            List<SysDeptDto> deptList = bizEmployeeMapper.getDepartByParentId(SysDeptDto.getId() + "");
            if (deptList != null && deptList.size() < 1) {
                SysDeptDto.setIsLeaf("1");
            } else {
                SysDeptDto.setIsLeaf("0");
            }
            return SysDeptDto;
        }).collect(Collectors.toList());

        if (list.size() != 0) {
            return Results.message(0, "success", list);
        } else {
            return Results.message(100, "查无相关的部门信息", null);
        }
    }

    @RequestMapping("/input")
    @ResponseBody
    @CrossOrigin
    @Transactional(rollbackFor = {Exception.class,RuntimeException.class})
    public R imageUpload(@RequestParam("file") MultipartFile file){
        String fileName = file.getOriginalFilename();
        List<EmployeeEntity> list = new ArrayList<>();
        System.out.println(fileName);
        boolean notNull = false;
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
            System.out.println("上传文件格式不正确");
            return R.error("上传文件格式不正确");
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        InputStream is = null;
        try {
            is = file.getInputStream();
            Workbook wb = null;
            if (isExcel2003) {
                wb = new HSSFWorkbook(is);
            } else {
                wb = new XSSFWorkbook(is);
            }
            Sheet sheet = wb.getSheetAt(0);
            if(sheet!=null){
                notNull = true;
            }
            Set idCodeSet = new HashSet();
            Set contactSet = new HashSet();
            for (int i = 2, len = sheet.getPhysicalNumberOfRows(); i <= len; i++) {
                EmployeeEntity excelInput = new EmployeeEntity();
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                Cell cell = row.getCell(0);
                String name = ExcelInputUtil.getCellValue(row, 0);
                log.info("name: " + name);
                String gender = ExcelInputUtil.getCellValue(row, 1);
                log.info("gender: " + gender);
                String contact = ExcelInputUtil.getCellValue(row, 2);
                log.info("contact: " + contact);
                String idCard = ExcelInputUtil.getCellValue(row, 3);
                log.info("idCard: " + idCard);
                String departmentName = ExcelInputUtil.getCellValue(row, 4);
                log.info("departmentName: " + name);
                String address = ExcelInputUtil.getCellValue(row, 5);
                log.info("address: " + address);
                String code = ExcelInputUtil.getCellValue(row, 6);
                Integer zipCode = code != null ? Integer.parseInt(code) : null;
                log.info("zipCode: " + zipCode);
                if (!idCodeSet.add(idCard)) {
                    System.out.println("第" + (i+1) + "行存在重复身份证号码" + idCard);
                    return R.error("第" + (i+1) + "行存在重复身份证号码" + idCard);
                }
                if (!contactSet.add(contact)) {
                    return R.error("第" + (i+1) + "行存在重复手机号" + contact);
                }
                if ((StringUtils.isBlank(idCard))){
                    return R.error("第" + (i+1) + "行身份证信息不能为空！" + idCard);
                }
//                boolean validCard = IdcardUtil.isValidCard(idCard);
//                if (!validCard){
//                    return R.error("第" + (i+1) + "行身份证号码格式不正确!");
//                }
                //验证手机号码格式
                if ((StringUtils.isBlank(contact))){
                    return R.error("第" + (i+1) + "行手机号码不能为空！" + contact);
                }
                boolean isMobile = Validator.isMobile(contact);
                if (!isMobile){
                    return R.error("第" + (i+1) + "行手机号码格式不正确！" + contact);
                }
                excelInput.setCreateTime(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                // 查表，是否已存在该身份证号码
                Integer count = bizEmployeeMapper.selectCount(new QueryWrapper<BizEmployee>()
                        .eq("contact", contact));
                if (count > 0) {
                    return R.error("第" + (i+1) + "行手机号已存在" + contact);
                }
                // 查表，是否已存在该手机号
                count = bizEmployeeMapper.selectCount(new QueryWrapper<BizEmployee>()
                        .eq("id_card", idCard));
                if (count > 0) {
                    return R.error("第" + (i+1) + "行身份证信息已存在" + idCard);
                }
                // 查询部门是否存在且部门ID
                List<SysDepartEntity> departs = sysDepartService.getBaseMapper().selectList(new QueryWrapper<SysDepartEntity>()
                        .eq("departname", departmentName));
                Integer departmentId = null;
                if (departs.size() > 0) {
                    SysDepartEntity sysDepartEntity = departs.get(0);
                    departmentId = sysDepartEntity.getId();
                } else {
                    return R.error("第" + (i+1) + "行部门不存在" + departmentName);
                }
                excelInput.setInfo(name, gender, contact, idCard, departmentId, address, zipCode);
                list.add(excelInput);
            }
            // 分割list后，入表
            employeeService.saveBatch(list);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.ok().putList(list);
    }

}
