package com.bonc.rrs.workManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bonc.rrs.attribute.dao.WorderInformationAttributeMapper;
import com.bonc.rrs.attribute.entity.WorderInformationAttribute;
import com.bonc.rrs.baidumap.entity.DotSendsRecord;
import com.bonc.rrs.baidumap.service.DotSendsRecordService;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.warning.annotation.Warning;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.*;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.BizAttendantService;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationSubService;
import com.bonc.rrs.worder.service.WorderSendAuditService;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.workManager.dao.*;
import com.bonc.rrs.workManager.entity.*;
import com.bonc.rrs.workManager.service.*;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.exception.RetryException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.lenmoncore.common.validator.Assert;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryService;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: MinJunping
 * @date: 2020/04/02
 */

@Service
@Slf4j
//@Transactional( rollbackFor={Exception.class})
@EnableAspectJAutoProxy(exposeProxy = true)
@Log4j2
public class AutoSendServiceImpl implements AutoSendService {

    @Autowired(required = false)
    private AutoSendMapper autoSendMapper;
    @Autowired(required = false)
    private WorkMsgDao workMsgDao;
    @Autowired(required = false)
    private SendOrdersMapper sendOrdersMapper;
    @Autowired(required = false)
    private DotDispatchMapper dotDispatchMapper;
    @Autowired(required = false)
    private SendOrderService sendOrderService;
    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    DotInformationDao dotInformationDao;
    @Autowired
    SendWorderRecordService sendWorderRecordService;
    @Autowired
    SysDictionaryService sysDictionaryService;
    @Autowired
    BizAttendantService bizAttendantService;
    @Autowired
    WorderInformationSubService worderInformationSubService;
    @Autowired
    WorderRemarkLogDao worderRemarkLogDao;
    @Autowired
    AttendantSendsRecordService attendantSendsRecordService;
    @Autowired
    DotSendsRecordService dotSendsRecordService;
    @Autowired
    private BizEmployeeDao bizEmployeeDao;

    @Autowired
    DotInfoMapper dotInfoMapper;

    @Autowired
    private DotAreaService dotAreaService;

    @Autowired
    private BizRegionService bizRegionService;

    @Autowired
    private WorderTemplateDao worderTemplateDao;

    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;

    @Autowired
    private FlowCommon flowCommon;

    @Autowired
    private BrandDotScoreMapper brandDotScoreMapper;

    @Autowired
    private UserRoleAreaPermissionsService userRoleAreaPermissionsService;

    @Autowired
    private WorderExtFieldDao worderExtFieldDao;

    @Autowired(required = false)
    private RemarkLogMapper remarkLogMapper;

    @Autowired
    private WorderInformationAttributeMapper worderInformationAttributeMapper;

    @Autowired
    private SysUserDao sysUserDao;

    @Autowired
    private WorderSendAuditService worderSendAuditService;

    @Override
    public void reSendWorder(String params) {
        UserUtil.createDefaultLoginUser();
        //处理未派单的
        Integer worderExecStatus = 0;
        //查找最近五天未派单并且是勘安订单的
        List<WorderInformationEntity> worderInformationEntities = autoSendMapper.selectWorderBySendStatus(worderExecStatus,null,30);
        for (WorderInformationEntity worderInformationEntity : worderInformationEntities) {
            this.autoSendMapper.updateWorderStatus(worderInformationEntity.getWorderId(), 0, "weipaidan");
            this.autoSendMagrDot("系统自动", worderInformationEntity.getWorderNo());
        }
        log.info("未派单重新派单数量 {}", worderInformationEntities.size());

    }


    @Override
    public void reSendWorderFWJL(String params) {
        UserUtil.createDefaultLoginUser();
        //服务经理未派单
        List<WorderInformationEntity> entities = autoSendMapper.selectWorderBySendStatus(18,params,3);
        log.info("服务经理未派单重新派单数量 {}", entities.size());
        Integer i = 0;
        if (entities != null && entities.size() > 0) {
            List<Integer> wordIds = entities.stream().map(w -> w.getWorderId()).collect(Collectors.toList());
            QueryWrapper<WorderSendAuditEntity> queryWrapper = new QueryWrapper<>();
            List<Integer> status = new ArrayList<>();
            status.add(0);
//        status.add(1);
//        status.add(2);
            queryWrapper.in("worder_id", wordIds).in("status", status);
            List<WorderSendAuditEntity> list = worderSendAuditService.list(queryWrapper);
            for (WorderInformationEntity w : entities) {
                WorderSendAuditEntity worderSendAuditEntity = list.stream().filter(s -> w.getWorderId().equals(s.getWorderId())).findFirst().orElse(null);
                if (worderSendAuditEntity == null) {
                    this.autoSendMapper.updateWorderStatus(w.getWorderId(), 0, "weipaidan");
                    Results results = this.autoSendMagrDot("系统自动", w.getWorderNo());
                    if(results.getCode().equals(0)){
                        i++;
                    }
                }
            }
        }
        log.info("服务经理未派单重新派单成功数量 {}", i);

    }

    @Warning(value = "派单给服务经理")
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public R sendManager(String worderNo, Long worderId, Integer userId, String username, Integer areaId, Integer brandId) {
        Integer newAreaId = null;
        if (areaId != null) {
            //根据区域和用户ID获取区域级别最大值
            newAreaId = autoSendMapper.selectAreaByUserAndAreaIds(userId, handleCode(areaId));
        }
        String affectUser = autoSendMapper.selectUsername(userId);
        //自动派单给服务经理
        int allocatePmNum = 0;
        // 判断是否配置工作流
        if (flowCommon.hasFlowByWorderNo(worderNo)) {
            // 调用流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.AssignProjMng, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg());
            }
            allocatePmNum = workMsgDao.flowUpdataOrderMager(worderNo, affectUser, (long) userId);
        } else {
            allocatePmNum = workMsgDao.updataOrderMager(worderNo, affectUser, "待服务经理派单给网点", (long) userId, 18);
        }

        if (allocatePmNum > IntegerEnum.ZERO.getValue()) {
            OperationRecord operationRecord = new OperationRecord(username, "系统自动派单给" + affectUser, (long) userId, affectUser, 2, worderNo, "0", "18");
            Date currentDate = new Date();
            long time = currentDate.getTime();
            operationRecord.setCreateTime(new Date(time + 1000));
            workMsgDao.insertOperation(operationRecord);
            //保存派单给服务经理的派单记录
            SendWorderRecord sendWorderRecord = new SendWorderRecord()
                    .setWorderId(worderId)
                    .setSendWorderType(1L)
                    .setSendWorderUser(1L)
                    .setAcceptWorderUser(userId.longValue())
                    .setCreateTime(LocalDateTime.now())
                    .setOperationType(1L)
                    .setAreaId(newAreaId != null ? newAreaId.longValue() : null)
                    .setBrandId(brandId != null ? brandId.longValue() : null)
                    .setRemark("自动派单：" + username + "派单给" + affectUser);
            sendWorderRecordService.save(sendWorderRecord);
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：服务经理自动派单成功", worderNo + "自动派单成功派单给服务经理：" + affectUser, userId);
            return R.ok().putWorderNo(worderNo).putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_MANAGER)
                    .putWorderExecStatus(FieldConstant.WAIT_BRANCH_SEND_ORDER);
        } else {
            log.error(worderNo + "： 派单给服务经理： " + affectUser + "失败");
            return R.error();
        }
    }


    public R sendDot(String worderNo, Long worderId, List<Map<String, Object>> dotMapInfo, Map<String, Object> periodSum,
                     String username, Integer userId, Integer areaId, boolean isIdeal, Integer brandId, Integer operationType, String remark) {
        return ((AutoSendServiceImpl) AopContext.currentProxy()).sendDot(worderNo, worderId, dotMapInfo, periodSum, username, IntegerEnum.ZERO.getValue(), userId, areaId, isIdeal, brandId, operationType, remark);
    }

    @Warning(value = "派单给网点")
    @Transactional(rollbackFor = {Exception.class})
    public R sendDot(String worderNo, Long worderId, List<Map<String, Object>> dotMapInfo, Map<String, Object> periodSum,
                     String username, Integer memeber, Integer userId, Integer areaId, boolean isIdeal, Integer brandId, Integer operationType, String remark) {
        log.info("worderNo:{}, dotMapInfo:{}, periodSum:{}, areaId:{}, isIdeal:{}, brandId:{}, operationType:{}, remark:{}",
                worderNo, JSONObject.toJSONString(dotMapInfo), JSONObject.toJSONString(periodSum), areaId, isIdeal, brandId, operationType, remark);
        Map<String, Object> dotMapEntity = dotMapInfo.get(memeber);
        Long dotId = Long.parseLong(dotMapEntity.get("dotId").toString());

        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_no", worderNo);
        WorderInformationEntity worder = worderInformationDao.selectOne(wrapper);

        // 增值物料预占 -- start
        List<WorderInformationAttributeEntity> worderInformationAttributeList = null;

        Integer addedMaterialTypePreempCount = dotInfoMapper.queryAddedMaterialTypePreempCountByWorderId(worder.getWorderId());

        if (addedMaterialTypePreempCount == 0) {
            R checkR = sendOrderService.checkAddedMaterialStock(worder.getTemplateId(), dotId.intValue(), worder.getWorderId());
            if ((Integer) checkR.get("code") != 0) {
                return R.error(10, "网点：" + dotMapEntity.get("name") + "," + checkR.get("msg"));
            }
            worderInformationAttributeList = (List<WorderInformationAttributeEntity>) checkR.get("data");
        }
        // 增值物料预占 -- end

        Integer newAreaId = null;
        if (areaId != null) {
            //根据工单区域和网点匹配关联网点区域级别最大值
            newAreaId = autoSendMapper.selectDotAreaByDotAndAreaIds(dotId.intValue(), handleCode(areaId));
        }

        BizRegionEntity bizRegionEntity = null;
        if (areaId != null) {
            bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
        }


        int allocateDotNum = 0;
        if (flowCommon.hasFlowByWorderNo(worderNo)) {
            //调用网点已接单流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.AssignDot, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg());
            }
            allocateDotNum = sendOrdersMapper.updateOrder(worderNo, dotMapEntity.get("name").toString(), Long.valueOf(dotMapEntity.get("dotId").toString()));
        } else {
            allocateDotNum = sendOrdersMapper.updataOrderInfo(worderNo, dotMapEntity.get("name").toString(), "网点已接单", Long.valueOf(dotMapEntity.get("dotId").toString()), 1);
        }

        if (allocateDotNum > IntegerEnum.ZERO.getValue()) {

            // 保存增值物料
            if (worderInformationAttributeList != null && worderInformationAttributeList.size() > 0) {
                worderInformationAttributeDao.insertBatch(worderInformationAttributeList);
            }

            OperationRecord work = new OperationRecord(username, "系统自动派单给" + dotMapEntity.get("name"), Long.valueOf(dotMapEntity.get("dotId").toString()), dotMapEntity.get("name").toString(), 3, worderNo, "0", "1");
            Date currentDate = new Date();
            long time = currentDate.getTime();
            work.setCreateTime(new Date(time + 2000));
            workMsgDao.insertOperation(work);
            autoSendMapper.updateDotCount(Long.valueOf(dotMapEntity.get("dotId").toString()), Integer.parseInt(dotMapEntity.get("count").toString()) + 1);
            if (operationType != null && operationType == 1) {
                autoSendMapper.updataPeriodSumById(1, Integer.parseInt(periodSum.get("sum").toString()) + 1);
            }
            //保存网点派单记录
            SendWorderRecord sendWorderRecord = new SendWorderRecord()
                    .setWorderId(worderId)
                    .setSendWorderType(2L)
                    .setSendWorderUser(userId.longValue())
                    .setAcceptWorderUser(dotId)
                    .setOperationUser(userId.longValue())
                    .setOperationType((operationType != null && operationType != 1) ? operationType.longValue() : 1L)
                    .setCreateTime(LocalDateTime.now())
                    .setAreaId(newAreaId != null ? newAreaId.longValue() : null)
                    .setCityId(bizRegionEntity != null ? bizRegionEntity.getPid() : null)
                    .setBrandId(brandId != null ? brandId.longValue() : null)
                    .setRemark("自动派单：" + username + "派单给" + dotMapEntity.get("name"));
            sendWorderRecordService.save(sendWorderRecord);
            //记录网点派单数量
            if (operationType != null && operationType == 1) {
                recordDotSendNum(dotId, isIdeal);
            }
            //记录派单记录
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单成功", worderNo + remark + " 成功派给网点：" + dotMapEntity.get("name"), userId);
            log.debug("end****************************************autoSendInstallToConveyDot true");
            return R.ok().putWorderNo(worderNo)
                    .put("dotId", Integer.valueOf(dotMapEntity.get("dotId").toString()))
                    .putWorderTriggerEvent(WarningConstant.TRIGGER_EVENT_SEND_WORDER_TO_BRANCH)
                    .putWorderExecStatus(FieldConstant.WAIT_MANAGER_SEND_ORDER);
        } else {
            log.error(worderNo + "： 派单给网点： " + dotMapEntity.get("name") + "失败");
            return R.error("更新网点派单信息失败");
        }
    }

    /**
     * 记录网点派单数量
     *
     * @param dotId
     */
    private void recordDotSendNum(Long dotId, boolean isIdeal) {
        //如果是理想的网点派单不记录数量
        if (isIdeal) {
            return;
        }
        LocalDate now = LocalDate.now();
        //查询当天是否存在派单记录
        DotSendsRecord dotSendsRecord = dotSendsRecordService.query().eq("dot_id", dotId).eq("present_date", now).eq("delete_state", 0).one();
        if (dotSendsRecord == null) {
            saveDotSendsRecord(dotId, now);
        } else {
            //乐观锁
            ((AutoSendService) (AopContext.currentProxy())).updateDotSendsRecord(dotId, now);
        }

    }

    @Override
    @Retryable(value = RetryException.class, maxAttempts = 5, backoff = @Backoff(delay = 2000L, multiplier = 1))
    public void updateDotSendsRecord(Long dotId, LocalDate now) {
        DotSendsRecord dotSendsRecord = dotSendsRecordService.query().eq("dot_id", dotId).eq("present_date", now).eq("delete_state", 0).one();
        //乐观锁
        dotSendsRecord.setAutoSendNum(dotSendsRecord.getAutoSendNum() + 1);
        boolean b = dotSendsRecordService.updateById(dotSendsRecord);
//        if (!b) {
//            throw new RetryException("修改失败重试", 100);
//        }
    }


    // @Lock4j(keys = {"#dotId"}, expire = 1000L)
    public void saveDotSendsRecord(Long dotId, LocalDate now) {
        DotSendsRecord dotSendsRecord = dotSendsRecordService.query().eq("dot_id", dotId).eq("present_date", now).eq("delete_state", 0).one();
        if (dotSendsRecord == null) {
            dotSendsRecord = new DotSendsRecord();
            dotSendsRecord.setDeleteState(0)
                    .setDotId(dotId.intValue())
                    .setSendNum(1)
                    .setAutoSendNum(1)
                    .setPresentDate(now);
            try {
                dotSendsRecordService.save(dotSendsRecord);
            } catch (Exception e) {
                ((AutoSendService) (AopContext.currentProxy())).updateDotSendsRecord(dotId, now);
            }
        }
    }


    @Override
    @Transactional
    public Boolean autoSendInstallToConveyDot(String username, String worderNo, String conveyWorderNo) {
        log.debug("begin****************************************autoSendInstallToConveyDot");
        //查看自动派单开关状态
        int state = autoSendMapper.selectAutoSendStatus("Auto_Send_Single");
        if (state == IntegerEnum.TWO.getValue()) {
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：自动派单失败", "失败原因：自动派单开关已经关闭", null);
            throw new RRException("自动派单开关已经关闭,请手动派单");
        }
        Long worderId = null;
        //查询当前的工单
        WorderInformationEntity worderInfo = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().select("worder_id", "template_id").eq("worder_no", worderNo));
        Assert.isNull(worderInfo, "未查询到当前工单信息!");
        worderId = worderInfo.getWorderId().longValue();
        //获取工单模版
        WorderTemplateDto templateId = worderTemplateDao.findTemplateInfoById(Integer.valueOf(worderInfo.getTemplateId()));
        //校验工单模版是否支持自动派单
        if (templateId.getAutoSend() == 1) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "该工单模版不支持自动派单,请手动派单");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：自动派单失败", "失败原因：该工单模版不支持自动派单,请手动派单", null);
            return false;
        }
        //查询勘测获取安装的工单
        List<WorderInformationEntity> worderInformations = worderInformationDao.selectList(new QueryWrapper<WorderInformationEntity>().eq("worder_no", conveyWorderNo));

        Integer dotId = null;
        Integer userId = null;
        Integer brandId = Integer.parseInt(templateId.getBrandId());
        if (worderInformations.size() > IntegerEnum.ZERO.getValue() && worderInformations.get(IntegerEnum.ZERO.getValue()) != null) {
            WorderInformationEntity worderInformationEntity = worderInformations.get(IntegerEnum.ZERO.getValue());
            userId = worderInformationEntity.getPmId();
            if (userId == null) {
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：服务经理自动派单失败", "根据勘测工单号：" + conveyWorderNo + " 未查询到服务经理 ！", userId);
                return false;
            }
            dotId = worderInformationEntity.getDotId();
            if (dotId == null) {
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "根据勘测工单号：" + conveyWorderNo + "未查询到网点 ！", userId);
                return false;
            }
        }
        // 判断网点状态
        DotInformationEntity dotInformationEntity = dotInformationDao.selectById(dotId);
        // 网点状态无效
        Boolean flag = false;
        if (dotInformationEntity != null && IntegerEnum.ONE.getValue().equals(dotInformationEntity.getDotState())) {
            // 网点状态有效
            flag = true;
        }
        if (flag && userId != null) {
            //自动派单给服务经理
            ((AutoSendServiceImpl) AopContext.currentProxy()).sendManager(worderNo, worderId, userId, username, null, brandId);
            Set<Integer> dotIdlist = new HashSet();
            dotIdlist.add(dotId);
            List<Map<String, Object>> dotMapInfo = autoSendMapper.selectdotCountScorelist(dotIdlist);
            Map<String, Object> periodSum = autoSendMapper.selectPeriodSumInfo(1);

            //如果只有一个网点就派给他
            if (dotMapInfo.size() > IntegerEnum.ZERO.getValue() && null != dotMapInfo.get(IntegerEnum.ZERO.getValue())) {
                //校验网点品牌
                if (checkDotBrad(dotId, brandId)) {
                    sendDot(worderNo, worderId, dotMapInfo, periodSum, username, userId, null, true, brandId, templateId.getServiceTypeEnum(), "");
                } else {
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "根据勘测工单号匹配的网点：" + dotId + "品牌校验不通过 ！", userId);
                    flag = false;
                }
            } else {
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "根据勘测工单号匹配的网点" + dotId + " 未查询到网点或者网点未生效 ！", userId);
                flag = false;
            }
        }
        //进行工单打标,后续派单不记录派单次数
        if (flag) {
            assert worderId != null;
            WorderInformationSub worderInformationSub = worderInformationSubService.getBaseMapper().selectOne(new QueryWrapper<WorderInformationSub>().eq("worder_id", worderId.intValue()));
            if (worderInformationSub != null) {
                worderInformationSub.setDotCountState(0)
                        .setInsertTime(LocalDateTime.now());
            } else {
                worderInformationSub = new WorderInformationSub()
                        .setWorderId(worderId.intValue())
                        .setDotCountState(0)
                        .setInsertTime(LocalDateTime.now());
            }
            worderInformationSubService.saveOrUpdate(worderInformationSub);
        }
        log.debug("end****************************************autoSendInstallToConveyDot failure");
        return flag;
    }

    @Transactional
    @Override
    public R tslPresurvey(Integer worderId) {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时请重新登陆！");
        }
        LambdaQueryWrapper<WorderInformationEntity> worderInfoWrapper = Wrappers.lambdaQuery();
        worderInfoWrapper.eq(WorderInformationEntity::getWorderId, worderId);
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInfoWrapper);
        //工单号
        String worderNo = worderInformationEntity.getWorderNo();
        //区县
        Integer areaId = worderInformationEntity.getAreaId();
        //手机号
        String userPhone = worderInformationEntity.getUserPhone();
        //邮箱
        String postcode = worderInformationEntity.getPostcode();
        //工单类型
        Integer worderType = worderInformationEntity.getWorderTypeId();

        //查询工单模版
        WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worderInformationEntity.getTemplateId());
        Integer brandId = Integer.parseInt(template.getBrandId());

        //校验当前工单是否是特斯拉勘安
        boolean isTslKn = getIsTslKn(brandId, worderType);

        boolean isPresurvey = false;
        //在已满足特斯拉勘安的工单情况下，判断是否为预勘测单
        if (isTslKn) {
            isPresurvey = getIsPresurvey(worderNo);
        }

        Integer userId = worderInformationEntity.getPmId();
        //服务经理为空时查询服务经理
        if (worderInformationEntity.getPmId() == null) {
            R sendManagerResult = null;
            try {
                //查出服务经理id
                List<Integer> userIds = sendOrderService.getManager(areaId, brandId, template.getServiceTypeEnum());
                if (Objects.nonNull(userIds) && userIds.size() == 1) {
                    userId = userIds.get(0);
                    worderInformationEntity.setPmId(userId);
                } else if (Objects.nonNull(userIds) && userIds.size() > 1) {
                    List<String> names = sysUserDao.selectEmployeeUserIds(userIds);
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：服务经理自动派单失败", "失败原因：相同区域，服务类型和品牌存在有多个服务经理[" + names + "]", null);
                    return R.error("相同区域，服务类型和品牌存在有多个服务经理[" + names + "]");
                }
            } catch (Exception e) {
                log.error("数据库连接异常，自动派单失败,worderNo:{}", worderNo, e);
                saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：服务经理自动派单失败", "失败原因：数据库连接异常，自动派单失败", null);
                return R.error("数据库连接异常，自动派单失败");
            }
            if (StringUtils.isEmpty(userId) || userId == 0) {
                saveWorderRemark(worderNo, worderNo + "分配中，子状态：服务经理自动派单失败", "失败原因：无关联的服务经理", null);
                return R.error("无关联的服务经理");
            }
            //自动派单给服务经理
            sendManagerResult = ((AutoSendServiceImpl) AopContext.currentProxy()).sendManager(worderNo, worderId.longValue(), userId, user.getUsername(), areaId, brandId);
            if (sendManagerResult == null || (Integer) sendManagerResult.get("code") != 0) {
                saveWorderRemark(worderNo, worderNo + "分配中，子状态：服务经理自动派单失败", "失败原因：服务经理自动派单失败", null);
                return R.error("服务经理自动派单失败");
            }
        }

        //查询关联的勘测工单
        Results presurveyResults = getPresurveyWorder(userPhone, postcode, brandId);
        if (presurveyResults.getCode() != 0 && template.getAutoSend() == 1) {
            saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：特斯拉非预勘测自动派单失败", "失败原因：根据工单的客户联系电话和邮箱未查询到预勘测单信息，并且该工单模版不支持自动派单", null);
            return R.error("根据工单的客户联系电话和邮箱未查询到预勘测单信息，并且该工单模版不支持自动派单");
        } else if (presurveyResults.getCode() == 0) {
            worderInformationEntity = (WorderInformationEntity) presurveyResults.getData();
            //取消预勘测工单
            cancelPresurveyWorder(worderInformationEntity);
            //修改工单扩展字段
            updateWorderExtField(worderNo, worderInformationEntity);
            //插入一条工单转正标识记录
            insertWorderAttr(worderId.longValue());
        }

        String beforeMsg = "";
        if (worderInformationEntity.getDotId() != null) {
            //根据网点ID查询网点信息
            DotInformationEntity dotInformationEntity = dotInformationDao.selectById(worderInformationEntity.getDotId());
            //网点有效状态，就派单给当前这个网点，不记录派单次数
            if (dotInformationEntity.getDotState() == 1) {
                List<Map<String, Object>> dotMapInfo = new ArrayList<>();
                Map<String, Object> dotMap = new HashMap<>();
                dotMap.put("dotId", dotInformationEntity.getDotId());
                dotMap.put("name", dotInformationEntity.getDotName());
                dotMap.put("score", dotInformationEntity.getDotScore());
                dotMap.put("count", dotInformationEntity.getCount());
                dotMapInfo.add(dotMap);
                Map<String, Object> periodSum = autoSendMapper.selectPeriodSumInfo(1);
                R result = sendDot(worderNo, worderId.longValue(), dotMapInfo, periodSum, user.getUsername(), worderInformationEntity.getPmId(), areaId, false, brandId, worderType == 6 ? 4 : 1, "特斯拉预勘测单匹配网点成功，");
                if (result != null && (Integer) result.get("code") == 0) {
                    return R.ok("自动派单成功");
                } else {
                    log.info("=================工单号：{}=自动派单失败===============", worderNo);
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：自动派单失败", "失败原因：" + (result != null ? result.get("msg") : "更新网点派单信息失败"), worderInformationEntity.getPmId());
                    return R.error((result != null ? result.get("msg").toString() : "更新网点派单信息失败"));
                }
            } else if (template.getAutoSend() == 1) {
                log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "该工单模版不支持自动派单");
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：预勘测单网点：" + dotInformationEntity.getDotName() + " 非有效状态，并且工单模版不支持自动派单", worderInformationEntity.getPmId());
                return R.error("该工单模版不支持自动派单");
            } else {
                beforeMsg = "预勘测单网点：" + dotInformationEntity.getDotName() + " 非有效状态";
            }
        }

        /*
         * 对网点进行派单
         */
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        //获取派单的周期记录
        Map<String, Object> periodSum = autoSendMapper.selectPeriodSumInfo(1);
        //获取网点配置的周期
        String cycle = sysDictionaryService.selectDictionaryByNumber("dot_send_cycle");
        Date cycleTime = autoSendMapper.selectCycleTime(cycle);
        long nowTime = Long.parseLong(df.format(cycleTime.getTime()));
        Date periodTime = (Date) periodSum.get("timeStart");
        long startTime = Long.parseLong(df.format(periodTime));
        //计算当前配置周期时间是否超过period_sum表里周期时间如果超过重置时间和派单总计
        if (nowTime > startTime) {
            //重置时间为当前周期的时间
            autoSendMapper.resetCountTime();
            //重置网点的派单数量count=0
            autoSendMapper.updateDotInformation();
        }

        //获取该工单的区县信息
        BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
        if (bizRegionEntity == null) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败工单区域未获取到区县信息");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据工单区域未获取到区县信息", userId);
            return R.error("自动派单失败根据工单区域未获取到地市信息");
        }

        //获取工单的地市信息
        BizRegionEntity cityBizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(bizRegionEntity.getPid());
        if (cityBizRegionEntity == null) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败工单区域未获取到地市信息");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据工单区域未获取到地市信息", userId);
            return R.error("自动派单失败根据工单区域未获取到地市信息");
        }

        //查询网点信息
        List<Integer> dotIds = sendOrderService.selectDot(bizRegionEntity.getRegcode(), brandId, template.getServiceTypeEnum());
        if (dotIds == null || dotIds.isEmpty()) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息", userId);
            return R.error("自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息");
        }

        //剔除不符合的网点
        List<DotArea> dotAreas = dotAreaService.selectListByDotsBrand(dotIds, brandId, template.getServiceTypeEnum());

        if (dotAreas != null && !dotAreas.isEmpty()) {
            //根据网点分组
            Map<Integer, List<DotArea>> dotAreaGroupMap = dotAreas.stream().collect(Collectors.groupingBy(DotArea::getDotId));
            //遍历Map
            for (Map.Entry<Integer, List<DotArea>> entry : dotAreaGroupMap.entrySet()) {
                //查询工单的地市是否匹配
                List<DotArea> dotAreaList = entry.getValue();
                // 判断一: 拿工单地市ID和该网点下关联的区域ID进行匹配。
                long count = dotAreaList.stream().filter(item -> item.getAreaId() == cityBizRegionEntity.getId().intValue()).count();
                if (count > 0) {
                    continue;
                }
                // 判断二： 查询这个网点下关联的有没有该工单的省，如果有省，在看有没有绑定该省下的地市
                List<BizRegionEntity> itemBizRegionEntity = new ArrayList<>();
                for (DotArea dotArea : dotAreaList) {
                    itemBizRegionEntity.add(InitRegionUtil.REGION_ID_MAP.get(dotArea.getAreaId().longValue()));
                }
                long countyCount = itemBizRegionEntity.stream().filter(item -> item.getType() == 3 && Objects.equals(item.getPid(), cityBizRegionEntity.getId())).count();
                if (countyCount > 0) {
                    continue;
                }
                long cityCount = itemBizRegionEntity.stream().filter(item -> item.getType() == 2 && Objects.equals(item.getPid(), cityBizRegionEntity.getPid())).count();
                if (cityCount > 0) {
                    dotIds.remove(entry.getKey());
                    break;
                }
            }
        }

        //获取网点主键集合
        Set<Integer> dotIdList = new HashSet<>(dotIds);
        if (dotIdList.size() > 12) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "相同地市区域网点数量多余12个");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：相同品牌和地市区域网点数量大于12个", userId);
            return R.error("相同品牌和地市区域网点数量大于12个");
        }
        //根据网点计算评分 大 -> 小 ，查询网点
        List<Map<String, Object>> dotMapInfo = autoSendMapper.selectdotCountScorelist(dotIdList);
        List<Float> sendValueList = new ArrayList<>();
        R result = null;
        Map<String, String> errorMsg = new HashMap<>();
        //如果只有一个网点就派给他
        if (dotMapInfo.size() == 1) {
            Map<String, Object> dotMap = dotMapInfo.get(0);
            //校验网点你品牌和网点派单上限，如果符合才会添加到sendValueList集合
            if (checkDotBradAndCeiling((Integer) dotMap.get("dotId"), brandId, areaId, (String) dotMap.get("name"), !isPresurvey, template.getServiceTypeEnum(), errorMsg)) {
                result = sendDot(worderNo, worderId.longValue(), dotMapInfo, periodSum, user.getUsername(), userId, areaId, false, brandId, worderType == 6 ? 4 : isPresurvey ? 5 : 1, beforeMsg);
            } else {
                String message = getDotAutoSendMessage(errorMsg);
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：" + beforeMsg + message, userId);
                return R.error("自动派单失败：" + message);
            }
        } else if (dotMapInfo.size() > 1) {
            //查询评分表重新根据规则进行评分
            // 网点业绩得分优先按照这个表的品牌+网点取分，如果品牌存在，没有网点的的按0分计算，如果品牌不存在的去网点表的得分
            dotMapInfo = handleDotScore(dotMapInfo, brandId, cityBizRegionEntity.getId());

            //如果网点数量大于12个直接手动派单
            int dotNum = dotMapInfo.size();
            //如果是上海特斯拉的需要定制比例
            String ratio = "";
            if (brandId == 14 && cityBizRegionEntity.getId() == 105L) {
                ratio = dotDispatchMapper.selectDispatchRatio(dotNum, cityBizRegionEntity.getId(), brandId);
                if (StringUtils.isBlank(ratio)) {
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：品牌：特斯拉，网点数量：" + dotNum + "未查询到派单比例", userId);
                    return R.error("失败原因：" + beforeMsg + "品牌：特斯拉，网点数量：" + dotNum + "未查询到派单比例");
                }
            } else {
                //根据网点数量和品牌获取匹配的网点规则比例 x:x:x...
                ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, brandId);
                if (StringUtils.isBlank(ratio)) {
                    ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, -1);
                }
            }

            if (StringUtils.isBlank(ratio)) {
                log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "未配置相应网点数量比例规则");
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：未配置相应网点数量比例规则", userId);
                return R.error("未配置相应网点数量比例规则");
            }

            //查询该品牌，工单的地市，周期内，指定品牌自动派单的数量
            List<SendWorderRecord> totalSendRecords = sendWorderRecordService.selectCycleDotSendList(dotIdList, cityBizRegionEntity.getId().intValue(), brandId, cycleTime, worderType == 6 ? 4 : isPresurvey ? 5 : 1);
            //兰山区根据区查询派单量
            if (areaId.equals(1669)) {
                totalSendRecords = sendWorderRecordService.selectCycleDotSendListByArea(dotIdList, areaId, brandId, cycleTime, worderType == 6 ? 4 : isPresurvey ? 5 : 1);
            }
            //按照网点分组
            Map<Integer, Long> dotCountMap = totalSendRecords.stream().collect(Collectors.groupingBy(item -> item.getAcceptWorderUser().intValue(), Collectors.counting()));

            //获取所有网点对应数量的总和
            int dotCount = totalSendRecords.size();
            String[] numArray = ratio.split(":");
            float sunCount = 0f;

            //如果有网点评分一样，以最高比例计算
            for (int i = 0; i < dotMapInfo.size() - 1; i++) {
                Map<String, Object> dotInfoOne = dotMapInfo.get(i);
                Float current = Float.valueOf(dotInfoOne.get("score").toString());
                Map<String, Object> dotInfoTwo = dotMapInfo.get(i + 1);
                Float next = Float.valueOf(dotInfoTwo.get("score").toString());
                if (Math.abs(current - next) == 0) {
                    numArray[i + 1] = numArray[i];
                }
            }

            //计算网点比例之和
            for (String proportion : numArray) {
                sunCount += Float.parseFloat(proportion);
            }
            //保留的网点ID集合
            Set<Integer> keepDotId = new HashSet<>();
            //计算派单值
            for (int i = 0; i < dotMapInfo.size(); i++) {
                Map<String, Object> dotMapEntity = dotMapInfo.get(i);
                //新的计算网点派单值 = （当前网点的比例值/网点比例值之和）x (当月区域下网点所有派单数量之和 + 1) - (当月区域当前网点派单数量count)
                int dotId = (Integer) dotMapEntity.get("dotId");
                Float sendValue = (Float.parseFloat(numArray[i]) / sunCount) * (dotCount + 1) - (dotCountMap.get(dotId) != null ? dotCountMap.get(dotId) : 0);
                log.info("==========网点：" + dotId + "=====,派单值：" + sendValue + "=");
                //校验网点你品牌和网点派单上限，如果符合才会添加到sendValueList集合
                if (checkDotBradAndCeiling(dotId, brandId, areaId, (String) dotMapEntity.get("name"), !isPresurvey, template.getServiceTypeEnum(), errorMsg)) {
                    sendValueList.add(sendValue);
                    keepDotId.add(dotId);
                }
            }

            String message = getDotAutoSendMessage(errorMsg);
            if (sendValueList.isEmpty() || keepDotId.isEmpty()) {
                log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, message);
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：" + message, userId);
                return R.error("自动派单失败：" + beforeMsg + message);
            }

            //值计算出来以后，由高往低排序
            Collections.sort(sendValueList);
            Collections.reverse(sendValueList);
            //获取最大值
            Float maxValue = sendValueList.get(0);
            int memeber = 0;
            //获取和最大值相匹配的网点
            for (int i = 0; i < dotMapInfo.size(); i++) {
                Map<String, Object> dotMapEntity = dotMapInfo.get(i);
                //新的计算网点派单值 = （当前网点的比例值/网点比例值之和）x (当月区域下网点所有派单数量之和 + 1) - (当月区域当前网点派单数量count)
                int dotId = (Integer) dotMapEntity.get("dotId");
                Float sendValue = (Float.parseFloat(numArray[i]) / sunCount) * (dotCount + 1) - (dotCountMap.get(dotId) != null ? dotCountMap.get(dotId) : 0);
                //当前网点的判断值等于最大判断值并且当前网点的ID在保留网点集合中
                if (Math.abs(maxValue - sendValue) == 0 && keepDotId.contains(dotId)) {
                    memeber = i;
                    break;
                }
            }
            //派单
            result = ((AutoSendServiceImpl) AopContext.currentProxy()).sendDot(worderNo, worderId.longValue(), dotMapInfo, periodSum, user.getUsername(),
                    memeber, userId, areaId, false, brandId, worderType == 6 ? 4 : isPresurvey ? 5 : 1, beforeMsg);
        }
        log.info("工单号：" + worderNo + "网点派单结果：" + JSON.toJSONString(result));
        if (result != null && (Integer) result.get("code") == 0) {
            return R.ok("自动派单成功");
        } else {
            log.info("=================工单号：{}=自动派单失败===============", worderNo);
            if (result != null) {
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：自动派单失败", "失败原因：" + beforeMsg + result.get("msg"), userId);
            }
            return R.error((result != null ? result.get("msg").toString() : "更新网点派单信息失败"));
        }
    }

    /**
     * 自动派单给服务经理和自动派单给网点
     *
     * @param username
     * @param worderNo
     * @return
     */
    @Override
    public Results autoSendMagrDot(String username, String worderNo) {
        log.info("=========================工单号：{}=自动派单开始====================================", worderNo);
        //查看自动派单总开关状态
        int state = autoSendMapper.selectAutoSendStatus("Auto_Send_Single");
        if (state == 2) {
            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单开关已经关闭,请手动派单");
            saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：自动派单失败", "失败原因：自动派单开关已经关闭", null);
            throw new RRException("自动派单开关已经关闭,请手动派单");
        }
        try {
            //根据工单关联的areaid(地区id)和品牌id,关联查出唯一的服务经理
            Map<String, Object> areaBrand = autoSendMapper.selectAreaBrand(worderNo);
            //工单主键
            Long worderId = Long.parseLong(areaBrand.get("worder_id").toString());
            //区域ID
            Integer areaId = Integer.valueOf(areaBrand.get("areaId").toString());
            //客户联系电话
            String userPhone = (String) areaBrand.get("userPhone");
            //客户邮箱
            String postcode = (String) areaBrand.get("postcode");
            //工单类型
            Integer worderType = (Integer) areaBrand.get("worderType");

            //获取工单模版
            WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(Integer.valueOf(areaBrand.get("templateId").toString()));

            //品牌ID
            Integer brandId = Integer.parseInt(template.getBrandId());

            //校验当前工单是否是特斯拉勘安
            boolean isTslKn = getIsTslKn(brandId, worderType);

            boolean isPresurvey = false;
            //在已满足特斯拉勘安的工单情况下，判断是否为预勘测单
            if (isTslKn) {
                isPresurvey = getIsPresurvey(worderNo);
            }

            if (isTslKn && !isPresurvey) {
            } else if (template.getAutoSend() == 1 && (worderType != 6 || isPresurvey)) {
                log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "该工单模版不支持自动派单,请手动派单");
                saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：自动派单失败", "失败原因：该工单模版不支持自动派单,请手动派单", null);
                return Results.message(10, "该工单模版不支持自动派单,请手动派单", null);
            }
            if (!StringUtils.isEmpty(areaId) && !StringUtils.isEmpty(brandId)) {
                R sendManagerResult = null;
                /*
                 * 首先先对服务经理派单
                 */
                Integer userId = null;
                try {
                    //查出服务经理id
                    List<Integer> userIds = sendOrderService.getManager(areaId, brandId, template.getServiceTypeEnum());
                    if (Objects.nonNull(userIds) && userIds.size() == 1) {
                        userId = userIds.get(0);
                    } else if (Objects.nonNull(userIds) && userIds.size() > 1) {
                        List<String> names = sysUserDao.selectEmployeeUserIds(userIds);
                        saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：服务经理自动派单失败", "失败原因：相同区域，服务类型和品牌存在有多个服务经理[" + names + "]", null);
                        return Results.message(3, "相同区域，服务类型和品牌存在有多个服务经理[" + names + "]", null);
                    }
                } catch (Exception e) {
                    log.error("数据库连接异常，自动派单失败,worderNo:{}", worderNo, e);
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：服务经理自动派单失败", "失败原因：数据库连接异常，自动派单失败", null);
                    return Results.message(3, "数据库连接异常，自动派单失败", null);
                }
                if (StringUtils.isEmpty(userId) || userId == 0) {
                    saveWorderRemark(worderNo, worderNo + "分配中，子状态：服务经理自动派单失败", "失败原因：无关联的服务经理", null);
                    return Results.message(3, "无关联的服务经理", null);
                }
                //自动派单给服务经理
                sendManagerResult = ((AutoSendServiceImpl) AopContext.currentProxy()).sendManager(worderNo, worderId, userId, username, areaId, brandId);
                if (sendManagerResult == null || (Integer) sendManagerResult.get("code") != 0) {
                    saveWorderRemark(worderNo, worderNo + "分配中，子状态：服务经理自动派单失败", "失败原因：服务经理自动派单失败", null);
                    return Results.message(3, "服务经理自动派单失败", null);
                }

                WorderInformationEntity worderInformationEntity = null;
                //维修单逻辑和特斯拉非预勘测单逻辑
                String beforeMsg = "";
                if (worderType == 6 || (isTslKn && !isPresurvey)) {
                    if (worderType == 6) {
                        Results results = getWorderInfoWXu(worderNo, userPhone);
                        //未查询到安装完成工单并且该工单模版不支持自动派单
                        if (results.getCode() != 0 && template.getAutoSend() == 1) {
                            saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：维修单自动派单失败", "失败原因：分别根据手机号码，桩编码，车架号未查询到安装完成工单，并且工单模版不支持自动派单", null);
                            return Results.message(10, "分别根据手机号码，桩编码，车架号未查询到安装完成工单，并且工单模版不支持自动派单", null);
                        } else if (results.getCode() == 0) {
                            worderInformationEntity = (WorderInformationEntity) results.getData();
                        }
                    } else {
                        //查询关联的勘测工单
                        Results presurveyResults = getPresurveyWorder(userPhone, postcode, brandId);
                        if (presurveyResults.getCode() != 0 && template.getAutoSend() == 1) {
                            saveWorderRemark(worderNo, worderNo + "主状态：分配中，子状态：特斯拉非预勘测自动派单失败", "失败原因：根据工单的客户联系电话和邮箱未查询到预勘测单信息，并且该工单模版不支持自动派单", null);
                            return Results.message(10, "根据工单的客户联系电话和邮箱未查询到预勘测单信息，并且该工单模版不支持自动派单", null);
                        } else if (presurveyResults.getCode() == 0) {
                            worderInformationEntity = (WorderInformationEntity) presurveyResults.getData();
                            //取消预勘测工单
                            cancelPresurveyWorder(worderInformationEntity);
                            //修改工单扩展字段
                            updateWorderExtField(worderNo, worderInformationEntity);
                            //插入一条工单转正标识记录
                            insertWorderAttr(worderId);
                        }
                    }

                    if (worderInformationEntity != null && worderInformationEntity.getDotId() != null) {
                        //根据网点ID查询网点信息
                        DotInformationEntity dotInformationEntity = dotInformationDao.selectById(worderInformationEntity.getDotId());
                        //网点有效状态，就派单给当前这个网点，不记录派单次数
                        if (dotInformationEntity.getDotState() == 1) {
                            List<Map<String, Object>> dotMapInfo = new ArrayList<>();
                            Map<String, Object> dotMap = new HashMap<>();
                            dotMap.put("dotId", dotInformationEntity.getDotId());
                            dotMap.put("name", dotInformationEntity.getDotName());
                            dotMap.put("score", dotInformationEntity.getDotScore());
                            dotMap.put("count", dotInformationEntity.getCount());
                            dotMapInfo.add(dotMap);
                            Map<String, Object> periodSum = autoSendMapper.selectPeriodSumInfo(1);
                            R result = sendDot(worderNo, worderId, dotMapInfo, periodSum, username, userId, areaId, false, brandId, worderType == 6 ? 4 : 1, "特斯拉预勘测单匹配网点成功，");
                            if (result != null && (Integer) result.get("code") == 0) {
                                return Results.message(0, "自动派单成功", null);
                            } else {
                                log.info("=================工单号：{}=自动派单失败===============", worderNo);
                                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：自动派单失败", "失败原因：" + (result != null ? result.get("msg") : "更新网点派单信息失败"), userId);
                                return Results.message(10, "自动派单失败", null);
                            }
                        } else if (template.getAutoSend() == 1) {
                            log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "该工单模版不支持自动派单");
                            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：预勘测单网点：" + dotInformationEntity.getDotName() + " 非有效状态，并且工单模版不支持自动派单", userId);
                            return Results.message(10, "该工单模版不支持自动派单", null);
                        } else {
                            beforeMsg = "预勘测单网点：" + dotInformationEntity.getDotName() + " 非有效状态";
                        }
                    }
                }

                /*
                 * 对网点进行派单
                 */
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                //获取派单的周期记录
                Map<String, Object> periodSum = autoSendMapper.selectPeriodSumInfo(1);
                //获取网点配置的周期
                String cycle = sysDictionaryService.selectDictionaryByNumber("dot_send_cycle");
                Date cycleTime = autoSendMapper.selectCycleTime(cycle);
                long nowTime = Long.parseLong(df.format(cycleTime.getTime()));
                Date periodTime = (Date) periodSum.get("timeStart");
                long startTime = Long.parseLong(df.format(periodTime));
                //计算当前配置周期时间是否超过period_sum表里周期时间如果超过重置时间和派单总计
                if (nowTime > startTime) {
                    //重置时间为当前周期的时间
                    autoSendMapper.resetCountTime();
                    //重置网点的派单数量count=0
                    autoSendMapper.updateDotInformation();
                }

                //获取该工单的区县信息
                BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
                if (bizRegionEntity == null) {
                    log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败工单区域未获取到区县信息");
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据工单区域未获取到区县信息", userId);
                    return Results.message(10, "自动派单失败根据工单区域未获取到地市信息", null);
                }

                //获取工单的地市信息
                BizRegionEntity cityBizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(bizRegionEntity.getPid());
                if (cityBizRegionEntity == null) {
                    log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败工单区域未获取到地市信息");
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据工单区域未获取到地市信息", userId);
                    return Results.message(10, "自动派单失败根据工单区域未获取到地市信息", null);
                }

                //查询网点信息
                List<Integer> dotIds = sendOrderService.selectDot(bizRegionEntity.getRegcode(), brandId, template.getServiceTypeEnum());
                if (dotIds == null || dotIds.isEmpty()) {
                    log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息");
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息", userId);
                    return Results.message(10, "自动派单失败根据品牌，地市和服务类型，未查询到符合的网点信息", null);
                }

                //剔除不符合的网点
                List<DotArea> dotAreas = dotAreaService.selectListByDotsBrand(dotIds, brandId, template.getServiceTypeEnum());

                if (dotAreas != null && !dotAreas.isEmpty()) {
                    //根据网点分组
                    Map<Integer, List<DotArea>> dotAreaGroupMap = dotAreas.stream().collect(Collectors.groupingBy(DotArea::getDotId));
                    //遍历Map
                    for (Map.Entry<Integer, List<DotArea>> entry : dotAreaGroupMap.entrySet()) {
                        //查询工单的地市是否匹配
                        List<DotArea> dotAreaList = entry.getValue();
                        // 判断一: 拿工单地市ID和该网点下关联的区域ID进行匹配。
                        long count = dotAreaList.stream().filter(item -> item.getAreaId() == cityBizRegionEntity.getId().intValue()).count();
                        if (count > 0) {
                            continue;
                        }
                        // 判断二： 查询这个网点下关联的有没有该工单的省，如果有省，在看有没有绑定该省下的地市
                        List<BizRegionEntity> itemBizRegionEntity = new ArrayList<>();
                        for (DotArea dotArea : dotAreaList) {
                            itemBizRegionEntity.add(InitRegionUtil.REGION_ID_MAP.get(dotArea.getAreaId().longValue()));
                        }
                        long countyCount = itemBizRegionEntity.stream().filter(item -> item.getType() == 3 && Objects.equals(item.getPid(), cityBizRegionEntity.getId())).count();
                        if (countyCount > 0) {
                            continue;
                        }
                        long cityCount = itemBizRegionEntity.stream().filter(item -> item.getType() == 2 && Objects.equals(item.getPid(), cityBizRegionEntity.getPid())).count();
                        if (cityCount > 0) {
                            dotIds.remove(entry.getKey());
                            break;
                        }
                    }
                }

                //获取网点主键集合
                Set<Integer> dotIdList = new HashSet<>(dotIds);
                if (dotIdList.size() > 12) {
                    log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "相同地市区域网点数量多余12个");
                    saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：相同品牌和地市区域网点数量大于12个", userId);
                    return Results.message(3, "相同品牌和地市区域网点数量大于12个", null);
                }
                //根据网点计算评分 大 -> 小 ，查询网点
                List<Map<String, Object>> dotMapInfo = autoSendMapper.selectdotCountScorelist(dotIdList);
                List<Float> sendValueList = new ArrayList<>();
                R result = null;
                Map<String, String> errorMsg = new HashMap<>();
                //如果只有一个网点就派给他
                if (dotMapInfo.size() == 1) {
                    Map<String, Object> dotMap = dotMapInfo.get(0);
                    //校验网点你品牌和网点派单上限，如果符合才会添加到sendValueList集合
                    if (checkDotBradAndCeiling((Integer) dotMap.get("dotId"), brandId, areaId, (String) dotMap.get("name"), !isPresurvey, template.getServiceTypeEnum(), errorMsg)) {
                        result = sendDot(worderNo, worderId, dotMapInfo, periodSum, username, userId, areaId, false, brandId, worderType == 6 ? 4 : isPresurvey ? 5 : 1, beforeMsg);
                    } else {
                        String message = getDotAutoSendMessage(errorMsg);
                        saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：" + beforeMsg + message, userId);
                        return Results.message(10, "自动派单失败：" + message, null);
                    }
                } else if (dotMapInfo.size() > 1) {
                    //查询评分表重新根据规则进行评分
                    // 网点业绩得分优先按照这个表的品牌+网点取分，如果品牌存在，没有网点的的按0分计算，如果品牌不存在的去网点表的得分
                    dotMapInfo = handleDotScore(dotMapInfo, brandId, cityBizRegionEntity.getId());

                    //如果网点数量大于12个直接手动派单
                    int dotNum = dotMapInfo.size();
                    //如果是上海特斯拉的需要定制比例
                    String ratio = "";
                    if (brandId == 14 && cityBizRegionEntity.getId() == 105L) {
                        ratio = dotDispatchMapper.selectDispatchRatio(dotNum, cityBizRegionEntity.getId(), brandId);
                        if (StringUtils.isBlank(ratio)) {
                            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：品牌：特斯拉，网点数量：" + dotNum + "未查询到派单比例", userId);
                            return Results.message(10, "失败原因：" + beforeMsg + "品牌：特斯拉，网点数量：" + dotNum + "未查询到派单比例", null);
                        }
                        // 如果是理想并且是台州，温州，绍兴市，需查询自定义派单比例
                    } else if (brandId == 18 && (cityBizRegionEntity.getId() == 128L || cityBizRegionEntity.getId() == 121L || cityBizRegionEntity.getId() == 124L)) {
                        ratio = dotDispatchMapper.selectDispatchRatio(dotNum, cityBizRegionEntity.getId(), brandId);
                        if (StringUtils.isBlank(ratio)) {
                            saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：品牌：理想|" + cityBizRegionEntity.getName() + "，网点数量：" + dotNum + "未查询到派单比例", userId);
                            return Results.message(10, "失败原因：" + beforeMsg + "品牌：品牌：理想|" + cityBizRegionEntity.getName() + "，网点数量：" + dotNum + "未查询到派单比例", null);
                        }
                    } else if (brandId == 82 || brandId == 89 || brandId == 90 || brandId == 21 || brandId == 87) {
                        ratio = dotDispatchMapper.selectDispatchRatio(dotNum, cityBizRegionEntity.getId(), brandId);
                        if (StringUtils.isBlank(ratio)) {
                            ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, -1);
                        }
                        // 特斯拉，柳州需要定制比例
                    } else if (brandId == 14 && cityBizRegionEntity.getId() == 251L) {
                        ratio = dotDispatchMapper.selectDispatchRatio(dotNum, cityBizRegionEntity.getId(), brandId);
                        if (StringUtils.isBlank(ratio)) {
                            ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, -1);
                        }
                    } else {
                        //根据网点数量和品牌获取匹配的网点规则比例 x:x:x...
                        ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, brandId);
                        if (StringUtils.isBlank(ratio)) {
                            ratio = dotDispatchMapper.selectDispatchRatioById(dotNum, -1);
                        }
                    }


                    if (StringUtils.isBlank(ratio)) {
                        log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "未配置相应网点数量比例规则");
                        saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：未配置相应网点数量比例规则", userId);
                        return Results.message(3, "未配置相应网点数量比例规则", null);
                    }

                    //查询该品牌，工单的地市，周期内，指定品牌自动派单的数量
                    List<SendWorderRecord> totalSendRecords = sendWorderRecordService.selectCycleDotSendList(dotIdList, cityBizRegionEntity.getId().intValue(), brandId, cycleTime, worderType == 6 ? 4 : isPresurvey ? 5 : 1);

                    //兰山区根据区查询派单量
                    if (areaId.equals(1669)) {
                        totalSendRecords = sendWorderRecordService.selectCycleDotSendListByArea(dotIdList, areaId, brandId, cycleTime, worderType == 6 ? 4 : isPresurvey ? 5 : 1);
                    }

                    //按照网点分组
                    Map<Integer, Long> dotCountMap = totalSendRecords.stream().collect(Collectors.groupingBy(item -> item.getAcceptWorderUser().intValue(), Collectors.counting()));
                    for (Integer id : dotIdList) {
                        if (!dotCountMap.containsKey(id)) {
                            dotCountMap.put(id, 0L);
                        }
                    }

                    //获取所有网点对应数量的总和
                    int dotCount = totalSendRecords.size();
                    String[] numArray = ratio.split(":");
                    float sunCount = 0f;

                    //如果有网点评分一样，以最高比例计算
                    for (int i = 0; i < dotMapInfo.size() - 1; i++) {
                        Map<String, Object> dotInfoOne = dotMapInfo.get(i);
                        Float current = Float.valueOf(dotInfoOne.get("score").toString());
                        Map<String, Object> dotInfoTwo = dotMapInfo.get(i + 1);
                        Float next = Float.valueOf(dotInfoTwo.get("score").toString());
                        if (Math.abs(current - next) == 0) {
                            numArray[i + 1] = numArray[i];
                        }
                    }

                    //计算网点比例之和
                    for (String proportion : numArray) {
                        sunCount += Float.parseFloat(proportion);
                    }
                    //保留的网点ID集合
                    Set<Integer> keepDotId = new HashSet<>();
                    //计算派单值
                    for (int i = 0; i < dotMapInfo.size(); i++) {
                        Map<String, Object> dotMapEntity = dotMapInfo.get(i);
                        //新的计算网点派单值 = （当前网点的比例值/网点比例值之和）x (当月区域下网点所有派单数量之和 + 1) - (当月区域当前网点派单数量count)
                        int dotId = (Integer) dotMapEntity.get("dotId");
                        Float sendValue = (Float.parseFloat(numArray[i]) / sunCount) * (dotCount + 1) - (dotCountMap.get(dotId) != null ? dotCountMap.get(dotId) : 0);
                        log.info("==========网点：" + dotId + "=====,派单值：" + sendValue + "=");
                        //校验网点你品牌和网点派单上限，如果符合才会添加到sendValueList集合
                        if (checkDotBradAndCeiling(dotId, brandId, areaId, (String) dotMapEntity.get("name"), !isPresurvey, template.getServiceTypeEnum(), errorMsg)) {
                            sendValueList.add(sendValue);
                            keepDotId.add(dotId);
                        }
                    }

                    String message = getDotAutoSendMessage(errorMsg);
                    if (sendValueList.isEmpty() || keepDotId.isEmpty()) {
                        log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, message);
                        saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：网点自动派单失败", "失败原因：" + message, userId);
                        return Results.message(10, "自动派单失败：" + beforeMsg + message, null);
                    }

                    //值计算出来以后，由高往低排序
                    Collections.sort(sendValueList);
                    Collections.reverse(sendValueList);
                    //获取最大值
                    Float maxValue = sendValueList.get(0);
                    int memeber = 0;
                    //获取和最大值相匹配的网点
                    for (int i = 0; i < dotMapInfo.size(); i++) {
                        Map<String, Object> dotMapEntity = dotMapInfo.get(i);
                        //新的计算网点派单值 = （当前网点的比例值/网点比例值之和）x (当月区域下网点所有派单数量之和 + 1) - (当月区域当前网点派单数量count)
                        int dotId = (Integer) dotMapEntity.get("dotId");
                        Float sendValue = (Float.parseFloat(numArray[i]) / sunCount) * (dotCount + 1) - (dotCountMap.get(dotId) != null ? dotCountMap.get(dotId) : 0);
                        //当前网点的判断值等于最大判断值并且当前网点的ID在保留网点集合中
                        if (Math.abs(maxValue - sendValue) == 0 && keepDotId.contains(dotId)) {
                            memeber = i;
                            break;
                        }
                    }
                    //派单
                    result = ((AutoSendServiceImpl) AopContext.currentProxy()).sendDot(worderNo, worderId, dotMapInfo, periodSum, username,
                            memeber, userId, areaId, false, brandId, worderType == 6 ? 4 : isPresurvey ? 5 : 1, beforeMsg);
                }
                log.info("工单号：" + worderNo + "网点派单结果：" + JSON.toJSONString(result));
                if (result != null && (Integer) result.get("code") == 0) {
                    return Results.message(0, "自动派单成功", null).putDotId((Integer) result.get("dotId"));
                } else {
                    log.info("=================工单号：{}=自动派单失败===============", worderNo);
                    if (result != null) {
                        saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：自动派单失败", "失败原因：" + beforeMsg + result.get("msg"), userId);
                    }
                    return Results.message(10, "自动派单失败", null);
                }
            } else {
                log.info("=================工单号：{}=自动派单失败：{}===============", worderNo, "工单的品牌和区域参数不能为空");
                saveWorderRemark(worderNo, worderNo + "主状态：分配中 子状态：服务经理自动派单失败", "失败原因：工单的品牌和区域参数不能为空", null);
                return Results.message(100, "工单的品牌和区域参数不能为空", null);
            }
        } catch (Exception e) {
            log.error("=================工单号：{}=自动派单异常：{}===============", worderNo, e.getMessage());
            log.error("worderNo:{}派单失败:", worderNo, e);
            return Results.message(110, "自动派单接口异常", null);
        }
    }

    private void insertWorderAttr(Long worderId) {
        LambdaQueryWrapper<WorderInformationAttribute> worderInformationAttributeWrapper = Wrappers.lambdaQuery();
        worderInformationAttributeWrapper.eq(WorderInformationAttribute::getWorderId, worderId.intValue())
                .eq(WorderInformationAttribute::getAttribute, "presurvey")
                .eq(WorderInformationAttribute::getAttributeCode, "presurvey-formal");
        worderInformationAttributeMapper.delete(worderInformationAttributeWrapper);

        worderInformationAttributeMapper.insert(new WorderInformationAttribute(worderId.intValue(), "presurvey", "presurvey-formal", "预勘测工单转正标识", "Y", 0));
    }

    private void updateWorderExtField(String worderNo, WorderInformationEntity worderInformationEntity) {
        LambdaUpdateWrapper<WorderExtFieldEntity> worderExtFieldWrapper1 = Wrappers.lambdaUpdate();
        worderExtFieldWrapper1.set(WorderExtFieldEntity::getFieldValue, worderInformationEntity.getCompanyOrderNumber())
                .eq(WorderExtFieldEntity::getFieldId, 1221)
                .eq(WorderExtFieldEntity::getWorderNo, worderNo);
        worderExtFieldDao.update(null, worderExtFieldWrapper1);

        LambdaUpdateWrapper<WorderExtFieldEntity> worderExtFieldWrapper2 = Wrappers.lambdaUpdate();
        worderExtFieldWrapper2.set(WorderExtFieldEntity::getFieldValue, "是")
                .eq(WorderExtFieldEntity::getFieldId, 1225)
                .eq(WorderExtFieldEntity::getWorderNo, worderNo);
        worderExtFieldDao.update(null, worderExtFieldWrapper2);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date createTime = worderInformationEntity.getCreateTime();
        LambdaUpdateWrapper<WorderExtFieldEntity> worderExtFieldWrapper3 = Wrappers.lambdaUpdate();
        worderExtFieldWrapper3.set(WorderExtFieldEntity::getFieldValue, simpleDateFormat.format(createTime))
                .eq(WorderExtFieldEntity::getFieldId, 1223)
                .eq(WorderExtFieldEntity::getWorderNo, worderNo);
        worderExtFieldDao.update(null, worderExtFieldWrapper3);
    }

    /**
     * 取消预勘测工单
     *
     * @param worderInformationEntity
     */
    private void cancelPresurveyWorder(WorderInformationEntity worderInformationEntity) {
        remarkLogMapper.addOperationLog(worderInformationEntity.getWorderNo(), "预勘测单取消", "预勘测单取消", 1);
        remarkLogMapper.updateWordeInfoState(worderInformationEntity.getWorderId(), 5, "勘测单取消", 21, "取消服务", 6);
    }

    /**
     * 根据手机号码，邮箱，品牌，查询两年内勘安的预勘测工单
     *
     * @param userPhone
     * @param postcode
     * @param brandId
     * @return
     */
    private Results getPresurveyWorder(String userPhone, String postcode, Integer brandId) {
        //先根据手机号码查询，查询不到在根据邮箱查询
        WorderInformationEntity worderInformation = null;
        if (StringUtils.isNotBlank(userPhone)) {
            worderInformation = worderInformationDao.getPresurveyWorder(userPhone, null, brandId);
        }
        if (worderInformation == null && StringUtils.isNotBlank(postcode)) {
            worderInformation = worderInformationDao.getPresurveyWorder(null, postcode, brandId);
        }
        if (worderInformation == null) {
            return Results.message(10, "失败", null);
        }
        return Results.message(0, "成功", worderInformation);
    }

    /**
     * 查看当前是否为预勘测工单
     *
     * @param worderNo
     * @return
     */
    private boolean getIsPresurvey(String worderNo) {
        LambdaQueryWrapper<WorderExtFieldEntity> worderExtFieldWrapper = Wrappers.lambdaQuery();
        worderExtFieldWrapper.select(WorderExtFieldEntity::getFieldValue)
                .eq(WorderExtFieldEntity::getWorderNo, worderNo)
                .eq(WorderExtFieldEntity::getFieldId, 1224);
        List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldDao.selectList(worderExtFieldWrapper);
        if (worderExtFieldEntities != null && !worderExtFieldEntities.isEmpty()) {
            WorderExtFieldEntity worderExtFieldEntity = worderExtFieldEntities.get(0);
            return "是".equals(worderExtFieldEntity.getFieldValue());
        }
        return false;
    }

    /**
     * 判断当前是否为特斯拉勘安工单
     *
     * @param brandId
     * @param worderType
     * @return
     */
    private boolean getIsTslKn(Integer brandId, Integer worderType) {
        return brandId.equals(14) && worderType.equals(5);
    }

    /**
     * 根据维修类工单查询关联的安装单
     */
    private Results getWorderInfoWXu(String worderNo, String userPhone) {
        WorderInformationEntity worderInformation;
        // 根据联系电话查询关联安装的工单
        if (StringUtils.isNotBlank(userPhone)) {
            LambdaQueryWrapper<WorderInformationEntity> worderInformationWrapper = Wrappers.lambdaQuery();
            worderInformationWrapper.select(WorderInformationEntity::getWorderNo, WorderInformationEntity::getPmId, WorderInformationEntity::getDotId)
                    .gt(WorderInformationEntity::getCreateTime, new SimpleDateFormat("yyyy-MM-dd").format(DateUtils.addYears(new Date(), -2)))
                    .in(WorderInformationEntity::getWorderExecStatus, 17, 22)
                    .in(WorderInformationEntity::getWorderTypeId, 2, 5)
                    .eq(WorderInformationEntity::getIsDelete, 0)
                    .eq(WorderInformationEntity::getUserPhone, userPhone)
                    .ne(WorderInformationEntity::getWorderNo, worderNo)
                    .orderByDesc(WorderInformationEntity::getCreateTime)
                    .last("limit 1");
            worderInformation = worderInformationDao.selectOne(worderInformationWrapper);
            if (worderInformation != null) {
                return Results.message(0, "成功", worderInformation);
            }
        }
        //根据当前工单号查询桩编码和车架号
        LambdaQueryWrapper<WorderExtFieldEntity> worderExtFieldWrapper = Wrappers.lambdaQuery();
        worderExtFieldWrapper.eq(WorderExtFieldEntity::getWorderNo, worderNo)
                .in(WorderExtFieldEntity::getFieldId, 153, 950, 1161, 1162, 1232);

        List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldDao.selectList(worderExtFieldWrapper);
        if (worderExtFieldEntities == null || worderExtFieldEntities.isEmpty()) {
            return Results.message(10, "", null);
        }

        ArrayList<Integer> codesFields = new ArrayList<>();
        codesFields.add(950);
        codesFields.add(1232);
        //获取桩编码并且查询工单
        List<String> codes = worderExtFieldEntities.stream().filter(item -> ((item.getFieldId() != null && codesFields.contains(item.getFieldId())) && StringUtils.isNotBlank(item.getFieldValue()))).map(WorderExtFieldEntity::getFieldValue).collect(Collectors.toList());
        if (!codes.isEmpty()) {
            //根据充电桩编码查询安装中工单
            worderInformation = worderExtFieldDao.selectWorderInfoByFields(codesFields, worderNo, codes);
            if (worderInformation != null) {
                return Results.message(0, "成功", worderInformation);
            }
        }

        ArrayList<Integer> frameNumsFields = new ArrayList<>();
        frameNumsFields.add(153);
        frameNumsFields.add(1161);
        frameNumsFields.add(1162);
        //获取车架号并且查询工单
        List<String> frameNums = worderExtFieldEntities.stream().filter(item -> ((item.getFieldId() != null && frameNumsFields.contains(item.getFieldId())) && StringUtils.isNotBlank(item.getFieldValue()))).map(WorderExtFieldEntity::getFieldValue).collect(Collectors.toList());
        if (!frameNums.isEmpty()) {
            //根据充电桩编码查询安装中工单
            worderInformation = worderExtFieldDao.selectWorderInfoByFields(frameNumsFields, worderNo, frameNums);
            if (worderInformation != null) {
                return Results.message(0, "成功", worderInformation);
            }
        }
        return Results.message(10, "", null);
    }

    /**
     * 获取网点自动派单失败原因
     *
     * @param errorMsg
     * @return
     */
    private String getDotAutoSendMessage(Map<String, String> errorMsg) {
        if (errorMsg == null) {
            return "网点品牌，辐射区域不符合或者该网点已达到派单上限";
        }
        StringBuilder context = new StringBuilder("");
        errorMsg.forEach((k, v) -> {
            context.append(k).append(",").append(v).append(";");
        });
        return context.toString();
    }

    /**
     * 从评分表获取评分并且赋值给网点
     *
     * @param dotMapInfo
     * @param brandId
     * @return
     */
    private List<Map<String, Object>> handleDotScore(List<Map<String, Object>> dotMapInfo, Integer brandId, Long cityId) {
        if (dotMapInfo.isEmpty()) {
            return dotMapInfo;
        }
        //根据品牌查询评分表
        LambdaQueryWrapper<BrandDotScoreBean> branddotScoreWrapper = Wrappers.lambdaQuery();
        branddotScoreWrapper.select(BrandDotScoreBean::getId, BrandDotScoreBean::getBrandId, BrandDotScoreBean::getDotId, BrandDotScoreBean::getScore, BrandDotScoreBean::getManualScore);
        //这里业绩评分查询需要增加周期
        LocalDate currentDate = LocalDate.now();
        LocalDate previousMonth = currentDate.minusMonths(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String formattedDate = previousMonth.format(formatter);
        branddotScoreWrapper.eq(BrandDotScoreBean::getBrandId, brandId)
                .eq(BrandDotScoreBean::getDeleteState, 0)
                .eq(BrandDotScoreBean::getCycle, formattedDate)
                .eq(BrandDotScoreBean::getCity, cityId);
        List<BrandDotScoreBean> brandDotScoreBeans = brandDotScoreMapper.selectList(branddotScoreWrapper);
        if (brandDotScoreBeans.isEmpty()) {
            return dotMapInfo;
        }
        //根据网点分组获取每个网点的评分
        Map<Integer, BigDecimal> dotScoreMap = brandDotScoreBeans.stream().collect(Collectors.toMap(BrandDotScoreBean::getDotId, BrandDotScoreBean::getScore, (val1, val2) -> val1));
        //手动综合评分map
        Map<Integer, BigDecimal> dotManualScoreMap = brandDotScoreBeans.stream().collect(Collectors.toMap(BrandDotScoreBean::getDotId, BrandDotScoreBean::getManualScore, (val1, val2) -> val1));

        //遍历查询到的网点信息
        for (Map<String, Object> dotMap : dotMapInfo) {
            Integer dotId = (Integer) dotMap.get("dotId");
            //根据网点ID查询评分是否存在
            BigDecimal newScore = dotManualScoreMap.get(dotId);
            if (newScore != null && newScore.compareTo(BigDecimal.ZERO) > 0) {
                dotMap.put("score", newScore.doubleValue());
            } else if (dotScoreMap.containsKey(dotId)) {
                newScore = dotScoreMap.get(dotId);
                dotMap.put("score", newScore.doubleValue());
            } else {
                dotMap.put("score", Double.parseDouble("0"));
            }
        }
        //重新排序
        return dotMapInfo.stream().sorted((item, nextItem) -> {
            double score = (double) item.get("score");
            double nextScore = (double) nextItem.get("score");
            return (int) (nextScore - score);
        }).collect(Collectors.toList());
    }

    /**
     * 校验网点的品牌和派单上限
     *
     * @param dotId
     * @param brandId
     * @return
     */
    private boolean checkDotBradAndCeiling(Integer dotId, Integer brandId, Integer areaId, String dotName, boolean checkDotSendLimit, Integer serviceType, Map<String, String> errorMsg) {
        //校验网点是否服务到工单得到区域内
        ArrayList<Integer> dotIds = new ArrayList<>();
        dotIds.add(dotId);
        List<DotArea> dotAreas = dotAreaService.selectListByDotsBrand(dotIds, brandId, serviceType);

        if (!dotAreas.isEmpty()) {
            //查询网点区域所有下级的区域信息
            Set<Integer> dotAreaIds = new HashSet<>();
            userRoleAreaPermissionsService.traverseArea(dotAreas.stream().map(DotArea::getAreaId).collect(Collectors.toList()), dotAreaIds);

            if (!dotAreaIds.contains(areaId)) {
                log.info("============网点ID：{}===网点辐射区域不符合=============", dotId);
                errorMsg.put(dotName, "网点辐射区域不符合");
                return false;
            }
        }
        //如果网点未配置品牌默认有所有品牌权限
        //如果根据品牌和网点，未查询到网点关联的当前品牌，校验不通过
        List<Integer> brandIds = dotInformationDao.selectCountByDotAndBrand(dotId, null);
        if (brandIds != null && !brandIds.isEmpty() && !brandIds.contains(brandId)) {
            log.info("=================网点ID：{}===品牌不符合====================", dotId);
            errorMsg.put(dotName, "品牌不符合");
            return false;
        }

        //如果传了不校验派单上限直接返回true
        if (!checkDotSendLimit) {
            return true;
        }

        //获取配置的服务兵上限的数量
        String ceiling = sysDictionaryService.selectDictionaryByNumber("attendant_sendCeiling");
        //获取网点下服务兵数量
        Integer attendantCount = bizAttendantService.getBaseMapper().selectCount(new QueryWrapper<BizAttendantEntity>().eq("dot_id", dotId).eq("attendant_state", 1).eq("attendant_flag", 1));
        //获取网点上限
        int dotCeiling = Integer.parseInt(ceiling) * attendantCount;
        //查询当天网点派单数量
        DotSendsRecord dotSendsRecord = dotSendsRecordService.query().eq("dot_id", dotId).eq("present_date", LocalDate.now()).eq("delete_state", 0).one();
        int count = dotSendsRecord != null && dotSendsRecord.getAutoSendNum() != null ? dotSendsRecord.getAutoSendNum() : 0;
        if (count >= dotCeiling) {
            log.info("=================网点ID：{}===派单已经达到了上限====================", dotId);
            errorMsg.put(dotName, "派单已经达到了上限");
        }
        return count < dotCeiling;
    }

    /**
     * 校验网点的品牌
     *
     * @param dotId
     * @param brandId
     * @return
     */
    private boolean checkDotBrad(Integer dotId, Integer brandId) {
        //如果网点未配置品牌默认有所有品牌权限
        //如果根据品牌和网点，未查询到网点关联的当前品牌，校验不通过
        List<Integer> brandIds = dotInformationDao.selectCountByDotAndBrand(dotId, null);
        if (brandIds != null && !brandIds.isEmpty() && !brandIds.contains(brandId)) {
            log.info("=================品牌不符合====================");
            return false;
        }
        return true;
    }

    @Override
    public Results updateNewStarRecord(String dotStarDesign) {

        DotStarDesign work = JSONObject.parseObject(dotStarDesign, DotStarDesign.class);
        try {
            if (!StringUtils.isEmpty(work)) {
                int index = autoSendMapper.selectNewStarCount(work.getStarName());
                int nums = 0;
                if (index > 0) {
                    nums = autoSendMapper.updataNewStar(work);
                } else {
                    nums = autoSendMapper.addNewStar(work);
                }
                if (nums > 0) {
                    return Results.message(0, "success", null);
                } else {
                    return Results.message(10, "更新或者添加失败", null);
                }
            } else {
                return Results.message(100, "参数不能为空", null);
            }
        } catch (Exception e) {
            return Results.message(110, "更新添加星级评分", null);
        }
    }

    /**
     * 根据区域ID获取所有上级的区域编码
     *
     * @param areaId
     * @return
     */
    public String handleCode(Integer areaId) {
        BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
        String code = bizRegionEntity.getRegcode();
        StringBuilder regcode = new StringBuilder("(");
        regcode.append("'").append(code, 0, 3).append("'");
        if (code.length() > 3) {
            regcode.append(", '").append(code, 0, 6).append("'");
        }
        if (code.length() > 6) {
            regcode.append(", '").append(code, 0, 9).append("'");
        }
        regcode.append(")");
        return regcode.toString();
    }

    /**
     * 保存工单备注
     *
     * @param worderNo
     * @param title
     * @param content
     * @param userId
     */
    public void saveWorderRemark(String worderNo, String title, String content, Integer userId) {
        Date currentDate = new Date();
        // 使用Calendar来增加一秒
        long time = currentDate.getTime();
        if (content.contains("服务经理")) {
            time = time + 1000;
        } else if (content.contains("网点")) {
            time = time + 2000;
        }
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity()
                .setWorderNo(worderNo)
                .setTitle(title)
                .setContent(content)
                .setCreateTime(new Date(time))
                .setUserId(userId != null ? userId.longValue() : null);
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }
}
