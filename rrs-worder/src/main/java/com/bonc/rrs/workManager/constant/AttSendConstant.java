package com.bonc.rrs.workManager.constant;

import java.util.HashMap;
import java.util.Map;

public class AttSendConstant {
    public static final String OVER_FLOW ="attendant_threshold";
    public static final String THRESHOLD_SWITCH ="threshold_switch";
    public static final String AUTH_CODE ="auth_code";

    /**
     * 默认派单策略，如果数据库查询不到使用默认的
     */
    public static final Map<Integer, String> DEFAULT_RULE_MAP = new HashMap<>();

    static {
        DEFAULT_RULE_MAP.put(2, "6:4");
        DEFAULT_RULE_MAP.put(3, "4.5:3.5:2");
        DEFAULT_RULE_MAP.put(4, "4:3:2:1");
        DEFAULT_RULE_MAP.put(5, "3.5:2.5:2:1.5:0.5");
        DEFAULT_RULE_MAP.put(6, "3:2.2:1.8:1.5:1:0.5");
        DEFAULT_RULE_MAP.put(7, "2.5:2.1:1.7:1.4:1:0.8:0.5");
        DEFAULT_RULE_MAP.put(8, "2.3:1.9:1.5:1.3:1:0.9:0.7:0.4");
        DEFAULT_RULE_MAP.put(9, "2.2:1.8:1.4:1.2:1:0.9:0.7:0.5:0.3");
        DEFAULT_RULE_MAP.put(10, "2.1:1.7:1.3:1.1:0.9:0.8:0.7:0.6:0.5:0.3");
        DEFAULT_RULE_MAP.put(11, "2:1.6:1.3:1:0.9:0.8:0.7:0.6:0.5:0.4:0.2");
        DEFAULT_RULE_MAP.put(12, "1.9:1.5:1.2:1:0.:0.8:0.7:0.6:0.5:0.4:0.3:0.1");
    }
}
