package com.bonc.rrs.workManager.service.impl;

import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.bonc.rrs.workManager.dao.SendWorderRecordMapper;
import com.bonc.rrs.workManager.service.SendWorderRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 派单记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Service
@AllArgsConstructor
public class SendWorderRecordServiceImpl extends ServiceImpl<SendWorderRecordMapper, SendWorderRecord> implements SendWorderRecordService {

    final SendWorderRecordMapper sendWorderRecordMapper;

    @Override
    public List<Map<String,Object>> selectCycleDotSendCount(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime) {
        String regcode = handleCode(areaId);
        return sendWorderRecordMapper.selectCycleDotSendCount(dotIdList,regcode,brandId,cycleTime);
    }

    @Override
    public List<SendWorderRecord> selectCycleDotSendList(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime,Integer operationType) {
        return sendWorderRecordMapper.selectCycleDotSendList(dotIdList,areaId,brandId,cycleTime,operationType != null ? operationType : 1);
    }

    @Override
    public List<SendWorderRecord> selectCycleDotSendListByArea(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime,Integer operationType) {
        return sendWorderRecordMapper.selectCycleDotSendListByArea(dotIdList,areaId,brandId,cycleTime,operationType != null ? operationType : 1);
    }

    //处理区域编码
    public String handleCode(Integer areaId){
        BizRegionEntity bizRegionEntity = InitRegionUtil.REGION_ID_MAP.get(areaId.longValue());
        String code = bizRegionEntity.getRegcode();
        String regcode = "(" + code.substring(0, 3);
        if (code.length() > 3) {
            regcode += ", ";
            regcode += code.substring(0, 6);
        }
        if (code.length() > 6) {
            regcode += ", ";
            regcode += code.substring(0, 9);
        }
        regcode += ")";
        return regcode;
    }
}
