package com.bonc.rrs.workManager.service;

import com.bonc.rrs.util.Results;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface SysFilesService {

    Results batchAddFilelist(Integer ObjValue, Integer ObjType, MultipartFile[] Filelist) throws IOException;


    /**
     * 获取一条数据
     *
     * @param fileId
     * @return
     */
    SysFileEntity getSysFileById(String fileId);

    /**
     * 添加一条url
     *
     * @param path
     * @return
     */
    Integer saveSysFilePath(String path, String name);

    Integer saveSysFilePath(String path, String name, String oldName,String md5Str);

    Integer saveFilePath(String path, String name, String oldName,String md5Str);

    Integer saveFile(String path, String name, String oldName,Integer objValue,String md5Str);



    /**
     * 添加一条
     *
     * @param sysFile
     * @return
     */
    Integer saveSysFile(SysFileEntity sysFile);

    List<Map<String,Object>> getUrl(String worderNo,Integer purpose);
    Boolean fingByValue(String worderNo,String name);

    List<SysFileEntity> getAll();

    Integer updateCode(SysFileEntity sysFileEntity);

    R getScoreImportFile();

    List<SysFileEntity> getSysFileByIds(String collect);

    String getPathByName(String name);
}
