package com.bonc.rrs.workManager.service.impl;

import com.bonc.rrs.sparesettlement.config.WordConfigs;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.ScalingUtils;
import com.bonc.rrs.workManager.dao.BrandMapper;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.entity.WorkVo;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.bonc.rrs.workManager.service.WordService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.MiniTableRenderData;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.util.BytePictureUtils;
import com.lowagie.text.Row;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.reflect.FieldUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.shiro.SecurityUtils;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class WordServiceImpl implements WordService {
    @Autowired
    SysFilesService sysFilesService;
    @Autowired(required = false)
    BrandMapper brandMapper;
    @Autowired(required = false)
    SysFilesMapper sysFilesMapper;

    /**
     * 替换勘测文件占位符
     */
    @SneakyThrows
    @Override
    public R substitute(List<Map<String,Object>> images) throws IOException {
        if (images.size() == 0) {
            return R.error("该工单没有图片");
        }
        //替换占位符
        Map<String, ArrayList<String>> datas = new HashMap<String, ArrayList<String>>();
        ArrayList<String> report=new ArrayList<String>();
        ArrayList<String> power=new ArrayList<String>();
        ArrayList<String> location=new ArrayList<String>();
        int line =0;
        ArrayList<String> offer=new ArrayList<String>();

        for (int i = 0; i < images.size(); i++) {
            String field_id = images.get(i).get("field_id").toString();
            //获取图片宽高
            if (field_id.equals("263")) { report.add("{{@" + "report" + i + "}}"); }
            if (field_id.equals("264")) {
                power.add("{{@" + "power" + i + "}}");
            }
            if (field_id.equals("265")) {
                location.add("{{@" + "location" + i + "}}");
            }
            if (field_id.equals("287")) {
                line++;
            }
            if (field_id.equals("266")) {
                offer.add("{{@" + "offer" + i + "}}");
            }
        }

        //替换勘测报告
        datas.put("{{report}}", report);
        //替换勘测电源点
        datas.put("{{power}}", power);
        //安装位置
        datas.put("{{location}}", location);
        //线路走向
        //datas.put("line", line);
        datas.put("{{offer}}",offer);
        //datas.put("offer",offer);

        InputStream inputStream = FieldUtils.class.getClassLoader().getResourceAsStream("勘测报告模板.docx");
        if(inputStream==null){
            return R.error("找不到勘测模板");
        }
        int num=0;
        if(line<=3){
            num+=1;
        }
        if(line<=6&&line>3){
            num+=2;
        }
        if(line>=7){
            num+=3;
        }
        XWPFDocument hwpf = new XWPFDocument(inputStream);
        replaceInAllParagraphs(hwpf.getTables(),datas,"{{#line}}",num,3);
        FileOutputStream out = new FileOutputStream(WordConfigs.getSurveyPath());
        hwpf.write(out);
        out.flush();
        hwpf.close();
        inputStream.close();
        //替换图片
        Map<String, Object> data = new HashMap<String, Object>();
        for (int i=1;i<10;i++){
            data.put("line"+i,null);
        }
        for (int i = 0; i < images.size(); i++) {
            String field_id = images.get(i).get("field_id").toString();
            String path=images.get(i).get("path").toString();
            //获取图片宽高
            BufferedImage bufferedImage = ScalingUtils.getBufferedImage(path);
            if (field_id.equals("263")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("report" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("report" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }
            if (field_id.equals("264")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("power" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("power" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }
            if (field_id.equals("265")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("location" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("location" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }
            if (field_id.equals("287")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                WorkVo workVo = ScalingUtils.zoomByScalePre(transition.getWidth(), transition.getHeight(), path);
                for(int j=1;j<=10;j++){
                    Object val =data.get("line"+j);
                    if(val==null){
                        if(transition.getImage()!=null){
                            data.put("line"+j, new PictureRenderData(workVo.getWidth(), workVo.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                            break;
                        }else{
                            data.put("line"+j, new PictureRenderData(workVo.getWidth(),workVo.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                            break;
                        }
                    }
                }
            }
            if (field_id.equals("266")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);

                if(transition.getImage()!=null){
                    data.put("offer" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("offer" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }
        }
        XWPFTemplate templates = XWPFTemplate.compile(WordConfigs.getSurveyPath())
                .render(data);
        FileOutputStream outs = new FileOutputStream(WordConfigs.getReportPath());
        templates.write(outs);
        outs.flush();
        outs.close();
        templates.close();
        //删除替换中间文件
        File file = new File(WordConfigs.getSurveyPath());
        file.delete();
        return R.ok();
    }
    /**
     * 替换安装文件占位符
     */
    @Override
    public R install(List<Map<String,Object>> images) throws Exception {
        if (images.size() == 0) {
            return R.error("该工单没有图片");
        }
        //替换占位符
        Map<String,Object> maps=new HashMap<>();
        Map<String, ArrayList<String>> datas = new HashMap<String, ArrayList<String>>();
        ArrayList<String > installConfirm=new ArrayList<>();//安装确认单
        ArrayList<String >  offer = new ArrayList<>();//安装报价单
        ArrayList<String >  power =new ArrayList<>();//电源点图
        ArrayList<String > connection =new ArrayList<>();//漏电保护断路器图
        ArrayList<String > photo = new ArrayList<>();//人桩合影
        ArrayList<String > nameplate = new ArrayList<>();//充电器TSN码
        ArrayList<String > green = new ArrayList<>();//绿灯图（绿灯常亮）
        ArrayList<String > recharge = new ArrayList<>();//充电器接线图
        ArrayList<String > stall = new ArrayList<>();//车位图
        ArrayList<String > cable = new ArrayList<>();//线缆米标图
        //String direction = "";//线路走向
        ArrayList<String > other = new ArrayList<>();//其他照片
        ArrayList<String >  breaker=new ArrayList<>();//漏保断路器接线扭矩
        ArrayList<String > electrician=new ArrayList<>();//电工证
        ArrayList<String > Charger=new ArrayList<>();;//铭牌-充电器TSN码（充电器主体背部小铭牌）
        ArrayList<String > install=new ArrayList<>();//安装背板接线扭矩
        ArrayList<String > initialOne=new ArrayList<>();;//初始设置-1.检查软件版本
        ArrayList<String > initialTwo=new ArrayList<>();;//初始设置-2.升级至最新软件版本
        ArrayList<String > initialThree=new ArrayList<>();//初始设置-3.初始设置完成
        ArrayList<String > initialFour=new ArrayList<>();//初始设置-4.Wi-Fi 连接成功子界面
        ArrayList<String > circuit=new ArrayList<>();//断路器合格证
        ArrayList<String > lock=new ArrayList<>();//线缆锁紧头
        ArrayList<String > distribution=new ArrayList<>();;//配电盒、加装电表厂家和型号
        ArrayList<String > Notice=new ArrayList<>();//告知书
        ArrayList<String > soil=new ArrayList<>();//电缆地埋布线
        ArrayList<String > too=new ArrayList<>();//过墙孔穿管
        int direction=0;
        int tem=0;
        for (int i = 0; i < images.size(); i++) {
            String field_id = images.get(i).get("field_id").toString();
            if (field_id.equals("269")) {installConfirm.add("{{@" + "installConfirm" + i +"}}");}//安装确认单
            if (field_id.equals("277")) {offer.add("{{@" + "offer" + i +"}}");}//安装报价单
            if (field_id.equals("270")) {power.add("{{@" + "power" + i +"}}");}//电源点
            if (field_id.equals("271")) {connection.add("{{@" + "connection" + i +"}}");}//漏电保护断路器图
            if (field_id.equals("274")) {photo.add("{{@" + "photo" + i +"}}");}//人桩合影
            if (field_id.equals("1202")) {nameplate.add("{{@" + "nameplate" + i +"}}");}//铭牌-充电器TSN码（外壳侧面铭牌）
            if (field_id.equals("273")) {green.add("{{@" + "green" + i +"}}");}//绿灯图（绿灯常亮）
            if (field_id.equals("272")) {recharge.add("{{@" + "recharge" + i +"}}");}//充电器接线图
            if (field_id.equals("276")) {stall.add("{{@" + "stall" + i +"}}");}//车位图
            if (field_id.equals("278")) {cable.add("{{@" + "cable" + i +"}}");}//线缆米标图开始
            if (field_id.equals("310")) {cable.add("{{@" + "cable" + i +"}}");}//线缆米标图结束
            if(field_id.equals("279")){
                direction++;
            }
            if (field_id.equals("282")) {other.add("{{@" + "other" + i +"}}");}//其他照片
            if (field_id.equals("1151")) {breaker.add("{{@" + "breaker" + i +"}}");}//漏保断路器接线扭矩
            if (field_id.equals("1179")) {breaker .add("{{@" + "breaker" + i +"}}");}//漏保断路器接线扭矩
            if (field_id.equals("1187")) {electrician.add("{{@" + "electrician" + i +"}}");}//电工证
            if (field_id.equals("1203")) {Charger.add("{{@" + "Charger" + i +"}}");}//铭牌-充电器TSN码（充电器主体背部小铭牌）
            if (field_id.equals("1186")) {install.add("{{@" + "install" + i +"}}");}//安装背板接线扭矩
            if (field_id.equals("1198")) {initialOne.add("{{@" + "initialOne" + i +"}}");}//初始设置-1.检查软件版本
            if (field_id.equals("1199")) {initialTwo.add("{{@" + "initialTwo" + i +"}}");}//初始设置-2.升级至最新软件版本
            if (field_id.equals("1200")) {initialThree.add("{{@" + "initialThree" + i +"}}");}///初始设置-3.初始设置完成
            if (field_id.equals("1201")) {initialFour.add("{{@" + "initialFour" + i +"}}");}///初始设置-4.Wi-Fi 连接成功子界面
            if (field_id.equals("1188")) {circuit.add("{{@" + "circuit" + i +"}}");}///断路器合格证
            if (field_id.equals("1190")) {lock.add("{{@" + "lock" + i +"}}");}///线缆锁紧头
            if (field_id.equals("1191")) {distribution.add("{{@" + "distribution" + i +"}}");}///配电盒、加装电表厂家和型号
            if (field_id.equals("285")) {other.add("{{@" + "other" + i +"}}");}///告知书
            if (field_id.equals("1195")) {installConfirm.add( "{{@" + "installConfirm" + i +"}}");}///授权代签证明
            if (field_id.equals("999")) {soil.add("{{@" + "soil" + i +"}}");}///电缆地埋布线
            if (field_id.equals("998")) {too.add("{{@" + "too" + i +"}}");}///电缆穿墙、穿管照片
            if (field_id.equals("280")) {other.add("{{@" + "other" + i +"}}");}///pvc管道细节
            if (field_id.equals("934")) {other.add("{{@" + "other" + i +"}}");}///充电桩包装箱序列号图
            if (field_id.equals("286")) {other.add("{{@" + "other" + i +"}}");}///承诺书*
            if (field_id.equals("1189")) {other.add("{{@" + "other" + i +"}}");}///防撕毁标签
            if (field_id.equals("932")) {other.add("{{@" + "other" + i +"}}");}///充电桩安装告知书
        }
        datas.put("{{too}}",too);
        datas.put("{{soil}}",soil);
        datas.put("{{installConfirm}}",installConfirm);
        datas.put("{{offer}}", offer);
        datas.put("{{power}}", power);
        datas.put("{{connection}}", connection);
        datas.put("{{photo}}", photo);
        datas.put("{{nameplate}}", nameplate);
        datas.put("{{green}}", green);
        datas.put("{{recharge}}", recharge);
        datas.put("{{stall}}", stall);
        datas.put("{{cable}}", cable);
        //datas.put("pvc",pvc);
        //datas.put("direction", direction);
        datas.put("{{other}}", other);
        datas.put("{{breaker}}",breaker);
        datas.put("{{electrician}}",electrician);
        datas.put("{{Charger}}",Charger);
        datas.put("{{install}}",install);
        datas.put("{{initialOne}}",initialOne);
        datas.put("{{initialTwo}}",initialTwo);
        datas.put("{{initialThree}}",initialThree);
        datas.put("{{initialFour}}",initialFour);
        datas.put("{{circuit}}",circuit);
        datas.put("{{distribution}}",distribution);
        datas.put("{{lock}}",lock);
        //datas.put("Notice",Notice);
        /*maps.forEach((v,k)->{
            datas.put(v,k);
        });*/
       /* if(direction<=3){
            RowRenderData row1= RowRenderData.build("{{@direction1}}","{{@direction2}}","{{@direction3}}");
            datas.put("direction",new MiniTableRenderData(Arrays.asList(row1),15.8f));
        }
        if(direction<=6&&direction>3){
            RowRenderData row1= RowRenderData.build("{{@direction1}}","{{@direction2}}","{{@direction3}}");
            RowRenderData row2= RowRenderData.build("{{@direction4}}","{{@direction5}}","{{@direction6}}");
            datas.put("direction",new MiniTableRenderData(Arrays.asList(row1,row2),15.8f));
        }
        if(direction>=7){
            RowRenderData row1= RowRenderData.build("{{@direction1}}","{{@direction2}}","{{@direction3}}");
            RowRenderData row2= RowRenderData.build("{{@direction4}}","{{@direction5}}","{{@direction6}}");
            RowRenderData row3= RowRenderData.build("{{@direction7}}","{{@direction8}}","{{@direction9}}");
            datas.put("direction",new MiniTableRenderData(Arrays.asList(row1,row2,row3),15.8f));
        }*/
        /*RowRenderData row1 = RowRenderData.build("{{@direction1}}","{{@direction2}}","{{@direction3}}");
        RowRenderData row2 = RowRenderData.build("{{@direction4}}","{{@direction5}}","{{@direction6}}");
        RowRenderData row3 = RowRenderData.build("{{@direction7}}","{{@direction8}}","{{@direction9}}");
        datas.put("direction",new MiniTableRenderData(Arrays.asList(row1,row2,row3),15));*/


        InputStream inputStream = FieldUtils.class.getClassLoader().getResourceAsStream("安装报告模板.docx");
        if(inputStream==null){
            return R.error("找不到安装模板");
        }
        int num=0;
        if(direction<=3){
            num+=1;
        }
        if(direction<=6&&direction>3){
            num+=2;
        }
        if(direction>=7){
            num+=3;
        }
        XWPFDocument hwpf = new XWPFDocument(inputStream);

        replaceInAllParagraphs(hwpf.getTables(),datas,"{{#line}}",num,3);
        FileOutputStream out = new FileOutputStream(WordConfigs.replacePath()); //replacePath = /mnt2/file/replace.docx
        hwpf.write(out);
        out.flush();
        hwpf.close();
        inputStream.close();
        //替换图片
        Map<String, Object> data = new HashMap<String, Object>();
        for (int i=1;i<10;i++){
            data.put("line"+i,null);
        }
        for (int i = 0; i < images.size(); i++) {
            String field_id = images.get(i).get("field_id").toString();
            String path = images.get(i).get("path").toString();
            //获取图片宽高
            BufferedImage bufferedImage = ScalingUtils.getBufferedImage(path);
            if (field_id.equals("269")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("installConfirm" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("installConfirm" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1195")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("installConfirm" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("installConfirm" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("277")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("offer" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("offer" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("270")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("power" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("power" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("271")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("connection" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("connection" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("274")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("photo" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("photo" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1202")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("nameplate" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("nameplate" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("273")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("green" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("green" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("272")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("recharge" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("recharge" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("276")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("stall" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("stall" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("278")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("cable" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("cable" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("310")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("cable" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("cable" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("279")) {

                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                WorkVo workVo = ScalingUtils.zoomByScalePre(transition.getWidth(), transition.getHeight(), path);
                for(int j=1;j<10;j++){
                    Object val =data.get("line"+j);
                    if(val==null){
                        if(transition.getImage()!=null){
                            data.put("line"+j, new PictureRenderData(workVo.getWidth(), workVo.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                            break;
                        }else{
                            data.put("lien"+j, new PictureRenderData(workVo.getWidth(), workVo.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                            break;
                        }
                    }

                }
            }if (field_id.equals("282")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1151")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("breaker" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("breaker" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1179")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("breaker" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("breaker" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1187")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("electrician" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("electrician" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1203")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("Charger" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("Charger" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1186")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("install" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("install" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1198")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("initialOne" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("initialOne" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1199")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("initialTwo" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("initialTwo" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1200")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("initialThree" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("initialThree" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1201")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("initialFour" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("initialFour" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1190")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("lock" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("lock" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1188")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("circuit" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("circuit" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("1191")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("distribution" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("distribution" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("285")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("999")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("soil" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("soil" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if (field_id.equals("998")) {
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("too" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("too" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if(field_id.equals("280")){
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if(field_id.equals("286")){
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if(field_id.equals("934")){
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if(field_id.equals("1189")){
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }if(field_id.equals("932")){
                WorkVo transition = ScalingUtils.transition(bufferedImage.getWidth(), bufferedImage.getHeight(), path);
                if(transition.getImage()!=null){
                    data.put("other" + i, new PictureRenderData(transition.getWidth(), transition.getHeight(), ".png", BytePictureUtils.getBufferByteArray(transition.getImage())));
                }else{
                    data.put("other" + i, new PictureRenderData(bufferedImage.getWidth(), bufferedImage.getHeight(), ".png", BytePictureUtils.getUrlByteArray(path)));
                }
            }
        }
        XWPFTemplate templates = XWPFTemplate.compile(WordConfigs.replacePath())//replacePath = /mnt2/file/replace.docx
                .render(data);
        FileOutputStream outs = new FileOutputStream(WordConfigs.installPath());//installPath = /mnt2/file/install.docx
        templates.write(outs);
        outs.flush();
        outs.close();
        templates.close();
//        //删除替换中间文件
        File file = new File(WordConfigs.replacePath());
        file.delete();
        return R.ok();
    }
    /**
     *上传勘测文件到oss
     */
    @Override
    public  Integer uploadurvey(File files, List<Map<String,Object>> images,String worderNo,String names) {
        long size = files.length();
        if (files != null && size > 0) {
            //根据工单号获取品牌名称和车企单号
            List<Map<String, String>> maps = brandMapper.selectBrandCarName(worderNo);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateString = sdf.format(new Date());
            String oldName="";
            if(names.equals("勘测报告-word")){
                oldName=worderNo+"###"+ maps.get(0).get("company_order_number")+"###" + "勘测报告" +"###"+ dateString + "自动.docx";
            }else{
                oldName=worderNo+"###"+ maps.get(0).get("company_order_number") +"###" +"安装报告" +"###"+dateString + "自动.docx";
            }
            Map<String, String> urlMap = null;
            try {
                urlMap = FileUtils.getOssURL(files,oldName);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String path = urlMap.get("url");
            String name = urlMap.get("name");
            String md5Str = urlMap.get("md5Str");
            //判断工单是否有id 有的话就修改 没有的话就添加
            Integer fileId=0;
            SysFileEntity sysFileEntity = new SysFileEntity();
            sysFileEntity.setOldName(oldName);
            sysFileEntity.setNewName(name);
            sysFileEntity.setPath(path);
            sysFileEntity.setFileCode(md5Str);
            fileId= sysFilesService.saveSysFile(sysFileEntity);
            //删除本地word文件
            files.delete();
            return fileId;
        }
        return 0;
    }
    private  void replaceInAllParagraphs(List<XWPFTable> parasList, Map<String, ArrayList<String>> params, String newTableName, int rowC, int columnC) {
        for (XWPFTable xWPFTable : parasList) {
            for (Map.Entry<String, ArrayList<String>> entry : params.entrySet()) {
                String oneParaStr="";
                int row_num=0;
                for (int i = 0; i < entry.getValue().size(); i++) {
                    if (i == 0) {
                        List<XWPFTableRow> xWPFTableRows = xWPFTable.getRows();
                        for (int p=0;p<xWPFTableRows.size();p++) {
                            XWPFTableRow xWPFTableRow=xWPFTableRows.get(p);
                            XWPFTableCell xWPFTableCell = xWPFTableRow.getCell(0);
                            String oneParaString = xWPFTableRow.getCell(0).getText();
                            if (!StringUtils.isEmpty(oneParaString)) {
                                if (oneParaString.equals(entry.getKey())) {
                                    oneParaStr=oneParaString;
                                    row_num=p;
                                    XWPFParagraph newPara = new XWPFParagraph(xWPFTableCell.getCTTc().addNewP(), xWPFTableCell);
                                    XWPFRun r1 = newPara.createRun();
                                    r1.setText(entry.getValue().get(i));
                                    xWPFTableCell.removeParagraph(0);
                                    xWPFTableCell.setParagraph(newPara);

                                }
                                if (oneParaString.equals(newTableName)) {
                                    XWPFParagraph newPara = new XWPFParagraph(xWPFTableCell.getCTTc().addNewP(), xWPFTableCell);
                                    inertTable(xWPFTableCell, rowC, columnC);
                                    xWPFTableCell.removeParagraph(0);
                                    xWPFTableCell.setParagraph(newPara);

                                }
                            }
                        }
                    } else if (oneParaStr.equals(entry.getKey())) {
                        XWPFTableRow row = xWPFTable.insertNewTableRow(row_num+i);
                        row.createCell().setText(entry.getValue().get(i));
                    }
                }
            }
        }
    }
    private  void inertTable(XWPFTableCell xWPFTableCell,int rowC,int columnC) {
        XWPFParagraph cellPara = xWPFTableCell.getParagraphArray(0);
        cellPara.setAlignment(ParagraphAlignment.CENTER);
        XWPFTable cellTable = xWPFTableCell.insertNewTbl(cellPara.getCTP().newCursor());  //在此游标处插入新表格
        cellTable.getCTTbl().addNewTblPr().addNewTblBorders().addNewLeft()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        cellTable.getCTTbl().getTblPr().getTblBorders().addNewRight()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        cellTable.getCTTbl().getTblPr().getTblBorders().addNewTop()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        cellTable.getCTTbl().getTblPr().getTblBorders().addNewBottom()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        cellTable.getCTTbl().getTblPr().getTblBorders().addNewInsideH()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        cellTable.getCTTbl().getTblPr().getTblBorders().addNewInsideV()
                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE);
        CTTblWidth ctTblWidth = cellTable.getCTTbl().getTblPr().addNewTblW();
        ctTblWidth.setType(STTblWidth.DXA);
        ctTblWidth.setW(BigInteger.valueOf(xWPFTableCell.getWidth()-250));
        for (int i=0;i<rowC;i++) {
            XWPFTableRow row_1 = cellTable.createRow();
            for (int j=0;j<columnC;j++) {
                if(i==0) {
                    row_1.createCell().setText("{{@line" + (i*columnC+j+1) + "}}");
                }else{
                    row_1.getCell(j).setText("{{@line" + (i*columnC+j+1)  + "}}");
                }
            }
        }
    }
}
