
package com.bonc.rrs.sapreceivable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;


/**
 * <p>outType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="outType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BUKRS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="BUTXT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="KUNNR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="NAME1" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="UMSKZ" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RACCT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TXT50" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="HSLQM" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="RTCUR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "outType", propOrder = {
    "bukrs",
    "butxt",
    "kunnr",
    "name1",
    "umskz",
    "racct",
    "txt50",
    "hslqm",
    "rtcur"
})
public class OutType {

    @XmlElement(name = "BUKRS", required = true)
    protected String bukrs;
    @XmlElement(name = "BUTXT", required = true)
    protected String butxt;
    @XmlElement(name = "KUNNR", required = true)
    protected String kunnr;
    @XmlElement(name = "NAME1", required = true)
    protected String name1;
    @XmlElement(name = "UMSKZ", required = true)
    protected String umskz;
    @XmlElement(name = "RACCT", required = true)
    protected String racct;
    @XmlElement(name = "TXT50", required = true)
    protected String txt50;
    @XmlElement(name = "HSLQM", required = true)
    protected BigDecimal hslqm;
    @XmlElement(name = "RTCUR", required = true)
    protected String rtcur;

    /**
     * 获取bukrs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBUKRS() {
        return bukrs;
    }

    /**
     * 设置bukrs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBUKRS(String value) {
        this.bukrs = value;
    }

    /**
     * 获取butxt属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBUTXT() {
        return butxt;
    }

    /**
     * 设置butxt属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBUTXT(String value) {
        this.butxt = value;
    }

    /**
     * 获取kunnr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKUNNR() {
        return kunnr;
    }

    /**
     * 设置kunnr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKUNNR(String value) {
        this.kunnr = value;
    }

    /**
     * 获取name1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNAME1() {
        return name1;
    }

    /**
     * 设置name1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNAME1(String value) {
        this.name1 = value;
    }

    /**
     * 获取umskz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUMSKZ() {
        return umskz;
    }

    /**
     * 设置umskz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUMSKZ(String value) {
        this.umskz = value;
    }

    /**
     * 获取racct属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRACCT() {
        return racct;
    }

    /**
     * 设置racct属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRACCT(String value) {
        this.racct = value;
    }

    /**
     * 获取txt50属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTXT50() {
        return txt50;
    }

    /**
     * 设置txt50属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTXT50(String value) {
        this.txt50 = value;
    }

    /**
     * 获取hslqm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getHSLQM() {
        return hslqm;
    }

    /**
     * 设置hslqm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setHSLQM(BigDecimal value) {
        this.hslqm = value;
    }

    /**
     * 获取rtcur属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRTCUR() {
        return rtcur;
    }

    /**
     * 设置rtcur属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRTCUR(String value) {
        this.rtcur = value;
    }

}
