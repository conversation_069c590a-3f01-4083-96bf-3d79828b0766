package com.bonc.rrs.sapreceivable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.youngking.lenmoncore.common.imail.service.IMailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/1811:34
 */
@Component
@Slf4j
public class SapReceiveService {

    @Autowired(required = false)
    private WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    private IMailService mailService;

    public static void main(String[] args) {
        SapReceiveService sapReceiveService = new SapReceiveService();
        InType in = new InType();
        in.setBUKRS("0RK0");
        in.setKUNNR("8800348792");
        in.setFLAG("");
        in.setSYSNAME("GVS");
        OutType outTypes = sapReceiveService.outTypeList(in);  //查询sap实时应收余额
    }

    public OutType outTypeList(InType in){

        OutType sap = new OutType();
        try{
            TransSupplierBalanceFromEVSToSAP_Service sapService = new TransSupplierBalanceFromEVSToSAP_Service();
            TransSupplierBalanceFromEVSToSAP sapsoap = sapService.getTransSupplierBalanceFromEVSToSAPSOAP();
            List<OutType> out = sapsoap.transSupplierBalanceFromEVSToSAP(in);
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(out));
            log.info("sap接口响应结果"+jsonArray);
            if(CollectionUtils.isNotEmpty(out)){
                for (OutType outType : out) {
                    //查询应收账款-集团外科目,1122002000
                    if (outType.getRACCT().equals("1122002000")) {
                        sap = outType;
                        break;
                    }
                }
                if (sap.getHSLQM()==null){
                    sap.setHSLQM(new BigDecimal(0));
                }
            }else {
                sap.setHSLQM(new BigDecimal(0));
            }
            log.info("调用应收sap接口返回信息"+out.toString());
        }catch (Exception e){
            sap.setHSLQM(new BigDecimal(0));
            log.error("调用应收sap接口返回信息,发生未知异常",e);
            //发送邮件通知相关人员
            List<String> mailList = worderInformationDao.getMailList("2");
            if(CollectionUtils.isNotEmpty(mailList)){
                String[] list = (String[])mailList.toArray(new String[0]);
                mailService.sendSimpleMailMulti(list,"sap实时应收接口调用","接口返回信息,发生未知异常，请尽相关人员快处理!");
            }
        }

        return sap;
    }
}
