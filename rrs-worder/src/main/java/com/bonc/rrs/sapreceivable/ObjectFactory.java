
package com.bonc.rrs.sapreceivable;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.bonc.rrs.sapreceivable package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.bonc.rrs.sapreceivable
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link TransSupplierBalanceFromEVSToSAP_Type }
     * 
     */
    public TransSupplierBalanceFromEVSToSAP_Type createTransSupplierBalanceFromEVSToSAP_Type() {
        return new TransSupplierBalanceFromEVSToSAP_Type();
    }

    /**
     * Create an instance of {@link InType }
     * 
     */
    public InType createInType() {
        return new InType();
    }

    /**
     * Create an instance of {@link TransSupplierBalanceFromEVSToSAPResponse }
     * 
     */
    public TransSupplierBalanceFromEVSToSAPResponse createTransSupplierBalanceFromEVSToSAPResponse() {
        return new TransSupplierBalanceFromEVSToSAPResponse();
    }

    /**
     * Create an instance of {@link OutType }
     * 
     */
    public OutType createOutType() {
        return new OutType();
    }

}
