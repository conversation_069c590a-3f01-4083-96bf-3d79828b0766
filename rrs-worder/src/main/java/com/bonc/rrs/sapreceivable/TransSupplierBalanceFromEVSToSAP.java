
package com.bonc.rrs.sapreceivable;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;
import java.util.List;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "TransSupplierBalanceFromEVSToSAP", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface TransSupplierBalanceFromEVSToSAP {


    /**
     * 
     * @param in
     * @return
     *     returns java.util.List<com.bonc.rrs.sapreceivable.OutType>
     */
    @WebMethod(operationName = "TransSupplierBalanceFromEVSToSAP", action = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/TransSupplierBalanceFromEVSToSAP")
    @WebResult(name = "out", targetNamespace = "")
    @RequestWrapper(localName = "TransSupplierBalanceFromEVSToSAP", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/", className = "com.bonc.rrs.sapreceivable.TransSupplierBalanceFromEVSToSAP_Type")
    @ResponseWrapper(localName = "TransSupplierBalanceFromEVSToSAPResponse", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/", className = "com.bonc.rrs.sapreceivable.TransSupplierBalanceFromEVSToSAPResponse")
    public List<OutType> transSupplierBalanceFromEVSToSAP(
            @WebParam(name = "in", targetNamespace = "")
                    InType in);

}
