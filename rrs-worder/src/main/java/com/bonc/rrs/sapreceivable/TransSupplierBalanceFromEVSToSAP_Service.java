
package com.bonc.rrs.sapreceivable;

import com.bonc.rrs.wsdlproperties.WsdlProperties;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.URL;


/**
 * OSB Service
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
//@WebServiceClient(name = "TransSupplierBalanceFromEVSToSAP", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/", wsdlLocation = "file:/C:/Users/<USER>/Desktop/TransSupplierBalanceFromEVSToSAP.wsdl")
//@WebServiceClient(name = "TransSupplierBalanceFromEVSToSAP", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/", wsdlLocation = "classpath:TransSupplierBalanceFromEVSToSAP.wsdl")
@WebServiceClient(name = "TransSupplierBalanceFromEVSToSAP", targetNamespace = "http://www.example.org/TransSupplierBalanceFromEVSToSAP/", wsdlLocation = "${saplocation}")
public class TransSupplierBalanceFromEVSToSAP_Service
    extends Service
{

    private final static URL TRANSSUPPLIERBALANCEFROMEVSTOSAP_WSDL_LOCATION;
    private final static WebServiceException TRANSSUPPLIERBALANCEFROMEVSTOSAP_EXCEPTION;
    private final static QName TRANSSUPPLIERBALANCEFROMEVSTOSAP_QNAME = new QName("http://www.example.org/TransSupplierBalanceFromEVSToSAP/", "TransSupplierBalanceFromEVSToSAP");


    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = TransSupplierBalanceFromEVSToSAP_Service.class.getClassLoader().getResource(WsdlProperties.sapurl);
//            url = TransSupplierBalanceFromEVSToSAP_Service.class.getClassLoader().getResource("TransSupplierBalanceFromEVSToSAP.wsdl");
        } catch (Exception ex) {
            e = new WebServiceException(ex);
        }
        TRANSSUPPLIERBALANCEFROMEVSTOSAP_WSDL_LOCATION = url;
        TRANSSUPPLIERBALANCEFROMEVSTOSAP_EXCEPTION = e;
    }

    public TransSupplierBalanceFromEVSToSAP_Service() {
        super(__getWsdlLocation(), TRANSSUPPLIERBALANCEFROMEVSTOSAP_QNAME);
    }

    public TransSupplierBalanceFromEVSToSAP_Service(WebServiceFeature... features) {
        super(__getWsdlLocation(), TRANSSUPPLIERBALANCEFROMEVSTOSAP_QNAME, features);
    }

    public TransSupplierBalanceFromEVSToSAP_Service(URL wsdlLocation) {
        super(wsdlLocation, TRANSSUPPLIERBALANCEFROMEVSTOSAP_QNAME);
    }

    public TransSupplierBalanceFromEVSToSAP_Service(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, TRANSSUPPLIERBALANCEFROMEVSTOSAP_QNAME, features);
    }

    public TransSupplierBalanceFromEVSToSAP_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TransSupplierBalanceFromEVSToSAP_Service(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns TransSupplierBalanceFromEVSToSAP
     */
    @WebEndpoint(name = "TransSupplierBalanceFromEVSToSAPSOAP")
    public TransSupplierBalanceFromEVSToSAP getTransSupplierBalanceFromEVSToSAPSOAP() {
        return super.getPort(new QName("http://www.example.org/TransSupplierBalanceFromEVSToSAP/", "TransSupplierBalanceFromEVSToSAPSOAP"), TransSupplierBalanceFromEVSToSAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns TransSupplierBalanceFromEVSToSAP
     */
    @WebEndpoint(name = "TransSupplierBalanceFromEVSToSAPSOAP")
    public TransSupplierBalanceFromEVSToSAP getTransSupplierBalanceFromEVSToSAPSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.example.org/TransSupplierBalanceFromEVSToSAP/", "TransSupplierBalanceFromEVSToSAPSOAP"), TransSupplierBalanceFromEVSToSAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (TRANSSUPPLIERBALANCEFROMEVSTOSAP_EXCEPTION!= null) {
            throw TRANSSUPPLIERBALANCEFROMEVSTOSAP_EXCEPTION;
        }
        return TRANSSUPPLIERBALANCEFROMEVSTOSAP_WSDL_LOCATION;
    }

}
