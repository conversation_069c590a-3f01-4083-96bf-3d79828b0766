package com.bonc.rrs.gace.client.impl;

import com.bonc.rrs.gace.dto.request.WOrderUpdateRequest;
import com.bonc.rrs.gace.dto.response.WorderUpdateResponse;
import com.bonc.rrs.gace.enums.GaceApiEnum;
import com.bonc.rrs.gace.util.GaceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年10月15日 09:22
 */

@Slf4j
@Service
public class GaceUpdateOrderApiImpl extends AbstractGaceApi<WOrderUpdateRequest, WorderUpdateResponse> {

    @Override
    public GaceApiEnum gaceApiType() {
        return GaceApiEnum.UPDATE_WORK_ORDER;
    }

    @Override
    protected HttpEntity<String> marshal(WOrderUpdateRequest request) {
        try {
            HashMap<String, List<WOrderUpdateRequest>> map = new HashMap<>();
            ArrayList<WOrderUpdateRequest> list = new ArrayList<>();
            list.add(request);
            map.put("data", list);
            String params = objectMapper.writeValueAsString(map);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("accesstoken", gaceAuthApi.getToken());
            return new HttpEntity<>(params, headers);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected GaceResponse<WorderUpdateResponse> unmarshal(String body) throws IOException {
        Map object = objectMapper.readValue(body, Map.class);
        GaceResponse<WorderUpdateResponse> response = new GaceResponse<>();
        response.setData(null);
        response.setStatus((Boolean) object.get("status"));
        response.setMessage((String) object.get("message"));
        response.setErrorCode((String) object.get("errorCode"));
        return response;
    }

    @Override
    protected void afterExecute(WOrderUpdateRequest request, GaceResponse<WorderUpdateResponse> response) {
        try {
            intfLogService.saveIntfLog(gaceApiType().getUri(), request.getBillno(), config.getBaseUrl() + gaceApiType().getUri(), gaceApiType().getDesc(), 1, objectMapper.writeValueAsString(request), objectMapper.writeValueAsString(response));
        } catch (Exception e) {
            log.error("广汽保存修改工单接口日志报错",e);
        }
    }
}
