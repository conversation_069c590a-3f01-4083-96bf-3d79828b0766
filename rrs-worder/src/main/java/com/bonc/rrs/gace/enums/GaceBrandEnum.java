package com.bonc.rrs.gace.enums;

/**
 * <AUTHOR>
 */
public enum GaceBrandEnum {
    /**
     * 品牌
     */
    //能源-云影高配：NY-LGGP；能源-云影低配：NY-LGDP；广汽埃安：GQAA；广汽传祺：GQCQ；广汽本田：GQBT；埃安昊铂：AAHB
    NY_LGGP("NY-LGGP", "广汽-云影高配（用客户的桩子）","能源-云影高配"),
    NY_LGDP("NY-LGDP", "广汽-云影低配（用客户的桩子）","能源-云影低配"),
    GQAA("GQAA", "广汽新能源","广汽埃安"),
    GQCQ("GQCQ", "广汽新能源","广汽传祺"),
    GQBT("GQBT", "广汽新能源","广汽本田"),
    AAHB("AAHB", "广汽昊铂","埃安昊铂");

    private final String code;
    private final String brandName;
    private final String description;

    GaceBrandEnum(String code, String brandName, String description) {
        this.code = code;
        this.brandName = brandName;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getBrandName() {
        return brandName;
    }

    // 根据状态码获取对应枚举
    public static GaceBrandEnum fromCode(String code) {
        for (GaceBrandEnum brandEnum : GaceBrandEnum.values()) {
            if (brandEnum.getCode().equals(code)) {
                return brandEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.code + " (" + this.description + ")";
    }
}