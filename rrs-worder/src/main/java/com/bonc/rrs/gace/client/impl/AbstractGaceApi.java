package com.bonc.rrs.gace.client.impl;

import com.bonc.rrs.gace.client.GaceApi;
import com.bonc.rrs.gace.client.GaceAuthApi;
import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.dto.request.RequestDto;
import com.bonc.rrs.gace.dto.response.ResponseDto;
import com.bonc.rrs.gace.exception.ApiClientException;
import com.bonc.rrs.gace.exception.ApiServerException;
import com.bonc.rrs.gace.util.GaceResponse;
import com.bonc.rrs.intf.service.IntfLogService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024年10月14日 17:51
 */
@Slf4j
public abstract class AbstractGaceApi<R extends RequestDto, T extends ResponseDto> implements GaceApi<R, T> {

    @Autowired
    @Qualifier("restTemplate4Gace")
    protected RestTemplate restTemplate4Gace;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected GaceAuthApi gaceAuthApi;

    @Autowired
    protected GaceConfig config;

    @Autowired
    protected IntfLogService intfLogService;

    @Override
    public GaceResponse<T> execute(R request) {
        GaceResponse<T> response = null;
        try {
            beforeExecute(request);
            HttpEntity<String> httpEntity = marshal(request);
            log.info("请求:" + httpEntity.toString());
            ResponseEntity<String> responseEntity = restTemplate4Gace.exchange(getUrl(request), gaceApiType().getMethod(), httpEntity, String.class);
            String truncatedResult = responseEntity.toString().substring(0, Math.min(responseEntity.toString().length(), 1000));
            log.info("响应:" + truncatedResult);
            if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
                log.error("请求广汽接口失败:{}", responseEntity);
                throw new ApiServerException("请求广汽接口失败" + responseEntity.getStatusCode() + " " + responseEntity.getStatusCode().getReasonPhrase());
            }
            response = unmarshal(responseEntity.getBody());
            return response;
        } catch (ApiServerException ose) {
            log.error("服务器:" + ose.getMessage(), ose);
            throw ose;
        } catch (ApiClientException oce) {
            log.error("客户端:" + oce.getMessage(), oce);
            throw oce;
        } catch (Exception e) {
            log.error("内部错误:" + e.getMessage(), e);
            throw new ApiClientException("内部错误:" + e.getMessage());
        } finally {
            afterExecute(request, response);
        }
    }

    /**
     * 同步请求前置处理
     *
     * @param request
     */
    protected void beforeExecute(R request) {

    }

    protected String getUrl(R request) {
        return config.getBaseUrl() + gaceApiType().getUri();
    }

    /**
     * 同步请求后置处理
     *
     * @param request
     * @param response
     */
    protected void afterExecute(R request, GaceResponse<T> response) {
        // save intf log
    }

    /**
     * 序列化请求参数
     * @param request
     * @return
     */
    protected HttpEntity<String> marshal(R request) {
        try {
            String params = objectMapper.writeValueAsString(request);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("accesstoken", gaceAuthApi.getToken());
            return new HttpEntity<>(params, headers);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 反序列化响应参数
     *
     * @param body
     * @return
     */
    protected GaceResponse<T> unmarshal(String body) throws IOException {
        GaceResponse<T> response = objectMapper.readValue(body, new TypeReference<GaceResponse<T>>() {
        });
        return response;
    }

    ;

}
