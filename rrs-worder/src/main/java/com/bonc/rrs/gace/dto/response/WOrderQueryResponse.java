package com.bonc.rrs.gace.dto.response;

import com.bonc.rrs.gace.util.GaceResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class WOrderQueryResponse extends ResponseDto {

    private Long page;
    private Long totalPage;
    private Long size;
    private Long totalSize = 0L;

    private List<WOrder> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WOrder {

        /**
         * id
         * 示例: "6962077952754979840"
         */
        private Long id;

        /**
         * 工单编号
         * 示例: "W202409140004"
         */
        private String billno;

        /**
         * 单据状态 A:暂存 B:已提交 C:已审核
         * 示例: "C"
         */
        private String billstatus;

        /**
         * 工单概述
         * 示例: "充电桩安装单-广汽埃安"
         */
        private String name;

        /**
         * 服务区域.编码
         * 示例: "GQNY"
         */
        private String site_id_number;

        /**
         * 服务区域.名称
         * 示例: "广汽能源"
         */
        private String site_id_name;

        /**
         * 工作单类型.编码
         * 示例: "SZAZ"
         */
        private String wo_type_id_number;

        /**
         * 工作单类型.名称
         * 示例: "私桩安装"
         */
        private String wo_type_id_name;

        /**
         * 工单状态 [DRAFT:拟定, APPROVED:待处理, DEPART:已出发, INPRG:处理中, COMPLETED:工作完成, CLOSED:已结束, CANCEL:取消, PAUSE:暂停作业, PRECLOSED:等待关闭, PRECANCEL:申请取消, UNABLE:无法执行, WRD:需改派, REWORK:需返工, REPORTED:待报告, REJECTED:已拒绝, RETAINING:申请保留观察, RETAINED:持续观察中, WPCOND:等待作业条件, SUBMITTED:已提交]
         * 示例: "WSCH"
         */
        private String status;

        /**
         * 客服系统坐席员工号
         * 示例: "8000"
         */
        private String ab38_user;

        /**
         * 上游系统工单编号
         * 示例: "DMS24091402"
         */
        private String ab38_upstream_code;

        /**
         * 上游系统工单ID
         * 示例: "DMS24091402"
         */
        private String ab38_upstream_id;

        /**
         * 数据来源系统 [OPERATIONS:运维监控系统, CUSTOME:能源客服系统, GRT:GRT系统, AADMS:埃安DMS系统, HBDMS:昊铂DMS系统, NYAPP:能源APP]
         * 示例: "OPERATIONS"
         */
        private String ab38_source_system;

        /**
         * APP订单号
         * 示例: "DMS24091402"
         */
        private String ab38_app_order_no;

        /**
         * 是否已实销 0:否 1:是
         * 示例: "0"
         */
        private String ab38_actual_sale_flag;

        /**
         * 设备.资产编码
         * 示例: "202409090001"
         */
        private String asset_id_number;

        /**
         * 设备.资产全名称
         * 示例: "7kW交流桩-埃安品牌-倍利通达-通用版"
         */
        private String asset_id_name;

        /**
         * 位置.位置名称/标识
         * 示例: "GQNY-01-03-01"
         */
        private String location_id_number;

        /**
         * 位置.位置说明
         * 示例: "北京市"
         */
        private String location_id_name;

        /**
         * 专业归类.专业归类名称
         * 示例: "广汽埃安"
         */
        private String digi_professional_class_name;

        /**
         * 专业归类.专业归类编码 专业归类.专业归类编码；能源-云影高配：NY-LGGP；能源-云影低配：NY-LGDP；广汽埃安：GQAA；广汽传祺：GQCQ；广汽本田：GQBT；埃安昊铂：AAHB
         * 示例: "GQAA"
         */
        private String digi_professional_class_number;

        /**
         * VIN
         * 示例: "zPd3F"
         */
        private String digi_attribute1;

        /**
         * 业务类型
         * 示例: "C04Ko"
         */
        private String digi_attribute2;

        /**
         * 省份
         * 示例: "广东省"
         */
        private String digi_attribute3;

        /**
         * 城市
         * 示例: "广州市"
         */
        private String digi_attribute4;

        /**
         * 区域
         * 示例: "番禺区"
         */
        private String digi_attribute5;

        /**
         * 报装地址/安装地址
         * 示例: "广东省广州市番禺区XXX"
         */
        private String digi_attribute6;

        /**
         * 开单时间
         * 示例: "2023-12-06"
         */
        private String digi_attribute7;

        /**
         * 备注/故障类型
         * 示例: "备注"
         */
        private String digi_attribute8;

        /**
         * 销售订单.编码
         * 示例: "GQDD-20231121-000001"
         */
        private String digi_ext_ab38_salorder_number;

        /**
         * 销售订单.订单名称
         * 示例: "广汽埃安埃安品牌随车送桩"
         */
        private String digi_ext_ab38_salorder_name;

        /**
         * 工作优先级.编码
         * 示例: "01"
         */
        private String priority_id_number;

        /**
         * 工作优先级.名称
         * 示例: "一般"
         */
        private String priority_id_name;

        /**
         * 计划工期
         * 示例: "5"
         */
        private String duration_schedualed;

        /**
         * 工期单位 HOUR:小时 MINUTE:分钟 DAY:天
         * 示例: "HOUR"
         */
        private String duration_uom;

        /**
         * 计划开始时间
         * 示例: "2024-10-14 14:46:04"
         */
        private String schedualed_start;

        /**
         * 计划完成时间
         * 示例: "2024-10-14 14:46:04"
         */
        private String schedualed_finish;

        /**
         * 实际开始时间
         * 示例: "2024-10-14 14:46:04"
         */
        private String actual_start;

        /**
         * 实际完成时间
         * 示例: "2024-10-14 14:46:04"
         */
        private String actual_finish;

        /**
         * 超时提醒次数
         * 示例: "28"
         */
        private Integer ab38_timeout_count;

        /**
         * 是否已抽查 Y:是 N:否
         * 示例: "N"
         */
        private String ab38_check;

        /**
         * 桩主名字
         * 示例: "张三"
         */
        private String contacts;

        /**
         * 桩主联系电话
         * 示例: "13143332791"
         */
        private String contact_mobile;

        /**
         * 备注
         */
        private String description;

        /**
         * 单据体（工单任务）
         */
        private List<WorkOrderOperation> workorder_operations;

        private List<DigiAttachmentEntry> digi_attachment_entry;

        /**
         * 工单检查项
         */
        private List<WorkOrderCheck> digi_wo_checklists;

        /**
         * 工单检查项组
         */
        private List<WorkOrderCheckGroup> digi_wo_checklist_groups;

        @Data
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class DigiAttachmentEntry {
            private String digiAttName;
            private List<DigiTypeAttachment> digiTypeAttachment;
            private String id;
            private String digiAttType;
            private String digiAttTypeCom;
            private int seq;

            @Data
            public static class DigiTypeAttachment {
                private String createtime;
                private String previewurl;
                private String modifytime;
                private String tempfile;
                private String description;
                private String type;
                private String url;
                private int filesource;
                private String number;
                private String masterid;
                private String uid;
                private String size;
                private String name;
                private String id;
                private String status;
            }
        }


        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class WorkOrderOperation {

            private Long id;

            /**
             * 单据体.任务编号，默认为10
             * 示例："10"
             */
            private String woop_number;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class WorkOrderCheck {
            /**
             * 工单检查项ID
             * 示例: 4060268422342152192
             */
            private Long id;

            /**
             * 序号
             * 示例: 1
             */
            private String ckNumber;

            /**
             * 名称
             * 示例: tcI9X
             */
            private String ckName;

            /**
             * 实际值
             * 示例: bSTjy
             */
            private String ckActualValue;

            /**
             * 描述_详情
             * 示例: EDtwv
             */
            private String ckActualDescriptionTag;

            /**
             * 值类型 (Y/N:是/否, NUM:数字, TEXT:文本说明, CLASS:索引项, METER:仪表, SINGLE:单选, MUTI:多选, DATE:日期)
             * 示例: Y/N
             */
            private String digiValueType;

            /**
             * 检查项组ID
             * 示例: 8197914301759538176
             */
            private String ckWoChecklistGroupId;

            /**
             * 参考标准
             * 示例: 关联库存，需注意为厂家配套立柱选择
             */
            private String ckStandardReference;

            /**
             * 辅助录入方式 (SCAN:扫码, LOC:定位)
             * 示例: SCAN
             */
            private String digiAssistInput;

            /**
             * 多个结果值
             * 示例: false
             */
            private Boolean digiMultiselect;

            /**
             * 必检
             * 示例: true
             */
            private Boolean ckMustCheckFlag;

            /**
             * 描述
             * 示例: JUAyP
             */
            private String ckActualDescription;

            /**
             * 来源标准检查项ID
             * 示例: 2215412209874174976
             */
            private Long ckSourceStdchecklistId;

            /**
             * 上限值
             * 示例: 99999999.00
             */
            private BigDecimal ckMaxRangeValue;

            /**
             * 附件必填
             * 示例: false
             */
            private Boolean digiAnnex;
        }

        /**
         * 工单检查项组实体类
         */
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class WorkOrderCheckGroup {

            /**
             * 工单检查项组编码
             * 示例: "EhBfb"
             */
            private String ckgNumber;

            /**
             * 工单检查项组工作任务ID
             * 示例: "6172296550563515392"
             */
            private String ckgWoopId;

            /**
             * 工单检查项组名称
             * 示例: "是否具备安装条件"
             */
            private String ckgName;

            /**
             * 工单检查项组ID
             * 示例: "8197914301759538176"
             */
            private Long id;

            /**
             * 工单检查项组业务场景
             * 示例: "WOOP_INPRG"
             * 业务场景包括:
             * - WO_START: 开工前确认
             * - WO_FINISH: 完工情况确认
             * - WOOP_WSCH: 待计划安排
             * - WOOP_START: 任务开工检查
             * - WOOP_INPRG: 任务执行
             * - WOOP_FINISH: 任务完工情况确认
             */
            private String ckgScenario;

            /**
             * 工单检查项组检查项来源
             * 示例: "MANUAL"
             * 来源包括:
             * - MANUAL: 手动创建
             * - ACT: 标准作业
             * - WO_TYPE: 工单类型
             */
            private String digiClgroupSource;

            /**
             * 标准检查项组ID
             * 示例: "7243343979859755008"
             */
            private Long digiStdClGroupIdId;

            /**
             * 标准检查项组编码
             * 示例: "ymzBa"
             */
            private String digiStdClGroupIdNumber;

            /**
             * 标准检查项组名称
             * 示例: "vTEuk"
             */
            private String digiStdClGroupIdName;
        }
    }

    public static void main(String[] args) throws IOException {
        String body = "{\"data\":{\"filter\":null,\"lastPage\":null,\"pageNo\":0,\"pageSize\":0,\"rows\":[{\"ab38_actual_sale_flag\":\"0\",\"digi_wo_checklists\":[{\"ck_actual_value\":\"bSTjy\",\"ck_must_check_flag\":true,\"ck_actual_description_tag\":\"EDtwv\",\"ck_source_stdchecklist_id\":\"2215412209874174976\",\"ck_standard_reference\":\"关联库存，需注意为厂家配套立柱选择\",\"digi_value_type\":\"Y/N\",\"digi_annex\":false,\"ck_actual_description\":\"JUAyP\",\"ck_name\":\"tcI9X\",\"ck_wo_checklist_group_id\":\"8197914301759538176\",\"digi_assist_input\":\"SCAN\",\"digi_multiselect\":false,\"id\":\"4060268422342152192\",\"ck_number\":\"1\",\"ck_max_range_value\":\"99999999.00\"}],\"priority_id_number\":\"01\",\"ab38_app_order_no\":\"DMS24091402\",\"digi_ext_ab38_salorder_number\":\"GQDD-20231121-000001\",\"ab38_timeout_count\":28,\"actual_finish\":\"2024-10-14 14:46:04\",\"location_id_name\":\"北京市\",\"actual_start\":\"2024-10-14 14:46:04\",\"workorder_operations\":[{\"woop_name\":\"充电桩安装单-广汽埃安\",\"woop_description\":\"KyWf7\",\"woop_duration_uom\":\"HOUR\",\"woop_schedualed_finish\":\"2024-09-18 15:53:54\",\"woop_workcenter_id_number\":\"HERRS\",\"woop_schedualed_start\":\"2024-09-18 15:53:54\",\"woop_workcenter_id_name\":\"海尔日日顺\",\"woop_location_id_id\":\"7675773798870725632\",\"woop_status\":\"WSCH\",\"woop_actual_finish\":\"2024-09-18 15:53:54\",\"woop_duration_schedualed\":\"5\",\"woop_asset_id_number\":\"2024042515\",\"woop_number\":\"10\",\"woop_wcp_id_number\":\"ID-001410\",\"createdatefield\":\"2024-09-18 15:53:54\",\"modifydatefield\":\"2024-09-18 15:53:54\",\"woop_location_id_number\":\"BLTD-GDGZ\",\"digi_skd_status\":\"PENDING\",\"id\":\"6172296550563515392\",\"woop_wcp_id_name\":\"张三\",\"woop_location_id_name\":\"倍利通达-广东广州\",\"woop_actual_start\":\"2024-09-18 15:53:54\"}],\"id\":\"6962077952754979840\",\"wo_type_id_name\":\"私桩安装\",\"duration_schedualed\":\"5\",\"ab38_user\":\"8000\",\"billstatus\":\"C\",\"ab38_upstream_code\":\"DMS24091402\",\"digi_wo_failures\":[{\"wf_remedy_manual\":\"已解决\",\"wf_digi_relation_name\":\"私桩维修故障树\",\"wf_risk_id_name\":\"状态指示灯故障\",\"wf_description\":\"这个是备注\",\"wf_woop_id\":\"00\",\"wf_part_id_id\":\"2502535038134266880\",\"wf_cause_id_id\":\"6704217331114465280\",\"wf_failures_finish_time\":\"2024-09-18 15:58:24\",\"wf_part_id_name\":\"设备制造商\",\"digi_failure_source\":\"Manual\",\"wf_digi_relation_number\":\"SZWXGZ\",\"wf_failures_time\":\"2024-09-18 15:58:24\",\"wf_risk_id_id\":\"7247362217260769280\",\"wf_cause_manual\":\"已维修，故障详细描述\",\"id\":\"1389670262574013440\",\"wf_failure_types\":\"0\",\"wf_digi_relation_id\":\"2779619537618869248\",\"wf_cause_id_name\":\"灯板损坏\"}],\"ab38_check\":\"N\",\"digi_professional_class_name\":\"广汽埃安\",\"asset_id_number\":\"202409090001\",\"name\":\"充电桩安装单-广汽埃安\",\"contact_mobile\":\"13143332791\",\"ab38_source_system\":\"OPERATIONS\",\"location_id_number\":\"GQNY-01-03-01\",\"billno\":\"W202409140004\",\"schedualed_start\":\"2024-10-14 14:46:04\",\"status\":\"WSCH\",\"duration_uom\":\"HOUR\",\"site_id_name\":\"广汽能源\",\"digi_attribute10\":\"广汽经销商\",\"digi_ext_ab38_salorder_name\":\"广汽埃安埃安品牌随车送桩\",\"schedualed_finish\":\"2024-10-14 14:46:04\",\"ab38_upstream_id\":\"DMS24091402\",\"digi_attachment_entry\":[{\"digi_att_name\":\"NtK3L\",\"digi_type_attachment\":[{\"createtime\":\"2024-09-19 17:47:53\",\"previewurl\":\"1hnt3\",\"modifytime\":\"2024-09-19 17:47:53\",\"tempfile\":\"7O0vi\",\"description\":\"j56YD\",\"type\":\"12LPJ\",\"url\":\"HkYmK\",\"filesource\":75,\"number\":\"WzC3i\",\"masterid\":\"6289749108332389376\",\"uid\":\"X4khU\",\"size\":\"1907530668437320704\",\"name\":\"ON7Sm\",\"id\":\"6828586494497983488\",\"status\":\"A\"}],\"id\":\"1091456200392031232\",\"digi_att_type\":\"V6pjR\",\"digi_att_type_com\":\"00\",\"seq\":73}],\"asset_id_name\":\"7kW交流桩-埃安品牌-倍利通达-通用版\",\"site_id_number\":\"GQNY\",\"digi_attribute1\":\"zPd3F\",\"digi_attribute3\":\"广东省\",\"digi_attribute2\":\"C04Ko\",\"digi_professional_class_number\":\"GQAA\",\"digi_wo_labors\":[{\"lb_issue_qty\":\"1\",\"lb_woop_id\":\"6172296550563515392\",\"lb_wo_id\":\"6144573055010187264\",\"lb_uom\":\"HOUR\",\"lb_description\":\"rwJdR\",\"id\":\"2713943493728051200\"}],\"digi_wo_checklist_groups\":[{\"ckg_scenario\":\"WOOP_INPRG\",\"digi_clgroup_source\":\"MANUAL\",\"digi_std_cl_group_id_id\":\"7243343979859755008\",\"digi_std_cl_group_id_number\":\"ymzBa\",\"ckg_number\":\"EhBfb\",\"ckg_woop_id\":\"6172296550563515392\",\"ckg_name\":\"是否具备安装条件\",\"id\":\"8197914301759538176\",\"digi_std_cl_group_id_name\":\"vTEuk\"}],\"priority_id_name\":\"一般\",\"digi_attribute8\":\"备注\",\"wo_type_id_number\":\"SZAZ\",\"digi_attribute5\":\"番禺区\",\"digi_attribute4\":\"广州市\",\"digi_attribute7\":\"2023-12-06\",\"contacts\":\"张三\",\"digi_attribute6\":\"广东省广州市番禺区XXX\"}],\"totalCount\":-1},\"errorCode\":\"\",\"message\":\"\",\"status\":true}";
        ObjectMapper objectMapper = new ObjectMapper();
        GaceResponse<WOrderQueryResponse> response = objectMapper.readValue(body, new TypeReference<GaceResponse<WOrderQueryResponse>>() {
        });
        System.out.println(response);
    }
}