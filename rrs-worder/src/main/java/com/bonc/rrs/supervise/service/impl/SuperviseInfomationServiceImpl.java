package com.bonc.rrs.supervise.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.byd.domain.WorderParm;
import com.bonc.rrs.byd.response.PageApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.supervise.dao.*;
import com.bonc.rrs.supervise.dto.SuperviseInfoExport;
import com.bonc.rrs.supervise.dto.SuperviseInfomationInfo;
import com.bonc.rrs.supervise.dto.SuperviseTeleRecordInfo;
import com.bonc.rrs.supervise.dto.WorderInfo;
import com.bonc.rrs.supervise.entity.*;
import com.bonc.rrs.supervise.service.SuperviseDictionaryTripTypeService;
import com.bonc.rrs.supervise.service.SuperviseInfomationService;
import com.bonc.rrs.supervise.vo.*;
import com.bonc.rrs.util.InitRegionUtil;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.*;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.xk.service.XkApiService;
import com.common.pay.common.utils.DateUtil;
import com.gexin.fastjson.JSON;
import com.gexin.fastjson.JSONArray;
import com.gexin.fastjson.JSONObject;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.SpringContextUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysDictionaryDetailDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 督办工单表;(supervise_infomation)表服务实现类
 *
 * <AUTHOR> liujunpeng
 * @date : 2023-9-26
 */
@Service
@AllArgsConstructor
@Log4j2
public class SuperviseInfomationServiceImpl extends ServiceImpl<SuperviseInfomationMapper, SuperviseInfomation> implements SuperviseInfomationService {

    final SuperviseInfomationMapper superviseInfomationMapper;

    final BizRegionDao bizRegionDao;

    final WorderInformationDao worderInformationDao;

    final WorderTemplateDao worderTemplateDao;

    final BrandDao brandDao;

    final SysUserDao sysUserDao;

    final SysDictionaryDetailDao sysDictionaryDetailDao;

    final SuperviseDictionaryMapper superviseDictionaryMapper;

    final WorderVersionDao worderVersionDao;

    final SuperviseTeleRecordMapper superviseTeleRecordMapper;

    final SuperviseOperationRecordMapper superviseOperationRecordMapper;

    final SuperviseRecoverRecordMapper superviseRecoverRecordMapper;

    final WorderInformationAttributeDao worderInformationAttributeDao;

    final XkApiService xkApiService;

    final WorderInformationService worderInformationService;

    final Environment env;
    final SuperviseDictionaryTripTypeService superviseDictionaryTripTypeService;


    @Override
    public R superviseList(SuperviseQueryListParam param) {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        // 账户角色集合
        List<Long> roleIdList = user.getRoleIdList();
        if (roleIdList.size() == 1 && roleIdList.contains(31L)) {
            // 如果当前当前角色是运营，并且只有一个，查自己创建的督办单和自己区域品牌服务类下的督办单
            param.setQueryRoleType(3);
            param.setUserId(user.getUserId());
        } else if (roleIdList.contains(3L)) {
            // 如果是项目经理角色只能查询当前责任人是自己的单子
            param.setQueryRoleType(1);
            param.setUserId(user.getUserId());
        } else if (roleIdList.contains(2L)) {
            // 如果是客服经理，查询自己创建的单子
            param.setQueryRoleType(2);
            param.setUserId(user.getUserId());
        } else if (roleIdList.contains(4L)) {
            // 如果是网点管理员，查询自己创建的单子
            param.setQueryRoleType(4);
            param.setUserId(user.getUserId());
        }

        IPage<SuperviseInfomationInfo> page = superviseInfomationMapper.queryListPage(new Page<>(param.getPage(), param.getPageSize()), param);
        // 翻译督办属性
        List<SuperviseInfomationInfo> records = page.getRecords();
        translateSupervise(records);
        return Objects.requireNonNull(R.ok().put("list", records)).put("total", page.getTotal());
    }


    @Override
    public R getWorderInfo(Integer worderId, Integer worderSystem) {
        if (worderSystem == null) {
            worderSystem = 1;
        }
        //如果是news订单
        if (worderSystem == 1) {
            WorderInfo worderInfo = superviseInfomationMapper.getWorderInfo(worderId);
            // 获取工单省市县，存入数组
            String[] areas = new String[3];
            BizRegionEntity disRegion = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(worderInfo.getAreaId().toString()));
            BizRegionEntity cityRegion = InitRegionUtil.REGION_ID_MAP.get(disRegion.getPid());
            areas[0] = cityRegion.getPid().toString();
            areas[1] = cityRegion.getId().toString();
            areas[2] = worderInfo.getAreaId().toString();
            worderInfo.setAreas(areas);
            return R.ok().put("worderInfo", worderInfo);
        }
        if (worderSystem == 2) {
            WorderParm parm = new WorderParm();
            parm.setWorderId(worderId);
            parm.setLimit(1);
            parm.setPage(1);
            PageApiResponse pageApiResponse = xkApiService.selectOrder(parm);
            if (pageApiResponse.getPage().getTotalCount() == 0) {
                return R.error("小咖未查询到对应工单");
            }
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(pageApiResponse.getPage().getList().get(0)));
            String areaCode = jsonObject.getString("areaCode");

            String areas = bizRegionDao.getListByAreaCode(areaCode);

            WorderInfo worderInfo = new WorderInfo();
            worderInfo.setAreas(areas.split(","));
            worderInfo.setAreaId(Integer.parseInt(areas.split(",")[2]));
            worderInfo.setBrandId(48);
            worderInfo.setDotId(825);
            worderInfo.setUserName(jsonObject.getString("userName"));
            worderInfo.setUserPhone(jsonObject.getString("userPhone"));
            worderInfo.setWorderId(jsonObject.getInteger("worderId"));
            worderInfo.setWorderNo(jsonObject.getString("worderNo"));
            //todo
            worderInfo.setServiceTypeEnum(jsonObject.getInteger("serviceTypeEnum"));
            worderInfo.setTemplateId(0);
            return R.ok().put("worderInfo", worderInfo);
        }
        return R.error("订单来源不正确");
    }

    @Override
    public R getWorderInfoBySuperviseId(Integer superviseId) {
        SuperviseInfomation superviseInfomation = this.baseMapper.selectById(superviseId);
        if (superviseInfomation != null && StringUtils.isNotBlank(superviseInfomation.getWorderNo())) {
            WorderInfo worderInfo = superviseInfomationMapper.selectWorderInfoByWorderNo(superviseInfomation.getWorderNo());
            return R.ok().put("worderInfo", worderInfo);
        }
        return R.ok().put("worderInfo", new WorderInfo());
    }

    @Override
    public R getBrands() {
        List<BrandEntity> brandEntities = brandDao.selectList(Wrappers.lambdaQuery());
        return R.ok().put("brandList", brandEntities);
    }

    @Override
    public R worderList(Map<String, Object> params) {
        WorderInformationService informationService = SpringContextUtils.getBean("worderInformationService", WorderInformationService.class);
        String pageNum = (String) params.get("page");
        String pageSize = (String) params.get("limit");
        //不要小咖的
        params.put("transfer", 0);
        PageUtils pageUtils = informationService.queryAll(params);
        if (pageUtils.getTotalCount() > 0) {
            List<?> list = pageUtils.getList();
            List<WorderInformationEntityExt> extList = new ArrayList<>();
            for (Object o : list) {
                if (o instanceof WorderInfoEntity) {
                    WorderInformationEntityExt ext = JSON.parseObject(JSON.toJSONString(o), WorderInformationEntityExt.class);
                    ext.setWorderSystem(1);
                    extList.add(ext);
                }
            }
            pageUtils.setList(extList);
            return R.ok().put("page", pageUtils);
        }
        //调用小咖
        WorderParm worderParm = new WorderParm();
        if (!StringUtils.isEmpty(params.get("worderNo"))) {
            worderParm.setWorderNo((String) params.get("worderNo"));
        }
        if (!StringUtils.isEmpty(params.get("companyOrderNumber"))) {
            worderParm.setCompanyOrderNumber((String) params.get("companyOrderNumber"));
        }
        if (!StringUtils.isEmpty(params.get("userPhone"))) {
            worderParm.setUserPhone((String) params.get("userPhone"));
        }
        if (!StringUtils.isEmpty(params.get("userName"))) {
            worderParm.setUserName((String) params.get("userName"));
        }
        worderParm.setPage(Integer.parseInt(pageNum));
        worderParm.setLimit(Integer.parseInt(pageSize));

        PageApiResponse response = xkApiService.selectOrder(worderParm);
        if (response.getCode() == 0) {
            PageUtils page = response.getPage();
            List<?> list = page.getList();
            List<WorderInformationEntityExt> extList = new ArrayList<>();
            for (Object o : list) {
                WorderInformationEntityExt ext = JSON.parseObject(JSON.toJSONString(o), WorderInformationEntityExt.class);
                //小咖
                ext.setBrandId(48);
                ext.setDotId(825);
                ext.setCarBrand("小卡电工");
                ext.setCandidateBranch("上海飞隼科技有限公司");
                ext.setWorderSystem(2);
                extList.add(ext);
            }
            page.setList(extList);
            return R.ok().put("page", page);
        }
        return R.ok();
    }

    @Override
    public R getAreaBrandService() {
        // 查询区域信息三级联动
        List<RegionTreeEntity> regionTreeEntities = bizRegionDao.listNationTree();
        // 查询所有品牌
        LambdaQueryWrapper<BrandEntity> brandWrapper = Wrappers.lambdaQuery();
        brandWrapper.select(BrandEntity::getId, BrandEntity::getBrandName);
        List<BrandEntity> brandEntities = brandDao.selectList(brandWrapper);
        // 查询所有服务属性
        List<SysDictionaryDetailEntity> serviceTypes = sysDictionaryDetailDao.selectDetailByNumbers("service_type");
        return Objects.requireNonNull(Objects.requireNonNull(R.ok().put("areaList", regionTreeEntities)).put("brandList", brandEntities)).put("serviceTypeList", serviceTypes);
    }

    @Override
    public R getSuperviseDictionary() {
        // 查询督办单字典信息
        List<SuperviseDictionary> superviseDictionaries = superviseDictionaryMapper.selectList(Wrappers.lambdaQuery());
        return R.ok().put("superviseDictionary", superviseDictionaries);
    }

    @Override
    public R getDutyAndRelatedPeo(QueryDutyAndRelatedPeoParam params) {

        // 责任人列表
        List<SysUserEntity> dutyPeoList = new ArrayList<>();
        // 相关责任人列表
        List<SysUserEntity> relatedPeoList;
        if (params.getWorderSystem() == null) {
            params.setWorderSystem(1);
        }

        //如果是小咖
        if (2 == params.getWorderSystem()) {
            relatedPeoList = new ArrayList<>();
            WorderParm worderParm = new WorderParm();
            worderParm.setPage(1);
            worderParm.setLimit(1);
            worderParm.setWorderId(params.getWorderId());
            PageApiResponse pageApiResponse = xkApiService.selectOrder(worderParm);
            if (pageApiResponse.getPage().getTotalCount() == 0) {
                return R.error("小咖未查询到责任人信息");
            }
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(pageApiResponse.getPage().getList().get(0)));
            JSONArray array = jsonObject.getJSONArray("dutyPeoList");
            if (array == null || array.size() == 0) {
                return R.error("小咖未查询到责任人信息");
            }
            List<String> names = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                String userName = obj.getString("username");
                names.add(userName);
                SysUserEntity sysUserEntity = sysUserDao.queryByUserName(userName);
                if (sysUserEntity != null) {
                    SysUserEntity entity = new SysUserEntity();
                    entity.setUserId(sysUserEntity.getUserId());
                    entity.setEmployeeName(sysUserEntity.getEmployeeName());
                    entity.setUsername(sysUserEntity.getUsername());
                    relatedPeoList.add(entity);
                    dutyPeoList.add(sysUserEntity);

                }
            }
            if (dutyPeoList.size() == 0) {
                return R.error("责任人:" + JSON.toJSONString(names) + "未在news系统维护");
            }
            // 获取责任人
            return R.ok().put("dutyPeoList", dutyPeoList).put("relatedPeoList", relatedPeoList).put("dufaultDuty", dutyPeoList.get(0).getUserId());
        }
        //小咖订单
        // 查询数据字典获取一级对应的责任人和相关责任人角色
        List<SysDictionaryDetailEntity> superviseLv1 = sysDictionaryDetailDao.selectDetailByNumbers("supervise_lv_" + params.getSuperviseLv());
        Map<String, String> superviseLvMap = superviseLv1.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailName, SysDictionaryDetailEntity::getDetailNumber, (val1, val2) -> val1));

        // 如果督办等级一级，并且工单号不等于空的，责任人即为项目经理
        if (params.getWorderNo() != null) {
            // 查询当前工单属性表是否存在转单标识
            LambdaQueryWrapper<WorderInformationAttributeEntity> worderAttributeWrapper = Wrappers.lambdaQuery();
            // 查询工单对应的属性表是否存在转单标识
            WorderInformationAttributeEntity worderInformationAttribute = worderInformationAttributeDao.selectAttributeByWorderNo(params.getWorderNo(), "Transfer-Order", "TransferOrder");
            if (worderInformationAttribute != null && "1".equals(worderInformationAttribute.getAttributeValue()) && (params.getBrandId() != 41 && params.getBrandId() != 48)) {
                // 如果是非小咖品牌并且有转单标识，根据小咖品牌，加区域，服务类型查询对应的责任人
                params.setBrandId(41);
                dutyPeoList = this.baseMapper.queryQueryDutyAndRelatedPeo(params, superviseLvMap.get("duty_peo_role"));
            } else {
                // 未有转单标识，或者不是小咖品牌，直接读取当前工单的服务经理作为责任人
                SysUserEntity sysUser = this.baseMapper.getServiceUserByWorder(params.getWorderNo());
                // 如果工单还没派单服务经理，则继续根据区域品牌，服务属性查询
                if (sysUser != null) {
                    dutyPeoList.add(sysUser);
                } else {
                    dutyPeoList = this.baseMapper.queryQueryDutyAndRelatedPeo(params, superviseLvMap.get("duty_peo_role"));
                }
            }
        } else {
            // 其他情况根据，区域，品牌，服务属性，和指定角色查询相关账户
            dutyPeoList = this.baseMapper.queryQueryDutyAndRelatedPeo(params, superviseLvMap.get("duty_peo_role"));
        }
        // 查询所有账户
        List<SysUserEntity> listUsers = superviseInfomationMapper.getDutyPeoList();
        Long defaultUserId = !dutyPeoList.isEmpty() ? dutyPeoList.get(0).getUserId() : null;
        if (defaultUserId != null) {
            int index = -1;
            for (int i = 0; i < listUsers.size(); i++) {
                if (defaultUserId.equals(listUsers.get(i).getUserId())) {
                    index = i;
                }
            }
            if (index != -1) {
                SysUserEntity remove = listUsers.remove(index);
                listUsers.add(0, remove);
            }
        }
        // 查询相关责任人
        relatedPeoList = this.baseMapper.queryQueryDutyAndRelatedPeo(params, superviseLvMap.get("related_peo_role"));
        return Objects.requireNonNull(R.ok().put("dutyPeoList", listUsers)).put("dufaultDuty", defaultUserId).put("relatedPeoList", relatedPeoList);
    }

    @Override
    @Transactional
    public R saveSupervise(SuperviseInfomationVo params) throws InvocationTargetException, IllegalAccessException {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        //只有400客服角色才能创建督办来源是400热线的督办单
        if (params.getSuperviseClassification().equals(2)) {
            List<Long> roleIdList = user.getRoleIdList();
            if (!roleIdList.contains(68L)) {
                return R.error("非400客服督办来源不可选400热线");
            }
        }
        // 生成督办单号
        String prefix;
        if (params.getSuperviseSource() == 1) {
            prefix = "GD-";
        } else {
            prefix = "XZ-";
        }

        // 查询督办单字典表
        LambdaQueryWrapper<SuperviseDictionary> superviseDictionaryWrapper = Wrappers.lambdaQuery();
        superviseDictionaryWrapper.eq(SuperviseDictionary::getSuperviseClassification, params.getSuperviseClassification()).eq(SuperviseDictionary::getSuperviseType, params.getSuperviseType());
        List<SuperviseDictionary> superviseDictionaries = superviseDictionaryMapper.selectList(superviseDictionaryWrapper);
        SuperviseDictionary superviseDictionary = superviseDictionaries.get(0);
        prefix = prefix + superviseDictionary.getClassificationAbbre() + "-" + superviseDictionary.getTypeAbbre() + "-";

        String superviseNo = createSuperviseNo(prefix);
        // 对象复制属性
        SuperviseInfomation superviseInfomation = new SuperviseInfomation();
        BeanUtils.copyProperties(superviseInfomation, params);
        superviseInfomation.setWorderSuperviseNo(superviseNo);
        superviseInfomation.setCreateUserId(user.getUserId().intValue());
        superviseInfomation.setUpdateUserId(user.getUserId().intValue());
        superviseInfomation.setCreateTime(new Date());
        superviseInfomation.setSuperviseLv(1);
        WorderInformationEntity worderInformation = null;
        Optional<WorderInformationEntity> worderInformationEntity = Optional.ofNullable(params.getWorderNo()).map(e -> worderInformationService.getDotInfoByWorderNo(e));
        String candidateBranch = worderInformationEntity.map(w -> {
            return StringUtils.isBlank(w.getDotShortName()) ? w.getCandidateBranch() : w.getDotShortName();
        }).orElse("");

        Date time = worderInformationEntity.map(WorderInformationEntity::getCreateTime).orElse(null);
        superviseInfomation.setCandidateBranch(candidateBranch);
        superviseInfomation.setWorderCreateTime(time);
        superviseInfomation.setNextConnectionTime(params.getNextConnectionTime());
        // 如果责任人为空，保存督办单状态为已关闭
        if (params.getDutyPeo() == null) {
            superviseInfomation.setState(3);
        }
        // 保存督办单主表
        superviseInfomationMapper.insert(superviseInfomation);

        // 保存来电记录
        SuperviseTeleRecord superviseTeleRecord = new SuperviseTeleRecord();
        superviseTeleRecord.setSuperviseId(superviseInfomation.getId());
        superviseTeleRecord.setSuperviseClassification(superviseInfomation.getSuperviseClassification());
        superviseTeleRecord.setSuperviseType(superviseInfomation.getSuperviseType());
        superviseTeleRecord.setSuperviseLv(superviseInfomation.getSuperviseLv());
        superviseTeleRecord.setCreateUserId(user.getUserId().intValue());
        superviseTeleRecord.setSuperviseContent(superviseInfomation.getSuperviseContent());
        superviseTeleRecord.setProcessTime(superviseInfomation.getProcessTime());
        superviseTeleRecord.setNextConnectionTime(params.getNextConnectionTime());
        superviseTeleRecord.setDutyPeo(superviseInfomation.getDutyPeo());
        superviseTeleRecord.setRelatedPeo(superviseInfomation.getRelatedPeo());
        superviseTeleRecord.setOperationType(0);
        if (params.getDutyPeo() == null) {
            superviseTeleRecord.setResult(0);
        }
        superviseTeleRecordMapper.insert(superviseTeleRecord);

        // 记录操作日志
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
        superviseOperationRecord.setOperationType(0);
        superviseOperationRecord.setOptrationContent(user.getEmployeeName() + "创建督办单：" + superviseInfomation.getWorderSuperviseNo() + ", 督办内容：" + params.getSuperviseContent());
        superviseOperationRecord.setDutyPeo(superviseInfomation.getDutyPeo() != null ? getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()) : "");
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        // 给责任人和相关责任人发送短信,省市+品牌+用户姓名+news单号+网点+跳闸类目+TS？+投诉事由
        sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), superviseInfomation.getRelatedPeo());

        return R.ok();
    }


    @Override
    public R getSuperviseListByWorder(SuperviseWorderVo params) {
        // 分页查询工单号关联的督办单
        IPage<SuperviseInfomationInfo> page = superviseInfomationMapper.selectListByWorder(new Page<>(params.getPage(), params.getPageSize()), params);
        // 翻译督办属性
        List<SuperviseInfomationInfo> records = page.getRecords();
        translateSupervise(records);
        return Objects.requireNonNull(R.ok().put("list", records)).put("total", page.getTotal());
    }

    @Override
    public R superviseClassTypeLv() {
        // 督办来源
        List<SuperviseDictionary> superviseClassificationList = new ArrayList<>();
        // 督办类型
        List<SuperviseDictionary> superviseTypeList = new ArrayList<>();

        // 查询督办单字典信息
        List<SuperviseDictionary> superviseDictionaries = superviseDictionaryMapper.selectList(Wrappers.lambdaQuery());

        Map<Integer, String> superviseClassificationMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseClassification, SuperviseDictionary::getSuperviseClassificationName, (val1, val2) -> val1));
        superviseClassificationMap.forEach((k, v) -> {
            SuperviseDictionary superviseDictionary = new SuperviseDictionary();
            superviseDictionary.setSuperviseClassification(k);
            superviseDictionary.setSuperviseClassificationName(v);
            superviseClassificationList.add(superviseDictionary);
        });

        Map<Integer, String> superviseTypeMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseType, SuperviseDictionary::getSuperviseTypeName, (val1, val2) -> val1));
        superviseTypeMap.forEach((k, v) -> {
            SuperviseDictionary superviseDictionary = new SuperviseDictionary();
            superviseDictionary.setSuperviseType(k);
            superviseDictionary.setSuperviseTypeName(v);
            superviseTypeList.add(superviseDictionary);
        });

        // 查询督办等级数据字典
        List<SysDictionaryDetailEntity> dictionaryDetailEntities = sysDictionaryDetailDao.selectDetailLikeNumber("supervise_lv", "value");
        return Objects.requireNonNull(Objects.requireNonNull(R.ok().put("superviseClassificationList", superviseClassificationList)).put("superviseTypeList", superviseTypeList)).put("superviseLvList", dictionaryDetailEntities);
    }

    @Override
    public R getSuperviseTripType(QueryDutyAndRelatedPeoParam param) {
        String typeAbbre = param.getTypeAbbre();
        Integer superviseType = param.getSuperviseType();
        if(StringUtils.isBlank(typeAbbre) && superviseType == null){
            List<SysDictionaryDetailEntity> superviseTripTypes = sysDictionaryDetailDao.selectDetailByNumbers("supervise_trip_type");
            return R.ok().put("tripList", superviseTripTypes);
        }
        QueryWrapper<SuperviseDictionaryTripType> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(typeAbbre)){
            queryWrapper.eq("supervise_type_abbre",typeAbbre);
        };
        if(superviseType != null){
            queryWrapper.eq("supervise_type",superviseType);
        };
        List<SuperviseDictionaryTripType> list = superviseDictionaryTripTypeService.list(queryWrapper);
        return R.ok().put("tripList", list);
    }

    @Override
    public R getDutyPeoList() {
        List<SysUserEntity> dutyPeoList = superviseInfomationMapper.getDutyPeoList();
        return R.ok().put("dutyPeoList", dutyPeoList);
    }

    @Override
    public R getSuperviseDetail(Integer id) {
        // 根据ID查询督办单详情
        List<SuperviseInfomationInfo> list = superviseInfomationMapper.querySuperviseDetailById(id);
        if (list == null || list.isEmpty()) {
            return R.error("查询不到督办单信息");
        }
        // 翻译相关字段
        translateSupervise(list);
        SuperviseInfomationInfo superviseInfomationInfo = list.get(0);
        // 翻译区域信息
        // 区县
        BizRegionEntity bizRegionDis = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(superviseInfomationInfo.getAreaId().toString()));
        // 地市
        BizRegionEntity bizRegionCity = InitRegionUtil.REGION_ID_MAP.get(bizRegionDis.getPid());
        // 省份
        BizRegionEntity bizRegionPro = InitRegionUtil.REGION_ID_MAP.get(bizRegionCity.getPid());
        List<String> areaIds = new ArrayList<>();
        areaIds.add(bizRegionPro.getId().toString());
        areaIds.add(bizRegionCity.getId().toString());
        areaIds.add(bizRegionDis.getId().toString());
        superviseInfomationInfo.setAreaName(bizRegionPro.getName() + bizRegionCity.getName() + bizRegionDis.getName());
        superviseInfomationInfo.setAreaIds(areaIds);
        // 翻译相关责任人
        String relatedPeo = superviseInfomationInfo.getRelatedPeo();
        if (StringUtils.isNotBlank(relatedPeo)) {
            String relatedPeoName = getEmployeeNameByUser(relatedPeo);
            superviseInfomationInfo.setRelatedPeoName(relatedPeoName);
        }
        return R.ok().put("superviseDetail", superviseInfomationInfo);
    }

    @Override
    public R getTeleRecords(QueryTeleRecordVo param) {
        // 分页查询来电记录信息
        IPage<SuperviseTeleRecordInfo> pages = superviseTeleRecordMapper.selectTelePage(new Page<>(param.getPage(), param.getPageSize()), param.getSuperviseId());
        List<SuperviseTeleRecordInfo> records = pages.getRecords();
        if (!records.isEmpty()) {
            // 翻译督办来源，督办分类，督办等级
            translateSuperviseTele(records);
            // 获取当前分页内的来电记录ID
            List<Integer> teleIds = records.stream().map(SuperviseTeleRecordInfo::getId).collect(Collectors.toList());
            // 查询回复记录
            List<SuperviseRecoverRecord> superviseRecoverRecords = superviseRecoverRecordMapper.selectRecoverList(teleIds);
            // 遍历来电记录
            records.forEach(teleInfo -> {
                Integer id = teleInfo.getId();
                List<SuperviseRecoverRecord> collects = superviseRecoverRecords.stream().filter(item -> Objects.equals(item.getTeleId(), id)).collect(Collectors.toList());
                teleInfo.setRecoverRecords(collects);
            });
        }
        return Objects.requireNonNull(R.ok().put("list", records)).put("total", pages.getTotal());
    }

    @Override
    public R getOperationRecords(QueryOperationVo param) {
        // 分页查询督办单操作记录
        LambdaQueryWrapper<SuperviseOperationRecord> operationRecordWrapper = Wrappers.lambdaQuery();
        operationRecordWrapper.eq(SuperviseOperationRecord::getSuperviseId, param.getSuperviseId()).eq(SuperviseOperationRecord::getIsDelete, 0).orderByDesc(SuperviseOperationRecord::getId);
        IPage<SuperviseOperationRecord> page = superviseOperationRecordMapper.selectPage(new Page<>(param.getPage(), param.getPageSize()), operationRecordWrapper);
        List<SuperviseOperationRecord> records = page.getRecords();
        // 翻译督办单状态
        List<SysDictionaryDetailEntity> operationTypeList = sysDictionaryDetailDao.selectDetailByNumbers("supervise_operation_type");
        Map<String, String> operationTypeMap = operationTypeList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
        // 翻译操作类型
        records.forEach(item -> {
            item.setOperationTypeName(operationTypeMap.get(item.getOperationType().toString()));
        });

        return Objects.requireNonNull(R.ok().put("list", records)).put("total", page.getTotal());
    }

    @Override
    @Transactional
    public R recover(SuperviseRecoverVo param) {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        String name = StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername();
        // 根据督办单ID查询督办单信息
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(param.getSuperviseId());
        // 根据督办单ID查询来电记录信息
        LambdaQueryWrapper<SuperviseTeleRecord> teleRecordyWrapper = Wrappers.lambdaQuery();
        teleRecordyWrapper.eq(SuperviseTeleRecord::getSuperviseId, param.getSuperviseId()).eq(SuperviseTeleRecord::getIsDelete, 0).orderByDesc(SuperviseTeleRecord::getId);
        List<SuperviseTeleRecord> superviseTeleRecords = superviseTeleRecordMapper.selectList(teleRecordyWrapper);
        // 获取最新一次来电记录
        SuperviseTeleRecord superviseTeleRecord = superviseTeleRecords.get(0);

        // 保存回复记录
        SuperviseRecoverRecord superviseRecoverRecord = new SuperviseRecoverRecord();
        superviseRecoverRecord.setTeleId(superviseTeleRecord.getId());
        superviseRecoverRecord.setCreateUserId(user.getUserId().intValue());
        superviseRecoverRecord.setCreateUserName(name);
        superviseRecoverRecord.setRecoverContent(param.getRecoverContent());
        superviseRecoverRecord.setFileId(param.getFileId());
        superviseRecoverRecord.setDutyPeo(getEmployeeNameByUser(superviseTeleRecord.getDutyPeo().toString()));
        superviseRecoverRecord.setRelatedPeo(getEmployeeNameByUser(superviseTeleRecord.getRelatedPeo()));
        superviseRecoverRecord.setRecoverType(param.getRecoverType());
        superviseRecoverRecordMapper.insert(superviseRecoverRecord);

        // 更新督办单的回复时间和状态
        superviseInfomation.setRecoverTime(new Date());
        superviseInfomation.setRecoverType(param.getRecoverType());
        superviseInfomation.setState(1);
        superviseInfomationMapper.updateById(superviseInfomation);

        // 记录操作记录

        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(name);
        superviseOperationRecord.setOperationType(2);
        superviseOperationRecord.setOptrationContent(name + "回复，督办单：" + superviseInfomation.getWorderSuperviseNo() + ", 回复内容：" + param.getRecoverContent());
        superviseOperationRecord.setDutyPeo(superviseRecoverRecord.getDutyPeo());
        superviseOperationRecord.setRelatedPeo(superviseRecoverRecord.getRelatedPeo());
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        return R.ok();
    }

    @Override
    @Transactional
    public R updateSupervise(UpdateSuperviseInfomationVo params) {
        //获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        // 查询督办单号
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(params.getId());
        // 判断责任人是否变更
        Integer sendMsgDutyPeo = null;
        if (!superviseInfomation.getDutyPeo().equals(params.getDutyPeo())) {
            sendMsgDutyPeo = params.getDutyPeo();
        }
        // 判断相关责任人是否变更
        String sendMsgRelatedPeo = "";
        if (StringUtils.isNotBlank(params.getRelatedPeo())) {
            if (StringUtils.isBlank(superviseInfomation.getRelatedPeo())) {
                sendMsgRelatedPeo = params.getRelatedPeo();
            } else {
                Set<String> aSet = new HashSet<>(Arrays.asList(superviseInfomation.getRelatedPeo().split(",")));
                Set<String> bSet = new HashSet<>(Arrays.asList(params.getRelatedPeo().split(",")));
                // 使用差集操作获取不在a中但在b中的内容
                bSet.removeAll(aSet);
                // 将结果转换回字符串
                sendMsgRelatedPeo = String.join(",", bSet);
            }
        }
        superviseInfomation.setWorderNo(params.getWorderNo());
        superviseInfomation.setTripType(params.getTripType());
        superviseInfomation.setNextConnectionTime(params.getNextConnectionTime());
        superviseInfomation.setDutyPeo(params.getDutyPeo());
        superviseInfomation.setRelatedPeo(params.getRelatedPeo());
        superviseInfomation.setUpdateUserId(user.getUserId().intValue());
        superviseInfomation.setUpdateTime(new Date());
        superviseInfomationMapper.updateById(superviseInfomation);
        // 更新最新一笔来电记录中的下次联系时间，责任人，相关责任人
        // 根据督办单ID查询来电记录信息
        LambdaQueryWrapper<SuperviseTeleRecord> teleRecordyWrapper = Wrappers.lambdaQuery();
        teleRecordyWrapper.eq(SuperviseTeleRecord::getSuperviseId, params.getId()).eq(SuperviseTeleRecord::getIsDelete, 0).orderByDesc(SuperviseTeleRecord::getId);
        List<SuperviseTeleRecord> superviseTeleRecords = superviseTeleRecordMapper.selectList(teleRecordyWrapper);
        // 获取最新一次来电记录
        SuperviseTeleRecord superviseTeleRecord = superviseTeleRecords.get(0);
        superviseTeleRecord.setNextConnectionTime(params.getNextConnectionTime());
        superviseTeleRecord.setDutyPeo(params.getDutyPeo());
        superviseTeleRecord.setRelatedPeo(params.getRelatedPeo());
        superviseTeleRecordMapper.updateById(superviseTeleRecord);
        // 保存操作记录
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
        superviseOperationRecord.setOperationType(5);
        superviseOperationRecord.setOptrationContent(user.getEmployeeName() + "编辑了，督办单：" + superviseInfomation.getWorderSuperviseNo());
        superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(params.getDutyPeo().toString()));
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(params.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        // 如果责任人和相关责任人变更，发送短信
        if (sendMsgDutyPeo != null || StringUtils.isNotBlank(sendMsgRelatedPeo)) {
            sendMsg(superviseInfomation, sendMsgDutyPeo, sendMsgRelatedPeo);
        }

        return R.ok();
    }

    @Override
    @Transactional
    public R superviseUp(SuperviseUpVo params) {
        // 获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        String userName = StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername();
        // 查询督办单号
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(params.getSuperviseId());
        // 根据督办单ID查询来电记录信息
        LambdaQueryWrapper<SuperviseTeleRecord> teleRecordyWrapper = Wrappers.lambdaQuery();
        teleRecordyWrapper.eq(SuperviseTeleRecord::getSuperviseId, superviseInfomation.getId()).eq(SuperviseTeleRecord::getIsDelete, 0).orderByDesc(SuperviseTeleRecord::getId);
        List<SuperviseTeleRecord> superviseTeleRecords = superviseTeleRecordMapper.selectList(teleRecordyWrapper);
        // 获取最新一次来电记录
        SuperviseTeleRecord superviseTeleRecord = superviseTeleRecords.get(0);
        // 根据当前督办等级加一
        Integer superviseLv = superviseTeleRecord.getSuperviseLv();
        // 最高等级5级，不能在升级
        if (superviseLv >= 6) {
            return R.error("当前督办等级已是最高，无法升级");
        }
        superviseLv++;
        // 最新的一次来电处理结果修改为升级
        superviseTeleRecord.setResult(1);
        superviseTeleRecord.setNextConnectionTime(params.getNextConnectionTime());
        superviseTeleRecord.setSuperviseLv(superviseLv);
        superviseTeleRecord.setLvUpReason(params.getLvUpReason());
        superviseTeleRecord.setTripTime(new Date());
        if (superviseInfomation.getState() == 1) {
            superviseInfomation.setState(2);
        }
        superviseInfomation.setNextConnectionTime(params.getNextConnectionTime());
        superviseInfomation.setLvUpReason(params.getLvUpReason());
        superviseInfomation.setSuperviseLv(superviseLv);
        // 查询相关责任人
        // 查询数据字典获取一级对应的责任人和相关责任人角色
        List<SysDictionaryDetailEntity> superviseLvDic = sysDictionaryDetailDao.selectDetailByNumbers("supervise_lv_" + superviseLv);
        Map<String, String> superviseLvMap = superviseLvDic.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailName, SysDictionaryDetailEntity::getDetailNumber, (val1, val2) -> val1));
        QueryDutyAndRelatedPeoParam queryDutyAndRelatedPeoParam = new QueryDutyAndRelatedPeoParam();
        queryDutyAndRelatedPeoParam.setBrandId(superviseInfomation.getBrandId());
        queryDutyAndRelatedPeoParam.setServiceType(superviseInfomation.getServiceType());
        // 区县
        BizRegionEntity bizRegionDis = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(superviseInfomation.getAreaId().toString()));
        // 地市
        BizRegionEntity bizRegionCity = InitRegionUtil.REGION_ID_MAP.get(bizRegionDis.getPid());
        // 省份
        BizRegionEntity bizRegionPro = InitRegionUtil.REGION_ID_MAP.get(bizRegionCity.getPid());
        List<String> areaIds = new ArrayList<>();
        areaIds.add(bizRegionPro.getId().toString());
        areaIds.add(bizRegionCity.getId().toString());
        areaIds.add(bizRegionDis.getId().toString());
        queryDutyAndRelatedPeoParam.setAreaIds(areaIds);
        List<SysUserEntity> relatedPeoRole = this.baseMapper.queryQueryDutyAndRelatedPeo(queryDutyAndRelatedPeoParam, superviseLvMap.get("related_peo_role"));
        if (!relatedPeoRole.isEmpty()) {
            String relatedPeo = relatedPeoRole.stream().map(item -> item.getUserId().toString()).collect(Collectors.joining(","));
            // 更新相关责任人
            superviseTeleRecord.setRelatedPeo(relatedPeo);
            superviseInfomation.setRelatedPeo(relatedPeo);
        } else {
            superviseTeleRecord.setRelatedPeo("");
            superviseInfomation.setRelatedPeo("");
        }
        // 给责任人发送短信
        // 给责任人和相关责任人发送短信,省市+品牌+用户姓名+news单号+网点+跳闸类目+TS？+投诉事由
        sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), superviseInfomation.getRelatedPeo());

        superviseTeleRecordMapper.updateById(superviseTeleRecord);
        superviseInfomation.setUpdateUserId(user.getUserId().intValue());
        superviseInfomation.setUpdateTime(new Date());
        this.baseMapper.updateById(superviseInfomation);

        // 记录操作记录
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(userName);
        superviseOperationRecord.setOperationType(3);
        superviseOperationRecord.setOptrationContent(userName + "升级了，督办单：" + superviseInfomation.getWorderSuperviseNo() + "，升级原因：" + params.getLvUpReason());
        superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()));
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        return R.ok();
    }

    @Override
    @Transactional
    public R superviseClose(SuperviseCloseVo params) {
        // 获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        // 查询督办单号
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(params.getSuperviseId());
        superviseInfomation.setCloseRemark(params.getCloseRemark());
        superviseInfomation.setStimulate(params.getStimulate());
        superviseInfomation.setNextConnectionTime(params.getNextConnectionTime());
        // 根据督办单ID查询来电记录信息
        LambdaQueryWrapper<SuperviseTeleRecord> teleRecordyWrapper = Wrappers.lambdaQuery();
        teleRecordyWrapper.eq(SuperviseTeleRecord::getSuperviseId, superviseInfomation.getId()).eq(SuperviseTeleRecord::getIsDelete, 0).orderByDesc(SuperviseTeleRecord::getId);
        List<SuperviseTeleRecord> superviseTeleRecords = superviseTeleRecordMapper.selectList(teleRecordyWrapper);
        // 获取最新一次来电记录
        SuperviseTeleRecord superviseTeleRecord = superviseTeleRecords.get(0);
        // 最新的一次来电处理结果修改为已关闭
        superviseTeleRecord.setResult(0);
        superviseTeleRecord.setNextConnectionTime(params.getNextConnectionTime());
        superviseTeleRecordMapper.updateById(superviseTeleRecord);
        // 在没有回复的来电记录下增加一条回复记录，标准当前来电记录已关闭
        List<SuperviseTeleRecord> notRecoverTeleRecords = superviseTeleRecordMapper.selectTeleNotRecover(superviseInfomation.getId());
        for (SuperviseTeleRecord notRecoverRecord : notRecoverTeleRecords) {
            SuperviseRecoverRecord superviseRecoverRecord = new SuperviseRecoverRecord();
            superviseRecoverRecord.setTeleId(notRecoverRecord.getId());
            superviseRecoverRecord.setCreateUserId(user.getUserId().intValue());
            superviseRecoverRecord.setCreateUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
            superviseRecoverRecord.setRecoverContent("督办单已关闭");
            superviseRecoverRecord.setDutyPeo(getEmployeeNameByUser(notRecoverRecord.getDutyPeo().toString()));
            superviseRecoverRecord.setRelatedPeo(getEmployeeNameByUser(notRecoverRecord.getRelatedPeo()));
            superviseRecoverRecordMapper.insert(superviseRecoverRecord);
            notRecoverRecord.setResult(0);
            superviseTeleRecordMapper.updateById(notRecoverRecord);
        }

        // 更新督办单主表信息
        superviseInfomation.setState(3);
        superviseInfomation.setCloseTime(new Date());
        superviseInfomation.setUpdateUserId(user.getUserId().intValue());
        superviseInfomation.setUpdateTime(new Date());
        this.baseMapper.updateById(superviseInfomation);

        // 记录操作记录
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
        superviseOperationRecord.setOperationType(4);
        superviseOperationRecord.setOptrationContent(user.getEmployeeName() + "关闭了，督办单：" + superviseInfomation.getWorderSuperviseNo() + "，关单备注：" + params.getCloseRemark());
        superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()));
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        return R.ok();
    }

    @Override
    public R checkSuperviseByWorder(String worderNo) {
        LambdaQueryWrapper<SuperviseInfomation> superviseInfomationWrapper = Wrappers.lambdaQuery();
        superviseInfomationWrapper
                .eq(SuperviseInfomation::getWorderNo, worderNo)
                .eq(SuperviseInfomation::getIsDelete, 0)
                .ne(SuperviseInfomation::getState, 3);
        Integer count = this.baseMapper.selectCount(superviseInfomationWrapper);
        if (count <= 0) {
            return R.error("该工单未查询到符合追加的督办单，请进行督办");
        }
        return R.ok();
    }

    @Override
    public R getSuperviseLv() {
        List<SysDictionaryDetailEntity> dictionaryDetailEntities = sysDictionaryDetailDao.selectDetailLikeNumber("supervise_lv", "value");
        return R.ok().put("superviseLvList", dictionaryDetailEntities);
    }

    @Override
    @Transactional
    public R readdition(SuperviseReadditionVo params) {
        // 获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        // 查询督办单号
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(params.getSuperviseId());

        // 在没有回复的来电记录下增加一条回复记录，标准当前来电记录已关闭
        List<SuperviseTeleRecord> notRecoverTeleRecords = superviseTeleRecordMapper.selectTeleNotRecover(superviseInfomation.getId());
        for (SuperviseTeleRecord notRecoverRecord : notRecoverTeleRecords) {
            SuperviseRecoverRecord superviseRecoverRecord = new SuperviseRecoverRecord();
            superviseRecoverRecord.setTeleId(notRecoverRecord.getId());
            superviseRecoverRecord.setCreateUserId(user.getUserId().intValue());
            superviseRecoverRecord.setCreateUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
            superviseRecoverRecord.setRecoverContent("督办单已追加来电");
            superviseRecoverRecord.setDutyPeo(getEmployeeNameByUser(notRecoverRecord.getDutyPeo().toString()));
            superviseRecoverRecord.setRelatedPeo(getEmployeeNameByUser(notRecoverRecord.getRelatedPeo()));
            superviseRecoverRecordMapper.insert(superviseRecoverRecord);
            notRecoverRecord.setResult(2);
            superviseTeleRecordMapper.updateById(notRecoverRecord);
        }

        // 创建一笔来电记录
        SuperviseTeleRecord superviseTeleRecord = new SuperviseTeleRecord();
        superviseTeleRecord.setSuperviseId(superviseInfomation.getId());
        superviseTeleRecord.setSuperviseClassification(params.getSuperviseClassification());
        superviseTeleRecord.setSuperviseType(params.getSuperviseType());
        superviseTeleRecord.setSuperviseLv(params.getSuperviseLv());
        superviseTeleRecord.setSuperviseContent(params.getSuperviseContent());
        superviseTeleRecord.setProcessTime(params.getProcessTime());
        superviseTeleRecord.setNextConnectionTime(params.getNextConnectionTime());
        superviseTeleRecord.setDutyPeo(params.getDutyPeo());
        superviseTeleRecord.setRelatedPeo(params.getRelatedPeo());
        superviseTeleRecord.setCreateUserId(user.getUserId().intValue());
        superviseTeleRecord.setOperationType(2);
        superviseTeleRecordMapper.insert(superviseTeleRecord);

        // 更新督办单
        superviseInfomation.setSuperviseClassification(params.getSuperviseClassification());
        superviseInfomation.setSuperviseType(params.getSuperviseType());
        superviseInfomation.setSuperviseLv(params.getSuperviseLv());
        superviseInfomation.setSuperviseContent(params.getSuperviseContent());
        superviseInfomation.setProcessTime(params.getProcessTime());
        superviseInfomation.setDutyPeo(params.getDutyPeo());
        superviseInfomation.setRelatedPeo(params.getRelatedPeo());
        superviseInfomation.setUpdateTime(new Date());
        superviseInfomation.setUpdateUserId(user.getUserId().intValue());
        superviseInfomation.setState(0);
        superviseInfomation.setRecoverType(0);
        superviseInfomation.setNextConnectionTime(params.getNextConnectionTime());
        this.baseMapper.updateById(superviseInfomation);
        // 给责任人和相关责任人发送短信
        // 给责任人和相关责任人发送短信,省市+品牌+用户姓名+news单号+网点+跳闸类目+TS？+投诉事由
        sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), superviseInfomation.getRelatedPeo());

        // 记录操作记录
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(user.getUserId().intValue());
        superviseOperationRecord.setUserName(StringUtils.isNotBlank(user.getEmployeeName()) ? user.getEmployeeName() : user.getUsername());
        superviseOperationRecord.setOperationType(1);
        superviseOperationRecord.setOptrationContent(user.getEmployeeName() + "追加来电记录，督办单：" + superviseInfomation.getWorderSuperviseNo() + "，督办内容：" + params.getSuperviseContent());
        superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()));
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);

        return R.ok();
    }

    @Override
    public void export(SuperviseQueryListParam param, HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            //获取登陆用户
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            if (user == null) {
                throw new Exception("登陆超时");
            }

            // 账户角色集合
            List<Long> roleIdList = user.getRoleIdList();
            if (roleIdList.size() == 1 && roleIdList.contains(31L)) {
                // 如果当前当前角色是运营，并且只有一个，查自己创建的督办单和自己区域品牌服务类下的督办单
                param.setQueryRoleType(3);
                param.setUserId(user.getUserId());
            } else if (roleIdList.contains(3L)) {
                // 如果是项目经理角色只能查询当前责任人是自己的单子
                param.setQueryRoleType(1);
                param.setUserId(user.getUserId());
            } else if (roleIdList.contains(2L)) {
                // 如果是客服经理，查询自己创建的单子
                param.setQueryRoleType(2);
                param.setUserId(user.getUserId());
            } else if (roleIdList.contains(4L)) {
                // 如果是网点管理员，查询自己创建的单子
                param.setQueryRoleType(4);
                param.setUserId(user.getUserId());
            }

            String filename = new String("督办单信息".getBytes("gbk"), StandardCharsets.ISO_8859_1);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xls");
            excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
            //查询导出数据
            List<SuperviseInfoExport> superviseInfoExports = this.baseMapper.querySuperviseReport(param);

            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "督办单信息").head(SuperviseInfoExport.class).build();
            excelWriter.write(superviseInfoExports, writeSheet);
        } catch (Exception e) {
            log.error("导出督办单信息出现异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    @Transactional
    public void tripTask() {
        // 查询逾期未回复的来电记录或者超过回复时间的来电记录
        List<SuperviseTeleRecord> list = superviseTeleRecordMapper.selectOverdueTripList();
        if (list.isEmpty()) {
            log.info("未查询到需要跳闸处理的来电记录");
            return;
        }
        List<Integer> ids = list.stream().map(SuperviseTeleRecord::getId).collect(Collectors.toList());
        UpdateWrapper<SuperviseTeleRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids).set("task_state", 1).set("task_time", new Date());
        superviseTeleRecordMapper.update(null, updateWrapper);
        // 更新来电记录的定时任务处理状态为处理中
        // 获取当前时间
        Date now = new Date();
        for (SuperviseTeleRecord superviseTeleRecord : list) {
            // 当前的督办等级
            Integer beforeSuperviseLv = superviseTeleRecord.getSuperviseLv();
            Integer autoUp = superviseTeleRecord.getAutoUp();

            // 如果督办单逾期，并且是投诉单，并且未超过五级以上，自动升级督办单
            if (superviseTeleRecord.getOverdue() == 1 && superviseTeleRecord.getSuperviseType() == 3 && superviseTeleRecord.getAutoUp() == 0 && superviseTeleRecord.getSuperviseLv() <= 5) {
                // 升级督办单
                superviseTeleRecord.setAutoUp(1);
                upSupervise(superviseTeleRecord);
            }

            // 如果督办等级小于等于5，升级一次
            if (beforeSuperviseLv <= 5 && autoUp == 1 && superviseTeleRecord.getOverdue() == 1 && superviseTeleRecord.getSuperviseType() == 3) {
                upSupervise(superviseTeleRecord);
            }

            // 跳闸一次
            superviseTeleRecord.setTripTime(now);
            superviseTeleRecord.setTaskState(0);
            superviseTeleRecord.setTripNum(superviseTeleRecord.getTripNum() + 1);
            superviseTeleRecordMapper.updateById(superviseTeleRecord);

            // 如果当前督办等于大于5级发送短信
            if (beforeSuperviseLv > 5) {
                SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(superviseTeleRecord.getSuperviseId());
                // 发送短信
                sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), superviseInfomation.getRelatedPeo());
            }

            // 记录操作日志
            SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
            superviseOperationRecord.setSuperviseId(superviseTeleRecord.getSuperviseId());
            superviseOperationRecord.setUserId(1);
            superviseOperationRecord.setUserName("系统自动");
            superviseOperationRecord.setOperationType(7);
            superviseOperationRecord.setOptrationContent("责任人超过要求回复时长：" + superviseTeleRecord.getDuration() + "分钟，系统自动跳闸一次");
            superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseTeleRecord.getDutyPeo().toString()));
            superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseTeleRecord.getRelatedPeo()));
            superviseOperationRecordMapper.insert(superviseOperationRecord);
        }
    }

    @Override
    @Transactional
    public R transferOrder(Integer worderId) {
        // 获取登陆用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return R.error("登陆超时，请重新登陆");
        }
        // 根据工单ID查询工单信息
        LambdaQueryWrapper<WorderInformationEntity> worderInforWrapper = Wrappers.lambdaQuery();
        worderInforWrapper.select(WorderInformationEntity::getWorderNo, WorderInformationEntity::getTemplateId, WorderInformationEntity::getPmId, WorderInformationEntity::getAreaId);
        worderInforWrapper.eq(WorderInformationEntity::getWorderId, worderId);
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInforWrapper);
        // 查询工单模版
        WorderTemplateDto templateInfo = worderTemplateDao.findTemplateInfoById(worderInformationEntity.getTemplateId());
        // 获取品牌
        String brandId = templateInfo.getBrandId();
        Integer serviceTypeEnum = templateInfo.getServiceTypeEnum();
        // 保存工单属性记录表，增加转单标识
        insertTransferOrderAttr(worderId);
        // 查询工单对应的未完成的督办单信息
        LambdaQueryWrapper<SuperviseInfomation> superviseInfoWrapper = Wrappers.lambdaQuery();
        superviseInfoWrapper.eq(SuperviseInfomation::getWorderNo, worderInformationEntity.getWorderNo())
                .ne(SuperviseInfomation::getState, 3).eq(SuperviseInfomation::getIsDelete, 0);
        List<SuperviseInfomation> superviseInfomations = this.baseMapper.selectList(superviseInfoWrapper);
        if (superviseInfomations == null || superviseInfomations.isEmpty()) {
            return R.ok();
        }
        // 如果非小咖的品牌，需要根据小咖的品牌，和当前工单的区域，服务类型查询角色是项目经理的责任人
        if (!"41".equals(brandId) && !"48".equals(brandId)) {
            QueryDutyAndRelatedPeoParam queryDutyAndRelatedPeoParam = new QueryDutyAndRelatedPeoParam();
            queryDutyAndRelatedPeoParam.setBrandId(41);
            queryDutyAndRelatedPeoParam.setServiceType(serviceTypeEnum);
            // 区县
            BizRegionEntity bizRegionDis = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(worderInformationEntity.getAreaId().toString()));
            // 地市
            BizRegionEntity bizRegionCity = InitRegionUtil.REGION_ID_MAP.get(bizRegionDis.getPid());
            // 省份
            BizRegionEntity bizRegionPro = InitRegionUtil.REGION_ID_MAP.get(bizRegionCity.getPid());
            List<String> areaIds = new ArrayList<>();
            areaIds.add(bizRegionPro.getId().toString());
            areaIds.add(bizRegionCity.getId().toString());
            areaIds.add(bizRegionDis.getId().toString());
            queryDutyAndRelatedPeoParam.setAreaIds(areaIds);
            // 根据品牌，区域，服务类型，角色，查询符合条件的责任人
            List<SysUserEntity> dutyPeos = this.baseMapper.queryQueryDutyAndRelatedPeo(queryDutyAndRelatedPeoParam, "3");
            if (dutyPeos != null && !dutyPeos.isEmpty()) {
                // 如果多个取第一个，责任人
                SysUserEntity sysUserEntity = dutyPeos.get(0);
                // 更新督办单的责任人
                updateSuperviseDutyPeo(superviseInfomations, sysUserEntity, user);
            }
        }
        return R.ok();
    }

    /**
     * 更新督办单的责任人信息
     *
     * @param superviseInfomations
     * @param sysUserEntity
     */
    private void updateSuperviseDutyPeo(List<SuperviseInfomation> superviseInfomations, SysUserEntity sysUserEntity, SysUserEntity loginUser) {
        for (SuperviseInfomation superviseInfomation : superviseInfomations) {
            superviseInfomation.setDutyPeo(sysUserEntity.getUserId().intValue());
            // 更新最新一笔来电记录中的下责任人
            // 根据督办单ID查询来电记录信息
            LambdaQueryWrapper<SuperviseTeleRecord> teleRecordyWrapper = Wrappers.lambdaQuery();
            teleRecordyWrapper.eq(SuperviseTeleRecord::getSuperviseId, superviseInfomation.getId()).eq(SuperviseTeleRecord::getIsDelete, 0).orderByDesc(SuperviseTeleRecord::getId);
            List<SuperviseTeleRecord> superviseTeleRecords = superviseTeleRecordMapper.selectList(teleRecordyWrapper);
            // 获取最新一次来电记录
            SuperviseTeleRecord superviseTeleRecord = superviseTeleRecords.get(0);
            superviseTeleRecord.setDutyPeo(sysUserEntity.getUserId().intValue());
            superviseTeleRecordMapper.updateById(superviseTeleRecord);
            // 保存操作记录
            SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
            superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
            superviseOperationRecord.setUserId(loginUser.getUserId().intValue());
            superviseOperationRecord.setUserName(StringUtils.isNotBlank(loginUser.getEmployeeName()) ? loginUser.getEmployeeName() : loginUser.getUsername());
            superviseOperationRecord.setOperationType(8);
            superviseOperationRecord.setOptrationContent(superviseOperationRecord.getUserName() + "操作转单，工单：" + superviseInfomation.getWorderNo() + "，责任人：" + sysUserEntity.getEmployeeName());
            superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()));
            superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
            superviseOperationRecordMapper.insert(superviseOperationRecord);

            superviseInfomation.setUpdateTime(new Date());
            superviseInfomation.setUpdateUserId(loginUser.getUserId().intValue());
            this.baseMapper.updateById(superviseInfomation);
            // 发送短信
            sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), null);
        }
    }

    /**
     * 插入工单属性记录表，转单标识
     *
     * @param worderId
     */
    private void insertTransferOrderAttr(Integer worderId) {
        // 先根据工单ID删除转单标识，再插入
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderInformationAttrWrapper = Wrappers.lambdaQuery();
        worderInformationAttrWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderId)
                .eq(WorderInformationAttributeEntity::getIsDelete, 0)
                .eq(WorderInformationAttributeEntity::getAttributeCode, "Transfer-Order")
                .eq(WorderInformationAttributeEntity::getAttribute, "TransferOrder");
        worderInformationAttributeDao.delete(worderInformationAttrWrapper);
        // 插入
        WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
        entity.setWorderId(worderId);
        entity.setAttributeCode("Transfer-Order");
        entity.setAttributeName("转单标识");
        entity.setAttributeValue("1");
        entity.setAttribute("TransferOrder");
        worderInformationAttributeDao.insert(entity);
    }

    // 定时任务系统自动升级督办单
    private void upSupervise(SuperviseTeleRecord superviseTeleRecord) {
        Integer beforeLv = superviseTeleRecord.getSuperviseLv();
        // 逾期，督办单升级
        Integer superviseLv = superviseTeleRecord.getSuperviseLv();
        if (superviseLv >= 6) {
            log.info("当前督办等级已达到最高");
            return;
        }
        superviseLv++;
        superviseTeleRecord.setResult(3);
        superviseTeleRecord.setTaskState(0);
        superviseTeleRecord.setSuperviseLv(superviseLv);
        superviseTeleRecord.setLvUpReason("责任人逾期未回复，系统自动升级督办单,由 " + beforeLv + "级 升级到 " + superviseLv + "级");
        superviseTeleRecord.setTripTime(new Date());

        // 查询督办单号
        SuperviseInfomation superviseInfomation = superviseInfomationMapper.selectById(superviseTeleRecord.getSuperviseId());
        superviseInfomation.setLvUpReason("责任人逾期未回复，系统自动升级督办单");
        superviseInfomation.setSuperviseLv(superviseLv);

        // 查询相关责任人
        // 查询数据字典获取一级对应的责任人和相关责任人角色
        List<SysDictionaryDetailEntity> superviseLvDic = sysDictionaryDetailDao.selectDetailByNumbers("supervise_lv_" + superviseLv);
        Map<String, String> superviseLvMap = superviseLvDic.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailName, SysDictionaryDetailEntity::getDetailNumber, (val1, val2) -> val1));
        QueryDutyAndRelatedPeoParam queryDutyAndRelatedPeoParam = new QueryDutyAndRelatedPeoParam();
        queryDutyAndRelatedPeoParam.setBrandId(superviseInfomation.getBrandId());
        queryDutyAndRelatedPeoParam.setServiceType(superviseInfomation.getServiceType());
        // 区县
        BizRegionEntity bizRegionDis = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(superviseInfomation.getAreaId().toString()));
        // 地市
        BizRegionEntity bizRegionCity = InitRegionUtil.REGION_ID_MAP.get(bizRegionDis.getPid());
        // 省份
        BizRegionEntity bizRegionPro = InitRegionUtil.REGION_ID_MAP.get(bizRegionCity.getPid());
        List<String> areaIds = new ArrayList<>();
        areaIds.add(bizRegionPro.getId().toString());
        areaIds.add(bizRegionCity.getId().toString());
        areaIds.add(bizRegionDis.getId().toString());
        queryDutyAndRelatedPeoParam.setAreaIds(areaIds);
        List<SysUserEntity> relatedPeoRole = this.baseMapper.queryQueryDutyAndRelatedPeo(queryDutyAndRelatedPeoParam, superviseLvMap.get("related_peo_role"));
        if (!relatedPeoRole.isEmpty()) {
            String relatedPeo = relatedPeoRole.stream().map(item -> item.getUserId().toString()).collect(Collectors.joining(","));
            // 更新相关责任人
            superviseTeleRecord.setRelatedPeo(relatedPeo);
            superviseInfomation.setRelatedPeo(relatedPeo);
        } else {
            superviseTeleRecord.setRelatedPeo("");
            superviseInfomation.setRelatedPeo("");
        }
        // 给责任人发送短信
        // 给责任人和相关责任人发送短信,省市+品牌+用户姓名+news单号+网点+跳闸类目+TS？+投诉事由
        sendMsg(superviseInfomation, superviseInfomation.getDutyPeo(), superviseInfomation.getRelatedPeo());

        superviseTeleRecordMapper.updateById(superviseTeleRecord);
        superviseInfomation.setUpdateUserId(1);
        superviseInfomation.setUpdateTime(new Date());
        this.baseMapper.updateById(superviseInfomation);

        // 记录操作记录
        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
        superviseOperationRecord.setUserId(1);
        superviseOperationRecord.setUserName("系统自动");
        superviseOperationRecord.setOperationType(3);
        superviseOperationRecord.setOptrationContent("责任人逾期未回复，系统自动升级督办单,由 " + beforeLv + "级 升级到 " + superviseLv + "级");
        superviseOperationRecord.setDutyPeo(getEmployeeNameByUser(superviseInfomation.getDutyPeo().toString()));
        superviseOperationRecord.setRelatedPeo(getEmployeeNameByUser(superviseInfomation.getRelatedPeo()));
        superviseOperationRecordMapper.insert(superviseOperationRecord);
    }

    /**
     * 给责任人和相关责任人发送短信
     * 省市+品牌+用户姓名+news单号+网点+跳闸类目+TS？+投诉事由
     *
     * @param superviseInfomation
     */
    private void sendMsg(SuperviseInfomation superviseInfomation, Integer dutyPeo, String relatedPeo) {
        // 获取发送短信的手机号码
        String ids = "";
        //取消项目经理（责任人）“咨询类”短信通知（投诉催单类短信照旧推送）
        if (superviseInfomation.getSuperviseType().equals(1)) {
            dutyPeo = null;
        }
        if (dutyPeo != null) {
            ids = String.valueOf(dutyPeo);
        }

        if (StringUtils.isNotBlank(relatedPeo)) {
            if (StringUtils.isNotBlank(ids)) {
                ids = ids + "," + relatedPeo;
            } else {
                ids = relatedPeo;
            }
        }
        if (StringUtils.isBlank(ids)) {
            return;
        }
        // 获取发送短信的手机号
        List<SysUserEntity> userEntities = sysUserDao.selectByUserIds(ids);
        if (userEntities.isEmpty()) {
            return;
        }
        // 获取短信内容
        StringBuilder content = new StringBuilder();
        // 获取省市县
        BizRegionEntity disRegionEntity = InitRegionUtil.REGION_ID_MAP.get(Long.parseLong(superviseInfomation.getAreaId().toString()));
        BizRegionEntity cityRegionEntity = InitRegionUtil.REGION_ID_MAP.get(disRegionEntity.getPid());
        BizRegionEntity proRegionEntity = InitRegionUtil.REGION_ID_MAP.get(cityRegionEntity.getPid());
        content.append("\n 区域：").append(proRegionEntity.getName()).append(cityRegionEntity.getName()).append(disRegionEntity.getName());
        // 获取品牌
        BrandEntity brandEntity = brandDao.selectById(superviseInfomation.getBrandId());
        content.append("\n 品牌：").append(brandEntity.getBrandName());
        // 获取姓名
        content.append("\n 姓名：").append(superviseInfomation.getUserName());
        // 获取联系电话
        content.append("\n 联系电话：").append(superviseInfomation.getUserPhone());
        // 地址
        if (StringUtils.isNotBlank(superviseInfomation.getWorderNo())) {
            LambdaQueryWrapper<WorderInformationEntity> worderInformationWrapper = Wrappers.lambdaQuery();
            worderInformationWrapper.select(WorderInformationEntity::getAddressDup);
            worderInformationWrapper.eq(WorderInformationEntity::getWorderNo, superviseInfomation.getWorderNo());
            WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInformationWrapper);
            content.append("\n 地址：").append(worderInformationEntity != null ? worderInformationEntity.getAddressDup() : "");
        } else {
            content.append("\n 地址：").append("");
        }
        // 督办单号
        content.append("\n 督办单号：").append(superviseInfomation.getWorderSuperviseNo());
        // 获取news单号
        if (StringUtils.isNotBlank(superviseInfomation.getWorderNo())) {
            content.append("\n news单号：").append(superviseInfomation.getWorderNo());
        } else {
            content.append("\n news单号：");
        }
        // 获取网点
        if (StringUtils.isNotBlank(superviseInfomation.getWorderNo())) {
            String dotName = this.baseMapper.selectDotNameByWorder(superviseInfomation.getWorderNo());
            content.append("\n 网点：").append(dotName);
        } else {
            content.append("\n 网点：");
        }
        // 翻译督办单跳闸类型
        List<SysDictionaryDetailEntity> tripTypeList = sysDictionaryDetailDao.selectDetailByNumbers("supervise_trip_type");
        Map<String, String> tripTypeMap = tripTypeList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
        QueryWrapper<SuperviseDictionaryTripType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("supervise_type",superviseInfomation.getSuperviseType());
        List<SuperviseDictionaryTripType> list = superviseDictionaryTripTypeService.list(queryWrapper);
        if (superviseInfomation.getTripType() != null) {
            //老的督办用这个
            if(superviseInfomation.getCreateTime().before(DateUtil.stringtoDate("2024-10-20","yyyy-mm-dd"))){
                content.append("\n 跳闸类目：").append(tripTypeMap.get(superviseInfomation.getTripType().toString()));
            }
            else {
                SuperviseDictionaryTripType tripType = list.stream().filter(l->l.getDetailNumber().equals(superviseInfomation.getTripType())).findFirst().orElse(null);
                if(tripType!=null){
                    content.append("\n 跳闸类目：").append(tripType.getDetailName());
                }
            }
        } else {
            content.append("\n 跳闸类目：");
        }
        content.append("\n 投诉事由：").append(superviseInfomation.getSuperviseContent());
        content.append("\n 处理链接：").append(env.getProperty("baseUrl") + "h5/?id=" + superviseInfomation.getId() + "&userId=aaaaaaaaa");
        String string = content.toString();
        log.info("发送短信的号码：{}", userEntities.toString());
        log.info("短信内容：{}", content.toString());
        // 发送短信
        for (SysUserEntity entity : userEntities) {
            String replace = string.replace("aaaaaaaaa", entity.getUserId().toString());
            SmsUtil.sendSms(entity.getMobile(), replace, "【到每家科技服务】");
        }
        //如果督办等级是一级 给网点发短信
        if (superviseInfomation.getSuperviseLv().equals(1) && StringUtils.isNotBlank(superviseInfomation.getDotUserTelephone())) {
            String replace = string.replace("aaaaaaaaa", superviseInfomation.getDotUserId().toString());
            SmsUtil.sendSms(superviseInfomation.getDotUserTelephone(), replace, "【到每家科技服务】");
        }
    }

    /**
     * 根据userid查询用户名称
     *
     * @param userIds
     * @return
     */
    private String getEmployeeNameByUser(String userIds) {
        if (StringUtils.isBlank(userIds)) {
            return null;
        }
        return this.baseMapper.getEmployeeNameByUser(userIds);
    }

    /**
     * 翻译，督办来源，督办分类，督办等级字段
     *
     * @param records
     */
    private void translateSuperviseTele(List<SuperviseTeleRecordInfo> records) {
        List<SuperviseDictionary> superviseDictionaries = superviseDictionaryMapper.selectList(Wrappers.lambdaQuery());
        // 督办来源翻译Map
        Map<Integer, String> superviseClassificationMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseClassification, SuperviseDictionary::getSuperviseClassificationName, (val1, val2) -> val1));
        // 督办分类翻译Map
        Map<Integer, String> getSuperviseTypeMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseType, SuperviseDictionary::getSuperviseTypeName, (val1, val2) -> val1));
        // 督办等级翻译Map
        List<SysDictionaryDetailEntity> dictionaryDetailEntities = sysDictionaryDetailDao.selectDetailLikeNumber("supervise_lv", "value");
        Map<String, String> getSuperviseLvMap = dictionaryDetailEntities.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getRemark, (val1, val2) -> val1));

        records.forEach(item -> {
            item.setSuperviseClassificationName(superviseClassificationMap.get(item.getSuperviseClassification()));
            item.setSuperviseTypeName(getSuperviseTypeMap.get(item.getSuperviseType()));
            item.setSuperviseLvName(getSuperviseLvMap.get(item.getSuperviseLv().toString()));
        });
    }

    /**
     * 翻译，督办来源，督办分类，督办等级字段
     *
     * @param records
     */
    private void translateSupervise(List<SuperviseInfomationInfo> records) {
        List<SuperviseDictionary> superviseDictionaries = superviseDictionaryMapper.selectList(Wrappers.lambdaQuery());
        // 督办来源翻译Map
        Map<Integer, String> superviseClassificationMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseClassification, SuperviseDictionary::getSuperviseClassificationName, (val1, val2) -> val1));
        // 督办分类翻译Map
        Map<Integer, String> getSuperviseTypeMap = superviseDictionaries.stream().collect(Collectors.toMap(SuperviseDictionary::getSuperviseType, SuperviseDictionary::getSuperviseTypeName, (val1, val2) -> val1));
        // 督办等级翻译Map
        List<SysDictionaryDetailEntity> dictionaryDetailEntities = sysDictionaryDetailDao.selectDetailLikeNumber("supervise_lv", "value");
        Map<String, String> getSuperviseLvMap = dictionaryDetailEntities.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getRemark, (val1, val2) -> val1));
        // 翻译服务类型
        List<SysDictionaryDetailEntity> listDic = sysDictionaryDetailDao.selectDetailByNumbers("service_type");
        Map<String, String> serviceTypeMap = listDic.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
        // 翻译督办单状态
        List<SysDictionaryDetailEntity> superviseStateList = sysDictionaryDetailDao.selectDetailByNumbers("supervise_state");
        Map<String, String> superviseStateMap = superviseStateList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
        // 翻译督办单跳闸类型
        List<SysDictionaryDetailEntity> tripTypeList = sysDictionaryDetailDao.selectDetailByNumbers("supervise_trip_type");
        Map<String, String> tripTypeMap = tripTypeList.stream().collect(Collectors.toMap(SysDictionaryDetailEntity::getDetailNumber, SysDictionaryDetailEntity::getDetailName, (val1, val2) -> val1));
        records.forEach(item -> {
            item.setSuperviseClassificationName(superviseClassificationMap.get(item.getSuperviseClassification()));
            item.setSuperviseTypeName(getSuperviseTypeMap.get(item.getSuperviseType()));
            item.setSuperviseLvName(getSuperviseLvMap.get(item.getSuperviseLv().toString()));
            item.setServiceTypeName(serviceTypeMap.get(item.getServiceType().toString()));
            item.setStateName(superviseStateMap.get(item.getState().toString()));
            if (item.getTripType() != null) {
                item.setTripTypeName(tripTypeMap.get(item.getTripType().toString()));
            }
        });
    }

    /**
     * 创建督办单号
     *
     * @param prefix
     * @return
     */
    private String createSuperviseNo(String prefix) {
        String currDate = DateUtil.getCurrDate(DateUtil.LONG_DATE_FORMAT);
        return prefix + currDate + getNum(worderVersionDao.getSuperviseNo(currDate));
    }

    private String getNum(Integer integer) {
        if (integer > 999999) {
            throw new RuntimeException();
        }
        return String.format("%06d", integer); //25为int型
    }
}