package com.bonc.rrs.supervise.task;

import com.bonc.rrs.supervise.service.SuperviseInfomationService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

/**
 * @Description: 督办单跳闸定时任务
 * @Author: liujunpeng
 * @Date: 2023/10/17
 * @Version: 1.0
 */
@Component("superviseTripTask")
@Log4j2
@AllArgsConstructor
public class SuperviseTripTask implements ITask {

    final SuperviseInfomationService superviseInfomationService;

    @Override
    public void run(String params) {
        log.info("督办单跳闸定时任务开始...");
        superviseInfomationService.tripTask();
        log.info("督办单跳闸定时任务结束...");
    }
}
