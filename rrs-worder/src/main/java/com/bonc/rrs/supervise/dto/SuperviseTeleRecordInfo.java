package com.bonc.rrs.supervise.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.rrs.supervise.entity.SuperviseRecoverRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 督办工单来电记录表;
* <AUTHOR> liujunpeng
* @date : 2023-9-26
*/
@ApiModel(value = "督办工单来电记录表",description = "")
@Data
public class SuperviseTeleRecordInfo implements Serializable{
   /** 主键 */
   @ApiModelProperty(name = "主键",notes = "")
   @TableId
   private Integer id ;
   /** 督办工单ID */
   @ApiModelProperty(name = "督办工单ID",notes = "")
   private Integer superviseId ;
   /** 督办来源(字典supervise_classification) */
   @ApiModelProperty(name = "督办来源",notes = "")
   private Integer superviseClassification ;
   /** 督办来源名称 */
   @ApiModelProperty(name = "督办来源名称",notes = "")
   private String superviseClassificationName ;
   /** 督办类型 */
   @ApiModelProperty(name = "督办类型",notes = "")
   private Integer superviseType ;
   /** 督办类型名称 */
   @ApiModelProperty(name = "督办类型名称",notes = "")
   private String superviseTypeName ;
   /** 督办等级 */
   @ApiModelProperty(name = "督办等级",notes = "")
   private Integer superviseLv ;
   /** 督办等级 */
   @ApiModelProperty(name = "督办等级",notes = "")
   private String superviseLvName ;
   /** 来电时间 */
   @ApiModelProperty(name = "来电时间",notes = "")
   private Date createTime ;
   /** 提交人 */
   @ApiModelProperty(name = "提交人",notes = "")
   private Integer createUserId ;
   /** 提交人 */
   @ApiModelProperty(name = "提交人",notes = "")
   private String createUserName ;
   /** 督办内容 */
   @ApiModelProperty(name = "督办内容",notes = "")
   private String superviseContent ;
   /** 用户要求解决时间 */
   @ApiModelProperty(name = "用户要求解决时间",notes = "")
   private Date processTime ;
    /** 下次联系时间 */
    @ApiModelProperty(name = "下次联系时间",notes = "")
    private Date nextConnectionTime ;
   /** 责任人 */
   @ApiModelProperty(name = "责任人",notes = "")
   private Integer dutyPeo ;
   /** 责任人 */
   @ApiModelProperty(name = "责任人",notes = "")
   private String dutyPeoName ;
   /** 相关责任人,多个逗号隔开 */
   @ApiModelProperty(name = "相关责任人,多个逗号隔开",notes = "")
   private String relatedPeo ;
   /** 责任人未回复跳闸次数合 */
   @ApiModelProperty(name = "责任人未回复跳闸次数合",notes = "")
   private Integer tripNum ;
   /** 跳闸时间 */
   @ApiModelProperty(name = "跳闸时间",notes = "")
   private Date tripTime ;
   /** 处理结果,0:关闭，1：升级 */
   @ApiModelProperty(name = "处理结果,0:关闭，1：升级，2：追加来电",notes = "")
   private Integer result ;
   /** 升级原因 */
   @ApiModelProperty(name = "升级原因",notes = "")
   private String lvUpReason ;
   /** 0:正常 1:删除 */
   @ApiModelProperty(name = "0:正常 1:删除",notes = "")
   private Integer isDelete ;
   /** 操作动作 */
   @ApiModelProperty(name = "0:创建 1:升级 2:追加来电",notes = "")
   private Integer operationType;

   private List<SuperviseRecoverRecord> recoverRecords;
}