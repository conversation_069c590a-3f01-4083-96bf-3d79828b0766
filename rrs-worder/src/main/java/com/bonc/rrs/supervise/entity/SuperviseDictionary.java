package com.bonc.rrs.supervise.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 督办字典表;
 *
 * <AUTHOR> liujunpeng
 * @date : 2023-9-26
 */
@ApiModel(value = "督办字典表", description = "")
@TableName("supervise_dictionary")
@Data
public class SuperviseDictionary implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(name = "主键", notes = "")
    @TableId
    private Integer id;
    /**
     * 督办来源
     */
    @ApiModelProperty(name = "督办来源", notes = "")
    private Integer superviseClassification;
    /**
     * 督办来源名称
     */
    @ApiModelProperty(name = "督办来源名称", notes = "")
    private String superviseClassificationName;
    /**
     * 督办来源缩写
     */
    @ApiModelProperty(name = "督办来源缩写", notes = "")
    private String classificationAbbre;
    /**
     * 督办类型
     */
    @ApiModelProperty(name = "督办类型", notes = "")
    private Integer superviseType;
    /**
     * 督办类型名称
     */
    @ApiModelProperty(name = "督办类型名称", notes = "")
    private String superviseTypeName;
    /**
     * 督办类型名称缩写
     */
    @ApiModelProperty(name = "督办类型名称缩写", notes = "")
    private String typeAbbre;
    /**
     * 要求回复时长
     */
    @ApiModelProperty(name = "要求回复时长", notes = "")
    private Integer duration;
}