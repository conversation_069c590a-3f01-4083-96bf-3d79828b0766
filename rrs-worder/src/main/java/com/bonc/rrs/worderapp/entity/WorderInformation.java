package com.bonc.rrs.worderapp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyibo on 2020-03-25 11:38
 */

@Data
@ApiModel("工单详情")
public class WorderInformation implements Serializable {

    private String worderId;
    @ApiModelProperty("工单编号")
    private String worderNo;
    @ApiModelProperty("勘测预约时间")
    private String conveyAppointTime;
    @ApiModelProperty("安装预约时间")
    private String installAppointTime;
    @ApiModelProperty("工单状态")
    private String worderStatus;
    @ApiModelProperty("工单执行状态")
    private String worderExecStatus;
    @ApiModelProperty("勘测签退时间")
    private String conveySignOutTime;
    @ApiModelProperty("安装签退时间")
    private String installSignOutTime;
    @ApiModelProperty("车企")
    private String companyName;
    @ApiModelProperty("车企订单号")
    private String companyOrderNumber;
    @ApiModelProperty("品牌")
    private String carBrand;
    @ApiModelProperty("模板id")
    private Integer templateId;
    @ApiModelProperty("客户名称")
    private String clientName;
    @ApiModelProperty("客户地址")
    private String clientAdress;
    @ApiModelProperty("客户电话")
    private String clientPhone;
    @ApiModelProperty("车架号")
    private String chargeModel;
    @ApiModelProperty("充电桩型号")
    private String carVin;
    @ApiModelProperty("工单增项结算状态")
    private String worderIncreStatus;
    @ApiModelProperty("开票状态")
    private String ticketStatus;
    @ApiModelProperty("用户增项结算实际收费金额")
    private String userActualCost;
    @ApiModelProperty("色标标签")
    private Integer colorLabel;
//    @ApiModelProperty("工单支付状态")
//    private Integer worderPayStatus;
    @ApiModelProperty("工单支付状态")
    private Object worderHadPay;
    @ApiModelProperty("工单发票状态")
    private Integer billingStatus;
    @ApiModelProperty("工单发票是否申请")
    private Boolean billingApply;

    @ApiModelProperty("工单类型编码")
    private String worderTypeId;

    @ApiModelProperty("售后类型")
    @TableField(exist = false)
    private String sellType;

    @ApiModelProperty("是否已暂停工单")
    @TableField(exist = false)
    private String stopStatus;
}
