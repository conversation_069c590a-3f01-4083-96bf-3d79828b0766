package com.bonc.rrs.worderapp.dao;

import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderapp.entity.DicKeyResultEntity;
import com.bonc.rrs.worderapp.entity.WorderInformation;
import com.bonc.rrs.worderapp.entity.dto.WorderExtFieldQueryDto;
import com.bonc.rrs.worderapp.entity.dto.WorderFieldDto;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.worderapp.entity.po.FieldInfoPo;
import com.bonc.rrs.worderapp.entity.po.FieldPo;
import com.bonc.rrs.worderapp.entity.vo.DicKeyVo;
import com.bonc.rrs.worderapp.entity.vo.FieldVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangyibo on 2020-03-18 16:43
 */

@Mapper
public interface WorderOrderDao {

    String getWorderInformationCount(Map map);

    Map queryTypeTitleData(Map map);

    /**
     *
     * @param map
     * @return
     */
    List<DicKeyResultEntity> listTable(Map<String, String> map);

    /**
     * 字典查询
     * @param dictionaryId
     * @return
     */
    List<DicKeyResultEntity> listDicKey(String dictionaryId);


    /**
     * 字典查询
     * @param ids
     * @return
     */
    List<DicKeyVo> listDicKeys(List ids);


    List<FieldInfoPo> getFieldInfo(Map map);

    /**
     *
     * @param map
     * @return
     */
    List<WorderInformation> listWorderInformation(Map map);

    /**
     * 根据工单ID，字段用途和字段类型获得字段信息
     * @param worderFieldDto
     * @return
     */
    List<FieldInfoPo> listWorderFieldInfo(WorderFieldDto worderFieldDto);

    /**
     * 根据工单ID，字段用途和字段类型获得字段
     * @param worderFieldDto
     * @return
     */
    List<FieldPo> listWorderField(WorderFieldDto worderFieldDto);

    /**
     * 保存工单字段和资料信息
     * @param fieldVo
     * @return
     */
    Integer saveWorderFieldInfo(FieldVo fieldVo);

    /**
     * 修改工单字段和资料信息
     * @param fieldVo
     * @return
     */
    Integer updateWorderFieldInfo(FieldVo fieldVo);

    /**
     * 根据工单编号获取工单信息
     * @param worderNo
     * @return
     */
    List<WorderInformationEntity> getWorderInformation(String worderNo);

    /**
     * 工单详情
     * @param worderNo
     * @return
     */
    WorderInformation getWorderDetails(String worderNo);

    /**
     * 修改工单信息
     * @param worderInformationDto
     * @return
     */
    Integer updateWorderInformation(WorderInformationDto worderInformationDto);

    Integer updateWorderInfo(WorderInformationDto worderInformationDto);

    Integer updateWorderLevel(WorderInformationDto worderInformationDto);
    /**
     * 查询工单字段信息保存数量
     * @param worderExtFieldQueryDto
     * @return
     */
    Integer getWorderFieldInfoCount(WorderExtFieldQueryDto worderExtFieldQueryDto);

    /**
     * 根据模版获取工单创建字段
     * @param templateId
     * @return
     */
    List<FieldPo> listWorderFieldByTemplateId(Integer templateId);


    WorderInformationDto getWorderbyId(Integer worderId );
}
