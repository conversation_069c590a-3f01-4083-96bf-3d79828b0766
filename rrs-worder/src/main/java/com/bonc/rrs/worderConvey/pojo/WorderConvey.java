package com.bonc.rrs.worderConvey.pojo;

import java.util.Date;

public class WorderConvey {
    private Integer id;

    private String worderId;

    private Date oppointmentTime;

    private String oppointmentModifiedReason;

    private String conveyStatus;

    private Boolean isSignOn;

    private Date signOnTime;

    private Date gmtCreate;

    private Date gmtModified;

    private String buildType;

    private String developers;

    private String propertyCompany;

    private String propertyPerson;

    private String contactNumber;

    private String contactEmail;

    private String carType;

    private String parkLocation;

    private String propertyAttitude;

    private String remarkInfo;

    private String transformerRate;

    private String mainCapacity;

    private String circuitCapacity;

    private String measuredCurrent;

    private String measuredVoltage;

    private String actualTemperature;

    private String wireDiameter;

    private String chargerCurrent;

    private String powerMode;

    private String customerFeedback;

    private String reconnaissReport;

    private String constructPlan;

    private String parkSpace;

    private String siteEnvironment;

    private String sourceFigure;

    private String auditResult;

    private String auditState;

    public WorderConvey(Integer id, String worderId, Date oppointmentTime, String oppointmentModifiedReason, String conveyStatus, Boolean isSignOn, Date signOnTime, Date gmtCreate, Date gmtModified, String buildType, String developers, String propertyCompany, String propertyPerson, String contactNumber, String contactEmail, String carType, String parkLocation, String propertyAttitude, String remarkInfo, String transformerRate, String mainCapacity, String circuitCapacity, String measuredCurrent, String measuredVoltage, String actualTemperature, String wireDiameter, String chargerCurrent, String powerMode, String customerFeedback, String reconnaissReport, String constructPlan, String parkSpace, String siteEnvironment, String sourceFigure, String auditResult, String auditState) {
        this.id = id;
        this.worderId = worderId;
        this.oppointmentTime = oppointmentTime;
        this.oppointmentModifiedReason = oppointmentModifiedReason;
        this.conveyStatus = conveyStatus;
        this.isSignOn = isSignOn;
        this.signOnTime = signOnTime;
        this.gmtCreate = gmtCreate;
        this.gmtModified = gmtModified;
        this.buildType = buildType;
        this.developers = developers;
        this.propertyCompany = propertyCompany;
        this.propertyPerson = propertyPerson;
        this.contactNumber = contactNumber;
        this.contactEmail = contactEmail;
        this.carType = carType;
        this.parkLocation = parkLocation;
        this.propertyAttitude = propertyAttitude;
        this.remarkInfo = remarkInfo;
        this.transformerRate = transformerRate;
        this.mainCapacity = mainCapacity;
        this.circuitCapacity = circuitCapacity;
        this.measuredCurrent = measuredCurrent;
        this.measuredVoltage = measuredVoltage;
        this.actualTemperature = actualTemperature;
        this.wireDiameter = wireDiameter;
        this.chargerCurrent = chargerCurrent;
        this.powerMode = powerMode;
        this.customerFeedback = customerFeedback;
        this.reconnaissReport = reconnaissReport;
        this.constructPlan = constructPlan;
        this.parkSpace = parkSpace;
        this.siteEnvironment = siteEnvironment;
        this.sourceFigure = sourceFigure;
        this.auditResult = auditResult;
        this.auditState = auditState;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWorderId() {
        return worderId;
    }

    public void setWorderId(String worderId) {
        this.worderId = worderId;
    }

    public Date getOppointmentTime() {
        return oppointmentTime;
    }

    public void setOppointmentTime(Date oppointmentTime) {
        this.oppointmentTime = oppointmentTime;
    }

    public String getOppointmentModifiedReason() {
        return oppointmentModifiedReason;
    }

    public void setOppointmentModifiedReason(String oppointmentModifiedReason) {
        this.oppointmentModifiedReason = oppointmentModifiedReason;
    }

    public String getConveyStatus() {
        return conveyStatus;
    }

    public void setConveyStatus(String conveyStatus) {
        this.conveyStatus = conveyStatus;
    }

    public Boolean getSignOn() {
        return isSignOn;
    }

    public void setSignOn(Boolean signOn) {
        isSignOn = signOn;
    }

    public Date getSignOnTime() {
        return signOnTime;
    }

    public void setSignOnTime(Date signOnTime) {
        this.signOnTime = signOnTime;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getBuildType() {
        return buildType;
    }

    public void setBuildType(String buildType) {
        this.buildType = buildType;
    }

    public String getDevelopers() {
        return developers;
    }

    public void setDevelopers(String developers) {
        this.developers = developers;
    }

    public String getPropertyCompany() {
        return propertyCompany;
    }

    public void setPropertyCompany(String propertyCompany) {
        this.propertyCompany = propertyCompany;
    }

    public String getPropertyPerson() {
        return propertyPerson;
    }

    public void setPropertyPerson(String propertyPerson) {
        this.propertyPerson = propertyPerson;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getParkLocation() {
        return parkLocation;
    }

    public void setParkLocation(String parkLocation) {
        this.parkLocation = parkLocation;
    }

    public String getPropertyAttitude() {
        return propertyAttitude;
    }

    public void setPropertyAttitude(String propertyAttitude) {
        this.propertyAttitude = propertyAttitude;
    }

    public String getRemarkInfo() {
        return remarkInfo;
    }

    public void setRemarkInfo(String remarkInfo) {
        this.remarkInfo = remarkInfo;
    }

    public String getTransformerRate() {
        return transformerRate;
    }

    public void setTransformerRate(String transformerRate) {
        this.transformerRate = transformerRate;
    }

    public String getMainCapacity() {
        return mainCapacity;
    }

    public void setMainCapacity(String mainCapacity) {
        this.mainCapacity = mainCapacity;
    }

    public String getCircuitCapacity() {
        return circuitCapacity;
    }

    public void setCircuitCapacity(String circuitCapacity) {
        this.circuitCapacity = circuitCapacity;
    }

    public String getMeasuredCurrent() {
        return measuredCurrent;
    }

    public void setMeasuredCurrent(String measuredCurrent) {
        this.measuredCurrent = measuredCurrent;
    }

    public String getMeasuredVoltage() {
        return measuredVoltage;
    }

    public void setMeasuredVoltage(String measuredVoltage) {
        this.measuredVoltage = measuredVoltage;
    }

    public String getActualTemperature() {
        return actualTemperature;
    }

    public void setActualTemperature(String actualTemperature) {
        this.actualTemperature = actualTemperature;
    }

    public String getWireDiameter() {
        return wireDiameter;
    }

    public void setWireDiameter(String wireDiameter) {
        this.wireDiameter = wireDiameter;
    }

    public String getChargerCurrent() {
        return chargerCurrent;
    }

    public void setChargerCurrent(String chargerCurrent) {
        this.chargerCurrent = chargerCurrent;
    }

    public String getPowerMode() {
        return powerMode;
    }

    public void setPowerMode(String powerMode) {
        this.powerMode = powerMode;
    }

    public String getCustomerFeedback() {
        return customerFeedback;
    }

    public void setCustomerFeedback(String customerFeedback) {
        this.customerFeedback = customerFeedback;
    }

    public String getReconnaissReport() {
        return reconnaissReport;
    }

    public void setReconnaissReport(String reconnaissReport) {
        this.reconnaissReport = reconnaissReport;
    }

    public String getConstructPlan() {
        return constructPlan;
    }

    public void setConstructPlan(String constructPlan) {
        this.constructPlan = constructPlan;
    }

    public String getParkSpace() {
        return parkSpace;
    }

    public void setParkSpace(String parkSpace) {
        this.parkSpace = parkSpace;
    }

    public String getSiteEnvironment() {
        return siteEnvironment;
    }

    public void setSiteEnvironment(String siteEnvironment) {
        this.siteEnvironment = siteEnvironment;
    }

    public String getSourceFigure() {
        return sourceFigure;
    }

    public void setSourceFigure(String sourceFigure) {
        this.sourceFigure = sourceFigure;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }
}