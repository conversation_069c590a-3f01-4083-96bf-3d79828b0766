package com.bonc.rrs.worderConvey.dao;


import com.bonc.rrs.worderConvey.pojo.WorderConvey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WorderConveyMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WorderConvey record);

    int insertSelective(WorderConvey record);

    WorderConvey selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(WorderConvey record);

    int updateByPrimaryKey(WorderConvey record);

    int updateByWorderno(@Param(value = "worderno") String worderid,
                         @Param(value = "result") String result,
                         @Param(value = "state") String state);
}