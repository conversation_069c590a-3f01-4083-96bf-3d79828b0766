package com.bonc.rrs.worderConvey.service;

import com.bonc.rrs.util.Results;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface WorderConveyService {

    Results selectEntitByKey(String worderid);

    void downDataService(String worderid,
                         HttpServletResponse response) throws IOException;

    Results auditResultService(String worderid,
                               String result,
                               String state);

}
