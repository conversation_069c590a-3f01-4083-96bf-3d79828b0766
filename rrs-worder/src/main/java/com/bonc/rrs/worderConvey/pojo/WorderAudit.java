package com.bonc.rrs.worderConvey.pojo;

public class WorderAudit {
    private Long id;

    private String worderNo;

    private String status;

    private String result;

    public WorderAudit(Long id, String worderNo, String status, String result) {
        this.id = id;
        this.worderNo = worderNo;
        this.status = status;
        this.result = result;
    }

    public WorderAudit() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWorderNo() {
        return worderNo;
    }

    public void setWorderNo(String worderNo) {
        this.worderNo = worderNo == null ? null : worderNo.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result == null ? null : result.trim();
    }
}