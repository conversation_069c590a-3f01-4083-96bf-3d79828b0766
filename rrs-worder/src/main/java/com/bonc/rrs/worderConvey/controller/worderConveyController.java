package com.bonc.rrs.worderConvey.controller;

import com.bonc.rrs.util.Results;
import com.bonc.rrs.worderConvey.service.WorderConveyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping(value = "/worder/convey")
public class worderConveyController {

    @Autowired
    private WorderConveyService worderConveyService;

    /*
    * 审核信息查询
    * */
    @RequestMapping(value = "selectentity", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results selectEntity(@RequestParam(value = "worderid") String worderid){
        return worderConveyService.selectEntitByKey(worderid);
    }

    /*
    * 下载资料
    * */
    @RequestMapping(value = "downdata", method = {RequestMethod.GET, RequestMethod.POST}, produces = "application/json;charset=UTF-8")
    public void downDataController(@RequestParam(value = "worderid") String worderid,
                                   HttpServletResponse response) throws IOException {
        worderConveyService.downDataService(worderid,response);
    }

    /*
    * 审核信息
    * */
    @RequestMapping(value = "auditresult",method = {RequestMethod.GET, RequestMethod.POST},produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Results auditResultController(@RequestParam(value = "worderid") String worderid,
                                         @RequestParam(value = "result") String result,
                                         @RequestParam(value = "state") String state){
        return worderConveyService.auditResultService(worderid, result, state);
    }
}
