package com.bonc.rrs.worderConvey.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.util.ExcelUtile;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worderConvey.dao.WorderAuditMapper;
import com.bonc.rrs.worderConvey.dao.WorderConveyMapper;
import com.bonc.rrs.worderConvey.pojo.WorderConvey;
import com.bonc.rrs.worderConvey.service.WorderConveyService;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/*
* @author: minJunping
* @date: 2020/03/09
* */

@Service
public class WorderConveyServiceImpl implements WorderConveyService {


    @Autowired(required = false)
    private WorderConveyMapper worderConveyMapper;

    @Autowired(required = false)
    private WorderAuditMapper worderAuditMapper;

    @Override
    public Results selectEntitByKey(String worderid) {

        try {
            if (!StringUtils.isEmpty(worderid)){
                WorderConvey worderConvey = worderConveyMapper.selectByPrimaryKey(worderid);
                if (!StringUtils.isEmpty(worderConvey)){
                    return Results.message(0,"success",worderConvey);
                }else {
                    return Results.message(10,"无相关id参数",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }
    }

    @Override
    public void downDataService(String worderid,
                                HttpServletResponse response) throws IOException {

            if (!StringUtils.isEmpty(worderid)){
                WorderConvey worderConvey = worderConveyMapper.selectByPrimaryKey(worderid);
                if (StringUtils.isEmpty(worderConvey)){
//                    return Results.message("10","没有相关id的参数",null);
                }
                List<String> listList = new ArrayList<>();
                listList.add(worderConvey.getBuildType());
                listList.add(worderConvey.getDevelopers());
                listList.add(worderConvey.getPropertyCompany());
                listList.add(worderConvey.getPropertyPerson());
                listList.add(worderConvey.getContactNumber());
                listList.add(worderConvey.getContactEmail());
                listList.add(worderConvey.getCarType());
                listList.add(worderConvey.getParkLocation());
                listList.add(worderConvey.getPropertyAttitude());
                listList.add(worderConvey.getRemarkInfo());
                listList.add(worderConvey.getTransformerRate());
                listList.add(worderConvey.getMainCapacity());
                listList.add(worderConvey.getCircuitCapacity());
                listList.add(worderConvey.getMeasuredCurrent());
                listList.add(worderConvey.getMeasuredVoltage());
                listList.add(worderConvey.getActualTemperature());
                listList.add(worderConvey.getWireDiameter());
                listList.add(worderConvey.getChargerCurrent());
                listList.add(worderConvey.getPowerMode());
                listList.add(worderConvey.getCustomerFeedback());

                HSSFWorkbook workbook = new HSSFWorkbook();
                String[] header = {"建筑类型", "小区开发商","物业管理公司","物业联系人", "联系人手机号", "联系人邮箱", "车位类型", "车位位置", "物业态度", "备注信息","变压器额定值",
                        "可接入主断路器容量","可接入断路器容量","实测相电流","实测相电压","接入点实际温度","可接入上线线径","可安装充电器电流量","电源接入方式","客户反馈信息"};
                HSSFSheet sheet = workbook.createSheet("勘测审核信息");
                String datStr = DateUtils.getSysTimeStamp().toString();
//                ExcelUtile.excelUtil(workbook, header, sheet, listList, 18, datStr);

                BufferedOutputStream fos = null;
                try {
                    response.setContentType("application/octet-stream");
                    //这后面可以设置导出Excel的名称，此例中名为kpi.xls
                    String name = null;
                    String fileName = datStr + "勘探审核信息.xls";//创建文件名
                    fileName = URLEncoder.encode(fileName, "UTF-8");
                    response.setHeader("Content-disposition", "attachment;filename=" + fileName);
                    //刷新缓冲
                    response.flushBuffer();
                    //workbook将Excel写入到response的输出流中，供页面下载
//            workbook.write(response.getOutputStream());
                    fos = new BufferedOutputStream(response.getOutputStream());
                    workbook.write(fos);


                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (fos != null) {
                        try {
                            //fos.flush();
                            fos.close();
                        } catch (IOException e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                }
//                return Results.message("0","success",null);
            }else {
//                return Results.message("100","参数不能为空",null);
            }
    }

    @Override
    public Results auditResultService(String worderid, String result, String state) {

        try {
            if (!StringUtils.isEmpty(worderid) && !StringUtils.isEmpty(result)){
                int index = worderConveyMapper.updateByWorderno(worderid, result, state);
                if (index > 0){
                    int nums = worderAuditMapper.insertSelective(worderid, state, result);
                        if (nums > 0){
                            return Results.message(0,"更新审核勘探信息并添加日志",null);
                        }else {
                            return Results.message(1,"更新审核勘探信息成功",null);
                        }
                }else {
                    return Results.message(10,"没有相关工单的信息",null);
                }
            }else {
                return Results.message(100,"参数不能为空",null);
            }
        }catch (Exception e){
            return Results.message(110,"接口异常",null);
        }

    }
}
