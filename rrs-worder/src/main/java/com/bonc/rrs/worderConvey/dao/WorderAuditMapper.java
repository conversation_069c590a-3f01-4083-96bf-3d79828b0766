package com.bonc.rrs.worderConvey.dao;


import com.bonc.rrs.worderConvey.pojo.WorderAudit;
import org.apache.ibatis.annotations.Param;

public interface WorderAuditMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WorderAudit record);

    int insertSelective(@Param(value = "worderno") String worderno,
                        @Param(value = "status") String status,
                        @Param(value = "result") String result);

    WorderAudit selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(@Param(value = "worderno") String worderno,
                                    @Param(value = "status") String status,
                                    @Param(value = "result") String result);

    int updateByPrimaryKey(WorderAudit record);

}