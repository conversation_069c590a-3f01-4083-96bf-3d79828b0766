<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="InsertLSMallZssItem" targetNamespace="http://www.example.org/InsertLSMallZssItem/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://www.example.org/InsertLSMallZssItem/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema targetNamespace="http://www.example.org/InsertLSMallZssItem/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/InsertLSMallZssItem/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:element name="LSMallZssItem">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="xsddm" type="xsd:string"/>
            <xsd:element name="dmgs" type="xsd:string"/>
            <xsd:element name="mxxh" type="xsd:decimal"/>
            <xsd:element name="cpdm" type="xsd:string"/>
            <xsd:element name="cpmc" type="xsd:string"/>
            <xsd:element name="xh" type="xsd:string"/>
            <xsd:element name="cpdw" type="xsd:string"/>
            <xsd:element name="cpsl" type="xsd:decimal"/>
            <xsd:element name="hsdj" type="xsd:decimal"/>
            <xsd:element name="hsje" type="xsd:decimal"/>
            <xsd:element name="xxdj" type="xsd:decimal"/>
            <xsd:element name="bhsje" type="xsd:decimal"/>
            <xsd:element name="se" type="xsd:decimal"/>
            <xsd:element name="sl" type="xsd:decimal"/>
            <xsd:element name="zbhsje" type="xsd:decimal"/>
            <xsd:element name="zse" type="xsd:decimal"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssItemResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FLAG" type="xsd:string"/>
            <xsd:element name="MESSAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssItemMUL">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element maxOccurs="unbounded" minOccurs="0" name="in" type="tns:inType"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssItemMULResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FLAG" type="xsd:string"/>
            <xsd:element name="MESSAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="inType">
        <xsd:sequence>
          <xsd:element name="xsddm" type="xsd:string"/>
          <xsd:element name="dmgs" type="xsd:string"/>
          <xsd:element name="mxxh" type="xsd:decimal"/>
          <xsd:element name="cpdm" type="xsd:string"/>
          <xsd:element name="cpmc" type="xsd:string"/>
          <xsd:element name="xh" type="xsd:string"/>
          <xsd:element name="cpdw" type="xsd:string"/>
          <xsd:element name="cpsl" type="xsd:decimal"/>
          <xsd:element name="hsdj" type="xsd:decimal"/>
          <xsd:element name="hsje" type="xsd:decimal"/>
          <xsd:element name="xxdj" type="xsd:decimal"/>
          <xsd:element name="bhsje" type="xsd:decimal"/>
          <xsd:element name="se" type="xsd:decimal"/>
          <xsd:element name="sl" type="xsd:decimal"/>
          <xsd:element name="zbhsje" type="xsd:decimal"/>
          <xsd:element name="zse" type="xsd:decimal"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="LSMallZssItemRequest">
    <WL5G3N0:part element="WL5G3N1:LSMallZssItem" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssItemResponse">
    <WL5G3N0:part element="WL5G3N1:LSMallZssItemResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssItemMULRequest">
    <WL5G3N0:part element="WL5G3N1:LSMallZssItemMUL" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssItemMULResponse">
    <WL5G3N0:part element="WL5G3N1:LSMallZssItemMULResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="InsertLSMallZssItem">
    <WL5G3N0:operation name="LSMallZssItem">
      <WL5G3N0:input message="WL5G3N1:LSMallZssItemRequest"/>
      <WL5G3N0:output message="WL5G3N1:LSMallZssItemResponse"/>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="LSMallZssItemMUL">
      <WL5G3N0:input message="WL5G3N1:LSMallZssItemMULRequest"/>
      <WL5G3N0:output message="WL5G3N1:LSMallZssItemMULResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="InsertLSMallZssItemSOAP" type="WL5G3N1:InsertLSMallZssItem">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="LSMallZssItem">
      <WL5G3N2:operation soapAction="http://www.example.org/InsertLSMallZssItem/LSMallZssItem"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="LSMallZssItemMUL">
      <WL5G3N2:operation soapAction="http://www.example.org/InsertLSMallZssItem/LSMallZssItemMUL"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="InsertLSMallZssItemSOAPQSService">
    <WL5G3N0:port binding="WL5G3N1:InsertLSMallZssItemSOAP" name="InsertLSMallZssItemSOAPQSPort">
      <WL5G3N2:address location="http://************:9001/EAI/RoutingProxyService/EAI_SOAP_ServiceRoot?INT_CODE=EAI_INT_2231"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>