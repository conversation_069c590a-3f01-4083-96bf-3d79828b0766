<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="QueryCancelInterface.wsdl" targetNamespace="http://www.example.org/QueryCancelInterface.wsdl/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://www.example.org/QueryCancelInterface.wsdl/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema targetNamespace="http://www.example.org/QueryCancelInterface.wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/QueryCancelInterface.wsdl/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:element name="QueryCancelInterface">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="in" type="tns:inType"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="QueryCancelInterfaceResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="out" type="xsd:string"/>
            <xsd:element name="EAIType" type="tns:EAIType"/>
            <xsd:element name="COUNTPAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="inType">
        <xsd:sequence>
          <xsd:element name="Department" type="xsd:string"/>
          <xsd:element name="TableName" type="xsd:string"/>
          <xsd:element name="condition_sql" type="xsd:string"/>
          <xsd:element name="currentPage" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="EAIType">
        <xsd:sequence>
          <xsd:element name="ERRORMSG" type="xsd:string"/>
          <xsd:element name="SK_FLAG" type="xsd:string"/>
          <xsd:element name="EAI_FLAG" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="QueryCancelInterfaceRequest">
    <WL5G3N0:part element="WL5G3N1:QueryCancelInterface" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="QueryCancelInterfaceResponse">
    <WL5G3N0:part element="WL5G3N1:QueryCancelInterfaceResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="QueryCancelInterface">
    <WL5G3N0:operation name="QueryCancelInterface">
      <WL5G3N0:input message="WL5G3N1:QueryCancelInterfaceRequest"/>
      <WL5G3N0:output message="WL5G3N1:QueryCancelInterfaceResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="QueryCancelInterfaceSOAP" type="WL5G3N1:QueryCancelInterface">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="QueryCancelInterface">
      <WL5G3N2:operation soapAction="http://www.example.org/QueryCancelInterface.wsdl/QueryCancelInterface"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="QueryCancelInterfaceSOAPQSService">
    <WL5G3N0:port binding="WL5G3N1:QueryCancelInterfaceSOAP" name="QueryCancelInterfaceSOAPQSPort">
      <WL5G3N2:address location="http://************:9001/EAI/RoutingProxyService/EAI_SOAP_ServiceRoot?INT_CODE=EAI_INT_2171"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>