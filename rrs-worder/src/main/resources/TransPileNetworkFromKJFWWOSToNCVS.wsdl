<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="JwWorkordersService" targetNamespace="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:complexType name="jwWorkordersDTO">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="account" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusCode" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusName" type="xs:string"/>
          <xsd:element minOccurs="0" name="effectDate" type="xs:dateTime"/>
          <xsd:element minOccurs="0" name="iar" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="idue" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="idue61" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="lapseDate" type="xs:dateTime"/>
          <xsd:element minOccurs="0" name="quotaSum" type="xs:decimal"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="getAllWorkOrders" type="getAllWorkOrders"/>
      <xsd:complexType name="getAllWorkOrders">
        <xsd:sequence/>
      </xsd:complexType>
      <xsd:element name="getAllWorkOrdersResponse" type="getAllWorkOrdersResponse"/>
      <xsd:complexType name="getAllWorkOrdersResponse">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="return" type="jwWorkordersDTO"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="getAllWorkOrders">
    <WL5G3N0:part element="WL5G3N1:getAllWorkOrders" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="getAllWorkOrdersResponse">
    <WL5G3N0:part element="WL5G3N1:getAllWorkOrdersResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="JwWorkordersServicePortType">
    <WL5G3N0:operation name="getAllWorkOrders">
      <WL5G3N0:input message="WL5G3N1:getAllWorkOrders" name="getAllWorkOrders"/>
      <WL5G3N0:output message="WL5G3N1:getAllWorkOrdersResponse" name="getAllWorkOrdersResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="JwWorkordersServiceSoapBinding" type="WL5G3N1:JwWorkordersServicePortType">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="getAllWorkOrders">
      <WL5G3N2:operation style="document"/>
      <WL5G3N0:input name="getAllWorkOrders">
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output name="getAllWorkOrdersResponse">
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="JwWorkordersService">
    <WL5G3N0:documentation>OSB Service</WL5G3N0:documentation>
    <WL5G3N0:port binding="WL5G3N1:JwWorkordersServiceSoapBinding" name="JwWorkordersServicePort">
      <WL5G3N2:address location="http://************:9001/Financial/NCVS/TransPileNetworkFromKJFWWOSToNCVS/TransPileNetworkFromKJFWWOSToNCVS"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>