<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="JwLimitReceivableService" targetNamespace="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://service.report.webservice.ncvs.pwcjw.haier.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:complexType name="jwLimitReceivableDTO">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="account" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusCode" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusCodePy" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusName" type="xs:string"/>
          <xsd:element minOccurs="0" name="ccusNamePy" type="xs:string"/>
          <xsd:element minOccurs="0" name="cdepCode" type="xs:string"/>
          <xsd:element minOccurs="0" name="cjytCode" type="xs:string"/>
          <xsd:element minOccurs="0" name="creSource" type="xs:string"/>
          <xsd:element minOccurs="0" name="effecDate" type="xs:dateTime"/>
          <xsd:element minOccurs="0" name="iar" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="idue61" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="iude" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="lapseDate" type="xs:dateTime"/>
          <xsd:element minOccurs="0" name="quotaSum" type="xs:decimal"/>
          <xsd:element minOccurs="0" name="type" type="xs:string"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="jwLimitReceivableReturnModel">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="list" nillable="true" type="tns:jwLimitReceivableDTO"/>
          <xsd:element minOccurs="0" name="message" type="xs:string"/>
          <xsd:element minOccurs="0" name="status" type="xs:string"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="getLimitReceivableBySource" type="tns:getLimitReceivableBySource"/>
      <xsd:complexType name="getLimitReceivableBySource">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="arg0" type="tns:jwLimitReceivableDTO"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="getLimitReceivableBySourceResponse" type="tns:getLimitReceivableBySourceResponse"/>
      <xsd:complexType name="getLimitReceivableBySourceResponse">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="return" type="tns:jwLimitReceivableReturnModel"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="getLimitReceivableBySourceResponse">
    <WL5G3N0:part element="WL5G3N1:getLimitReceivableBySourceResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="getLimitReceivableBySource">
    <WL5G3N0:part element="WL5G3N1:getLimitReceivableBySource" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="JwLimitReceivableServicePortType">
    <WL5G3N0:operation name="getLimitReceivableBySource">
      <WL5G3N0:input message="WL5G3N1:getLimitReceivableBySource" name="getLimitReceivableBySource"/>
      <WL5G3N0:output message="WL5G3N1:getLimitReceivableBySourceResponse" name="getLimitReceivableBySourceResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="JwLimitReceivableServiceSoapBinding" type="WL5G3N1:JwLimitReceivableServicePortType">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="getLimitReceivableBySource">
      <WL5G3N2:operation style="document"/>
      <WL5G3N0:input name="getLimitReceivableBySource">
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output name="getLimitReceivableBySourceResponse">
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="JwLimitReceivableService">
    <WL5G3N0:documentation>OSB Service</WL5G3N0:documentation>
    <WL5G3N0:port binding="WL5G3N1:JwLimitReceivableServiceSoapBinding" name="JwLimitReceivableServicePort">
      <WL5G3N2:address location="http://************:9001/Production/CBS/TransEffectiveLimitFromCBSTo360/TransEffectiveLimitFromCBSTo360"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>