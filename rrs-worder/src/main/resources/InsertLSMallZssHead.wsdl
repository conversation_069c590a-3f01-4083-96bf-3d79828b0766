<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="InsertLSMallZssHead" targetNamespace="http://www.example.org/InsertLSMallZssHead/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://www.example.org/InsertLSMallZssHead/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema targetNamespace="http://www.example.org/InsertLSMallZssHead/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/InsertLSMallZssHead/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:element name="LSMallZssHead">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="xsddm" type="xsd:string"/>
            <xsd:element name="dmgs" type="xsd:string"/>
            <xsd:element name="khdm" type="xsd:string"/>
            <xsd:element name="khswdjh" type="xsd:string"/>
            <xsd:element name="khmc" type="xsd:string"/>
            <xsd:element name="khsj" type="xsd:string"/>
            <xsd:element name="khdz" type="xsd:string"/>
            <xsd:element name="khyh" type="xsd:string"/>
            <xsd:element name="bz" type="xsd:string"/>
            <xsd:element name="skr" type="xsd:string"/>
            <xsd:element name="fhr" type="xsd:string"/>
            <xsd:element name="kprq" type="xsd:dateTime"/>
            <xsd:element name="fpzl" type="xsd:string"/>
            <xsd:element name="zx" type="xsd:string"/>
            <xsd:element name="fphm" type="xsd:string"/>
            <xsd:element name="xrrq" type="xsd:dateTime"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssHeadResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FLAG" type="xsd:string"/>
            <xsd:element name="MESSAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssHeadMUL">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element maxOccurs="unbounded" minOccurs="0" name="in" type="tns:inType"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="LSMallZssHeadMULResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FLAG" type="xsd:string"/>
            <xsd:element name="MESSAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="inType">
        <xsd:sequence>
          <xsd:element name="xsddm" type="xsd:string"/>
          <xsd:element name="dmgs" type="xsd:string"/>
          <xsd:element name="khdm" type="xsd:string"/>
          <xsd:element name="khswdjh" type="xsd:string"/>
          <xsd:element name="khmc" type="xsd:string"/>
          <xsd:element name="khsj" type="xsd:string"/>
          <xsd:element name="khdz" type="xsd:string"/>
          <xsd:element name="khyh" type="xsd:string"/>
          <xsd:element name="bz" type="xsd:string"/>
          <xsd:element name="skr" type="xsd:string"/>
          <xsd:element name="fhr" type="xsd:string"/>
          <xsd:element name="kprq" type="xsd:dateTime"/>
          <xsd:element name="fpzl" type="xsd:string"/>
          <xsd:element name="zx" type="xsd:string"/>
          <xsd:element name="fphm" type="xsd:string"/>
          <xsd:element name="xrrq" type="xsd:dateTime"/>
          <xsd:element name="kpbz" type="xsd:string"/>
          <xsd:element name="xrsj" type="xsd:dateTime"/>
          <xsd:element name="tzdh" type="xsd:string"/>
          <xsd:element name="chfph" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="LSMallZssHeadRequest">
    <WL5G3N0:part element="WL5G3N1:LSMallZssHead" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssHeadResponse">
    <WL5G3N0:part element="WL5G3N1:LSMallZssHeadResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssHeadMULRequest">
    <WL5G3N0:part element="WL5G3N1:LSMallZssHeadMUL" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="LSMallZssHeadMULResponse">
    <WL5G3N0:part element="WL5G3N1:LSMallZssHeadMULResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="InsertLSMallZssHead">
    <WL5G3N0:operation name="LSMallZssHead">
      <WL5G3N0:input message="WL5G3N1:LSMallZssHeadRequest"/>
      <WL5G3N0:output message="WL5G3N1:LSMallZssHeadResponse"/>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="LSMallZssHeadMUL">
      <WL5G3N0:input message="WL5G3N1:LSMallZssHeadMULRequest"/>
      <WL5G3N0:output message="WL5G3N1:LSMallZssHeadMULResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="InsertLSMallZssHeadSOAP" type="WL5G3N1:InsertLSMallZssHead">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="LSMallZssHead">
      <WL5G3N2:operation soapAction="http://www.example.org/InsertLSMallZssHead/LSMallZssHead"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
    <WL5G3N0:operation name="LSMallZssHeadMUL">
      <WL5G3N2:operation soapAction="http://www.example.org/InsertLSMallZssHead/LSMallZssHeadMUL"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="InsertLSMallZssHeadSOAPQSService">
    <WL5G3N0:port binding="WL5G3N1:InsertLSMallZssHeadSOAP" name="InsertLSMallZssHeadSOAPQSPort">
      <WL5G3N2:address location="http://************:9001/EAI/RoutingProxyService/EAI_SOAP_ServiceRoot?INT_CODE=EAI_INT_2227"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>