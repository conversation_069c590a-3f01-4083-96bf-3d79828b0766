<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.branchbalance.dao.BranchBalanceDao">
    <resultMap id="branchBalanceViewEntityMap" type="com.bonc.rrs.branchbalance.entity.BranchBalanceViewEntity">
        <result property="worderId" column="worder_id"/>
        <result property="worderTypeId" column="worder_type_id"/>
        <result property="worderTypeName" column="worder_type_name"/>
        <result property="worderNo" column="worder_no"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="balanceStatus" column="balance_status"/>
        <result property="balanceStatusValue" column="balance_status_value"/>
        <result property="balanceType" column="balance_type"/>
        <result property="balanceTypeName" column="balance_type_name"/>
        <result property="balanceFee" column="balance_fee"/>
        <result property="balanceFeeTax" column="balance_fee_tax"/>
        <result property="balanceFeeSum" column="balance_fee_sum"/>
        <result property="balancePublishTime" column="balance_publish_time"/>
    </resultMap>
    <resultMap id="worderDetailEntityMap" type="com.bonc.rrs.branchbalance.entity.WorderDetailEntity">
        <id property="worderId" column="worder_id"/>
        <result property="worderTypeId" column="worder_type_id"/>
        <result property="worderTypeName" column="worder_type_name"/>
        <result property="worderNo" column="worder_no"/>
        <result property="companyNo" column="company_no"/>
        <result property="companyName" column="company_name"/>
        <result property="dotNo" column="dot_Code"/>
        <result property="dotName" column="dot_name"/>
        <result property="worderStatus" column="worder_status"/>
        <result property="worderStatusValue" column="worder_status_value"/>
        <result property="worderSetStatus" column="worder_set_status"/>
        <result property="worderIncreStatus" column="worder_incre_status"/>
        <result property="worderExciStatus" column="worder_exci_status"/>
        <result property="worderSetStatusValue" column="worder_set_status_value"/>
        <result property="worderIncreStatusValue" column="worder_incre_status_value"/>
        <result property="worderExciStatusValue" column="worder_exci_status_value"/>
        <result property="userBalanceFee" column="user_balance_fee"/>
        <result property="userBalanceFeeTax" column="user_balance_fee_tax"/>
        <result property="userBalanceFeeSum" column="user_balance_fee_sum"/>
        <result property="companyBalanceFee" column="company_balance_fee"/>
        <result property="companyBalanceFeeTax" column="company_balance_fee_tax"/>
        <result property="companyBalanceFeeSum" column="company_balance_fee_sum"/>
        <result property="dotBalanceFee" column="dot_balance_fee"/>
        <result property="dotBalanceFeeTax" column="dot_balance_fee_tax"/>
        <result property="dotBalanceFeeSum" column="dot_balance_fee_sum"/>
        <result property="dotIncreBalanceFee" column="dot_incre_balance_fee"/>
        <result property="dotIncreBalanceFeeTax" column="dot_incre_balance_fee_tax"/>
        <result property="dotIncreBalanceFeeSum" column="dot_incre_balance_fee_sum"/>
        <result property="attendantBalanceFee" column="attendant_balance_fee"/>
        <result property="dotIncreDiscountAmount" column="dot_incre_discount_amount"/>
    </resultMap>
    <resultMap id="worderBalanceFeeEntityMap" type="com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity">
        <id property="worderId" column="worder_id"/>
        <result property="userActualCost" column="user_actual_cost"/>
        <result property="worderTypeId" column="worder_type_id"/>
        <result property="worderNo" column="worder_no"/>
        <result property="suiteId" column="suite_id"/>
        <result property="companyBalanceRuleId" column="company_balance_rule_id"/>
        <result property="dotBalanceRuleId" column="dot_balance_rule_id"/>
        <result property="dotIncreBalanceRuleId" column="dot_incre_balance_rule_id"/>
        <result property="attendantBalanceRuleId" column="attendant_balance_rule_id"/>
        <result property="worderStatus" column="worder_status"/>
        <result property="worderSetStatus" column="worder_set_status"/>
        <result property="worderIncreStatus" column="worder_incre_status"/>
        <result property="userBalanceFee" column="user_balance_fee"/>
        <result property="userBalanceFeeTax" column="user_balance_fee_tax"/>
        <result property="userBalanceFeeSum" column="user_balance_fee_sum"/>
        <result property="companyBalanceFee" column="company_balance_fee"/>
        <result property="companyBalanceFeeTax" column="company_balance_fee_tax"/>
        <result property="companyBalanceFeeSum" column="company_balance_fee_sum"/>
        <result property="dotBalanceFee" column="dot_balance_fee"/>
        <result property="dotBalanceFeeTax" column="dot_balance_fee_tax"/>
        <result property="dotBalanceFeeSum" column="dot_balance_fee_sum"/>
        <result property="dotIncreBalanceFee" column="dot_incre_balance_fee"/>
        <result property="dotIncreBalanceFeeTax" column="dot_incre_balance_fee_tax"/>
        <result property="dotIncreBalanceFeeSum" column="dot_incre_balance_fee_sum"/>
        <result property="dotIncreDiscountAmount" column="dot_incre_discount_amount"/>
        <result property="dotBalanceRate" column="dot_balance_rate"/>
        <result property="createTime" column="create_time"/>
        <result property="confirmCompletionTime" column="confirm_completion_time"/>
        <result property="worderFinishTime" column="worder_finish_time"/>
        <result property="dotQuality" column="dot_quality"/>
        <!--        <result property="dotTaxRate" column="dot_tax_rate"/>-->
        <result property="taxPoint" column="tax_point"/>
        <result property="dotName" column="dot_name"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="conveyAccountChargeFlag" column="convey_account_charge_flag"/>
        <collection property="materielList" ofType="com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity">
            <id property="id" column="id"/>
            <result property="worderId" column="worder_id"/>
            <result property="materielId" column="materiel_id"/>
            <result property="materielName" column="materiel_name"/>
            <result property="num" column="num"/>
        </collection>
    </resultMap>
    <select id="queryBranchBalanceViewCount" parameterType="java.util.HashMap" resultType="java.lang.Integer">
        select count(*) from (
        <if test="balanceType == null or balanceType == 0">
            SELECT a.worder_id, a.worder_type_id, b.name AS worder_type_name, a.worder_no, e.field_value as company_no,
            e.field_value_dup as company_name,
            a.worder_set_status AS balance_status, a.worder_set_status_value AS balance_status_value,
            0 AS balance_type, '工单结算' AS balance_type_name, a.dot_balance_fee AS balance_fee, a.worder_publish_time AS
            balance_publish_time
            FROM worder_information a
            LEFT JOIN worder_type b ON a.worder_type_id=b.id
            LEFT JOIN worder_template c ON a.template_id=c.id
            LEFT Join worder_ext_field e ON a.worder_no=e.worder_no and e.field_id=c.settle_way+100
            WHERE <!-- a.worder_set_status in (5,7) -->
            a.worder_id in ${worderIds} and a.dot_id = #{dotId}
            <if test="worderNo != null and worderNo != ''">
                <bind name="worderNoLike" value="'%' + worderNo + '%'"/>
                and a.worder_no like #{worderNoLike}
            </if>
            <if test="startTime != null and startTime != ''">
                and date(a.worder_publish_time) <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date(a.worder_publish_time) <![CDATA[ <= ]]> #{endTime}
            </if>
        </if>
        <if test="balanceType == null">
            UNION
        </if>
        <if test="balanceType == null or balanceType == 1">
            SELECT a.worder_id, a.worder_type_id, b.name AS worder_type_name, a.worder_no, e.field_value as company_no,
            e.field_value_dup as company_name,
            a.worder_Incre_status AS balance_status, a.worder_Incre_status_value AS balance_status_value,
            1 AS balance_type, '增项结算' AS balance_type_name, a.dot_incre_balance_fee AS balance_fee,
            a.worder_incre_publish_time AS balance_publish_time
            FROM worder_information a
            LEFT JOIN worder_type b ON a.worder_type_id=b.id
            LEFT JOIN worder_template c ON a.template_id=c.id
            LEFT Join worder_ext_field e ON a.worder_no=e.worder_no and e.field_id=c.settle_way+100
            WHERE <!-- a.worder_incre_status in (2,4) -->
            a.worder_id in ${increIds} and a.dot_id = #{dotId}
            <if test="worderNo != null and worderNo != ''">
                <bind name="worderNoLike" value="'%' + worderNo + '%'"/>
                and a.worder_no like #{worderNoLike}
            </if>
            <if test="startTime != null and startTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ <= ]]> #{endTime}
            </if>
        </if>
        ) t
    </select>
    <select id="queryBranchBalanceViewData" parameterType="java.util.HashMap" resultMap="branchBalanceViewEntityMap">
        select * from (
        <if test="balanceType == null or balanceType == 0">
            SELECT a.worder_id, a.worder_type_id, b.name AS worder_type_name, a.worder_no, e.field_value as company_no,
            ci.company_name,
            a.worder_set_status AS balance_status, a.worder_set_status_value AS balance_status_value,
            0 AS balance_type, '工单费用' AS balance_type_name, a.dot_balance_fee AS balance_fee, a.dot_balance_fee_tax AS
            balance_fee_tax,
            a.dot_balance_fee_sum AS balance_fee_sum, p.submit_time AS balance_publish_time,r.balance_tax_rate_value as
            balance_point
            FROM worder_information a
            LEFT JOIN worder_type b ON a.worder_type_id=b.id
            LEFT JOIN worder_template c ON a.template_id=c.id
            LEFT JOIN balance_rule r ON c.dot_balance_rule_id=r.id
            LEFT Join worder_ext_field e ON a.worder_no=e.worder_no and e.field_id=c.settle_way+100
            LEFT JOIN balance_publish p ON p.worder_ids LIKE concat('%',#{a.worder_id})
            or p.worder_ids LIKE concat(#{a.worder_id},'%') or p.worder_ids LIKE concat('%,',#{a.worder_id},',%')
            left join company_information ci on e.field_value = ci.company_id
            WHERE a.worder_set_status in (5,6,7,12) and
            a.worder_id in ${worderIds} and a.dot_id = #{dotId}
            <if test="worderNo != null and worderNo != ''">
                <bind name="worderNoLike" value="'%' + worderNo + '%'"/>
                and a.worder_no like #{worderNoLike}
            </if>
            <if test="startTime != null and startTime != ''">
                and date(a.worder_publish_time) <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date(a.worder_publish_time) <![CDATA[ <= ]]> #{endTime}
            </if>
        </if>
        <if test="balanceType == null">
            UNION
        </if>
        <if test="balanceType == null or balanceType == 1">
            SELECT a.worder_id, a.worder_type_id, b.name AS worder_type_name, a.worder_no,
            e.field_value as company_no, ci.company_name,
            a.worder_Incre_status AS balance_status, a.worder_Incre_status_value AS balance_status_value,
            1 AS balance_type, '增项费用' AS balance_type_name, a.dot_incre_balance_fee AS balance_fee,
            a.dot_incre_balance_fee_tax AS balance_fee_tax, a.dot_incre_balance_fee_sum AS balance_fee_sum,
            p.submit_time AS balance_publish_time,r.balance_tax_rate_value as balance_point
            FROM worder_information a
            LEFT JOIN worder_type b ON a.worder_type_id=b.id
            LEFT JOIN worder_template c ON a.template_id=c.id
            LEFT JOIN balance_rule r ON c.dot_incre_balance_rule_id=r.id
            LEFT Join worder_ext_field e ON a.worder_no=e.worder_no and e.field_id=c.settle_way+100
            LEFT JOIN balance_publish p ON p.incre_ids LIKE concat('%',#{a.worder_id}) OR p.incre_ids LIKE
            concat(#{a.worder_id},'%') OR p.incre_ids LIKE concat('%,',#{a.worder_id},',%')
            left join company_information ci on e.field_value = ci.company_id
            WHERE a.worder_incre_status in (2,3,4,6) and
            a.worder_id in ${increIds} and a.dot_id = #{dotId}
            <if test="worderNo != null and worderNo != ''">
                <bind name="worderNoLike" value="'%' + worderNo + '%'"/>
                and a.worder_no like #{worderNoLike}
            </if>
            <if test="startTime != null and startTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ <= ]]> #{endTime}
            </if>
        </if>
        <if test="balanceType == null">
            UNION
        </if>
        <if test="balanceType == null or balanceType == 2">
            SELECT t.id as worder_id, a.worder_type_id, b.name AS worder_type_name, a.worder_no,
            e.field_value as company_no, ci.company_name,
            t.status AS balance_status, case t.status when 13 then '激励结算已发布' when 14 then '激励已结算' end AS
            balance_status_value,
            2 AS balance_type, '奖惩金额' AS balance_type_name, t.stimulate_fee AS balance_fee,
            t.storage_tax AS balance_fee_tax, t.price_tax AS balance_fee_sum, t.publish_time AS balance_publish_time,0
            as balance_point
            FROM worder_pm_stimulate t
            LEFT JOIN worder_information a ON a.worder_id=t.worder_id
            LEFT JOIN worder_type b ON a.worder_type_id=b.id
            LEFT JOIN worder_template c ON a.template_id=c.id
            LEFT Join worder_ext_field e ON a.worder_no=e.worder_no and e.field_id=c.settle_way+100
            left join company_information ci on e.field_value = ci.company_id
            WHERE <!-- t.status in (13,14) -->
            t.id in ${stimulateIds} and a.dot_id = #{dotId} and t.is_delete = 0
            <if test="worderNo != null and worderNo != ''">
                <bind name="worderNoLike" value="'%' + worderNo + '%'"/>
                and a.worder_no like #{worderNoLike}
            </if>
            <if test="startTime != null and startTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date(a.worder_incre_publish_time) <![CDATA[ <= ]]> #{endTime}
            </if>
        </if>
        ) t limit ${startRow}, ${limit}
    </select>
    <select id="queryWorderDetail" parameterType="java.lang.Integer" resultMap="worderDetailEntityMap">
        SELECT a.worder_id,
               a.worder_type_id,
               b.full_name AS worder_type_name,
               a.worder_no,
               d.company_no,
               d.company_name,
               e.dot_code,
               e.dot_name,
               a.worder_status,
               (select sdd.detail_name from sys_dictionary sd left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id where sd.dic_number ='worder_status' and sdd.detail_number = a.worder_status) worder_status_value,
               a.worder_exec_status,
               a.worder_exec_status_value,
               a.worder_set_status,
               a.worder_incre_status,
               a.worder_exci_status,
               a.worder_set_status_value,
               a.worder_incre_status_value,
               a.worder_exci_status_value,
               a.user_balance_fee,
               a.user_balance_fee_tax,
               a.user_balance_fee_sum,
               a.company_balance_fee,
               a.company_balance_fee_tax,
               a.company_balance_fee_sum,
               a.dot_balance_fee,
               a.dot_balance_fee_tax,
               a.dot_balance_fee_sum,
               a.dot_incre_balance_fee,
               a.dot_incre_balance_fee_tax,
               a.dot_incre_balance_fee_sum,
               a.attendant_balance_fee,
               a.dot_incre_discount_amount
        FROM worder_information a
                 LEFT JOIN worder_type b ON a.worder_type_id = b.id
                 LEFT JOIN worder_template t ON a.template_id = t.id
                 LEFT JOIN worder_ext_field c ON a.worder_no = c.worder_no AND c.field_id = t.settle_way + 100
                 LEFT JOIN company_information d ON c.field_value = d.company_id
                 LEFT JOIN dot_information e ON a.dot_id = e.dot_id
        where a.worder_id = #{worderId}
    </select>

    <select id="queryWorderForBalanceByWorderId" resultMap="worderBalanceFeeEntityMap">
        SELECT a.worder_id,
               a.worder_no,
               a.worder_type_id,
               a.worder_status,
               a.worder_set_status,
               a.worder_incre_status,
               a.company_balance_fee,
               a.company_balance_fee_tax,
               a.company_balance_fee_sum,
               a.dot_balance_fee,
               a.dot_balance_fee_tax,
               dot_balance_fee_sum,
               a.dot_incre_balance_fee,
               a.dot_incre_balance_fee_tax,
               a.dot_incre_balance_fee_sum,
               a.dot_incre_discount_amount,
               b.suite_id,
               b.company_balance_rule_id,
               d.tax_point,
               dot_name,
               b.dot_balance_rule_id,
               b.dot_incre_balance_rule_id,
               b.attendant_balance_rule_id,
               c.id,
               c.materiel_id,
               c.num,
               e.settle_account as        dot_balance_rate,
               a.invoice_id,
               if(f.id is not null, 1, 0) convey_account_charge_flag,
               a.create_time,
               a.confirm_completion_time,
               a.worder_finish_time,
               d.dot_quality
        FROM worder_information a
                 LEFT JOIN worder_template b on a.template_id = b.id
                 LEFT JOIN worder_used_materiel c on a.worder_id = c.worder_id
                 LEFT JOIN dot_information d on a.dot_id = d.dot_id
                 LEFT JOIN dot_star_design e on d.star_id = e.id
                 left join worder_template_fixed_material f on a.template_id = f.template_id and f.worder_status = 5
        WHERE a.worder_id = #{worderId}
    </select>

    <!--    <select id="queryWorderForBalanceByWorderId" resultMap="worderBalanceFeeEntityMap">-->
    <!--        SELECT a.worder_id,a.worder_no,a.worder_status,a.worder_set_status,a.worder_incre_status,a.company_balance_fee,-->
    <!--            a.company_balance_fee_tax,a.company_balance_fee_sum,a.dot_balance_fee,a.dot_balance_fee_tax,dot_balance_fee_sum,a.dot_incre_balance_fee,-->
    <!--            a.dot_incre_balance_fee_tax,a.dot_incre_balance_fee_sum,a.dot_incre_discount_amount,b.suite_id,b.company_balance_rule_id,-->
    <!--            d.tax_point, dot_name,b.dot_balance_rule_id,b.dot_incre_balance_rule_id,b.attendant_balance_rule_id,c.id,c.materiel_id,c.num,-->
    <!--            e.settle_account as dot_balance_rate-->
    <!--        FROM worder_information a LEFT JOIN dot_information d on a.dot_id=d.dot_id-->
    <!--            LEFT JOIN dot_star_design e on d.star_id=e.id ,-->
    <!--          worder_template b, worder_used_materiel c-->
    <!--        WHERE a.template_id=b.id AND a.worder_id=c.worder_id AND a.worder_id = #{worderId}-->
    <!--    </select>-->

    <select id="queryWorderForBalance" resultMap="worderBalanceFeeEntityMap">
        SELECT a.worder_id,
               a.worder_no,
               a.worder_type_id,
               a.worder_status,
               a.worder_set_status,
               a.worder_incre_status,
               a.company_balance_fee,
               a.company_balance_fee_tax,
               a.company_balance_fee_sum,
               a.dot_balance_fee,
               a.dot_balance_fee_tax,
               dot_balance_fee_sum,
               a.dot_incre_balance_fee,
               a.dot_incre_balance_fee_tax,
               a.dot_incre_balance_fee_sum,
               a.dot_incre_discount_amount,
               b.suite_id,
               b.company_balance_rule_id,
               d.tax_point,
               dot_name,
               b.dot_balance_rule_id,
               b.dot_incre_balance_rule_id,
               b.attendant_balance_rule_id,
               c.id,
               c.materiel_id,
               c.num,
               e.settle_account as dot_balance_rate,
               a.create_time,
               a.confirm_completion_time,
               a.worder_finish_time,
               d.dot_quality
        FROM worder_information a
                 LEFT JOIN dot_information d on a.dot_id = d.dot_id
                 LEFT JOIN dot_star_design e on d.star_id = e.id,
             worder_template b,
             worder_used_materiel c
        WHERE a.template_id = b.id
          AND a.worder_id = c.worder_id
          AND a.worder_set_status = 0
    </select>
    <select id="queryOneWorderForBalance" parameterType="java.lang.Integer" resultMap="worderBalanceFeeEntityMap">
        SELECT ifnull(a.user_actual_cost, 0) user_actual_cost,
               a.worder_id,
               a.worder_no,
               a.worder_status,
               a.worder_set_status,
               a.worder_incre_status,
               a.user_balance_fee,
               a.user_balance_fee_tax,
               a.user_balance_fee_sum,
               a.dot_incre_balance_fee,
               a.dot_incre_balance_fee_tax,
               a.dot_incre_balance_fee_sum,
               a.dot_incre_discount_amount,
               b.suite_id,
               b.company_balance_rule_id,
               b.dot_balance_rule_id,
               b.dot_incre_balance_rule_id,
               b.attendant_balance_rule_id,
               c.id,
               c.materiel_id,
               c.num,
               f.materiel_name,
               e.settle_account as           dot_balance_rate,
               d.tax_point,
               d.dot_name,
               a.create_time,
               a.confirm_completion_time,
               a.worder_finish_time,
               d.dot_quality
        FROM worder_information a
                 LEFT JOIN dot_information d on a.dot_id = d.dot_id
                 LEFT JOIN dot_star_design e on d.star_id = e.id,
             worder_template b,
             worder_used_materiel c,
             materiel_information f
        WHERE a.worder_id = #{worderId}
          and a.template_id = b.id
          AND a.worder_id = c.worder_id
          AND c.materiel_id = f.id
    </select>

    <select id="queryOneNoUsedMaterialWorderForBalance" parameterType="java.lang.Integer" resultMap="worderBalanceFeeEntityMap">
        SELECT ifnull(a.user_actual_cost, 0) user_actual_cost,
               a.worder_id,
               a.worder_no,
               a.worder_status,
               a.worder_set_status,
               a.worder_incre_status,
               a.user_balance_fee,
               a.user_balance_fee_tax,
               a.user_balance_fee_sum,
               a.dot_incre_balance_fee,
               a.dot_incre_balance_fee_tax,
               a.dot_incre_balance_fee_sum,
               a.dot_incre_discount_amount,
               b.suite_id,
               b.company_balance_rule_id,
               b.dot_balance_rule_id,
               b.dot_incre_balance_rule_id,
               b.attendant_balance_rule_id,
               e.settle_account as           dot_balance_rate,
               d.tax_point,
               d.dot_name,
               a.create_time,
               a.confirm_completion_time,
               a.worder_finish_time,
               d.dot_quality
        FROM worder_information a
                 LEFT JOIN dot_information d on a.dot_id = d.dot_id
                 LEFT JOIN dot_star_design e on d.star_id = e.id,
             worder_template b
        WHERE a.worder_id = #{worderId}
          and a.template_id = b.id
    </select>

    <update id="batchSaveWorderBalanceFee" parameterType="java.util.List">
        update worder_information t
        <set>
            t.company_balance_fee = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.companyBalanceFee}
            </foreach>
            END,
            t.company_balance_fee_tax = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.companyBalanceFeeTax}
            </foreach>
            END,
            t.company_balance_fee_sum = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.companyBalanceFeeSum}
            </foreach>
            END,
            t.dot_balance_fee = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotBalanceFee}
            </foreach>
            END,
            t.dot_balance_fee_tax = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotBalanceFeeTax}
            </foreach>
            END,
            t.dot_balance_fee_sum = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotBalanceFeeSum}
            </foreach>
            END,
            t.worder_set_status = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.worderSetStatus}
            </foreach>
            END,
            t.worder_set_status_value = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN #{item.worderSetStatusValue}
            </foreach>
            END
        </set>
        <where>
            t.worder_id in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                ${item.worderId}
            </foreach>
        </where>
    </update>
    <update id="batchSaveIncreBalanceFee" parameterType="java.util.List">
        update worder_information t
        <set>
            t.dot_incre_balance_fee = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotIncreBalanceFee}
            </foreach>
            END,
            t.dot_incre_balance_fee_tax = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotIncreBalanceFeeTax}
            </foreach>
            END,
            t.dot_incre_balance_fee_sum = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.dotIncreBalanceFeeSum}
            </foreach>
            END,
            t.worder_incre_status = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN ${item.worderIncreStatus}
            </foreach>
            END,
            t.worder_incre_status_value = CASE worder_id
            <foreach collection="list" item="item" index="index">
                WHEN ${item.worderId} THEN #{item.worderIncreStatusValue}
            </foreach>
            END
        </set>
        <where>
            t.worder_id in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                ${item.worderId}
            </foreach>
        </where>
    </update>
    <update id="saveUserBalanceFee">
        update worder_information t
        set t.user_balance_fee     = #{userFee},
            t.user_balance_fee_tax = #{tax},
            t.user_balance_fee_sum = #{feeAll}
        where t.worder_id = #{worderId}
    </update>
    <update id="saveIncreBalanceFee" parameterType="com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity">
        update worder_information t
        set t.user_balance_fee          = #{userBalanceFee},
            t.user_balance_fee_tax      = #{userBalanceFeeTax},
            t.user_balance_fee_sum      = #{userBalanceFeeSum},
            t.dot_incre_balance_fee     = #{dotIncreBalanceFee},
            t.dot_incre_balance_fee_tax = #{dotIncreBalanceFeeTax},
            t.dot_incre_balance_fee_sum = #{dotIncreBalanceFeeSum},
            t.worder_incre_status       = #{worderIncreStatus},
            t.worder_incre_status_value = #{worderIncreStatusValue}
        where t.worder_id = #{worderId}
    </update>
    <update id="saveDotIncreBalanceFee" parameterType="com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity">
        update worder_information t
        set t.dot_incre_discount_amount = #{dotIncreDiscountAmount},
            t.dot_incre_balance_fee     = #{dotIncreBalanceFee},
            t.dot_incre_balance_fee_tax = #{dotIncreBalanceFeeTax},
            t.dot_incre_balance_fee_sum = #{dotIncreBalanceFeeSum},
            t.worder_incre_status       = #{worderIncreStatus},
            t.worder_incre_status_value = #{worderIncreStatusValue}
        where t.worder_id = #{worderId}
    </update>

    <update id="updateWorderInfo">
        update worder_information t
        <set>
            modify_time = now(),
            <if test="companyBalanceFee != null">
                t.company_balance_fee = #{companyBalanceFee},
            </if>
            <if test="companyBalanceFeeTax != null">
                t.company_balance_fee_tax = #{companyBalanceFeeTax},
            </if>
            <if test="companyBalanceFeeSum != null">
                t.company_balance_fee_sum = #{companyBalanceFeeSum},
            </if>
            <if test="dotBalanceFee != null">
                t.dot_balance_fee = #{dotBalanceFee},
            </if>
            <if test="dotBalanceFeeTax != null">
                t.dot_balance_fee_tax = #{dotBalanceFeeTax},
            </if>
            <if test="dotBalanceFeeSum != null">
                t.dot_balance_fee_sum = #{dotBalanceFeeSum},
            </if>
        </set>
        where t.worder_id = #{worderId}
    </update>

    <select id="querySurveyEfficiency" parameterType="java.lang.String" resultType="java.lang.String">
       select  * from (
             select  timestampdiff(hour  ,
                    (
                     select max(wi.create_time) from worder_information wi 
                      where wi.is_delete ='0'  and wi.worder_no =#{worderNo}
                     )
                   ,B.create_time) as convery_time
                    from (
                        select max(create_time) as create_time  from worder_operation_record wi
                            where wi.worder_exec_status ='5' and wi.worder_status ='1' and worder_no=#{worderNo}
                    ) B
              ) B where b.convery_time is not null

    </select>

    <select id="queryInstallEfficiency" parameterType="java.lang.String" resultType="java.lang.String">
      select  timestampdiff(day,
      (
              select max(wi.create_time) from worder_information wi 
                      where wi.is_delete ='0'  and wi.worder_no =#{worderNo}
             )
           ,B.confirm_completion_time)
          from (
            select max(wi.confirm_completion_time) as confirm_completion_time  from worder_information   wi
                 where  worder_no=#{worderNo}
        ) B
    </select>
</mapper>
