<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.branchbalance.dao.WorderBalanceFeeDetailDao">
    <resultMap id="worderBalanceFeeDetailEntityMap" type="com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity">
        <id  property="id" column="id"/>
        <result property="worderId" column="worder_id"/>
        <result property="balanceType" column="balance_type"/>
        <result property="balanceTarget" column="balance_target"/>
        <result property="materielId" column="materiel_id"/>
        <result property="isSuite" column="is_suite"/>
        <result property="num" column="num"/>
        <result property="balanceFee" column="balance_fee"/>
        <result property="description" column="description"/>
        <result property="materielName" column="materiel_name"/>
    </resultMap>
    <select id="listBalanceFeeDetailByWorderId"  parameterType="java.lang.Integer" resultMap="worderBalanceFeeDetailEntityMap">
        select a.*, t.materiel_name from worder_balance_fee_detail a
        left join (
          select b.id as materiel_id, b.materiel_name, 0 as is_suite
          from materiel_information b
          UNION
          select d.id as materiel_id, d.name as materiel_name, 1 as is_suite
          from suite_information d
        ) t on a.materiel_id=t.materiel_id and a.is_suite=t.is_suite
        where a.worder_id=#{worderId}
        and a.is_delete = 0
        order by a.is_suite desc, a.id asc
    </select>

    <select id="getStimulateList" resultType="com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity">
        select (CASE WHEN wps.incentive_type = 0 THEN '正激励'
        WHEN wps.incentive_type = 1 THEN '负激励'
        END) incentive_type_value,wps.stimulate_fee,(CASE WHEN wps.price_type = 0 THEN '含税价'
        WHEN wps.price_type = 1 THEN '不含税价'
        END) priceTypeValue
        from worder_pm_stimulate wps
        where wps.is_delete = 0
        and wps.status not in (10,11,16)
        and stimulate_type = #{type}
        and wps.worder_id = #{worderId}
    </select>

    <select id="getStatusByBCB" resultType="java.lang.String">
     select bcb.status_flag from worder_child_information wci
        inner join balance_publish bp on wci.publish_id = bp.id
        inner join balance_cvp_bill bcb on bp.cvp_bill_id = bcb.id
        where wci.is_delete = 0
        and wci.balance_source = 0
        and wci.worder_id = #{worderId}
        limit 1
    </select>
</mapper>