<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.branchbalance.dao.WorderUsedMaterielDao">
    <select id="queryCount" resultType="int">
        select count(*) from worder_used_materiel wum where wum.materiel_id = #{id}
    </select>
    <select id="getCountByWrderId" resultType="int" >
        select count(*) from worder_used_materiel wum where wum.materiel_id = #{materielId} and wum.worder_id=#{worderId}
    </select>
    <select id="selectListByWorderIdAndType"
            resultType="com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity">
        select
            wum.*
        from
            worder_used_materiel wum ,
            materiel_information mi
        where
            wum.materiel_id = mi.id
          and mi.materiel_type_id = #{type}
          and wum.worder_id = #{worderId}
    </select>

    <insert id="savaWorderUsedMateriel"  useGeneratedKeys="true" keyProperty="id"  parameterType="com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity" >
        insert into worder_used_materiel (worder_id,materiel_Id,num,brand_id,materiel_spec) values
        (#{worderId},#{materielId},#{num},#{brandId},#{materielSpec})
    </insert>

    <delete id="deleteByWorderIdAndMaterielId" parameterType="int">
        delete    from worder_used_materiel  where materiel_id =#{materielId} and worder_id =#{worderId}
    </delete>
</mapper>