<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.efficiencyarearule.dao.EfficiencyAreaRuleMapper">

    <resultMap id="EfficiencyAreaRuleMap" type="com.bonc.rrs.efficiencyarearule.entity.EfficiencyAreaRuleEntity">
        <result property="id" column="id"/>
        <result property="installCity" column="install_city"/>
        <result property="installCityCode" column="install_city_code"/>
        <result property="brand" column="brand"/>
        <result property="brandName" column="brand_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="execRule" column="exec_rule"/>
    </resultMap>
    <insert id="addEfficiencyAreaRule">
        INSERT INTO efficiency_area_rule (install_city, install_city_code, brand, brand_name, is_delete, exec_rule, `type`)
        select  concat(br1.name,'-',br.name),br.regcode,b.id,brand_name,0,${params.execrule},0 from biz_region br,biz_region br1,brand b where
            1=1 and br.regcode in
        <foreach item="regcode"  collection="params.regcodes" open="(" separator="," close=")">
            ${regcode}
        </foreach>
           and br1.regcode=substring(br.regcode,1,3)
            and b.id in
        <foreach item="brandId"  collection="params.brandIds" open="(" separator="," close=")">
            ${brandId}
        </foreach>
    </insert>

    <select id="queryOnefficiencyAreaRule"  resultMap="EfficiencyAreaRuleMap">
        select  ear.* from efficiency_area_rule ear,
            (select max(install_city_code) install_city_code,brand from (
                select ear.* from efficiency_area_rule ear ,biz_region br where
              (
                 ear.install_city_code =substring(br.regcode,1,3)
                 or
                 ear.install_city_code =substring(br.regcode,1,6)
                 or
                  ear.install_city_code =substring(br.regcode,1,9)
                 )
              and  ear.brand  = #{brand}   and br.id =  #{areaId} and is_delete =0 and ear.type=0
              union
               select  ear.* from efficiency_area_rule ear  where ear.install_city_code ='-1'
                 and  ear.brand  =#{brand}  and is_delete =0 and ear.type=0
            ) b group by brand
         ) a where ear.brand =a.brand and ear.install_city_code =a.install_city_code and ear.type=0
    </select>


</mapper>