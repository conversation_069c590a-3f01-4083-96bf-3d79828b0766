<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.supervise.dao.SuperviseInfomationMapper">
    <select id="getWorderInfo" resultType="com.bonc.rrs.supervise.dto.WorderInfo">
        select
            wi.worder_id ,
            wi.worder_no ,
            wi.dot_id ,
            wi.candidate_branch ,
            wi.template_id ,
            wi.user_name ,
            wi.user_phone ,
            wt.brand_id ,
            wt.service_type_enum ,
            wi.area_id,
            su.username dotUserName,
            su.mobile dotUserTelephone,
            su.user_id dotUserId
        FROM
            worder_information wi
                LEFT JOIN
            worder_template wt ON wi.template_id = wt.id
                LEFT JOIN
            dot_information di ON wi.dot_id = di.dot_id
                left join  dot_contacts dc on di.dot_code = dc.dot_code
                LEFT JOIN
            sys_user su ON dc.contacts_phone = su.mobile and su.role_name = '网点管理员'
        WHERE
            wi.worder_id = #{worderId}
    </select>
    <select id="getServiceUserByWorder"
            resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity">
        select
            su.user_id ,
            su.username ,
            su.employee_name
        from
            worder_information wi ,
            sys_user su
        where wi.pm_id = su.user_id and wi.worder_no = #{worderNo}
    </select>

    <select id="queryQueryDutyAndRelatedPeo"
            resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity">
        select
            distinct
            su.user_id ,
            su.username ,
            su.employee_name
        from
            manager_area_id mai,
            sys_user su,
            sys_user_role sur
        where mai.user_id = su.user_id and su.user_id = sur.user_id
          and mai.brand_id = #{params.brandId}
          <foreach collection="params.areaIds" item="areaId" separator="," open="and mai.area_id in (" close=")">
              #{areaId}
          </foreach>
          and sur.role_id in (${roles})
          <if test="params.serviceType != 0">
              and (mai.service_type = 0 or mai.service_type = #{params.serviceType})
          </if>
    </select>
    <select id="selectListByWorder" resultType="com.bonc.rrs.supervise.dto.SuperviseInfomationInfo">
        select
            si.*,
            IFNULL(su.employee_name,su.username) as createUserName
        from
            supervise_infomation si ,
            sys_user su
        where si.create_user_id = su.user_id
          and si.is_delete = 0
          and si.worder_no = #{params.worderNo}
        <if test="params.noState != null">
            and si.state != #{params.noState}
        </if>
        order by
            si.id desc
    </select>
    <select id="getEmployeeNameByUser" resultType="java.lang.String">
        select group_concat(IFNULL(su.employee_name,su.username)) from sys_user su where su.user_id in (${userIds});
    </select>
    <select id="queryListPage" resultType="com.bonc.rrs.supervise.dto.SuperviseInfomationInfo">
        select
            si.*,
            su2.employee_name as create_user_name,
            b.brand_name,
            su.employee_name as duty_peo_name,
            (
                select
                    count(1)
                from
                    supervise_tele_record str
                where
                    str.supervise_id = si.id
                  and str.is_delete = 0) as tele_num,
            (
                select
                    sum(str.trip_num)
                from
                    supervise_tele_record str
                where
                    str.supervise_id = si.id
                  and str.is_delete = 0) as trip_num
        from
            supervise_infomation si
            left join sys_user su on su.user_id = si.duty_peo
            left join sys_user su2 on su2.user_id = si.create_user_id ,
            brand b
        where
            b.id = si.brand_id
          and si.is_delete = 0
        <if test="param.worderSuperviseNo != null and param.worderSuperviseNo != ''">
            and si.worder_supervise_no = #{param.worderSuperviseNo}
        </if>
        <if test="param.state != null">
            and si.state = #{param.state}
        </if>
        <if test="param.brandId != null">
            and si.brand_id = #{param.brandId}
        </if>
        <if test="param.userPhone != null and param.userPhone != ''">
            and si.user_phone like concat('%',#{param.userPhone},'%')
        </if>
        <if test="param.worderNo != null and param.worderNo != ''">
            and si.worder_no = #{param.worderNo}
        </if>
        <if test="param.superviseClassification != null">
            and si.supervise_classification = #{param.superviseClassification}
        </if>
        <if test="param.superviseType != null">
            and si.supervise_type = #{param.superviseType}
        </if>
        <if test="param.superviseLv != null">
            and si.supervise_lv = #{param.superviseLv}
        </if>
        <if test="param.recoverType != null">
            and si.recover_type = #{param.recoverType}
        </if>
        <if test="param.dutyReo != null">
            and si.duty_peo = #{param.dutyReo}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 1 and param.userId != null">
            and si.duty_peo = #{param.userId}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 2 and param.userId != null">
            and si.create_user_id = #{param.userId}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 3 and param.userId != null">
            and (
                (si.create_user_id = #{param.userId})
                or
                (exists (
                    select 1
                        from biz_region r,
                            (select m.group_id,
                                    m.child_group_id,
                                    GROUP_CONCAT(distinct (b.regcode) separator '|')      as regcodes,
                                    GROUP_CONCAT(distinct (m.brand_id) separator ',')     as brand_ids,
                                    GROUP_CONCAT(distinct (m.service_type) separator ',') as service_types
                            from sys_user s
                                left join manager_area_id m on
                                    s.user_id = m.user_id
                                left join biz_region b on
                                    m.area_id = b.id
                                where s.user_id = #{param.userId}
                                    group by m.group_id, m.child_group_id) temp
                        where r.regcode regexp (temp.regcodes)
                        and r.type = 3
                        and si.area_id = r.id
                        and find_in_set(si.brand_id, temp.brand_ids)
                        and (FIND_IN_SET(si.service_type, temp.service_types) OR
                        FIND_IN_SET('0', temp.service_types))))
                )
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 4 and param.userId != null">
            and si.dot_user_id = #{param.userId}
        </if>
        <if test="param.processTimeStart != null and param.processTimeEnd != null">
            and si.process_time >= #{param.processTimeStart}
            and si.process_time &lt;= #{param.processTimeEnd}
        </if>
        <if test="param.createTimeStart != null and param.createTimeEnd != null">
            and si.create_time >= #{param.createTimeStart}
            and si.create_time &lt;= #{param.createTimeEnd}
        </if>
        <if test="param.queryType == 1">
            and si.process_time >= concat(curdate(),' 00:00:00')
            and si.process_time &lt;= concat(curdate(),' 23:59:59')
            and si.state != 3
        </if>
        <if test="param.queryType == 2">
            and si.process_time >= concat(DATE_ADD(curdate(), INTERVAL 1 DAY),' 00:00:00')
            and si.process_time &lt;= concat(DATE_ADD(curdate(), INTERVAL 1 DAY),' 23:59:59')
            and si.state != 3
        </if>
        <if test="param.queryType == 3">
            and si.state != 3
        </if>
        <if test="param.queryType == 2 or param.queryType == 1">
            order by si.process_time
        </if>
        <if test="param.queryType == 3">
            order by si.process_time desc
        </if>
        <if test="param.queryType == 0">
            order by si.id desc
        </if>
    </select>
    <select id="getDutyPeoList" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity">
        select
            distinct
            su.user_id ,
            su.username ,
            ifnull(su.employee_name,su.username) as employee_name
        from
            sys_user su ,
            sys_user_role sr
        where su.user_id = sr.user_id and sr.role_id != 4 and sr.role_id != 5 and su.status = 1 and su.user_id != 1 order by user_id asc
    </select>
    <select id="querySuperviseDetailById" resultType="com.bonc.rrs.supervise.dto.SuperviseInfomationInfo">
        select
            si.*,
            b.brand_name,
            su.employee_name as duty_peo_name,
            su2.employee_name as createUserName,
            (
                select
                    count(1)
                from
                    supervise_tele_record str
                where
                    str.supervise_id = si.id
                  and str.is_delete = 0) as tele_num,
            (
                select
                    sum(str.trip_num)
                from
                    supervise_tele_record str
                where
                    str.supervise_id = si.id
                  and str.is_delete = 0) as trip_num
        from
            supervise_infomation si
            left join sys_user su on su.user_id = si.duty_peo ,
            sys_user su2 ,
            brand b
        where
            b.id = si.brand_id
          and su2.user_id = si.create_user_id
          and si.is_delete = 0
          and si.id = #{id}
    </select>
    <select id="querySuperviseReport" resultType="com.bonc.rrs.supervise.dto.SuperviseInfoExport">
        select si.worder_supervise_no,
               sd.supervise_classification_name,
               sd.supervise_type_name,
               case
                   when si.supervise_lv = 1 then '一级'
                   when si.supervise_lv = 2 then '二级'
                   when si.supervise_lv = 3 then '三级'
                   when si.supervise_lv = 4 then '四级'
                   when si.supervise_lv = 5 then '五级'
                   end                                          as supervise_lv_name,
             case
               when si.recover_type = 1 then '过程回复'
               when si.recover_type = 2 then '完结回复'
             end                                          as content_type,
               b.brand_name,
               si.worder_no,
               si.user_name,
               si.user_phone,
               t3.name                                          as province,
               t2.name                                          as city,
               t1.name                                          as county,
               st.detail_name                                   as service_type_name,
              (  SELECT  group_concat(CONCAT(create_user_name,'回复内容:',recover_content,recover_content,' 回复时间:',create_time) separator ';') from supervise_recover_record
              where tele_id in (
               SELECT id from supervise_tele_record where supervise_id = si.id
               )  and is_delete =0
               )                        as recoverContent,
               (select count(1)
                from supervise_tele_record str
                where str.supervise_id = si.id
                  and str.is_delete = 0)                        as tele_num,
               (select sum(str.trip_num)
                from supervise_tele_record str
                where str.supervise_id = si.id
                  and str.is_delete = 0)                        as trip_num,
               su.employee_name                                 as duty_peo_name,
               (select group_concat(ifNUll(su2.employee_name, su2.username))
                from sys_user su2
                where find_in_set(su2.user_id, si.related_peo)) as relatedPeoName,
               st3.detail_name as trip_type_name,
               si.process_time,
               si.create_time,
               si.close_time,
               si.recover_time,
               si.next_connection_time,
               si.create_time,
               IFNULL(su3.employee_name, su3.username)          as createUserName,
               st2.detail_name                                  as stateName,
               si.supervise_content,
               si.close_remark,
               si.lv_up_reason,
                case
                    when si.stimulate  = null then ''
                    when si.stimulate  = 0 then '否'
                    when si.stimulate  = 1 then '是'
                end                                             as stimulate,
        candidate_branch,
        worder_create_time worderCreateTime,
        case
        when TIMESTAMPDIFF(HOUR, si.worder_create_time,si.create_time )   > 12  then '是'
        else '否'
        end  as hours
        from supervise_infomation si
             left join
            (select sdd.*
             from sys_dictionary sd1,
               sys_dictionary_detail sdd
             where sd1.id = sdd.dictionary_id
               and sd1.dic_number = 'supervise_trip_type') as st3
               on st3.detail_number = si.trip_type
             left join sys_user su on su.user_id = si.duty_peo,
             sys_user su3,
             brand b,
             supervise_dictionary sd,
             biz_region t1,
             biz_region t2,
             biz_region t3,
             (select sdd.*
              from sys_dictionary sd1,
                   sys_dictionary_detail sdd
              where sd1.id = sdd.dictionary_id
                and sd1.dic_number = 'service_type') as st,
             (select sdd.*
              from sys_dictionary sd1,
                   sys_dictionary_detail sdd
              where sd1.id = sdd.dictionary_id
                and sd1.dic_number = 'supervise_state') as st2
        where b.id = si.brand_id
          and t1.id = si.area_id
          and t1.pid = t2.id
          and t2.pid = t3.id
          and si.create_user_id = su3.user_id
          and st.detail_number = si.service_type
          and st2.detail_number = si.state
          and si.supervise_classification = sd.supervise_classification
          and si.supervise_type = sd.supervise_type
          and si.is_delete = 0
        <if test="param.worderSuperviseNo != null and param.worderSuperviseNo != ''">
            and si.worder_supervise_no = #{param.worderSuperviseNo}
        </if>
        <if test="param.state != null">
            and si.state = #{param.state}
        </if>
        <if test="param.brandId != null">
            and si.brand_id = #{param.brandId}
        </if>
        <if test="param.recoverType != null">
            and si.recover_type = #{param.recoverType}
        </if>
        <if test="param.worderNo != null and param.worderNo != ''">
            and si.worder_no = #{param.worderNo}
        </if>
        <if test="param.superviseClassification != null">
            and si.supervise_classification = #{param.superviseClassification}
        </if>
        <if test="param.userPhone != null and param.userPhone != ''">
            and si.user_phone like concat('%',#{param.userPhone},'%')
        </if>
        <if test="param.superviseType != null">
            and si.supervise_type = #{param.superviseType}
        </if>
        <if test="param.superviseLv != null">
            and si.supervise_lv = #{param.superviseLv}
        </if>
        <if test="param.dutyReo != null">
            and si.duty_peo = #{param.dutyReo}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 1 and param.userId != null">
            and si.duty_peo = #{param.userId}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 2 and param.userId != null">
            and si.create_user_id = #{param.userId}
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 3 and param.userId != null">
            and (
            (si.create_user_id = #{param.userId})
            or
            (exists (
            select 1
            from biz_region r,
            (select m.group_id,
            m.child_group_id,
            GROUP_CONCAT(distinct (b.regcode) separator '|')      as regcodes,
            GROUP_CONCAT(distinct (m.brand_id) separator ',')     as brand_ids,
            GROUP_CONCAT(distinct (m.service_type) separator ',') as service_types
            from sys_user s
            left join manager_area_id m on
            s.user_id = m.user_id
            left join biz_region b on
            m.area_id = b.id
            where s.user_id = #{param.userId}
            group by m.group_id, m.child_group_id) temp
            where r.regcode regexp (temp.regcodes)
            and r.type = 3
            and si.area_id = r.id
            and find_in_set(si.brand_id, temp.brand_ids)
            and (FIND_IN_SET(si.service_type, temp.service_types) OR
            FIND_IN_SET('0', temp.service_types))))
            )
        </if>
        <if test="param.queryRoleType != null and param.queryRoleType == 4 and param.userId != null">
            and si.dot_user_id = #{param.userId}
        </if>
        <if test="param.processTimeStart != null and param.processTimeEnd != null">
            and si.process_time >= #{param.processTimeStart}
            and si.process_time &lt;= #{param.processTimeEnd}
        </if>
        <if test="param.createTimeStart != null and param.createTimeEnd != null">
            and si.create_time >= #{param.createTimeStart}
            and si.create_time &lt;= #{param.createTimeEnd}
        </if>
        order by si.id desc
    </select>
    <select id="selectDotNameByWorder" resultType="java.lang.String">
        select t2.dot_name from worder_information t1 left join dot_information t2 on t1.dot_id = t2.dot_id where t1.worder_no = #{worderNo} limit 1
    </select>
    <select id="selectWorderInfoByWorderNo" resultType="com.bonc.rrs.supervise.dto.WorderInfo">
        select
            wi.worder_id ,
            wi.worder_no ,
            wi.dot_id ,
            wi.candidate_branch ,
            wi.template_id ,
            wi.user_name ,
            wi.user_phone ,
            wt.brand_id ,
            wt.service_type_enum ,
            wi.area_id
        from
            worder_information wi ,
            worder_template wt
        where wi.template_id = wt.id and wi.worder_no = #{worderNo}
    </select>
</mapper>