<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderInformationDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.worder.entity.WorderInformationEntity" id="worderInformationMap">
        <id column="worder_id" property="worderId"/>
        <result column="worder_type_id" property="worderTypeId"/>
        <result column="worder_no" property="worderNo"/>
        <result column="worder_source_type" property="worderSourceType"/>
        <result column="worder_source_id" property="worderSourceId"/>
        <result column="brand_id" property="brandId"/>
        <result column="worder_status" property="worderStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="worder_source_type_value" property="worderSourceTypeValue"/>
        <result column="worder_status_value" property="worderStatusValue"/>
        <result column="charge_standard" property="chargeStandard"/>
        <result column="sell_shop" property="sellShop"/>
        <result column="charge_model" property="chargeModel"/>
        <result column="car_brand" property="carBrand"/>
        <result column="charge_code" property="chargeCode"/>
        <result column="buy_date" property="buyDate"/>
        <result column="car_model" property="carModel"/>
        <result column="car_vin" property="carVin"/>
        <result column="factory_link_man" property="factoryLinkMan"/>
        <result column="charge_cd" property="chargeCd"/>
        <result column="link_man_phone" property="linkManPhone"/>
        <result column="user_name" property="userName"/>
        <result column="user_phone" property="userPhone"/>
        <result column="postcode" property="postcode"/>
        <result column="park_type_value" property="parkTypeValue"/>
        <result column="park_type" property="parkType"/>
        <result column="user_certificate" property="userCertificate"/>
        <result column="electric_type" property="electricType"/>
        <result column="parking_type_value" property="parkingTypeValue"/>
        <result column="parking_type" property="parkingType"/>
        <result column="install_num" property="installNum"/>
        <result column="address" property="address"/>
        <result column="get_date" property="getDate"/>
        <result column="address_dup" property="addressDup"/>
        <result column="electric_type_value" property="electricTypeValue"/>
        <result column="files" property="files"/>
        <result column="candidate" property="candidate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="candidate_pm" property="candidatePm"/>
        <result column="candidate_branch" property="candidateBranch"/>
        <result column="candidate_attendant" property="candidateAttendant"/>
        <result column="worder_exec_status" property="worderExecStatus"/>
        <result column="worder_set_status" property="worderSetStatus"/>
        <result column="worder_Incre_status" property="worderIncreStatus"/>
        <result column="worder_exci_status" property="worderExciStatus"/>
        <result column="worder_exec_status_value" property="worderExecStatusValue"/>
        <result column="worder_set_status_value" property="worderSetStatusValue"/>
        <result column="worder_Incre_status_value" property="worderIncreStatusValue"/>
        <result column="worder_exci_status_value" property="worderExciStatusValue"/>
        <result column="pm_id" property="pmId"/>
        <result column="dot_id" property="dotId"/>
        <result column="service_id" property="serviceId"/>
        <result column="template_id" property="templateId"/>
        <result column="company_balance_fee" property="companyBalanceFee"/>
        <result column="dot_balance_fee" property="dotBalanceFee"/>
        <result column="dot_incre_balance_fee" property="dotIncreBalanceFee"/>
        <result column="dot_incre_discount_amount" property="dotIncreDiscountAmount"/>
        <result column="attendant_balance_fee" property="attendantBalanceFee"/>
        <result column="invoice_id" property="invoiceId"/>
        <result column="worder_publish_time" property="worderPublishTime"/>
        <result column="worder_incre_publish_time" property="worderIncrePublishTime"/>
        <result column="worder_finish_time" property="worderFinishTime"/>
        <result column="cus_apply_power_time" property="cusApplyPowerTime"/>
        <result column="cus_expect_power_time" property="cusExpectPowerTime"/>
        <result column="cus_real_power_time" property="cusRealPowerTime"/>
        <result column="description" property="description"/>
        <result column="charge_reach" property="chargeReach"/>
        <result column="convey_appoint_time" property="conveyAppointTime"/>
        <result column="install_appoint_time" property="installAppointTime"/>
        <result column="convey_sign_out_time" property="conveySignOutTime"/>
        <result column="install_sign_out_time" property="installSignOutTime"/>
        <result column="user_type" property="userType"/>
        <result column="company_id" property="companyId"/>
        <result column="company_order_number" property="companyOrderNumber"/>
        <result column="medal_order_number" property="medalOrderNumber"/>
        <result column="convey_order_number" property="conveyOrderNumber"/>
        <result column="install_order_number" property="installOrderNumber"/>
        <result column="area_id" property="areaId"/>

        <collection property="worderTypeList" ofType="com.bonc.rrs.worder.entity.WorderTypeEntity">
            <id property="id" column="id"/>
            <result property="pid" column="pid"/>
            <result property="pids" column="pids"/>
            <result property="level" column="level"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="createTime" column="create_time"/>
        </collection>

        <collection property="worderExtFieldList" ofType="com.bonc.rrs.worder.entity.WorderExtFieldEntity">
            <id  property="id" column="eid" />
            <result property="worderNo" column="eworderno"/>
            <result property="fieldId" column="field_id"/>
            <result property="fieldName" column="field_name"/>
            <result property="fieldValue" column="field_value"/>
            <result property="createTime" column="ecreate"/>
            <result property="fieldValueDup" column="field_value_dup"/>
        </collection>

    </resultMap>

    <resultMap type="com.bonc.rrs.worder.entity.WorderInfoEntity" id="informationMap">
        <id column="worder_id" property="worderId"/>
        <result column="worder_type_id" property="worderTypeId"/>
        <result column="full_name" property="worderTypeName"/>
        <result column="worder_no" property="worderNo"/>
        <result column="worder_source_type" property="worderSourceType"/>
        <result column="worder_source_id" property="worderSourceId"/>
        <result column="worder_status" property="worderStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="worder_source_type_value" property="worderSourceTypeValue"/>
        <result column="worder_status_value" property="worderStatusValue"/>
        <result column="charge_standard" property="chargeStandard"/>
        <result column="sell_shop" property="sellShop"/>
        <result column="charge_model" property="chargeModel"/>
        <result column="car_brand" property="carBrand"/>
        <result column="charge_code" property="chargeCode"/>
        <result column="buy_date" property="buyDate"/>
        <result column="car_model" property="carModel"/>
        <result column="car_vin" property="carVin"/>
        <result column="factory_link_man" property="factoryLinkMan"/>
        <result column="charge_cd" property="chargeCd"/>
        <result column="link_man_phone" property="linkManPhone"/>
        <result column="user_name" property="userName"/>
        <result column="user_phone" property="userPhone"/>
        <result column="postcode" property="postcode"/>
        <result column="park_type_value" property="parkTypeValue"/>
        <result column="park_type" property="parkType"/>
        <result column="user_certificate" property="userCertificate"/>
        <result column="electric_type" property="electricType"/>
        <result column="parking_type_value" property="parkingTypeValue"/>
        <result column="parking_type" property="parkingType"/>
        <result column="install_num" property="installNum"/>
        <result column="address" property="address"/>
        <result column="get_date" property="getDate"/>
        <result column="address_dup" property="addressDup"/>
        <result column="electric_type_value" property="electricTypeValue"/>
        <result column="files" property="files"/>
        <result column="candidate" property="candidate"/>
        <result column="creator" property="creator"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="candidate_pm" property="candidatePm"/>
        <result column="candidate_branch" property="candidateBranch"/>
        <result column="candidate_attendant" property="candidateAttendant"/>
        <result column="worder_exec_status" property="worderExecStatus"/>
        <result column="worder_set_status" property="worderSetStatus"/>
        <result column="worder_Incre_status" property="worderIncreStatus"/>
        <result column="worder_exci_status" property="worderExciStatus"/>
        <result column="worder_exec_status_value" property="worderExecStatusValue"/>
        <result column="worder_set_status_value" property="worderSetStatusValue"/>
        <result column="worder_Incre_status_value" property="worderIncreStatusValue"/>
        <result column="worder_exci_status_value" property="worderExciStatusValue"/>
        <result column="pm_id" property="pmId"/>
        <result column="dot_id" property="dotId"/>
        <result column="service_id" property="serviceId"/>
        <result column="template_id" property="templateId"/>
        <result column="company_balance_fee" property="companyBalanceFee"/>
        <result column="dot_balance_fee" property="dotBalanceFee"/>
        <result column="dot_incre_balance_fee" property="dotIncreBalanceFee"/>
        <result column="dot_incre_balance_fee_sum" property="dotIncreBalanceFeeSum"/>
        <result column="dot_incre_discount_amount" property="dotIncreDiscountAmount"/>
        <result column="attendant_balance_fee" property="attendantBalanceFee"/>
        <result column="invoice_id" property="invoiceId"/>
        <result column="worder_publish_time" property="worderPublishTime"/>
        <result column="worder_incre_publish_time" property="worderIncrePublishTime"/>
        <result column="worder_finish_time" property="worderFinishTime"/>
        <result column="cus_apply_power_time" property="cusApplyPowerTime"/>
        <result column="cus_expect_power_time" property="cusExpectPowerTime"/>
        <result column="cus_real_power_time" property="cusRealPowerTime"/>
        <result column="description" property="description"/>
        <result column="charge_reach" property="chargeReach"/>
        <result column="convey_appoint_time" property="conveyAppointTime"/>
        <result column="install_appoint_time" property="installAppointTime"/>
        <result column="convey_sign_out_time" property="conveySignOutTime"/>
        <result column="install_sign_out_time" property="installSignOutTime"/>
        <result column="user_type" property="userType"/>
        <result column="company_id" property="companyId"/>
        <result column="company_order_number" property="companyOrderNumber"/>
        <result column="medal_order_number" property="medalOrderNumber"/>
        <result column="convey_order_number" property="conveyOrderNumber"/>
        <result column="install_order_number" property="installOrderNumber"/>
        <result column="user_balance_fee" property="userBalanceFee"/>
        <result column="user_balance_fee_tax" property="userBalanceFeeTax"/>
        <result column="user_balance_fee_sum" property="userBalanceFeeSum"/>
        <result column="user_actual_cost" property="userActualCost"/>
        <result column="colorLabel" property="colorLabel"/>
        <result column="app_invoice_view_url" property="receiptUrl"/>
        <result column="ticket_status" property="ticketStatus"/>
        <result column="first_call_time" property="firstCallTime"/>
        <result column="cus_apply_power_time" property="cusApplyPowerTime"/>
        <result column="cus_expect_power_time" property="cusExpectPowerTime"/>
        <result column="cus_real_power_time" property="cusRealPowerTime"/>
        <result column="next_contact_time" property="nextContactTime"/>
        <result column="auto_send" property="autoSend"/>
        <result column="service_type_enum" property="serviceTypeEnum"/>
        <result column="fix_submit" property="fixSubmit"/>
        <result column="auto_send" property="autoSend"/>
        <result column="contacts_phone" property="contactsPhone"/>
        <result column="brand_id" property="brandId"/>
        <result column="worder_level" property="worderLevel"/>
        <collection property="worderTypeList" ofType="com.bonc.rrs.worder.entity.WorderTypeEntity">
            <id property="id" column="id"/>
            <result property="pid" column="pid"/>
            <result property="pids" column="pids"/>
            <result property="level" column="level"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="createTime" column="create_time"/>
        </collection>

        <collection property="dotInformationEntity" ofType="com.bonc.rrs.worder.entity.DotInformationEntity">
            <id property="dotId" column="dot_id"/>
            <result property="dotNo" column="dot_code"/>
            <result property="dotName" column="dot_name"/>
            <result property="dotState" column="dot_state"/>
            <result property="dotArea" column="dot_area"/>
            <result property="dotAddress" column="dot_address"/>
            <result property="dotCertificate" column="dot_certificate"/>
            <result property="createTime" column="create_time"/>
            <result property="isDelete" column="is_delete"/>
            <result property="count" column="count"/>
            <result property="vCode" column="v_code"/>
        </collection>



    </resultMap>

    <select id="getCountByCompanyOrderNumberAndBrand" resultType="java.lang.Integer">
        select count(1) from worder_information a
        left join worder_template b on a.template_id = b.id
        where a.is_delete = 0 and a.worder_status != 6 and a.worder_exec_status != 21
        and a.company_order_number = #{companyOrderNumber} and b.brand_id = #{brandId}
    </select>

        <select id="getWorderInformation" resultMap="informationMap">
        select i.*,b.brand_name,b.id as brand_id,c.full_name, t.auto_send, t.service_type_enum, fixSubmitWia.attribute_value fix_submit
        from worder_information i
        join worder_template t on i.template_id = t.id
        join brand b on t.brand_id = b.id
        left join worder_type c on c.id = i.worder_type_id
        <if test="p.companyId != null and p.companyId !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
        where i.is_delete = 0
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.worderSourceType != null and p.worderSourceType != ''" >
            and i.worder_source_type = #{p.worderSourceType}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name = #{p.userName}
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and p.candidatePm != ''">
            and i.candidate_pm like concat(#{p.candidatePm},'%')
        </if>
        <if test="p.carBrand != null and p.carBrand != ''">
            and b.brand_name = #{p.carBrand}
        </if>
        <if test="p.carBrands != null and p.carBrands.size() > 0">
            and b.brand_name in
            <foreach collection="p.carBrands" item="carBrand" open="(" separator="," close=")">
                #{carBrand}
            </foreach>
        </if>
        <if test="p.createBy != null and  p.createBy !=''">
            and i.create_by = #{p.createBy}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderExecStatuses != null and  p.worderExecStatuses !=''">
            and i.worder_exec_status in ${p.worderExecStatuses}
        </if>
        <if test="p.startTime != null and p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
            <if test="p.areas != null and p.areas.size()>0">
                And area_id in (
                select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size()>0">
                And area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
         <if test="p.userId != null">
             and exists (select 1 from biz_region r ,
                (select
                     m.group_id ,
                     m.child_group_id ,
                     GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                     GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                     GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                 from
                    sys_user s
                 left join manager_area_id m on
                    s.user_id = m.user_id
                 left join biz_region b on
                    m.area_id = b.id
                 where
                    s.user_id = #{p.userId}
                 group by m.group_id,m.child_group_id
                ) temp
             where r.regcode regexp (temp.regcodes)
                and r.type = 3
                and i.area_id = r.id
                and find_in_set(t.brand_id, temp.brand_ids)
                and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
             )
         </if>
        order by create_time desc
    </select>


    <select id="getOrderAllList" resultMap="informationMap">
        select i.*,c.full_name
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        <if test="p.submitDocTimeS != null and  p.submitDocTimeS !='' and p.submitDocTimeE != null and  p.submitDocTimeE !=''">
            inner join worder_operation_record wor on i.worder_no = wor.worder_no
            <if test="p.submitDocTimeS != null and  p.submitDocTimeS !=''">
                and wor.create_time <![CDATA[>=]]> #{p.submitDocTimeS}
            </if>
            <if test="p.submitDocTimeE != null and  p.submitDocTimeE !=''">
                and wor.create_time <![CDATA[<=]]> #{p.submitDocTimeE}
            </if>
            and wor.worder_exec_status = 13
            and wor.create_time =(
            select
            max(wor2.create_time)
            from
            worder_operation_record wor2
            where
            wor2.worder_no = wor.worder_no
            <if test="p.submitDocTimeS != null and  p.submitDocTimeS !=''">
                and wor.create_time <![CDATA[>=]]> #{p.submitDocTimeS}
            </if>
            <if test="p.submitDocTimeE != null and  p.submitDocTimeE !=''">
                and wor.create_time <![CDATA[<=]]> #{p.submitDocTimeE}
            </if>
            and wor2.worder_exec_status = 13 )
        </if>
        <if test="p.company != null and p.company !=''">
          join worder_ext_field w on i.worder_no = w.worder_no
          join ext_field e on w.field_id = e.field_id
        </if>
<!--        <if test="p.areaFlag == 1">-->
<!--            inner join (-->
<!--                select  r.id-->
<!--                from biz_region r where regcode REGEXP (-->
<!--                    select GROUP_CONCAT(DISTINCT(b.regcode) SEPARATOR '|')-->
<!--                    from sys_user s-->
<!--                    left join manager_area_id m on s.user_id = m.user_id-->
<!--                    left join biz_region b on m.area_id = b.id-->
<!--                    where s.user_id = #{p.userId}-->
<!--                ) and r.type = 3-->
<!--            ) r on i.area_id= r.id-->
<!--        </if>-->
        <if test="p.brandFlag == 1">
            inner join (
                select DISTINCT(m.brand_id)
                from manager_area_id m
                left join sys_user s on m.user_id = s.user_id
                left join biz_region b on m.area_id = b.id
                where s.user_id = #{p.userId}
                <if test="p.brandId !=null and p.brandId != ''">
                    AND m.brand_id = #{p.brandId}
                </if>
                <if test="p.brandIds != null and p.brandIds.size() > 0">
                    and m.brand_id in
                    <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                        #{brandId}
                    </foreach>
                </if>
            ) b on t.brand_id=b.brand_id
        </if>
        where i.is_delete = 0
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.areas != null and p.areas.size()>0">
            And area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size()>0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.createBy != null">
            and i.create_by = #{p.createBy}
        </if>
        <if test="p.pmId != null">
            and i.pm_id = #{p.pmId}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <!-- 不要转单的 -->
        <if test="p.transfer != null and p.transfer == 0">
            and i.dot_id != 825
        </if>
        <if test="p.serviceId != null">
            and i.service_id = #{p.serviceId}
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
<!--        <if test="p.finishStartTime != null and  p.finishStartTime !=''">-->
<!--            and i.worder_finish_time <![CDATA[>=]]> #{p.finishStartTime}-->
<!--        </if>-->
<!--        <if test="p.finishEndTime != null and  p.finishEndTime !=''">-->
<!--            and i.worder_finish_time <![CDATA[<=]]> #{p.finishEndTime}-->
<!--        </if>-->
        <if test="p.confirmCompletionTimeS != null and  p.confirmCompletionTimeS !=''">
            and i.confirm_completion_time <![CDATA[>=]]> #{p.confirmCompletionTimeS}
        </if>
        <if test="p.confirmCompletionTimeE != null and  p.confirmCompletionTimeE !=''">
            and i.confirm_completion_time <![CDATA[<=]]> #{p.confirmCompletionTimeE}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.nextContactTimeS != null and  p.nextContactTimeS !=''">
            and i.next_contact_time <![CDATA[>=]]> #{p.nextContactTimeS}
        </if>
        <if test="p.nextContactTimeE != null and  p.nextContactTimeE !=''">
            and i.next_contact_time <![CDATA[<=]]> #{p.nextContactTimeE}
        </if>
        <if test="p.areaFlag == 1">
            and exists (select 1 from biz_region r ,
                (select
                    m.group_id ,
                    m.child_group_id ,
                    GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                    GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                    GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                from
                    sys_user s
                left join manager_area_id m on
                    s.user_id = m.user_id
                left join biz_region b on
                    m.area_id = b.id
                where
                    s.user_id = #{p.userId}
                group by m.group_id,m.child_group_id
                    ) temp
                where r.regcode regexp (temp.regcodes)
                and r.type = 3
                and i.area_id = r.id
                and find_in_set(t.brand_id, temp.brand_ids)
                and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="companyCancelAuditQueryAll" resultMap="informationMap">
        select i.*,c.full_name
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        inner join (select worder_id from worder_information_attribute where attribute_code = 'CPIMCancelOrder' and is_delete = 0) wia on wia.worder_id = i.worder_id
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        <if test="p.company != null and p.company !=''">
          join worder_ext_field w on i.worder_no = w.worder_no
          join ext_field e on w.field_id = e.field_id
        </if>
        <if test="p.brandFlag == 1">
            inner join (
                select DISTINCT(m.brand_id)
                from manager_area_id m
                left join sys_user s on m.user_id = s.user_id
                left join biz_region b on m.area_id = b.id
                where s.user_id = #{p.userId}
                <if test="p.brandId !=null and p.brandId != ''">
                    AND m.brand_id = #{p.brandId}
                </if>
            ) b on t.brand_id=b.brand_id
        </if>
        where i.is_delete = 0
        and i.worder_status in (0 , 1, 2)
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.areas != null and p.areas.size()>0">
            And area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size()>0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.createBy != null">
            and i.create_by = #{p.createBy}
        </if>
        <if test="p.pmId != null">
            and i.pm_id = #{p.pmId}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.serviceId != null">
            and i.service_id = #{p.serviceId}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.areaFlag == 1">
            and exists (select 1 from biz_region r ,
                (select
                    m.group_id ,
                    m.child_group_id ,
                    GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                    GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                    GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                from
                    sys_user s
                left join manager_area_id m on
                    s.user_id = m.user_id
                left join biz_region b on
                    m.area_id = b.id
                where
                    s.user_id = #{p.userId}
                group by m.group_id,m.child_group_id
                    ) temp
                where r.regcode regexp (temp.regcodes)
                and r.type = 3
                and i.area_id = r.id
                and find_in_set(t.brand_id, temp.brand_ids)
                and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="companyReviewFailedQueryAll" resultMap="informationMap">
        select i.*,c.full_name
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        <if test="p.brandFlag == 1">
            inner join (
            select DISTINCT(m.brand_id)
            from manager_area_id m
            left join sys_user s on m.user_id = s.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{p.userId}
            <if test="p.brandId !=null and p.brandId != ''">
                AND m.brand_id = #{p.brandId}
            </if>
            ) b on t.brand_id=b.brand_id
        </if>
        where i.is_delete = 0
        and i.worder_exec_status = 23
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.areas != null and p.areas.size()>0">
            And area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size()>0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.createBy != null">
            and i.create_by = #{p.createBy}
        </if>
        <if test="p.pmId != null">
            and i.pm_id = #{p.pmId}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.serviceId != null">
            and i.service_id = #{p.serviceId}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.areaFlag == 1">
            and exists (select 1 from biz_region r ,
            (select
            m.group_id ,
            m.child_group_id ,
            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
            from
            sys_user s
            left join manager_area_id m on
            s.user_id = m.user_id
            left join biz_region b on
            m.area_id = b.id
            where
            s.user_id = #{p.userId}
            group by m.group_id,m.child_group_id
            ) temp
            where r.regcode regexp (temp.regcodes)
            and r.type = 3
            and i.area_id = r.id
            and find_in_set(t.brand_id, temp.brand_ids)
            and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="queryWorderOvertimeList" resultMap="informationMap">
        select i.*,c.full_name
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join biz_attendant ba on i.service_id = ba.id
        where i.is_delete = 0
        and i.worder_status <![CDATA[<]]> 3

        <choose>
            <when test="p.queryType != null and p.queryType == 'fp_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                        (`wor`.`type` = 1 or `wor`.`type` = 2)
                        and `wor`.`affected_user_id` is not null
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                        `wor`.`type` = 3
                        and `wor`.`affected_user_id` is not null
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and  TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                        `wor`.affected_user_id is not null
                        and `wor`.`type` = 3
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>

            <when test="p.queryType != null and p.queryType == 'kc_mr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and
                (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                        `wor`.`type` = 2
                        and `wor`.`affected_user_id` is not null
                        and `wor`.`worder_no` = i.worder_no
                ) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> now()
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_mr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_hr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time between date_format(current_date + 2,'%Y-%m-%d 00:00:00') and date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                (
                    (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                    or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                    or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    (
                        (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                        or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                        or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                    )
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> now()
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_mr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_hr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                        `wor`.`worder_status` = 2
                        and `wor`.`worder_exec_status` = 11
                        and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                    select max(`war`.`gmt_create`) from worder_audit_result `war`
                    where
                        `war`.`worder_status` = 2
                        and `war`.`worder_audit_status` = 21
                        and `war`.`worder_no` = i.worder_no
                ) ,now()) <![CDATA[>]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                (
                    (`wor`.`worder_status` = 2
                    and `wor`.`worder_exec_status` = 13)
                    or (`wor`.`worder_status` = 2
                    and `wor`.`worder_exec_status` = 15)
                )
                        and  `wor`.`worder_no` = i.worder_no
                    ) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and
                (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                (
                    (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13)
                    or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)
                )
                    and `wor`.`worder_no` = i.worder_no)
                        between date_format(current_date,'%Y-%m-%d 00:00:00')
                        and date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <otherwise></otherwise>
        </choose>

        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.confirmCompletionTimeS != null and  p.confirmCompletionTimeS !=''">
            and i.confirm_completion_time <![CDATA[>=]]> #{p.confirmCompletionTimeS}
        </if>
        <if test="p.confirmCompletionTimeE != null and  p.confirmCompletionTimeE !=''">
            and i.confirm_completion_time <![CDATA[<=]]> #{p.confirmCompletionTimeE}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.nextContactTimeS != null and  p.nextContactTimeS !=''">
            and i.next_contact_time <![CDATA[>=]]> #{p.nextContactTimeS}
        </if>
        <if test="p.nextContactTimeE != null and  p.nextContactTimeE !=''">
            and i.next_contact_time <![CDATA[<=]]> #{p.nextContactTimeE}
        </if>

        <if test="p.createBy != null and p.createBy != ''">
            and i.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null and p.pmId != ''">
            and i.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>

        <if test="p.serviceId != null and p.serviceId != ''">
            and ba.user_id = #{p.serviceId}
        </if>

        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and t.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>

        <if test="p.areas != null and p.areas.size() > 0">
            and i.area_id in (
            select b.id from biz_region a
            , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and i.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="p.userId != null">
            and exists (
            select
                1
            from
                biz_region r ,
                (
                    select
                        m.group_id ,
                        m.child_group_id ,
                        GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                        GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                        GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                    from
                        sys_user s
                    left join manager_area_id m on
                        s.user_id = m.user_id
                    left join biz_region b on
                        m.area_id = b.id
                    where
                        s.user_id = #{p.userId}
                    group by m.group_id,
                        m.child_group_id
                ) temp
            where
                r.regcode regexp (temp.regcodes)
                and r.type = 3
                and i.area_id = r.id
                and find_in_set(t.brand_id, temp.brand_ids)
                and (FIND_IN_SET(t.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="exportWorderOvertimeList" resultType="com.bonc.rrs.worder.dto.vo.ExportWorderOvertimeVo">
        select
            br3.regione,
            br3.name as 'province_name',
            b2.brand_name,
            di.v_code,
            i.worder_no,
            pm_su.employee_name as 'pm_name',
            create_su.employee_name as 'create_by_name',
            ba.name as 'service_name',
            c.name as 'worder_type_name'
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join biz_attendant ba on i.service_id = ba.id
        left join brand b2 on t.brand_id = b2.id
        left join biz_region br on i.area_id = br.id
        left join biz_region br2 on br.pid = br2.id
        left join biz_region br3 on br2.pid = br3.id
        left join dot_information di on i.dot_id = di.dot_id
        left join sys_user pm_su on i.pm_id = pm_su.user_id
        left join sys_user create_su on i.create_by = create_su.user_id
        where i.is_delete = 0
        and i.worder_status <![CDATA[<]]> 3

        <choose>
            <when test="p.queryType != null and p.queryType == 'fp_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                (`wor`.`type` = 1 or `wor`.`type` = 2)
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.`type` = 3
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and  TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.affected_user_id is not null
                and `wor`.`type` = 3
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>

            <when test="p.queryType != null and p.queryType == 'kc_mr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`type` = 2
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> now()
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_mr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_hr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time between date_format(current_date + 2,'%Y-%m-%d 00:00:00') and date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> now()
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_mr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_hr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 11
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`war`.`gmt_create`) from worder_audit_result `war`
                where
                `war`.`worder_status` = 2
                and `war`.`worder_audit_status` = 21
                and `war`.`worder_no` = i.worder_no
                ) ,now()) <![CDATA[>]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 15)
                )
                and  `wor`.`worder_no` = i.worder_no
                ) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)
                )
                and `wor`.`worder_no` = i.worder_no)
                between date_format(current_date,'%Y-%m-%d 00:00:00')
                and date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <otherwise></otherwise>
        </choose>

        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.confirmCompletionTimeS != null and  p.confirmCompletionTimeS !=''">
            and i.confirm_completion_time <![CDATA[>=]]> #{p.confirmCompletionTimeS}
        </if>
        <if test="p.confirmCompletionTimeE != null and  p.confirmCompletionTimeE !=''">
            and i.confirm_completion_time <![CDATA[<=]]> #{p.confirmCompletionTimeE}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.nextContactTimeS != null and  p.nextContactTimeS !=''">
            and i.next_contact_time <![CDATA[>=]]> #{p.nextContactTimeS}
        </if>
        <if test="p.nextContactTimeE != null and  p.nextContactTimeE !=''">
            and i.next_contact_time <![CDATA[<=]]> #{p.nextContactTimeE}
        </if>

        <if test="p.createBy != null and p.createBy != ''">
            and i.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null and p.pmId != ''">
            and i.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>

        <if test="p.serviceId != null and p.serviceId != ''">
            and ba.user_id = #{p.serviceId}
        </if>

        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and t.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>

        <if test="p.areas != null and p.areas.size() > 0">
            and i.area_id in (
            select b.id from biz_region a
            , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and i.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="p.userId != null">
            and exists (
            select
            1
            from
            biz_region r ,
            (
            select
            m.group_id ,
            m.child_group_id ,
            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
            from
            sys_user s
            left join manager_area_id m on
            s.user_id = m.user_id
            left join biz_region b on
            m.area_id = b.id
            where
            s.user_id = #{p.userId}
            group by m.group_id,
            m.child_group_id
            ) temp
            where
            r.regcode regexp (temp.regcodes)
            and r.type = 3
            and i.area_id = r.id
            and find_in_set(t.brand_id, temp.brand_ids)
            and (FIND_IN_SET(t.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by i.create_time desc
    </select>

    <select id="queryWorderOvertimeListNoLimit" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select i.*, dc.contacts_phone, t.brand_id
        from worder_information i
        left join worder_template t on i.template_id = t.id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        inner join dot_information di on i.dot_id = di.dot_id
        inner join dot_contacts dc on dc.dot_code = di.dot_code
        where i.is_delete = 0
        and i.worder_status <![CDATA[<]]> 3

        <choose>
            <when test="p.queryType != null and p.queryType == 'fp_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                and i.worder_exec_status = 0
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, i.create_time, now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                (`wor`.`type` = 1 or `wor`.`type` = 2)
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                and i.worder_exec_status = 18
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.`type` = 3
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                and (i.worder_exec_status = 1 or i.worder_exec_status = 19)
                and i.worder_status = 0
                and i.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and i.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and  TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.affected_user_id is not null
                and `wor`.`type` = 3
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 1
            </when>

            <when test="p.queryType != null and p.queryType == 'kc_mr_dkcyy'">
                and i.worder_exec_status = 2
                and i.worder_status = 1
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`type` = 2
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = i.worder_no
                ) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> now()
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_mr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_hr_dkc'">
                and i.worder_exec_status = 3
                and i.worder_status = 1
                and i.convey_appoint_time between date_format(current_date + 2,'%Y-%m-%d 00:00:00') and date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                and i.worder_exec_status = 9
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                and i.worder_exec_status = 10
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> now()
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_mr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_hr_daz'">
                and i.worder_exec_status = 11
                and i.worder_status = 2
                and i.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00')
                and i.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                and i.worder_exec_status = 4
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                and i.worder_exec_status = 5
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                and i.worder_exec_status = 6
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                and i.worder_exec_status = 7
                and i.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 11
                and `wor`.`worder_no` = i.worder_no
                ), now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                and i.worder_exec_status = 12
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = i.worder_no), now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                and i.worder_exec_status = 13
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`war`.`gmt_create`) from worder_audit_result `war`
                where
                `war`.`worder_status` = 2
                and `war`.`worder_audit_status` = 21
                and `war`.`worder_no` = i.worder_no
                ) ,now()) <![CDATA[>]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                and i.worder_exec_status = 14
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = i.worder_no) ,now()) <![CDATA[<=]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 15)
                )
                and  `wor`.`worder_no` = i.worder_no
                ) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                and i.worder_exec_status = 15
                and i.worder_status = 2
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)
                )
                and `wor`.`worder_no` = i.worder_no)
                between date_format(current_date,'%Y-%m-%d 00:00:00')
                and date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwwdsccq'">
                and i.worder_exec_status = 16
                and i.worder_status = 2
            </when>
            <otherwise></otherwise>
        </choose>

        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.confirmCompletionTimeS != null and  p.confirmCompletionTimeS !=''">
            and i.confirm_completion_time <![CDATA[>=]]> #{p.confirmCompletionTimeS}
        </if>
        <if test="p.confirmCompletionTimeE != null and  p.confirmCompletionTimeE !=''">
            and i.confirm_completion_time <![CDATA[<=]]> #{p.confirmCompletionTimeE}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.nextContactTimeS != null and  p.nextContactTimeS !=''">
            and i.next_contact_time <![CDATA[>=]]> #{p.nextContactTimeS}
        </if>
        <if test="p.nextContactTimeE != null and  p.nextContactTimeE !=''">
            and i.next_contact_time <![CDATA[<=]]> #{p.nextContactTimeE}
        </if>

        <if test="p.createBy != null and p.createBy != ''">
            and i.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null and p.pmId != ''">
            and i.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>

        <if test="p.areas != null and p.areas.size() > 0">
            and i.area_id in (
            select b.id from biz_region a
            , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and i.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="p.userId != null">
            and exists (
            select
            1
            from
            biz_region r ,
            (
            select
            m.group_id ,
            m.child_group_id ,
            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
            from
            sys_user s
            left join manager_area_id m on
            s.user_id = m.user_id
            left join biz_region b on
            m.area_id = b.id
            where
            s.user_id = #{p.userId}
            group by m.group_id,
            m.child_group_id
            ) temp
            where
            r.regcode regexp (temp.regcodes)
            and r.type = 3
            and i.area_id = r.id
            and find_in_set(t.brand_id, temp.brand_ids)
            and (FIND_IN_SET(t.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="getOrderThirdList" resultMap="informationMap">
        select i.*,c.full_name ,d.v_code
        from worder_information i
        <if test="p.areaFlag == 1">
            force index(worder_information_area_id_IDX)
        </if>
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template t on i.template_id = t.id
        left join dot_information d on i.dot_id = d.dot_id
        <if test="p.company != null and p.company !=''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
<!--        <if test="p.areaFlag == 1">-->
<!--            inner join (-->
<!--            select  r.id-->
<!--            from biz_region r where regcode REGEXP (-->
<!--            select GROUP_CONCAT(DISTINCT(b.regcode) SEPARATOR '|')-->
<!--            from sys_user s-->
<!--            left join manager_area_id m on s.user_id = m.user_id-->
<!--            left join biz_region b on m.area_id = b.id-->
<!--            where s.user_id = #{p.userId}-->
<!--            ) and r.type = 3-->
<!--            ) r on i.area_id= r.id-->
<!--        </if>-->
        <if test="p.brandFlag == 1">
            inner join (
            select DISTINCT(m.brand_id)
            from manager_area_id m
            left join sys_user s on m.user_id = s.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{p.userId}
            <if test="p.brandId !=null and p.brandId != ''">
                AND m.brand_id = #{p.brandId}
            </if>
            ) b on t.brand_id=b. brand_id
        </if>
        where i.is_delete = 0
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo},'%')
        </if>
        <if test="p.areas != null and p.areas.size()>0">
            And area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size()>0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and c.id  = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.startTime != null and  p.startTime !=''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and  p.endTime !=''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and  p.conveyStartTime !=''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and  p.conveyEndTime !=''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and  p.installStartTime !=''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and  p.installEndTime !=''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.finishStartTime != null and  p.finishStartTime !=''">
            and i.worder_finish_time <![CDATA[>=]]> #{p.finishStartTime}
        </if>
        <if test="p.finishEndTime != null and  p.finishEndTime !=''">
            and i.worder_finish_time <![CDATA[<=]]> #{p.finishEndTime}
        </if>
        <if test="p.worderExecStatus != null and  p.worderExecStatus !=''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderSetStatus != null and  p.worderSetStatus !=''">
            and i.worder_set_status = #{p.worderSetStatus}
        </if>
        <if test="p.worderIncreStatus != null and  p.worderIncreStatus !=''">
            and i.worder_Incre_status = #{p.worderIncreStatus}
        </if>
        <if test="p.worderExciStatus != null and  p.worderExciStatus !=''">
            and i.worder_exci_status = #{p.worderExciStatus}
        </if>
        <if test="p.companyOrderNumber != null and  p.companyOrderNumber !=''">
            and i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.candidatePm != null and  p.candidatePm !=''">
            and i.candidate_pm = #{p.candidatePm}
        </if>
        <if test="p.companyId != null and  p.companyId !=''">
            and e.field_desc = 'companyId' and w.field_value = #{p.companyId}
        </if>
        <if test="p.fzx != null and  p.fzx !=''">
            and e.field_desc = 'fzx' and w.field_value = #{p.fzx}
        </if>
        <if test="p.jxs != null and  p.jxs !=''">
            and e.field_desc = 'jxs' and w.field_value = #{p.jxs}
        </if>
        <if test="p.areaFlag == 1">
            and exists (select 1 from biz_region r ,
            (select
                m.group_id ,
                m.child_group_id ,
                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
            from
                sys_user s
            left join manager_area_id m on
                s.user_id = m.user_id
            left join biz_region b on
                m.area_id = b.id
            where
                s.user_id = #{p.userId}
                group by m.group_id,m.child_group_id
            ) temp
                where r.regcode regexp (temp.regcodes)
                and r.type = 3
                and i.area_id = r.id
                and find_in_set(t.brand_id, temp.brand_ids)
                and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
            )
        </if>
        order by create_time desc
    </select>

    <select id="getByWorderId" parameterType="java.lang.Integer" resultMap="informationMap">
        select i.*,c.full_name, wt.auto_send, wt.brand_id ,fixSubmitWia.attribute_value fix_submit
        from worder_information i
        left join worder_type c on c.id = i.worder_type_id
        left join worder_template wt on i.template_id = wt.id
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
            where i.worder_id = #{worderId}
    </select>
    
    <select id="getColorLabelCount" parameterType="java.util.Map" resultType="java.lang.Integer" >
        select count(1) count
        from worder_information i
        left join worder_type c on c.id = i.worder_type_id
        where i.is_delete = 0
        and i.create_by = #{currUserId}
        and i.worder_exec_status in ${worderStatusIds}
        and i.worder_status = #{worderStatus}
    </select>

    <select id="getListService" resultMap="informationMap">
        select i.*,
               b.brand_name,
               b.id as brand_id,
               c.full_name,
               t.auto_send,
               t.service_type_enum,
               case
                   when i.worder_exec_status in (2, 3, 4, 6, 9, 10, 11, 12, 14) then 1
                   else 0
                   end as colorLabel,
               case
                   when i.worder_exec_status in (2, 3, 4, 6, 9, 10, 11, 12, 14) then concat('1', i.create_time)
                   else concat('0', i.create_time)
                   end as ord,
               fixSubmitWia.attribute_value fix_submit
        from worder_information i
            join worder_template t on i.template_id = t.id
            join brand b on t.brand_id = b.id
            left join worder_type c on c.id = i.worder_type_id
            join biz_attendant ba on i.service_id = ba.id
        <if test="p.companyId != null and p.companyId != ''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
        where i.is_delete = 0
          and ba.user_id = #{p.serviceId}
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo}, '%')
        </if>
        <if test="p.worderSourceType != null and p.worderSourceType != ''">
            and i.worder_source_type = #{p.worderSourceType}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and i.worder_type_id = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name = #{p.userName}
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber != ''">
            and i.company_order_number like concat(#{p.companyOrderNumber}, '%')
        </if>
        <if test="p.candidatePm != null and p.candidatePm != ''">
            and i.candidate_pm like concat(#{p.candidatePm}, '%')
        </if>
        <if test="p.carBrand != null and p.carBrand != ''">
            and b.brand_name = #{p.carBrand}
        </if>
        <if test="p.carBrands != null and p.carBrands.size() > 0">
            and b.brand_name in
            <foreach collection="p.carBrands" item="carBrand" open="(" separator="," close=")">
                #{carBrand}
            </foreach>
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus != ''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderExecStatuses != null and p.worderExecStatuses != ''">
            and i.worder_exec_status in ${p.worderExecStatuses}
        </if>
        <if test="p.startTime != null and p.startTime != ''">

            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime != ''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and p.conveyStartTime != ''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and p.conveyEndTime != ''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and p.installStartTime != ''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and p.installEndTime != ''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.companyId != null and p.companyId != ''">
            and e.field_desc = 'companyId'
            and w.field_value = #{p.companyId}
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            And area_id in (
                select b.id from biz_region a
              , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        order by ord desc
    </select>


    <select id="getListCustomer" resultMap="informationMap">
        select i.*,
               c.full_name,
               b.id as brand_id,
               b.brand_name,
               t.auto_send,
               t.service_type_enum,
               case
                   when i.worder_exec_status in (0, 7, 8, 15, 16) then 1
                   else 0
                   end as colorLabel,
               case
                   when i.worder_exec_status in (0, 7, 8, 15, 16) then concat('1', i.create_time)
                   else concat('0', i.create_time)
                   end as ord,
               fixSubmitWia.attribute_value fix_submit
        from worder_information i
            join worder_template t on i.template_id = t.id
            join brand b on t.brand_id = b.id
            left join worder_type c on c.id = i.worder_type_id
        <if test="p.companyId != null and p.companyId != ''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
        where i.is_delete = 0
          and i.create_by = #{p.createBy}
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo}, '%')
        </if>
        <if test="p.worderSourceType != null and p.worderSourceType != ''">
            and i.worder_source_type = #{p.worderSourceType}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and i.worder_type_id = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name = #{p.userName}
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber != ''">
            and i.company_order_number like concat(#{p.companyOrderNumber}, '%')
        </if>
        <if test="p.candidatePm != null and p.candidatePm != ''">
            and i.candidate_pm like concat(#{p.candidatePm}, '%')
        </if>
        <if test="p.carBrand != null and p.carBrand != ''">
            and b.brand_name = #{p.carBrand}
        </if>
        <if test="p.carBrands != null and p.carBrands.size() > 0">
            and b.brand_name in
            <foreach collection="p.carBrands" item="carBrand" open="(" separator="," close=")">
                #{carBrand}
            </foreach>
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus != ''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderExecStatuses != null and p.worderExecStatuses != ''">
            and i.worder_exec_status in ${p.worderExecStatuses}
        </if>
        <if test="p.startTime != null and p.startTime != ''">
            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime != ''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and p.conveyStartTime != ''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and p.conveyEndTime != ''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and p.installStartTime != ''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and p.installEndTime != ''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.companyId != null and p.companyId != ''">
            and e.field_desc = 'companyId'
            and w.field_value = #{p.companyId}
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            And area_id in (
                select b.id from biz_region a
              , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        order by ord desc
    </select>

    <select id="getListPM" resultMap="informationMap">
        select i.*,
               c.full_name,
               b.id as brand_id,
               b.brand_name,
               t.auto_send,
               t.service_type_enum,
               case
                   when i.worder_exec_status in (5, 13, 18) then 1
                   else 0
                   end as colorLabel,
               case
                   when i.worder_exec_status in (5, 13, 18) then concat('1', i.create_time)
                   else concat('0', i.create_time)
                   end as ord,
               fixSubmitWia.attribute_value fix_submit
        from worder_information i
            join worder_template t on i.template_id = t.id
            join brand b on t.brand_id = b.id
            left join worder_type c on c.id = i.worder_type_id
        <if test="p.companyId != null and p.companyId != ''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
        where i.is_delete = 0
          and i.pm_id = #{p.pmId}
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo}, '%')
        </if>
        <if test="p.worderSourceType != null and p.worderSourceType != ''">
            and i.worder_source_type = #{p.worderSourceType}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and i.worder_type_id = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name = #{p.userName}
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber != ''">
            and i.company_order_number like concat(#{p.companyOrderNumber}, '%')
        </if>
        <if test="p.candidatePm != null and p.candidatePm != ''">
            and i.candidate_pm like concat(#{p.candidatePm}, '%')
        </if>
        <if test="p.carBrand != null and p.carBrand != ''">
            and b.brand_name = #{p.carBrand}
        </if>
        <if test="p.carBrands != null and p.carBrands.size() > 0">
            and b.brand_name in
            <foreach collection="p.carBrands" item="carBrand" open="(" separator="," close=")">
                #{carBrand}
            </foreach>
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus != ''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderExecStatuses != null and p.worderExecStatuses != ''">
            and i.worder_exec_status in ${p.worderExecStatuses}
        </if>
        <if test="p.startTime != null and p.startTime != ''">

            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime != ''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and p.conveyStartTime != ''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and p.conveyEndTime != ''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and p.installStartTime != ''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and p.installEndTime != ''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.companyId != null and p.companyId != ''">
            and e.field_desc = 'companyId'
            and w.field_value = #{p.companyId}
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            And area_id in (
                select b.id from biz_region a
              , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        order by ord desc
    </select>
    <select id="getListBranch" resultMap="informationMap">
        select i.*,
               b.id as brand_id,
               b.brand_name,
               c.full_name,
               t.auto_send,
               t.service_type_enum,
               case
                   when i.worder_exec_status in (1, 2, 6, 9, 10, 14) then 1
                   else 0
                   end as colorLabel,
               case
                   when i.worder_exec_status in (1, 2, 6, 9, 10, 14) then concat('1', i.create_time)
                   else concat('0', i.create_time)
                   end as ord,
               fixSubmitWia.attribute_value fix_submit,
               t.service_type_enum
        from worder_information i
            join worder_template t on i.template_id = t.id
            join brand b on t.brand_id = b.id
            left join worder_type c on c.id = i.worder_type_id
        <if test="p.companyId != null and p.companyId != ''">
            join worder_ext_field w on i.worder_no = w.worder_no
            join ext_field e on w.field_id = e.field_id
        </if>
        left join worder_information_attribute fixSubmitWia on i.worder_id = fixSubmitWia.worder_id and fixSubmitWia.attribute_code = 'InstallFixSubmit' and fixSubmitWia.`attribute` = 'FixDocSubmit' and fixSubmitWia.is_delete = 0
        where i.is_delete = 0
          and i.dot_id = #{p.dotId}
        <if test="p.worderNo != null and p.worderNo != ''">
            and i.worder_no like concat(#{p.worderNo}, '%')
        </if>
        <if test="p.worderSourceType != null and p.worderSourceType != ''">
            and i.worder_source_type = #{p.worderSourceType}
        </if>
        <if test="p.worderTypeId != null and p.worderTypeId != ''">
            and i.worder_type_id = #{p.worderTypeId}
        </if>
        <if test="p.worderStatus != null and p.worderStatus != ''">
            and i.worder_status = #{p.worderStatus}
        </if>
        <if test="p.userName != null and p.userName != ''">
            and i.user_name = #{p.userName}
        </if>
        <if test="p.userPhone != null and p.userPhone != ''">
            and i.user_phone = #{p.userPhone}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber != ''">
            and i.company_order_number like concat(#{p.companyOrderNumber}, '%')
        </if>
        <if test="p.candidatePm != null and p.candidatePm != ''">
            and i.candidate_pm like concat(#{p.candidatePm}, '%')
        </if>
        <if test="p.carBrand != null and p.carBrand != ''">
            and b.brand_name = #{p.carBrand}
        </if>
        <if test="p.carBrands != null and p.carBrands.size() > 0">
            and b.brand_name in
            <foreach collection="p.carBrands" item="carBrand" open="(" separator="," close=")">
                #{carBrand}
            </foreach>
        </if>
        <if test="p.worderExecStatus != null and p.worderExecStatus != ''">
            and i.worder_exec_status = #{p.worderExecStatus}
        </if>
        <if test="p.worderExecStatuses != null and p.worderExecStatuses != ''">
            and i.worder_exec_status in ${p.worderExecStatuses}
        </if>
        <if test="p.startTime != null and p.startTime != ''">


            and i.create_time <![CDATA[>=]]> #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime != ''">
            and i.create_time <![CDATA[<=]]> #{p.endTime}
        </if>
        <if test="p.conveyStartTime != null and p.conveyStartTime != ''">
            and i.convey_appoint_time <![CDATA[>=]]> #{p.conveyStartTime}
        </if>
        <if test="p.conveyEndTime != null and p.conveyEndTime != ''">
            and i.convey_appoint_time <![CDATA[<=]]> #{p.conveyEndTime}
        </if>
        <if test="p.installStartTime != null and p.installStartTime != ''">
            and i.install_appoint_time <![CDATA[>=]]> #{p.installStartTime}
        </if>
        <if test="p.installEndTime != null and p.installEndTime != ''">
            and i.install_appoint_time <![CDATA[<=]]> #{p.installEndTime}
        </if>
        <if test="p.dotId != null and p.dotId != ''">
            and i.dot_id = #{p.dotId}
        </if>
        <if test="p.companyId != null and p.companyId != ''">
            and e.field_desc = 'companyId'
            and w.field_value = #{p.companyId}
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            And area_id in (
                select b.id from biz_region a
              , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            And area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        order by ord desc
    </select>

    <select id="getListMateriel" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.dto.vo.WorderMaterielVo">
         select
             w.id,
             t.materiel_type_value,
             m.materiel_brand_value,
             w.num,
             m.materiel_unit_value,
             m.materiel_spec,
             m.materiel_no,
             m.materiel_name,
             m.sku
             from worder_used_materiel w
             left join materiel_information m on w.materiel_id = m.id
             left join materiel_type t on m.materiel_type_id = t.id
             where w.worder_id = #{worderId}
    </select>

    <!--修改工单物料时的回显-->
    <select id="getMaterielInfo" resultType="java.util.Map">
        select w.id,
        m.id as materielId,
        m.materiel_name as materielName,
        m.materiel_brand as brandId,
        m.materiel_brand_value as materielBrandValue,
        m.materiel_spec as materielSpec,
        w.num as materielNum
        from worder_used_materiel w
        left join materiel_information m on w.materiel_id = m.id
        <where>
            <if test="materielId != null and materielId != ''">
                and w.materiel_id = #{materielId}
            </if>
            <if test="worderId != null and worderId != ''">
                and w.worder_id = #{worderId}
            </if>
        </where>
    </select>

    <!--新增工单物料时获取物料名称-->
    <select id="getMateriel" resultType="java.util.Map">
        select id as materielId,materiel_name as materielName
        from materiel_information
    </select>

    <!--根据物料id获取该物料的品牌和规格-->
    <select id="getMaterielBrand" parameterType="java.lang.Integer" resultType="java.util.Map">
        select materiel_brand as materielBrandId, materiel_brand_value as materielBrandValue,materiel_spec as materielSpec
        from materiel_information
        <where>
            <if test="materielId != null and materielId != ''">
                and id = #{materielId}
            </if>
        </where>
    </select>

    <!--根据物料id获取该物料的规格-->
    <select id="getMaterielSpec" resultType="java.lang.String">
        select materiel_spec as materielSpec
        from materiel_information
        <where>
            <if test="materielId != null and materielId != ''">
                and id = #{materielId}
            </if>
        </where>
    </select>

    <select id="getMaterielById" resultType="java.util.Map">
        select
        t.materiel_type_value as materielType,
        t.materiel_child_type as materielChildType,
        m.materiel_name as materielName,
        m.materiel_brand_value as materielBrandValue,
        m.materiel_spec as materielSpec,
        m.materiel_unit_value as materielUnitValue
        from materiel_information m
        left join materiel_type t on m.materiel_type_id = t.id
        <where>
            <if test="materielId != null and materielId != ''">
                and m.id = #{materielId}
            </if>
        </where>
    </select>

    <select id="getExtField" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.ExtFieldEntity">
         select e.*
             from worder_template a
             left join worder_template_field b on a.id = b.template_id
             left join ext_field e on b.field_id = e.field_id
             where a.id = #{templateId} and e.field_purpose = 1
    </select>

    <select id="getBrand" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.po.BrandPo">
         select a.brand_id,b.brand_name
            from worder_template a
            left join brand b on b.id = a.brand_id
            where a.id = #{templateId}
    </select>

    <select id="getBrandByWorderNo" parameterType="java.lang.String"
            resultType="com.bonc.rrs.worder.entity.po.BrandPo">
         select b.brand_id,c.brand_name from worder_information a
         left join worder_template b on a.template_id = b.id
         left join brand c on b.brand_id = c.id
		 where a.worder_no = #{worderNo}
    </select>

    <select id="getCarType" parameterType="java.lang.Integer" resultType="java.lang.String">
         select t.car_type_name
            from worder_template a
            left join worder_template_car c on c.template_id = a.id
            left join car_type t on t.id = c.car_type_id
            where a.id = #{templateId}
    </select>

    <select id="getWorderType" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.WorderTypeEntity">
        select c.*
            from worder_template a
            left join worder_template_worder_type b on b.template_id = a.id
            left join worder_type c on c.id = b.worder_type_id
            where a.id = #{templateId}
    </select>

    <select id="getBizRegion" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.dto.vo.BizRegionVo">
        select
            (select (select p.id from biz_region p where p.id = e.pid)
            from biz_region e where e.id = b.pid) as provinceId,
            (select (select p.name from biz_region p where p.id = e.pid)
            from biz_region e where e.id = b.pid) as provinceName,
            (select c.id from biz_region c where c.id = b.pid) as cityId,
            (select d.name from biz_region d where d.id = b.pid) as cityName,
            b.id as areaId,b.name as areaName
            from worder_template a
            left join worder_template_region r on r.template_id = a.id
            left join biz_region b on b.id = r.region_id
            where a.id = #{templateId}
    </select>

    <select id="getSettleObject" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.dto.vo.SettleObjectVo">
        select settle_way as settleId
        from worder_template
        where id = #{templateId}
    </select>

    <select id="getFeeDetail" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.dto.dto.FeeDetailDto">

    </select>

    <select id="getIncreaseFee" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.dto.dto.IncreaseFeeDto">

    </select>

    <select id="getOperationRecord" parameterType="java.lang.String" resultType="java.lang.String">
        select CONCAT_WS("\t\t",date_format(w.create_time,"%Y/%m/%d %H:%i:%s"),w.record) as operationrecord
        from worder_operation_record w
        where w.worder_no = #{worderNo}
        order by w.create_time desc
    </select>


    <select id="getThirdOperationRecord" parameterType="java.lang.String" resultType="java.lang.String">
     select
     case when w.type ='3' then  CONCAT_WS("\t\t",date_format(w.create_time,"%Y/%m/%d %H:%i:%s"),replace(w.record,affected_user,di.v_code))
          else CONCAT_WS("\t\t",date_format(w.create_time,"%Y/%m/%d %H:%i:%s"),w.record) end as operationrecord
        from worder_operation_record w,
             worder_information wi,
             dot_information di
        where w.worder_no =wi.worder_no and wi.dot_id =di.dot_id and  w.worder_no = #{worderNo}
        order by w.create_time desc
    </select>

    <select id="getDotId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select i.dot_id
        from dot_contacts d
        left join dot_information i on i.dot_code = d.dot_code
        where d.contacts_id = #{userId}
    </select>

    <select id="getDotName" parameterType="java.lang.String" resultType="java.lang.String">
        select i.v_code
        from dot_contacts d
                 left join dot_information i on i.dot_code = d.dot_code
        where d.contacts_name = #{userName}
    </select>

    <select id="getWorderTemplate" resultType="com.bonc.rrs.worder.entity.po.WorderTemplatePo">
        select DISTINCT(t.id) as templateId,t.template_name as templateName
        from worder_template t
        join worder_template_car c on c.template_id = t.id
        left join manager_area_id m on t.brand_id = m.brand_id
        left join sys_user u on m.user_id = u.user_id
        where is_delete = 0
        <if test="p.userId != null and p.userId != ''">
            and u.user_id = #{p.userId}
        </if>
        <if test="p.brandId != null and p.brandId != ''">
            and t.brand_id = #{p.brandId}
        </if>
        <if test="p.carTypeId != null and p.carTypeId != ''">
            and c.car_type_id = #{p.carTypeId}
        </if>
        order by t.id desc

    </select>
    <select id="getWorderTemplateInfos" resultType="com.bonc.rrs.worder.entity.dto.WorderTemplateDto">
        select DISTINCT(t.id) as templateId,t.template_name as templateName
        from worder_template t
        join worder_template_car c on c.template_id = t.id
        left join manager_area_id m on t.brand_id = m.brand_id
        left join sys_user u on m.user_id = u.user_id
        where is_delete = 0
        <if test="p.userId != null and p.userId != ''">
            and u.user_id = #{p.userId}
        </if>
        <if test="p.brandId != null and p.brandId != ''">
            and t.brand_id = #{p.brandId}
        </if>
        <if test="p.carTypeId != null and p.carTypeId != ''">
            and c.car_type_id = #{p.carTypeId}
        </if>
        <if test="p.importType != null and p.importType != ''">
            and t.import_type = #{p.importType}
        </if>

    </select>

    <select id="getByCompanyId" parameterType="java.lang.Integer" resultType="com.bonc.rrs.worder.entity.CompanyInformationEntity">
       select *
           from company_information
           where company_id = #{companyId}
   </select>

    <select id="getFinishNoPay" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT sum(company_balance_fee_sum) FROM worder_information
        WHERE worder_set_status <![CDATA[<]]> 4 AND worder_set_status <![CDATA[>=]]> 1 AND company_id = #{companyId}
    </select>
    <select id="getNoFinishSuitPrice" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        SELECT sum(c.price*1.09) FROM worder_information a, worder_template b, balance_rule_detail c
        WHERE (a.worder_set_status=0 OR a.worder_set_status IS NULL) AND a.template_id = b.id
        AND b.company_balance_rule_id=c.rule_id AND b.suite_id=c.materiel_id AND c.is_suite=1 AND a.company_id = #{companyId}
    </select>

    <select id="getNoPayVendorDate" parameterType="java.lang.Integer" resultType="java.time.LocalDate">
          SELECT MIN(create_time) FROM vendor_invoice WHERE no_receivable_fee=0 and vendor_id= #{companyId}
    </select>

    <select id="listWorderType" resultType="com.bonc.rrs.worder.dto.dto.WorderTypeDto">
        select id,full_name from worder_type
        where level = 2
    </select>


    <update id="updateWorderExtField" parameterType="com.bonc.rrs.worder.entity.WorderExtFieldEntity">

        update worder_ext_field f
        <set>
            f.field_value = #{p.fieldValue}
            <if test="p.fieldValueDup != null and p.fieldValueDup != ''">
                ,f.field_value_dup = #{p.fieldValueDup}
            </if>
        </set>
        where f.field_id = #{p.fieldId} and f.worder_no = #{p.worderNo}
    </update>

    <update id="updateWorderFinishTime" parameterType="java.util.Map">
        update worder_information
        set worder_finish_time = now()
        where worder_id = #{p.worderId}
    </update>
    <update id="updateWorderExecStatus" parameterType="java.util.Map">
        update worder_information
        set worder_exec_status = #{p.worderExecStatus},worder_exec_status_value = #{p.worderExecStatusValue}
        where worder_id = #{p.worderId}
    </update>
    <update id="updateWorderStatus" parameterType="java.util.Map">
        update worder_information
        set worder_status = #{p.worderStatus},worder_status_value = #{p.worderStatusValue}
        where worder_id = #{p.worderId}
    </update>
    <update id="updateWorderSetStatus" parameterType="java.util.Map">
        update worder_information
        set worder_set_status = #{p.worderSetStatus},worder_set_status_value = #{p.worderSetStatusValue}
        where worder_id = #{p.worderId}
    </update>

    <select id="queryCollecDetailByWorderId" resultType="com.bonc.rrs.worder.entity.po.CollecDetailPo">
    select pay_code as payCode,dot_incre_discount_amount as dotIncreDisCountAmount,user_balance_fee_sum as userBalanceFeeSum,user_actual_cost as userActualCost
    from worder_information
    where worder_id=#{worderId}
    </select>

    <update id="updateOpenTicketStatus"   >
        update worder_information set ticket_status = #{ticketStatus} where worder_no = #{worderNo}
    </update>
    <update id="updateMoreVoteCountingStatus">
        update worder_information set ticket_status = #{ticketStatus}
        where worder_no in
        <foreach collection="worderNOs" item="worderNo" open="(" separator="," close=")">
            #{worderNo}
        </foreach>
    </update>
    <update id="updateMoreVoteCounting">
        update worder_information set ticket_status = #{ticketStatus}
        <if test="viewUrl != null and viewUrl != ''">, app_invoice_view_url = #{viewUrl}</if>
        <if test="pdfUnsignedUrl != null and pdfUnsignedUrl != ''">, app_invoice_pdf_url = #{pdfUnsignedUrl}</if>
        where worder_no in
        <foreach collection="worderNOs" item="worderNo" open="(" separator="," close=")">
            #{worderNo}
        </foreach>
    </update>

    <select id="findAreaNameByWorderNO" resultType="com.bonc.rrs.worder.entity.dto.AreaInfoDTO">
        select t1.name areaName,t.worder_no worderNo from worder_information t
        left join biz_region t1 on  t.area_id = t1.id
        where t1.type=#{type}
        and t.worder_no in
        <foreach collection="worderNOs" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <resultMap id="worderInfomationResult" type="com.bonc.rrs.worder.entity.dto.WorderInfoDTO">
        <result column="worder_no"  property="worderNo"></result>
        <result column="user_name"  property="userName"></result>
        <result column="user_phone"  property="userPhone"></result>
        <result column="ticket_status"  property="ticketStatus"></result>
        <result column="template_id"  property="templateId"></result>
        <result column="area_id"  property="areaId"></result>
        <result column="worder_id" property="worderId"></result>
    </resultMap>
    <select id="findWorderInfo" parameterType="java.lang.String" resultMap="worderInfomationResult">
        select t.worder_no,t.user_name,t.user_phone,t.ticket_status,t.template_id,t.area_id,t.worder_id, t.company_order_number
        from worder_information t
            where t.worder_no = #{worderNo}
    </select>

    <select id="getFactoryName" parameterType="java.lang.String" resultType="java.lang.String">
        select f.name from factory_information f where f.code = #{code} and f.type = 1 and f.is_delete = 0 limit 1
    </select>

    <insert id="addWorderInfo" parameterType="com.bonc.rrs.worder.entity.WorderInfoEntity" useGeneratedKeys="true" keyProperty="worderId">
        insert into worder_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="worderTypeId != null">
                worder_type_id,
            </if>
            <if test="worderNo != null">
                worder_no,
            </if>
            <if test="worderSourceId != null">
                worder_source_id,
            </if>
            <if test="worderStatus != null">
                worder_status,
            </if>
            create_time,
            <if test="worderSourceTypeValue != null">
                worder_source_type_value,
            </if>
            <if test="worderStatusValue != null">
                worder_status_value,
            </if>
            <if test="chargeStandard != null">
                charge_standard,
            </if>
            <if test="sellShop != null">
                sell_shop,
            </if>
            <if test="chargeModel != null">
                charge_model,
            </if>
            <if test="chargeCode != null">
                charge_code,
            </if>
            <if test="buyDate != null">
                buy_date,
            </if>
            <if test="carVin != null">
                car_vin,
            </if>
            <if test="factoryLinkMan != null">
                factory_link_man,
            </if>
            <if test="chargeCd != null">
                charge_cd,
            </if>
            <if test="linkManPhone != null">
                link_man_phone,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="userPhone != null">
                user_phone,
            </if>
            <if test="postcode != null">
                postcode,
            </if>
            <if test="parkTypeValue != null">
                park_type_value,
            </if>
            <if test="parkType != null">
                park_type,
            </if>
            <if test="userCertificate != null">
                user_certificate,
            </if>
            <if test="electricType != null">
                electric_type,
            </if>
            <if test="parkingTypeValue != null">
                parking_type_value,
            </if>
            <if test="parkingType != null">
                parking_type,
            </if>
            <if test="installNum != null">
                install_num,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="getDate != null">
                get_date,
            </if>
            <if test="addressDup != null">
                address_dup,
            </if>
            <if test="electricTypeValue != null">
                electric_type_value,
            </if>
            <if test="files != null">
                files,
            </if>
            <if test="candidate != null">
                candidate,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="worderExecStatus != null">
                worder_exec_status,
            </if>
            <if test="worderExecStatusValue != null">
                worder_exec_status_value,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="companyOrderNumber != null">
                company_order_number,
            </if>
            <if test="medalOrderNumber != null">
                medal_order_number,
            </if>
            <if test="conveyOrderNumber != null">
                convey_order_number,
            </if>
            <if test="installOrderNumber != null">
                install_order_number,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="worderTypeId != null">
                #{worderTypeId},
            </if>
            <if test="worderNo != null">
                #{worderNo},
            </if>
            <if test="worderSourceId != null">
                #{worderSourceId},
            </if>
            <if test="worderStatus != null">
                #{worderStatus},
            </if>
            current_timestamp,
            <if test="worderSourceTypeValue != null">
                #{worderSourceTypeValue},
            </if>
            <if test="worderStatusValue != null">
                #{worderStatusValue},
            </if>
            <if test="chargeStandard != null">
                #{chargeStandard},
            </if>
            <if test="sellShop != null">
                #{sellShop},
            </if>
            <if test="chargeModel != null">
                #{chargeModel},
            </if>
            <if test="chargeCode != null">
                #{chargeCode},
            </if>
            <if test="buyDate != null">
                #{buyDate},
            </if>
            <if test="carVin != null">
                #{carVin},
            </if>
            <if test="factoryLinkMan != null">
                #{factoryLinkMan},
            </if>
            <if test="chargeCd != null">
                #{chargeCd},
            </if>
            <if test="linkManPhone != null">
                #{linkManPhone},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="userPhone != null">
                #{userPhone},
            </if>
            <if test="postcode != null">
                #{postcode},
            </if>
            <if test="parkTypeValue != null">
                #{parkTypeValue},
            </if>
            <if test="parkType != null">
                #{parkType},
            </if>
            <if test="userCertificate != null">
                #{userCertificate},
            </if>
            <if test="electricType != null">
                #{electricType},
            </if>
            <if test="parkingTypeValue != null">
                #{parkingTypeValue},
            </if>
            <if test="parkingType != null">
                #{parkingType},
            </if>
            <if test="installNum != null">
                #{installNum},
            </if>
            <if test="address != null">
                #{address},
            </if>
            <if test="getDate != null">
                #{getDate},
            </if>
            <if test="addressDup != null">
                #{addressDup},
            </if>
            <if test="electricTypeValue != null">
                #{electricTypeValue},
            </if>
            <if test="files != null">
                #{files},
            </if>
            <if test="candidate != null">
                #{candidate},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="worderExecStatus != null">
                #{worderExecStatus},
            </if>
            <if test="worderExecStatusValue != null">
                #{worderExecStatusValue},
            </if>
            <if test="templateId != null">
                #{templateId},
            </if>
            <if test="areaId != null">
                #{areaId},
            </if>
            <if test="userType != null">
                #{userType},
            </if>
            <if test="companyId != null">
                #{companyId},
            </if>
            <if test="companyOrderNumber != null">
                #{companyOrderNumber},
            </if>
            <if test="medalOrderNumber != null">
                #{medalOrderNumber},
            </if>
            <if test="conveyOrderNumber != null">
                #{conveyOrderNumber},
            </if>
            <if test="installOrderNumber != null">
                #{installOrderNumber},
            </if>
        </trim>
    </insert>

    <update id="updateWorderInfo" parameterType="com.bonc.rrs.worder.entity.WorderInfoEntity">
        update worder_information
        <set>
            <if test="worderTypeId != null">
                worder_type_id = #{worderTypeId},
            </if>
            <if test="worderSourceId != null">
                worder_source_id = #{worderSourceId},
            </if>
            <if test="worderStatus != null">
                worder_status = #{worderStatus},
            </if>
            modify_time = current_timestamp,
            <if test="worderSourceTypeValue != null">
                worder_source_type_value = #{worderSourceTypeValue},
            </if>
            <if test="worderStatusValue != null">
                worder_status_value = #{worderStatusValue},
            </if>
            <if test="chargeStandard != null">
                charge_standard = #{chargeStandard},
            </if>
            <if test="sellShop != null">
                sell_shop = #{sellShop},
            </if>
            <if test="chargeModel != null">
                charge_model = #{chargeModel},
            </if>
            <if test="chargeCode != null">
                charge_code = #{chargeCode},
            </if>
            <if test="buyDate != null">
                buy_date = #{buyDate},
            </if>
            <if test="carVin != null">
                car_vin = #{carVin},
            </if>
            <if test="factoryLinkMan != null">
                factory_link_man = #{factoryLinkMan},
            </if>
            <if test="chargeCd != null">
                charge_cd = #{chargeCd},
            </if>
            <if test="linkManPhone != null">
                link_man_phone = #{linkManPhone},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="userPhone != null">
                user_phone = #{userPhone},
            </if>
            <if test="postcode != null">
                postcode = #{postcode},
            </if>
            <if test="parkTypeValue != null">
                park_type_value = #{parkTypeValue},
            </if>
            <if test="parkType != null">
                park_type = #{parkType},
            </if>
            <if test="userCertificate != null">
                user_certificate = #{userCertificate},
            </if>
            <if test="electricType != null">
                electric_type = #{electricType},
            </if>
            <if test="parkingTypeValue != null">
                parking_type_value = #{parkingTypeValue},
            </if>
            <if test="parkingType != null">
                parking_type = #{parkingType},
            </if>
            <if test="installNum != null">
                install_num = #{installNum},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="getDate != null">
                get_date = #{getDate},
            </if>
            <if test="addressDup != null">
                address_dup = #{addressDup},
            </if>
            <if test="electricTypeValue != null">
                electric_type_value = #{electricTypeValue},
            </if>
            <if test="files != null">
                files = #{files},
            </if>
            <if test="candidate != null">
                candidate = #{worderNo},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="worderExecStatus != null">
                worder_exec_status = #{worderExecStatus},
            </if>
            <if test="worderExecStatusValue != null">
                worder_exec_status_value = #{worderExecStatusValue},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="areaId != null">
                area_id = #{areaId},
            </if>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="worderLevel != null">
                worder_level = #{worderLevel},
            </if>
            <if test="companyOrderNumber != null">
                company_order_number = #{companyOrderNumber},
            </if>
            <if test="medalOrderNumber != null">
                medal_order_number = #{medalOrderNumber},
            </if>
            <if test="conveyOrderNumber != null">
                convey_order_number = #{conveyOrderNumber},
            </if>
            <if test="installOrderNumber != null">
                install_order_number = #{installOrderNumber},
            </if>
            <if test="worderSetStatus != null">
                worder_set_status = #{worderSetStatus},
            </if>
            <if test="worderSetStatusValue != null and worderSetStatusValue != ''">
                worder_set_status_value = #{worderSetStatusValue},
            </if>
            <if test="worderFinishTime != null">
                worder_finish_time = #{worderFinishTime},
            </if>
            <if test="installAppointTime != null and installAppointTime == 'aaaaaa'">
                install_appoint_time = null,
            </if>
        </set>
        where 1=1
        <if test="worderId != null and worderId != ''">
            and worder_id = #{worderId}
        </if>
        <if test="worderNo != null and worderNo != ''">
            and worder_no = #{worderNo}
        </if>
    </update>
    <select id="findWorderInfoByOrderNo"  resultMap="worderInfomationResult">
        select t.worder_no,t.user_name,t.user_phone,t.ticket_status,t.template_id,t.area_id,t.worder_id
        from worder_information t
        inner join worder_order_log t1 on t.worder_no = t1.worder_no
            where t1.order_no = #{orderNo}
    </select>
    <update id="updateWorderInfoAmount">
        update worder_information set dot_incre_discount_amount = #{discountAmount},user_actual_cost=#{userActualCost} where worder_no = #{worderNo}
    </update>
    <select id="findWorderInfoBySerialNo"  resultMap="worderInfomationResult">
        select t.worder_no,t.user_name,t.user_phone,t.ticket_status,t.template_id,t.area_id,t.worder_id
                from worder_information t
                        inner join worder_billing_order_record t1 on t.worder_no = t1.order_no
                 where t1.serial_no = #{serialNo}
    </select>

    <!--预约服务修改预约时间-->
    <update id="updateAppointTime">
        update worder_information
        <set>
            <if test="p.worderStatus != null and p.worderStatus != ''">
                worder_status = #{p.worderStatus},
            </if>
            <if test="p.worderExecStatus != null and p.worderExecStatus != ''">
                worder_exec_status = #{p.worderExecStatus},
            </if>
            <if test="p.conveyAppointTime != null and p.conveyAppointTime != ''">
                convey_appoint_time = #{p.conveyAppointTime},
            </if>
            <if test="p.installAppointTime != null and p.installAppointTime != ''">
                install_appoint_time = #{p.installAppointTime},
            </if>
            <if test="p.worderLevel != null and p.worderLevel != ''">
                worder_level = #{p.worderLevel},
            </if>
        </set>
        where worder_no = #{p.worderNo}
    </update>

    <update id="updateTimeAppoint">
        update worder_information
        <set>
            <if test="p.conveyAppointTime != null and p.conveyAppointTime != ''">
                convey_appoint_time = #{p.conveyAppointTime},
            </if>
            <if test="p.installAppointTime != null and p.installAppointTime != ''">
                install_appoint_time = #{p.installAppointTime},
            </if>
            <if test="p.worderLevel != null and p.worderLevel != ''">
                worder_level = #{p.worderLevel},
            </if>
        </set>
        where worder_no = #{p.worderNo}
    </update>

    <!--首次电联用户时间-->
    <update id="updateFirstCallTime">
        update worder_information
        set first_call_time = #{p.firstCallTime}
        where worder_no = #{p.worderNo}
    </update>

    <!--电力报桩时间-->
    <update id="updatePowerPile">
        update worder_information
        <set>
            <if test="p.cusApplyPowerTime != null and p.cusApplyPowerTime != ''">
                cus_apply_power_time = #{p.cusApplyPowerTime},
            </if>
            <if test="p.cusExpectPowerTime != null and p.cusExpectPowerTime != ''">
                cus_expect_power_time = #{p.cusExpectPowerTime},
            </if>
            <if test="p.cusRealPowerTime != null and p.cusRealPowerTime != ''">
                cus_real_power_time = #{p.cusRealPowerTime},
            </if>
        </set>
        where worder_no = #{p.worderNo}
    </update>


    <insert id="addWorderPmStimulate" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        insert into worder_pm_stimulate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="worderId != null">
                worder_id,
            </if>
            <if test="dotId != null">
                dot_id,
            </if>
            <if test="stimulateType != null">
                stimulate_type,
            </if>
            <if test="stimulateReason != null">
                stimulate_reason,
            </if>
            <if test="stimulateFee != null">
                stimulate_fee,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="worderId != null">
                #{worderId},
            </if>
            <if test="dotId != null">
                #{dotId},
            </if>
            <if test="stimulateType != null">
                #{stimulateType},
            </if>
            <if test="stimulateReason != null">
                #{stimulateReason},
            </if>
            <if test="stimulateFee != null">
                #{stimulateFee},
            </if>
            current_timestamp
        </trim>
    </insert>

    <insert id="addStimulate" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" useGeneratedKeys="true" keyProperty="id">
        insert into worder_pm_stimulate
        (worder_id,
        dot_id,
        stimulate_type,
        status,
        stimulate_reason,
        stimulate_fee,
        create_time,
        user_id,
        user_name,
        price_type,
        incentive_type
        <if test="taxPoint != null">
        ,tax_point
        </if>
        <if test="balanceFee != null">
        ,balance_fee
        </if>
        <if test="balanceFeeTax != null">
        ,balance_fee_tax
        </if>
        <if test="feeTax != null">
        ,fee_tax
        </if>
        <if test="reasonType != null">
            ,reason_type
        </if>)
        values
        (
        #{worderId},#{dotId},#{stimulateType},10,#{stimulateReason},#{stimulateFee},current_timestamp,#{userId},#{userName},#{priceType},#{incentiveType}
        <if test="taxPoint != null">
            ,#{taxPoint}
        </if>
        <if test="balanceFee != null">
            ,#{balanceFee}
        </if>
        <if test="balanceFeeTax != null">
            ,#{balanceFeeTax}
        </if>
        <if test="feeTax != null">
            ,#{feeTax}
        </if>
        <if test="reasonType != null">
            ,#{reasonType}
        </if>
        )
        <!--<foreach collection="stimulateFeeList" index="index" item="fee" separator=",">
            (
            #{worderId},#{dotId},#{stimulateType},#{stimulateReason},#{fee},current_timestamp
            )
        </foreach>-->
    </insert>

    <update id="updateStimulate">
        update worder_pm_stimulate wps
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="stimulateType != null and stimulateType != ''">
                stimulate_type = #{stimulateType},
            </if>
            <if test="stimulateReason != null and stimulateReason != ''">
                stimulate_reason = #{stimulateReason},
            </if>
            <if test="stimulateFee != null and stimulateFee != ''">
                stimulate_fee = #{stimulateFee},
            </if>
            <if test="priceType != null">
                price_type = #{priceType},
            </if>
            <if test="incentiveType != null ">
                incentive_type = #{incentiveType},
            </if>
            <if test="taxPoint != null and taxPoint != ''">
                tax_point = #{taxPoint},
            </if>
            <if test="balanceFee != null and balanceFee != ''">
                balance_fee = #{balanceFee},
            </if>
            <if test="balanceFeeTax != null and balanceFeeTax != ''">
                balance_fee_tax = #{balanceFeeTax},
            </if>
            <if test="feeTax != null and feeTax != ''">
                fee_tax = #{feeTax},
            </if>
            <if test="reasonType != null and reasonType != ''">
                reason_type = #{reasonType}
            </if>
        </set>
        where wps.id = #{id}
    </update>

    <select id="getStimulateType" resultType="java.util.Map">
        select sdd.detail_number as stimulateType,sdd.detail_name as stimulateTypeName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'stimulate_type'
    </select>

    <select id="getStimulateReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'incentive_reason'
    </select>
    <select id="getStimulateNegativeReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'negative_incentive_reason'
    </select>

    <select id="getStimulateDotReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'incentive_dot_reason'
    </select>

    <select id="getStimulateDotNegativeReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'negative_incentive_dot_reason'
    </select>

    <select id="listStimulateReason" resultType="java.util.Map">
        select sdd.detail_number as stimulateReason,sdd.detail_name as stimulateReasonName from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = #{dicName}
    </select>

    <select id="getStimulateList" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        select
        s.id,
        s.worder_id,
        s.dot_id,
        s.stimulate_type,
        s.status,
        s.price_type,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_id,
        s.user_name,
        s.create_time createTime,
        w.worder_no,
        (case
         when s.stimulate_type = 10 then d.dot_name
         when s.stimulate_type = 11 then '日日顺'
         end
        ) stimulateTypeName,
        incentive_type incentiveType
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join worder_template wt on w.template_id = wt.id
        left join biz_attendant ba on w.service_id = ba.id
        left join dot_information d on s.dot_id = d.dot_id
        <where>
            s.is_delete = 0
            <if test="stimulateType != null and stimulateType !=''">
                and s.stimulate_type = #{stimulateType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="dotId != null and dotId != ''">
                and s.dot_id = #{dotId}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>

            <if test="createBy != null">
                and w.create_by = #{createBy}
            </if>

            <if test="pmId != null">
                and w.pm_id = #{pmId}
            </if>

            <if test="serviceId != null">
                and ba.user_id = #{serviceId}
            </if>

            <if test="queryUserId != null">
                and exists (
                    select
                        1
                    from
                        biz_region r ,
                        (
                            select
                                m.group_id ,
                                m.child_group_id ,
                                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                            from
                                sys_user s
                            left join manager_area_id m on
                                s.user_id = m.user_id
                            left join biz_region b on
                                m.area_id = b.id
                            where
                                s.user_id = #{queryUserId}
                            group by m.group_id,
                                m.child_group_id
                        ) temp
                    where
                        r.regcode regexp (temp.regcodes)
                        and r.type = 3
                        and w.area_id = r.id
                        and find_in_set(wt.brand_id, temp.brand_ids)
                        and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>
        </where>
        order by s.id desc
        limit #{page},#{pageSize}
    </select>

    <select id="getStimulateIdList" resultType="java.lang.Integer">
        select distinct wwa.stimulate_id from worder_wait_account wwa
        where wwa.worder_invoice_type = 1 and deleted = 0
        <foreach collection="ids" item="id" separator="," open=" and wwa.id in (" close=")" >
            #{id}
        </foreach>
    </select>

    <select id="getStimulateListNoLimit" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateExportVo">
        select distinct
        s.id,
        w.worder_no,
        province_br.name provinceName,
        s.stimulate_type,
        (case
        when s.stimulate_type = '10' then '网点'
        when s.stimulate_type = '11' then '日日顺'
        end
        ) stimulateTypeValue,
        (case
        when s.stimulate_type = 10 then d.v_code
        when s.stimulate_type = 11 then '日日顺'
        end
        ) stimulateTypeName,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_name,
        s.create_time createTime,
        (case
        when s.price_type = 1 then '不含税价'
        when s.price_type = 0 then '含税价'
        end
        ) priceType,
        s.status,
        incentive_type incentiveType
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join worder_template wt on w.template_id = wt.id
        left join biz_attendant ba on w.service_id = ba.id
        left join dot_information d on s.dot_id = d.dot_id
        left join biz_region area_br on area_br.id = w.area_id
        left join biz_region city_br on city_br.id = area_br.pid
        left join biz_region province_br on city_br.pid = province_br.id
        <where>
            s.is_delete = 0 and w.is_delete = 0
            <if test="stimulateType != null and stimulateType !=''">
                and s.stimulate_type = #{stimulateType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="dotId != null and dotId != ''">
                and s.dot_id = #{dotId}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>

            <if test="createBy != null">
                and w.create_by = #{createBy}
            </if>

            <if test="pmId != null">
                and w.pm_id = #{pmId}
            </if>

            <if test="serviceId != null">
                and ba.user_id = #{serviceId}
            </if>

            <if test="queryUserId != null">
                and exists (
                select
                1
                from
                biz_region r ,
                (
                select
                m.group_id ,
                m.child_group_id ,
                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                from
                sys_user s
                left join manager_area_id m on
                s.user_id = m.user_id
                left join biz_region b on
                m.area_id = b.id
                where
                s.user_id = #{queryUserId}
                group by m.group_id,
                m.child_group_id
                ) temp
                where
                r.regcode regexp (temp.regcodes)
                and r.type = 3
                and w.area_id = r.id
                and find_in_set(wt.brand_id, temp.brand_ids)
                and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>
        </where>
        order by s.id desc
    </select>
    <select id="getStimulateToIstrator" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        select
        s.id,
        s.worder_id,
        s.dot_id,
        s.stimulate_type,
        s.status,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_id,
        s.user_name,
        s.create_time createTime,
        w.worder_no,
        (case
         when s.stimulate_type = 10 then d.dot_name
         when s.stimulate_type = 11 then '日日顺'
         end
        ) stimulateTypeName,
        incentive_type incentiveType
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join dot_information d on s.dot_id = d.dot_id
        <where>
            s.is_delete = 0 and s.status not in(11,20) and s.stimulate_type = '10'
            <if test="dotId != null and dotId !=''">
                and s.dot_id = #{dotId}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
        </where>
        order by s.id desc
        limit #{page},#{pageSize}
    </select>
    <select id="getStimulateToIstratorNoLimit" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateExportVo">
        select
        s.id,
        w.worder_no,
        province_br.name provinceName,
        s.stimulate_type,
        (case
        when s.stimulate_type = '10' then '网点'
        when s.stimulate_type = '11' then '日日顺'
        end
        ) stimulateTypeValue,
        (case
        when s.stimulate_type = 10 then d.v_code
        when s.stimulate_type = 11 then '日日顺'
        end
        ) stimulateTypeName,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_name,
        s.create_time createTime,
        (case
        when s.price_type = 1 then '不含税价'
        when s.price_type = 0 then '含税价'
        end
        ) priceType,
        s.status,
        incentive_type incentiveType
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join dot_information d on s.dot_id = d.dot_id
        left join biz_region area_br on area_br.id = w.area_id
        left join biz_region city_br on city_br.id = area_br.pid
        left join biz_region province_br on city_br.pid = province_br.id
        <where>
            s.is_delete = 0 and w.is_delete = 0 and s.status not in(11,20) and s.stimulate_type = '10'
            <if test="dotId != null and dotId !=''">
                and s.dot_id = #{dotId}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
        </where>
        order by s.id desc
    </select>
    <select id="getStimulateCountToIstrator" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="java.lang.Integer">
        select
       count(1)
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join dot_information d on s.dot_id = d.dot_id
        <where>
            s.is_delete = 0 and s.status not in(11,20) and s.stimulate_type = '10'
            <if test="dotId != null and dotId !=''">
                and s.dot_id = #{dotId}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
        </where>
    </select>
    <select id="getUserDotId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select i.dot_id from sys_user s
            left join dot_contacts d on s.username=d.contacts_name
            left join dot_information i on d.dot_code=i.dot_code where user_id=#{userId}
    </select>

    <select id="getStimulateCount" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo" resultType="java.lang.Integer">
        select
        count(1)
        from worder_pm_stimulate s
        left join worder_information w on s.worder_id = w.worder_id
        left join dot_information d on s.dot_id = d.dot_id
        left join worder_template wt on w.template_id = wt.id
        left join biz_attendant ba on w.service_id = ba.id
        <where>
            s.is_delete = 0
            <if test="stimulateType != null and stimulateType !=''">
                and s.stimulate_type = #{stimulateType}
            </if>
            <if test="stimulateReasons != null">
                <foreach collection="stimulateReasons" item="reason" separator="," open=" and s.stimulate_reason in (" close=")" >
                    #{reason}
                </foreach>
            </if>
            <if test="incentiveType != null">
                and s.incentive_type = #{incentiveType}
            </if>
            <if test="worderNo != null and worderNo != ''">
                and w.worder_no = #{worderNo}
            </if>
            <if test="startTime != null and startTime !=''">
                and s.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and s.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null and status != ''">
                and s.status = #{status}
            </if>
            <if test="dotId != null and dotId != ''">
                and s.dot_id = #{dotId}
            </if>
            <if test="userName != null and userName != ''">
                and s.user_name = #{userName}
            </if>
            <if test="reasonType != null">
                and s.reason_type = #{reasonType}
            </if>

            <if test="createBy != null">
                and w.create_by = #{createBy}
            </if>

            <if test="pmId != null">
                and w.pm_id = #{pmId}
            </if>

            <if test="serviceId != null">
                and ba.user_id = #{serviceId}
            </if>

            <if test="queryUserId != null">
                and exists (
                    select
                        1
                    from
                        biz_region r ,
                        (
                            select
                                m.group_id ,
                                m.child_group_id ,
                                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                            from
                                sys_user s
                            left join manager_area_id m on
                                s.user_id = m.user_id
                            left join biz_region b on
                                m.area_id = b.id
                            where
                                s.user_id = #{queryUserId}
                            group by m.group_id,
                                m.child_group_id
                        ) temp
                    where
                        r.regcode regexp (temp.regcodes)
                        and r.type = 3
                        and w.area_id = r.id
                        and find_in_set(wt.brand_id, temp.brand_ids)
                        and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>
        </where>
    </select>

    <select id="getStimulateInfo" resultType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        select
         s.id,
        s.worder_id,
        s.dot_id,
        s.stimulate_type,
        s.status,
        s.stimulate_reason,
        s.stimulate_fee,
        s.user_id,
        s.user_name,
        s.price_type,
        DATE_FORMAT(s.create_time,'%Y-%m-%d') createTime,
        (case
         when s.stimulate_type = 10 then d.dot_name
         when s.stimulate_type = 11 then '日日顺'
         end
        ) stimulateTypeName,
        s.tax_point,
        s.balance_fee,
        s.balance_fee_tax,
        s.fee_tax,
        s.incentive_type
         from worder_pm_stimulate s
         left join dot_information d on s.dot_id = d.dot_id
         where s.id = #{stimulateId} and s.is_delete = 0
    </select>

    <select id="queryStimulateByIdAndStatus" resultType="java.lang.Integer">
        select count(*) from worder_pm_stimulate wps
            where wps.id = #{id} and wps.status = #{status}
    </select>

    <select id="queryPublishByBatchNoAndStatus" resultType="java.lang.Integer">
        select count(*) from balance_publish bp
            where bp.batch_no = #{batchNo} and bp.batch_status = #{batchStatus}
    </select>

    <update id="updateStimulateStatus" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        update worder_pm_stimulate set status = #{status} where id = #{id}
    </update>

    <update id="updateStimulateCancel" parameterType="com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo">
        update worder_pm_stimulate set is_delete = 1 where id = #{id}
    </update>



    <insert id="addClientSatisfaction" parameterType="com.bonc.rrs.worder.dto.vo.ClientSatisfactionVo">
        insert into client_satisfaction
        (worder_no,
        service_satisfaction,
        satisfaction_score,
        is_work_cloth,
        is_show_document,
        is_show_agreement,
        is_assist_bind,
        customer_advice)
        values
        (
            #{worderNo},
            #{serviceSatisfaction},
            #{satisfactionScore},
            #{isWorkCloth},
            #{isShowDocument},
            #{isShowAgreement},
            #{isAssistBind},
            #{customerAdvice}
        )
    </insert>

    <!--根据userId获取对应的员工名称-->
    <select id="getUserName" resultType="java.lang.String">
        select b.name as userName
        from sys_user_employee s
        left join biz_employee b on s.employee_id = b.id
        where s.user_id = #{userId}
    </select>

    <select id="getStatusName" resultType="java.lang.String">
        select sdd.detail_name from sys_dictionary_detail sdd
            left join sys_dictionary sd
            on sdd.dictionary_id = sd.id
        where sd.dic_number = 'worder_status'
        and sdd.detail_number = #{worderStatus}
    </select>



    <resultMap id="fieldPoMap" type="com.bonc.rrs.worderapp.entity.po.FieldPo">
        <result property="worderNo" column="worder_no"></result>
        <result property="fieldId" column="field_id"></result>
        <result property="fieldName" column="field_name"></result>
        <result property="filedDicKey" column="filed_dic_key"></result>
        <result property="notNull" column="is_notnull"></result>
        <result property="fieldType" column="field_type"></result>
        <result property="fieldDicKeyMark" column="field_dic_key_mark"></result>
        <result property="necessary" column="is_nessary"></result>
        <result property="selectData" column="select_data"></result>
    </resultMap>

    <select id="listWorderFieldByWorderNo" parameterType="com.bonc.rrs.worderapp.entity.dto.WorderFieldDto"
            resultMap="fieldPoMap">
        select c.field_id, c.field_name, c.field_type ,c.field_desc,c.filed_dic_key, c.is_notnull
        , c.field_dic_key_mark, c.is_nessary, c.select_data, d.field_value fieldValues, a.template_id templateId
        from worder_information a
        left join worder_template_field b on a.template_id = b.template_id
        left join ext_field c on b.field_id = c.field_id
        left join worder_ext_field d on a.worder_no = d.worder_no and c.field_id = d.field_id
        where a.worder_no = #{worderNo}
        and c.field_purpose = 1
        and c.deleted = '0'
        order by b.sort, c.field_id asc
    </select>

    <!--获取工单开关的状态-->
    <select id="getSwitchStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        select status from sys_config where param_key = #{key}
    </select>

    <update id="updateSwitchStatus">
        update sys_config
        set status = #{status}
        where param_key = #{key}
    </update>

    <delete id="deleteQuota">
        delete from customer_guarantee_quota
    </delete>

    <insert id="addQuota">
        insert into customer_guarantee_quota
        (ccus_code,
         ccus_name,
         quota_sum,
         effect_date,
         lapse_date,
         account,
         idue,
         idue61,
         iar,
         cdep_code,
         cjyt_code,
         cre_source,
         type,
         before_march_idue
         )
        values
          <foreach collection="list" index="index" item="quota" separator=",">
              (
                #{quota.ccusCode},
                #{quota.ccusName},
                #{quota.quotaSum},
                #{quota.effectDate},
                #{quota.lapseDate},
                #{quota.account},
                #{quota.idue},
                #{quota.idue61},
                #{quota.iar},
                #{quota.cdepCode},
                #{quota.cjytCode},
                #{quota.creSource},
                #{quota.type},
                #{quota.beforeMarchIdue}
              )
          </foreach>

    </insert>

    <select id="getQuota" resultType="com.bonc.rrs.worder.entity.CustomerGuaranteeQuotaEntity">
        select * from customer_guarantee_quota
        where ccus_code = #{ccusCode} and cdep_code = #{cdepCode}
    </select>
    
    <select id="getCompanyFeeSum" resultType="java.math.BigDecimal">
        select sum(w.company_balance_fee_sum) as companyFeeSum
        from worder_information w
        left join worder_ext_field e on w.worder_no = e.worder_no
        left join ext_field f on e.field_id = f.field_id
        where w.worder_set_status in (0,2,8,9,10,1) and w.is_delete = 0 and f.field_purpose = 1
        and DATE_FORMAT(w.confirm_completion_time,'%Y-%m') <![CDATA[<]]>  DATE_FORMAT(NOW(),'%Y-%m')
        and f.field_desc = #{p.companyType} and e.field_value = #{p.companyId}
    </select>

    <select id="getMailList" resultType="java.lang.String">
        select mailcode from notice_mail where notice_type = #{noticeType}
    </select>

    <select id="getBeforAprilIdue" resultType="java.math.BigDecimal">
        select before_march_idue as beforeMarchIdue from before_april_idue where ccus_code = #{ccusCode} and cdep_code = #{cdepCode}
    </select>

    <select id="getCustormerWorderNum"  resultType="com.bonc.rrs.worder.entity.dto.CustormerWorderNumDto">
        select c.id,c.custormer_id as custormerId,c.worder_num as worderNum, c.brand_id as brandId
        from custormer_worder_num c
        where c.worder_num = (select min(worder_num) from custormer_worder_num <where>
            <if test="brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
        </where>
        )
        <if test="brandId != null and brandId != ''">
            and c.brand_id = #{brandId}
        </if>
    </select>

    <select id="getCustormerWorderNumByArea" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity">
        select
            distinct
            s.*
        from
            sys_user s
                left join manager_area_id m on
                m.user_id = s.user_id
                left join biz_region b on
                m.area_id = b.id
                left join sys_user_role r on
                r.user_id = s.user_id
        where
            b.regcode in ${regcode}
          and m.brand_id = #{brandId}
        <if test="serviceType != null">
            and (m.service_type = #{serviceType} or m.service_type = 0)
        </if>
          and s.status = 1
          and r.role_id = 2
    </select>

    <select id="getNumBycustormerId" resultType="com.bonc.rrs.worder.entity.dto.CustormerWorderNumDto">
        select c.id,c.custormer_id as custormerId,c.worder_num as worderNum, c.brand_id as brandId
        from custormer_worder_num c
        where c.custormer_id = #{custormerId}
        <if test="brandId != null and brandId != ''">
            and c.brand_id = #{brandId}
        </if>
    </select>

    <insert id="insertWorderNum" parameterType="com.bonc.rrs.worder.entity.dto.CustormerWorderNumDto">
        insert into custormer_worder_num (custormer_id,worder_num,create_time,brand_id)
        values(#{custormerId},1,current_timestamp ,#{brandId})
    </insert>

    <update id="updateWorderNum" parameterType="com.bonc.rrs.worder.entity.dto.CustormerWorderNumDto">
        update custormer_worder_num set worder_num = worder_num+1, update_time = current_timestamp
        where custormer_id = #{custormerId} and brand_id = #{brandId}
    </update>

    <select id="getCompanyId" resultType="java.lang.String">
    select company_id from company_brand_car where brand_id = #{brandId}
    </select>

    <select id="getCompanyType" resultType="java.lang.Integer">
        select company_type from company_information where company_id = #{companyId}
    </select>

    <select id="findWoderNoByIncreStatus" resultType="java.lang.String">
        select t.worder_no from worder_information t where  t.worder_Incre_status = #{worderIncreStatus}
    </select>

    <!--查询用户增项费用是否已开票-->
    <select id="getInvoiceStatus" resultType="java.lang.Integer">
        select o.billing_status
        from worder_billing_recode o
        left join worder_billing_order_record r on o.serial_no = r.serial_no
        left join worder_information w on r.order_no = w.worder_no
        where w.worder_id = #{worderId}
    </select>


    <select id="getUserAreaBrand" resultType="com.bonc.rrs.worder.dto.dto.ManagerAreaBrandDto">
       select m.id,m.user_id,m.area_id,m.brand_id
        from manager_area_id m
        left join sys_user s on m.user_id = s.user_id
        where s.user_id = #{userId}
    </select>


    <select id="listNoInvoiceWorderByBrandOrTemplate" resultMap="worderInformationMap">
        select * from worder_wait_account wa
        left join worder_information a on wa.worder_id = a.worder_id
        left join worder_template b on a.template_id = b.id
        where wa.status = 0
        and b.brand_id = #{brandId}
        <if test="templateId != null and templateId != ''">
            and a.template_id = #{templateId}
        </if>
    </select>


    <select id="listWorderByInovice" resultMap="worderInformationMap">
        select a.* from worder_information a
        left join worder_template b on a.template_id = b.id
        left join company_invoice c on a.invoice_id = c.id
        where c.company_invoice_no = #{companyInvoiceNo}
    </select>

    <select id="listInvoiceNoAcsWorderByBrandOrTemplate" resultMap="worderInformationMap">
        select a.* from worder_information a
        left join worder_template b on a.template_id = b.id
        left join company_invoice c on a.invoice_id = c.id
        where c.status = 5
        and b.brand_id = #{brandId}
        <if test="templateId != null and templateId != ''">
            and a.template_id = #{templateId}
        </if>
    </select>
    <update id="updateWorderInformation" >
        update worder_information set worder_Incre_status = 0
        where worder_no = #{worderNo}
    </update>
    <update id="updateWorderInformationIncreStatus" >
        update worder_information set worder_Incre_status = 0,worder_Incre_status_value = '用户已收费'
        where worder_id = #{worderId}
    </update>


    <update id="userTransfer" parameterType="com.bonc.rrs.worder.entity.vo.UserTransferVo" >
        update worder_information w
        <set>
            <if test="userType == 1">
                w.create_by = #{toUserId}
            </if>
            <if test="userType == 2">
                w.pm_id = #{toUserId},
                w.candidate_pm = (select employee_name from sys_user where user_id = #{toUserId} limit 1)
            </if>
        </set>
        <where>
            w.is_delete = 0
            and w.worder_exec_status NOT IN (20,21)
            <if test="userType == 1">
                and w.create_by = #{fromUserId}
            </if>
            <if test="userType == 2">
                and w.pm_id = #{fromUserId}
            </if>
            <foreach collection="regionList" index="idx" item="areaId" open="and (w.area_id in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or w.area_id in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{areaId}
            </foreach>
            <if test="brandId !=null and brandId !=''">
                and w.template_id in (select t.id from worder_template t
                <where>
                     w.template_id = t.id
                    <if test="brandId !=null and brandId !=''">
                    and t.brand_id = #{brandId}
                    </if>
                    <if test="templateId !=null and templateId !=''">
                        and t.id = #{templateId}
                    </if>
                </where>
                )
            </if>
        </where>
    </update>

    <update id="userSuperviseTransfer" parameterType="com.bonc.rrs.worder.entity.vo.UserTransferVo">
         update supervise_infomation si
        <set>
            si.duty_peo = #{toUserId}
        </set>
        <where>
            si.state != 3
            and si.is_delete = 0
            and si.duty_peo = #{fromUserId}
            <if test="brandId !=null and brandId !=''">
                and si.brand_id = #{brandId}
            </if>
            <foreach collection="regionList" index="idx" item="areaId" open="and (si.area_id in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.area_id in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{areaId}
            </foreach>
            <foreach collection="worderNoList" index="idx" item="worderNo" open="and (si.worder_no in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.worder_no in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{worderNo}
            </foreach>
        </where>
    </update>

    <update id="userSuperviseTeleTransfer" parameterType="com.bonc.rrs.worder.entity.vo.UserTransferVo">
        update supervise_infomation si,supervise_tele_record str
        <set>
            str.duty_peo = #{toUserId}
        </set>
        <where>
            str.supervise_id = si.id
            and si.duty_peo = #{fromUserId}
            and si.is_delete = 0
            and si.state != 3
            and str.is_delete = 0
            <if test="brandId !=null and brandId !=''">
                and si.brand_id = #{brandId}
            </if>
            <foreach collection="regionList" index="idx" item="areaId" open="and (si.area_id in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.area_id in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{areaId}
            </foreach>
            <foreach collection="worderNoList" index="idx" item="worderNo" open="and (si.worder_no in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.worder_no in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{worderNo}
            </foreach>
        </where>
    </update>


    <select id="querySuperviseByWorderNo" resultType="java.lang.Integer">
        select count(str.id) from supervise_infomation si
        inner join supervise_tele_record str on str.supervise_id = si.id
        <where>
            si.is_delete = 0
            and si.state != 3
            and str.is_delete = 0
            and str.supervise_type = 3
            and si.worder_no = #{worderNo}
        </where>
    </select>


    <select id="querySuperviseTransfer" resultType="com.bonc.rrs.supervise.entity.SuperviseInfomation">
        select si.* from supervise_infomation si
        <where>
            si.duty_peo = #{fromUserId}
            and si.is_delete = 0
            and si.state != 3
            <if test="brandId !=null and brandId !=''">
                and si.brand_id = #{brandId}
            </if>
            <foreach collection="regionList" index="idx" item="areaId" open="and (si.area_id in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.area_id in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{areaId}
            </foreach>
            <foreach collection="worderNoList" index="idx" item="worderNo" open="and (si.worder_no in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or si.worder_no in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{worderNo}
            </foreach>
        </where>
    </select>

    <select id="queryWorderNoTransfer" resultType="java.lang.String">
        select worder_no from worder_information w
        <where>
            w.is_delete = 0
            and w.worder_exec_status NOT IN (20,21)
            <if test="userType == 1">
                and w.create_by = #{fromUserId}
            </if>
            <if test="userType == 2">
                and w.pm_id = #{fromUserId}
            </if>
            <foreach collection="regionList" index="idx" item="areaId" open="and (w.area_id in (" close="))">
                <if test="idx != 0">
                    <choose>
                        <when test="idx % 900 == 0">
                            ) or w.area_id in (
                        </when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{areaId}
            </foreach>
            <if test="brandId !=null and brandId !=''">
                and w.template_id in (select t.id from worder_template t
                <where>
                    w.template_id = t.id
                    <if test="brandId !=null and brandId !=''">
                        and t.brand_id = #{brandId}
                    </if>
                    <if test="templateId !=null and templateId !=''">
                        and t.id = #{templateId}
                    </if>
                </where>
                )
            </if>
        </where>
    </select>

    <insert id="insertSuperviseOperationRecord">
        INSERT INTO rrs_pro.supervise_operation_record
        (supervise_id, user_id, user_name, operation_type, optration_content, duty_peo, related_peo)
        VALUES(#{superviseId}, #{userId}, #{userName}, 6, #{optrationContent}, #{dutyPeo}, #{relatedPeo});

    </insert>

    <select id="getNoInvoicedWorder" resultMap="worderInformationMap">
        select a.* from worder_information a
        where a.worder_no = #{worderNo}
        and a.invoice_id is null
    </select>
    <select id="getWorderStatusByWorderNo" resultMap="worderInformationMap">
        select wt.brand_id, a.worder_id, a.worder_no, a.worder_status, a.worder_exec_status, a.worder_exec_status, a.worder_set_status, a.worder_Incre_status, a.worder_exci_status from worder_information a
        join worder_template wt on a.template_id = wt.id
        where a.is_delete = 0
        and a.worder_no = #{worderNo}
    </select>
    <select id="getWorderStatusByWorderId" resultMap="worderInformationMap">
        select a.worder_id, a.worder_no, a.worder_status, a.worder_exec_status, a.worder_exec_status, a.worder_set_status, a.worder_Incre_status, a.worder_exci_status from worder_information a
        where a.is_delete = 0
        and a.worder_no = #{worderId}
    </select>

    <select id="queryCompanyOrderNumber" resultMap="worderInformationMap">
        select * from worder_information
        where company_order_number = #{companyOrderNumber}
        and worder_no != #{worderNo}
        and is_delete = 0
        and worder_status != 6
        and worder_exec_status != 21
    </select>

    <select id="getCompanyPriceTypeByTemplateId" resultType="integer" parameterType="integer">
        select
            br.price_type
        from
            worder_template wt
        inner join balance_rule br on
            wt.company_balance_rule_id = br.id
        where
            wt.id = #{templateId}
    </select>
    <select id="selectBrandByTemp" resultType="java.lang.Integer">
        select wt.brand_id from worder_information wi , worder_template wt where wi.template_id = wt.id and wi.worder_id = #{worderId}
    </select>
    <select id="selectBrandsByDot" resultType="java.lang.Integer">
        select db.brand_id from dot_brand db where db.dot_id = #{dotId}
    </select>

    <update id="updateStatusWorder">
        update
            worder_information
        set
            worder_status = 0 , worder_status_value = '分配中'
        where worder_no = #{worderNo}
    </update>

    <select id="getAddedMaterielTypeList" resultType="java.util.Map">
        select
            mt.id,
            mt.materiel_child_type materiel_type
        from
            materiel_type mt
        inner join (
            select
                cast(sdd.detail_number as unsigned INTEGER) as materiel_type_id, cast(sdd.detail_name as unsigned INTEGER) as materiel_id
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'added_material_type_to_material_id') mttomi on
            mt.id = mttomi.materiel_type_id
        where
            materiel_type = 1
    </select>

    <select id="getDotRuleExtFieldList" resultType="com.bonc.rrs.worder.entity.ExtFieldEntity">
        select
            field_id,
            field_name,
            field_desc,
            select_data
        from
            ext_field ef
        where
            ef.field_type = 6
            and deleted = 0
    </select>

    <!--修改工单物料时的回显-->
    <select id="getDotBalanceRuleMaterielInfo" resultType="java.util.Map" parameterType="integer">
        select distinct
            mi.id,
            mi.materiel_name
        from
            materiel_information mi
        inner join balance_rule_detail brd on
            brd.materiel_id = mi.id
        where
            brd.rule_id = #{dotBalanceRuleId}
            and mi.materiel_type_id in (76, 93)
        union all
        select distinct
            mi.id,
            mi.materiel_name
        from
            materiel_information mi
        inner join balance_rule_detail brd on
            brd.materiel_id = mi.id
        where
            brd.rule_id = #{dotBalanceRuleId}
            and mi.materiel_type_id not in (76, 93)
    </select>

    <select id="queryExportCSCockpitData" resultType="com.bonc.rrs.worder.dto.vo.ExportCSCockpitDataVo">
        SELECT * FROM (
            select
                br3.regione as 'regione',
                br3.name as 'privince_name',
                b2.brand_name as 'brand_name',
                di.v_code as 'dot_code',
                wi.worder_no as 'worder_no',
                pm_su.employee_name as 'pm_name',
                create_su.employee_name as 'create_by_name',
                ba.name as 'serviceName',
                wt2.name as 'worder_type_name',
                sum(if(wi.worder_exec_status = 0 and wi.worder_status = 0 and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[>]]> 0.5, 1, 0)) as `fp_cs_wpd`,
                sum(if(wi.worder_exec_status = 18 and wi.worder_status = 0 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 0.5, 1, 0)) as `fp_cs_fwjlwpd`,
                sum(if((wi.worder_exec_status = 1 or wi.worder_exec_status = 19) and wi.worder_status = 0 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `kc_cs_wdyjd`,
                sum(if(wi.worder_exec_status = 2 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `kc_cs_dkcyy`,
                sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1 and wi.convey_appoint_time <![CDATA[<]]> now(), 1, 0)) as `kc_cs_dkc`,
                null as `az_cs_ddcdzjpj`,
                sum(if(wi.worder_exec_status = 10 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.affected_user_id is not null and `wor`.`type` = 4) or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5) or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)) and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `az_cs_dazyy`,
                sum(if(wi.worder_exec_status = 11 and wi.worder_status = 2 and wi.install_appoint_time <![CDATA[<]]> now(), 1, 0)) as `az_cs_daz`,
                sum(if(wi.worder_exec_status = 4 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 6, 1, 0)) as `kczl_cs_kczlwtj`,
                null as `kczl_cs_kczltjdsh`,
                sum(if(wi.worder_exec_status = 6 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 12, 1, 0)) as `kczl_cs_kczlzgz`,
                sum(if(wi.worder_exec_status = 7 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2,1,0)) as `kczl_cs_dkfqr`,
                sum(if(wi.worder_exec_status = 12 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 12, 1, 0)) as `azzl_cs_azzlwtj`,
                null as `azzl_cs_azzlytjdsh`,
                sum(if(wi.worder_exec_status = 14 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 24, 1, 0)) as `azzl_cs_azzlzgz`,
                sum(if(wi.worder_exec_status = 15 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13) or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2,1,0)) as `azzl_cs_azzldkfsh`
            from
                worder_information wi
                left join worder_template wt on wi.template_id = wt.id
                left join biz_attendant ba on wi.service_id = ba.id
                left join brand b2 on wt.brand_id = b2.id
                left join biz_region br on wi.area_id = br.id
                left join biz_region br2 on br.pid = br2.id
                left join biz_region br3 on br2.pid = br3.id
                left join dot_information di on wi.dot_id = di.dot_id
                left join sys_user pm_su on wi.pm_id = pm_su.user_id
                left join sys_user create_su on wi.create_by = create_su.user_id
                left join worder_type wt2 on wi.worder_type_id = wt2.id
            where
                wi.is_delete = 0
                and wi.worder_status <![CDATA[<]]> 3

                <if test="p.createBy != null">
                    and wi.create_by = #{p.createBy}
                </if>

                <if test="p.pmId != null">
                    and wi.pm_id = #{p.pmId}
                </if>

                <if test="p.dotId != null">
                    and wi.dot_id = #{p.dotId}
                </if>

                <if test="p.serviceId != null">
                    and ba.user_id = #{p.serviceId}
                </if>

                <if test="p.brandIds != null and p.brandIds.size() > 0">
                    and wt.brand_id in
                    <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                        #{brandId}
                    </foreach>
                </if>

                <if test="p.areas != null and p.areas.size() > 0">
                    and wi.area_id in (
                        select b.id from biz_region a
                        , biz_region b where b.pid=a.id
                        and a.pid in
                        <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                            #{area}
                        </foreach>
                    )
                </if>
                <if test="p.areaIds != null and p.areaIds.size() > 0">
                    and wi.area_id in
                    <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                </if>

                <if test="p.userId != null">
                    and exists (
                        select
                            1
                        from
                            biz_region r ,
                            (
                                select
                                    m.group_id ,
                                    m.child_group_id ,
                                    GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                                    GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                                    GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                                from
                                    sys_user s
                                    left join manager_area_id m on
                                    s.user_id = m.user_id
                                    left join biz_region b on
                                    m.area_id = b.id
                                where
                                    s.user_id = #{p.userId}
                                    group by m.group_id,
                                    m.child_group_id
                            ) temp
                        where
                            r.regcode regexp (temp.regcodes)
                            and r.type = 3
                            and wi.area_id = r.id
                            and find_in_set(wt.brand_id, temp.brand_ids)
                            and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                    )
                </if>

            group by br3.regione, br3.name, b2.brand_name, di.dot_id, wi.worder_no
        ) a
        WHERE a.fp_cs_wpd != 0 or a.fp_cs_fwjlwpd != 0 or a.kc_cs_wdyjd != 0 or a.kc_cs_dkcyy != 0 or a.kc_cs_dkc != 0 or a.az_cs_ddcdzjpj != 0 or a.az_cs_dazyy != 0 or a.az_cs_daz != 0 or a.kczl_cs_kczlwtj != 0 or a.kczl_cs_kczltjdsh != 0 or a.kczl_cs_kczlzgz != 0 or a.kczl_cs_dkfqr != 0 or a.azzl_cs_azzlwtj != 0 or a.azzl_cs_azzlytjdsh != 0 or a.azzl_cs_azzlzgz != 0 or a.azzl_cs_azzldkfsh != 0
    </select>

    <select id="queryCSCockpitListData" resultType="com.bonc.rrs.worder.entity.vo.CSCockpitVo">
        select cast(sum(rkor.客服派单完成+rkor.未派单) AS char) wpdPdl,
            cast(sum(rkor.未派单) AS char) wpdCspdl,
            cast(sum(rkor.服务经理未派单+rkor.项目经理派单完成) AS char) fwjlwpdPdl,
            cast(sum(rkor.服务经理未派单) AS char) fwjlwpdCspdl,
            cast(sum(rkor.网点派服务兵完成 +rkor.网点已接单) AS char) wdyjdPdl,
            cast(sum(rkor.网点已接单) AS char) wdyjdCspdl,
            cast(sum(rkor.待勘测预约+rkor.已勘测预约) AS char) dkcyyPdl,
            cast(sum(rkor.待勘测预约) AS char) dkcyyCspdl,
            cast(sum(rkor.待勘测+rkor.已勘测) AS char) dkcPdl,
            cast(sum(rkor.待勘测) AS char) dkcCspdl,
            cast(sum(rkor.待安装预约+rkor.已安装预约) AS char) dazyyPdl,
            cast(sum(rkor.待安装预约) AS char) dazyyCspdl,
            cast(sum(rkor.已待安装+rkor.待安装) AS char) dazPdl,
            cast(sum(rkor.待安装) AS char) dazCspdl,
            cast(sum(rkor.勘测资料未提交+rkor.勘测资料已提交) AS char) kczlwtjPdl,
            cast(sum(rkor.勘测资料未提交) AS char) kczlwtjCspdl,
            cast(sum(rkor.安装资料未提交+rkor.安装资料已提交) AS char) azzlwtjPdl,
            cast(sum(rkor.安装资料未提交) AS char) azzlwtjCspdl,
            cast(sum(rkor.安装资料整改已提交+rkor.安装资料整改中) AS char) azzlzgzPdl,
            cast(sum(rkor.安装资料整改中) AS char) azzlzgzCspdl,
            cast(sum(rkor.安装资料待客服审核+rkor.安装资料客服已审核) AS char) azzldkfshPdl,
            cast(sum(rkor.安装资料待客服审核) AS char) azzldkfshCspdl,
            br3.name province
        from rrs_kanban_operate_realdate rkor
            left join biz_region br on rkor.area_id =br.id
            left join biz_region br2 on br2.id=br.pid
            left join biz_region br3 on br3.id=br2.pid
        <where>
            br3.name is not null
            <if test="p.startTime != null and p.startTime !=''">
                and rkor.`cycle` <![CDATA[>]]> #{p.startTime}
            </if>
            <if test="p.endTime != null and p.endTime !=''">
                and rkor.`cycle` <![CDATA[<]]> #{p.endTime}
            </if>
            <if test="p.createBy != null">
                and rkor.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and rkor.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and rkor.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and rkor.service_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and rkor.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>

            <if test="p.areas != null and p.areas.size() > 0">
                and rkor.area_id in (
                    select b.id from biz_region a
                    , biz_region b where b.pid=a.id
                    and a.pid in
                    <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                        #{area}
                    </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and rkor.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>

            <if test="p.userId != null">
                and exists (
                    select
                        1
                    from
                        biz_region r ,
                        (
                            select
                                m.group_id ,
                                m.child_group_id ,
                                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                            from
                                sys_user s
                            left join manager_area_id m on
                                s.user_id = m.user_id
                            left join biz_region b on
                                m.area_id = b.id
                            where
                                s.user_id = #{p.userId}
                                group by m.group_id,
                                m.child_group_id
                        ) temp
                    where
                        r.regcode regexp (temp.regcodes)
                        and r.type = 3
                        and rkor.area_id = r.id
                        and find_in_set(rkor.brand_id, temp.brand_ids)
                        and (FIND_IN_SET(rkor.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>
        </where>
        group by br3.name
    </select>

    <select id="queryCockpitData" resultType="java.util.Map">
        SELECT
            SUM(IF(wi.worder_exec_status = 0 and wi.worder_status = 0,1,0)) AS `fp_wpd`,
            SUM(IF(wi.worder_exec_status = 18 and wi.worder_status = 0,1,0)) AS `fp_fwjlwpd`,
            SUM(IF((wi.worder_exec_status = 1 OR wi.worder_exec_status = 19) and wi.worder_status = 0,1,0)) AS `kc_wdyjd`,
            SUM(IF(wi.worder_exec_status = 2 and wi.worder_status = 1,1,0)) AS `kc_dkcyy`,
            sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1, 1, 0)) AS `kc_dkc`,
            SUM(IF(wi.worder_exec_status = 9 and wi.worder_status = 2,1,0)) AS `az_ddcdzjpj`,
            SUM(IF(wi.worder_exec_status = 10 and wi.worder_status = 2,1,0)) AS `az_dazyy`,
            SUM(IF(wi.worder_exec_status = 11 and wi.worder_status = 2,1,0)) AS `az_daz`,
            SUM(IF(wi.worder_exec_status = 4 and wi.worder_status = 1,1,0)) AS `kczl_kczlwtj`,
            SUM(IF(wi.worder_exec_status = 5 and wi.worder_status = 1,1,0)) AS `kczl_kczltjdsh`,
            SUM(IF(wi.worder_exec_status = 6 and wi.worder_status = 1,1,0)) AS `kczl_kczlzgz`,
            SUM(IF(wi.worder_exec_status = 7 and wi.worder_status = 1,1,0)) AS `kczl_dkfqr`,
            SUM(IF(wi.worder_exec_status = 12 and wi.worder_status = 2,1,0)) AS `azzl_azzlwtj`,
            SUM(IF(wi.worder_exec_status = 13 and wi.worder_status = 2,1,0)) AS `azzl_azzlytjdsh`,
            SUM(IF(wi.worder_exec_status = 14 and wi.worder_status = 2,1,0)) AS `azzl_azzlzgz`,
            SUM(IF(wi.worder_exec_status = 15 and wi.worder_status = 2,1,0)) AS `azzl_azzldkfsh`,
            SUM(IF(wi.worder_exec_status = 16 and wi.worder_status = 2,1,0)) AS `azzl_azzlwwdsccq`
        FROM
            worder_information wi
            left join worder_template wt on wi.template_id = wt.id
            left join biz_attendant ba on wi.service_id = ba.id
        WHERE
            wi.is_delete = 0 AND
            wi.worder_status <![CDATA[<]]> 3
            <if test="p.createBy != null">
                and wi.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and wi.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and wi.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and ba.user_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and wt.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>

            <if test="p.areas != null and p.areas.size() > 0">
                and wi.area_id in (
                    select b.id from biz_region a
                    , biz_region b where b.pid=a.id
                    and a.pid in
                    <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                        #{area}
                    </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and wi.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
            <if test="p.startTime != null and p.startTime !=''">
                and wi.create_time &gt;= #{p.startTime}
            </if>
            <if test="p.endTime != null and p.endTime !=''">
                and wi.create_time &lt;= #{p.endTime}
            </if>
            <if test="p.userId != null">
                and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                        group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and wi.area_id = r.id
                    and find_in_set(wt.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>
    </select>

    <select id="queryCockpitCSData" resultType="java.util.Map">
        select
            sum(if(wi.worder_exec_status = 0 and wi.worder_status = 0 and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[>]]> 0.5, 1, 0)) as `fp_cs_wpd`,
            sum(if(wi.worder_exec_status = 18 and wi.worder_status = 0 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 0.5, 1, 0)) as `fp_cs_fwjlwpd`,
            sum(if((wi.worder_exec_status = 1 or wi.worder_exec_status = 19) and wi.worder_status = 0 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `kc_cs_wdyjd`,
            sum(if(wi.worder_exec_status = 2 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `kc_cs_dkcyy`,
            sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1 and wi.convey_appoint_time <![CDATA[<]]> now(), 1, 0)) as `kc_cs_dkc`,
            null as `az_cs_ddcdzjpj`,
            sum(if(wi.worder_exec_status = 10 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.affected_user_id is not null and `wor`.`type` = 4) or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5) or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)) and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 1 , 1, 0)) as `az_cs_dazyy`,
            sum(if(wi.worder_exec_status = 11 and wi.worder_status = 2 and wi.install_appoint_time <![CDATA[<]]> now(), 1, 0)) as `az_cs_daz`,
            sum(if(wi.worder_exec_status = 4 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 6, 1, 0)) as `kczl_cs_kczlwtj`,
            null as `kczl_cs_kczltjdsh`,
            sum(if(wi.worder_exec_status = 6 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 12, 1, 0)) as `kczl_cs_kczlzgz`,
            sum(if(wi.worder_exec_status = 7 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2,1,0)) as `kczl_cs_dkfqr`,
            sum(if(wi.worder_exec_status = 12 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[>]]> 12, 1, 0)) as `azzl_cs_azzlwtj`,
            null as `azzl_cs_azzlytjdsh`,
            sum(if(wi.worder_exec_status = 14 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 24, 1, 0)) as `azzl_cs_azzlzgz`,
            sum(if(wi.worder_exec_status = 15 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13) or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2,1,0)) as `azzl_cs_azzldkfsh`,
            null as `azzl_cs_azzlwwdsccq`
        from
            worder_information wi
            left join worder_template wt on wi.template_id = wt.id
            left join biz_attendant ba on wi.service_id = ba.id
        where
            wi.is_delete = 0
            and wi.worder_status <![CDATA[<]]> 3
            <if test="p.createBy != null">
                and wi.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and wi.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and wi.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and ba.user_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and wt.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>

            <if test="p.areas != null and p.areas.size() > 0">
                and wi.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and wi.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
        <if test="p.startTime != null and p.startTime !=''">
            and wi.create_time &gt;= #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime !=''">
            and wi.create_time &lt;= #{p.endTime}
        </if>
            <if test="p.userId != null">
                and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                        group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and wi.area_id = r.id
                    and find_in_set(wt.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>

    </select>

    <select id="queryCockpitJRData" resultType="java.util.Map">
        select
            sum(if(wi.worder_exec_status = 0 and wi.worder_status = 0 and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00') and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00') and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[<=]]> 0.5, 1, 0)) as `fp_jr_wpd`,
            sum(if(wi.worder_exec_status = 18 and wi.worder_status = 0 and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00') and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00') and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 0.5, 1, 0)) as `fp_jr_fwjlwpd`,
            sum(if((wi.worder_exec_status = 1 or wi.worder_exec_status = 19) and wi.worder_status = 0 and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00') and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00') and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1, 1, 0)) as `kc_jr_wdyjd`,
            sum(if(wi.worder_exec_status = 2 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1 , 1, 0)) as `kc_jr_dkcyy`,
            sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1 and wi.convey_appoint_time <![CDATA[>=]]> now() and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59'), 1, 0)) as `kc_jr_dkc`,
            sum(if(wi.worder_exec_status = 9 and wi.worder_status = 2, 1, 0)) as `az_jr_ddcdzjpj`,
            sum(if(wi.worder_exec_status = 10 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.affected_user_id is not null and `wor`.`type` = 4) or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5) or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)) and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1 , 1, 0)) as `az_jr_dazyy`,
            sum(if(wi.worder_exec_status = 11 and wi.worder_status = 2 and wi.install_appoint_time <![CDATA[>=]]> now() and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59'), 1, 0)) as `az_jr_daz`,
            sum(if(wi.worder_exec_status = 4 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 6, 1, 0)) as `kczl_jr_kczlwtj`,
            sum(if(wi.worder_exec_status = 5 and wi.worder_status = 1, 1, 0)) as `kczl_jr_kczltjdsh`,
            sum(if(wi.worder_exec_status = 6 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 12, 1, 0)) as `kczl_jr_kczlzgz`,
            sum(if(wi.worder_exec_status = 7 and wi.worder_status = 1 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 2,1,0)) as `kczl_jr_dkfqr`,
            sum(if(wi.worder_exec_status = 12 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 12, 1, 0)) as `azzl_jr_azzlwtj`,
            sum(if(wi.worder_exec_status = 13 and wi.worder_status = 2, 1, 0)) as `azzl_jr_azzlytjdsh`,
            sum(if(wi.worder_exec_status = 14 and wi.worder_status = 2 and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 24, 1, 0)) as `azzl_jr_azzlzgz`,
            sum(if(wi.worder_exec_status = 15 and wi.worder_status = 2 and (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13) or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)) and  `wor`.`worder_no` = wi.worder_no) between date_format(current_date,'%Y-%m-%d 00:00:00') and date_format(current_date,'%Y-%m-%d 23:59:59'),1,0)) as `azzl_jr_azzldkfsh`,
            sum(if(wi.worder_exec_status = 16 and wi.worder_status = 2, 1, 0)) as `azzl_jr_azzlwwdsccq`
        from
            worder_information wi
            left join worder_template wt on wi.template_id = wt.id
            left join biz_attendant ba on wi.service_id = ba.id
        where
            wi.is_delete = 0
            and wi.worder_status <![CDATA[<]]> 3
            <if test="p.createBy != null">
                and wi.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and wi.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and wi.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and ba.user_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and wt.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
        <if test="p.startTime != null and p.startTime !=''">
            and wi.create_time &gt;= #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime !=''">
            and wi.create_time &lt;= #{p.endTime}
        </if>
            <if test="p.areas != null and p.areas.size() > 0">
                and wi.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and wi.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
            <if test="p.userId != null">
                and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                        group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and wi.area_id = r.id
                    and find_in_set(wt.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>

    </select>

    <select id="queryCockpitMRData" resultType="java.util.Map">
        select
            null as `fp_mr_wpd`,
            null as `fp_mr_fwjlwpd`,
            null as `kc_mr_wdyjd`,
            sum(if(wi.worder_exec_status = 2 and wi.worder_status = 1 and (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 2 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00'), 1, 0)) as `kc_mr_dkcyy`,
            sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1 and wi.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00') and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59'), 1, 0)) as `kc_mr_dkc`,
            null as `az_mr_ddcdzjpj`,
            null as `az_mr_dazyy`,
            sum(if(wi.worder_exec_status = 11 and wi.worder_status = 2 and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00') and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59'), 1, 0)) as `az_mr_daz`,
            null as `kczl_mr_kczlwtj`,
            null as `kczl_mr_kczltjdsh`,
            null as `kczl_mr_kczlzgz`,
            null as `kczl_mr_dkfqr`,
            null as `azzl_mr_azzlwtj`,
            null as `azzl_mr_azzlytjdsh`,
            null as `azzl_mr_azzlzgz`,
            null as `azzl_mr_azzldkfsh`,
            null as `azzl_mr_azzlwwdsccq`
        from
            worder_information wi
            left join worder_template wt on wi.template_id = wt.id
            left join biz_attendant ba on wi.service_id = ba.id
        where
            wi.is_delete = 0
            and wi.worder_status <![CDATA[<]]> 3
            <if test="p.createBy != null">
                and wi.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and wi.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and wi.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and ba.user_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and wt.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
        <if test="p.startTime != null and p.startTime !=''">
            and wi.create_time &gt;= #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime !=''">
            and wi.create_time &lt;= #{p.endTime}
        </if>
            <if test="p.areas != null and p.areas.size() > 0">
                and wi.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>
            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and wi.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
            <if test="p.userId != null">
                and exists (
                    select
                        1
                    from
                        biz_region r ,
                        (
                            select
                                m.group_id ,
                                m.child_group_id ,
                                GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                                GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                                GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                            from
                                sys_user s
                            left join manager_area_id m on
                                s.user_id = m.user_id
                            left join biz_region b on
                                m.area_id = b.id
                            where
                                s.user_id = #{p.userId}
                            group by m.group_id,
                                m.child_group_id
                        ) temp
                    where
                        r.regcode regexp (temp.regcodes)
                        and r.type = 3
                        and wi.area_id = r.id
                        and find_in_set(wt.brand_id, temp.brand_ids)
                        and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>

    </select>

    <select id="queryCockpitHRData" resultType="java.util.Map">
        select
            null as `fp_hr_wpd`,
            null as `fp_hr_fwjlwpd`,
            null as `kc_hr_wdyjd`,
            null as `kc_hr_dkcyy`,
            sum(if(wi.worder_exec_status = 3 and wi.worder_status = 1 and wi.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00') and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59'), 1, 0)) as `kc_hr_dkc`,
            null as `az_hr_ddcdzjpj`,
            null as `az_hr_dazyy`,
            sum(if(wi.worder_exec_status = 11 and wi.worder_status = 2 and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00') and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59'), 1, 0)) as `az_hr_daz`,
            null as `kczl_hr_kczlwtj`,
            null as `kczl_hr_kczltjdsh`,
            null as `kczl_hr_kczlzgz`,
            null as `kczl_hr_dkfqr`,
            null as `azzl_hr_azzlwtj`,
            null as `azzl_hr_azzlytjdsh`,
            null as `azzl_hr_azzlzgz`,
            null as `azzl_hr_azzldkfsh`,
            null as `azzl_hr_azzlwwdsccq`
        from
            worder_information wi
            left join worder_template wt on wi.template_id = wt.id
            left join biz_attendant ba on wi.service_id = ba.id
        where
            wi.is_delete = 0
            and wi.worder_status <![CDATA[<]]> 3
        <if test="p.createBy != null">
            and wi.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null">
            and wi.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null">
            and wi.dot_id = #{p.dotId}
        </if>

        <if test="p.serviceId != null">
            and ba.user_id = #{p.serviceId}
        </if>

        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and wt.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="p.startTime != null and p.startTime !=''">
            and wi.create_time &gt;= #{p.startTime}
        </if>
        <if test="p.endTime != null and p.endTime !=''">
            and wi.create_time &lt;= #{p.endTime}
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            and wi.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and wi.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
        <if test="p.userId != null">
            and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                            group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and wi.area_id = r.id
                    and find_in_set(wt.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>

    </select>

    <select id="queryCockpitDataDetail" resultType="com.bonc.rrs.worder.dto.dto.CockpitDataStatisticsDto">
        select
            b2.brand_name as brand_name,
            br.name as province_name,
            count(br.name) as province_num
        from
            worder_information wi
        left join worder_template wt on
            wi.template_id = wt.id
        left join brand b2 on
            wt.brand_id = b2.id
        left join biz_region br on
            substring_index(wi.address, '_', 1) = br.id
        left join biz_attendant ba on
            wi.service_id = ba.id
        WHERE
            wi.is_delete = 0 AND
            wi.worder_status <![CDATA[<]]> 3

            <choose>
                <when test="p.queryType != null and p.queryType == 'fp_wpd'">
                    and wi.worder_exec_status = 0
                    and wi.worder_status = 0
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                    and wi.worder_exec_status = 0
                    and wi.worder_status = 0
                    and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[>]]> 0.5
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                    and wi.worder_exec_status = 0
                    and wi.worder_status = 0
                    and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                    and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                    and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[<=]]> 0.5
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_fwjlwpd'">
                    and wi.worder_exec_status = 18
                    and wi.worder_status = 0
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                    and wi.worder_exec_status = 18
                    and wi.worder_status = 0
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                    (`wor`.`type` = 1 or `wor`.`type` = 2)
                    and `wor`.`affected_user_id` is not null
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[>]]> 0.5
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                    and wi.worder_exec_status = 18
                    and wi.worder_status = 0
                    and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                    and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 0.5
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_wdyjd'">
                    and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                    and wi.worder_status = 0
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                    and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                    and wi.worder_status = 0
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                    `wor`.`type` = 3
                    and `wor`.`affected_user_id` is not null
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[>]]> 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                    and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                    and wi.worder_status = 0
                    and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                    and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_dkcyy'">
                    and wi.worder_exec_status = 2
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                    and wi.worder_exec_status = 2
                    and wi.worder_status = 1
                    and  TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`) from worder_operation_record `wor`
                    where
                    `wor`.affected_user_id is not null
                    and `wor`.`type` = 3
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[>]]> 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                    and wi.worder_exec_status = 2
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1
                </when>

                <when test="p.queryType != null and p.queryType == 'kc_mr_dkcyy'">
                    and wi.worder_exec_status = 2
                    and wi.worder_status = 1
                    and
                    (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    `wor`.`type` = 2
                    and `wor`.`affected_user_id` is not null
                    and `wor`.`worder_no` = wi.worder_no
                    ) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00')
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_dkc'">
                    and wi.worder_exec_status = 3
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                    and wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[<]]> now()
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                    and wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[>=]]> now()
                    and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_mr_dkc'">
                    and wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                    and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_hr_dkc'">
                    and wi.worder_exec_status = 3
                    and wi.worder_status = 1
                    and wi.convey_appoint_time between date_format(current_date + 2,'%Y-%m-%d 00:00:00') and date_format(current_date + 2,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'az_ddcdzjpj'">
                    and wi.worder_exec_status = 9
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                    and wi.worder_exec_status = 9
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'az_dazyy'">
                    and wi.worder_exec_status = 10
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                    and wi.worder_exec_status = 10
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    (
                    (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                    or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                    or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                    )
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[>]]> 1
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                    and wi.worder_exec_status = 10
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    (
                    (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                    or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                    or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                    )
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[<=]]> 1
                </when>
                <when test="p.queryType != null and p.queryType == 'az_daz'">
                    and wi.worder_exec_status = 11
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                    and wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[<]]> now()
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                    and wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[>=]]> now()
                    and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'az_mr_daz'">
                    and wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                    and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'az_hr_daz'">
                    and wi.worder_exec_status = 11
                    and wi.worder_status = 2
                    and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00')
                    and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_kczlwtj'">
                    and wi.worder_exec_status = 4
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                    and wi.worder_exec_status = 4
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 6
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                    and wi.worder_exec_status = 4
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 6
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_kczltjdsh'">
                    and wi.worder_exec_status = 5
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                    and wi.worder_exec_status = 5
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_kczlzgz'">
                    and wi.worder_exec_status = 6
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                    and wi.worder_exec_status = 6
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 12
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                    and wi.worder_exec_status = 6
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 12
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_dkfqr'">
                    and wi.worder_exec_status = 7
                    and wi.worder_status = 1
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                    and wi.worder_exec_status = 7
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                    and wi.worder_exec_status = 7
                    and wi.worder_status = 1
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_azzlwtj'">
                    and wi.worder_exec_status = 12
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                    and wi.worder_exec_status = 12
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    `wor`.`worder_status` = 2
                    and `wor`.`worder_exec_status` = 11
                    and `wor`.`worder_no` = wi.worder_no
                    ), now()) <![CDATA[>]]> 12
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                    and wi.worder_exec_status = 12
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 12
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_azzlytjdsh'">
                    and wi.worder_exec_status = 13
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                    and wi.worder_exec_status = 13
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_azzlzgz'">
                    and wi.worder_exec_status = 14
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                    and wi.worder_exec_status = 14
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (
                    select max(`war`.`gmt_create`) from worder_audit_result `war`
                    where
                    `war`.`worder_status` = 2
                    and `war`.`worder_audit_status` = 21
                    and `war`.`worder_no` = wi.worder_no
                    ) ,now()) <![CDATA[>]]> 24
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                    and wi.worder_exec_status = 14
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 24
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_azzldkfsh'">
                    and wi.worder_exec_status = 15
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                    and wi.worder_exec_status = 15
                    and wi.worder_status = 2
                    and TIMESTAMPDIFF(hour, (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    (
                    (`wor`.`worder_status` = 2
                    and `wor`.`worder_exec_status` = 13)
                    or (`wor`.`worder_status` = 2
                    and `wor`.`worder_exec_status` = 15)
                    )
                    and  `wor`.`worder_no` = wi.worder_no
                    ) ,now()) <![CDATA[>]]> 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                    and wi.worder_exec_status = 15
                    and wi.worder_status = 2
                    and
                    (
                    select max(`wor`.`create_time`)
                    from worder_operation_record `wor`
                    where
                    (
                    (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13)
                    or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)
                    )
                    and `wor`.`worder_no` = wi.worder_no)
                    between date_format(current_date,'%Y-%m-%d 00:00:00')
                    and date_format(current_date,'%Y-%m-%d 23:59:59')
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_azzlwwdsccq'">
                    and wi.worder_exec_status = 16
                    and wi.worder_status = 2
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwwdsccq'">
                    and wi.worder_exec_status = 16
                    and wi.worder_status = 2
                </when>
                <otherwise></otherwise>
            </choose>

            <if test="p.createBy != null">
                and wi.create_by = #{p.createBy}
            </if>

            <if test="p.pmId != null">
                and wi.pm_id = #{p.pmId}
            </if>

            <if test="p.dotId != null">
                and wi.dot_id = #{p.dotId}
            </if>

            <if test="p.serviceId != null">
                and ba.user_id = #{p.serviceId}
            </if>

            <if test="p.brandIds != null and p.brandIds.size() > 0">
                and wt.brand_id in
                <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and wt.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
            <if test="p.areas != null and p.areas.size() > 0">
                and wi.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>

            <if test="p.areaIds != null and p.areaIds.size() > 0">
                and wi.area_id in
                <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                    #{areaId}
                </foreach>
            </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and wi.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
             <if test="p.startTime != null and p.startTime !=''">
               and wi.create_time &gt;= #{p.startTime}
             </if>
            <if test="p.endTime != null and p.endTime !=''">
              and wi.create_time &lt;= #{p.endTime}
            </if>
            <if test="p.userId != null">
                and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                        group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and wi.area_id = r.id
                    and find_in_set(wt.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
                )
            </if>

        group by brand_name, br.name
    </select>

    <select id="queryCockpitDataDetail2" resultType="com.bonc.rrs.worder.dto.dto.CockpitDataStatistics2Dto">
        select
            b2.brand_name as brand_name,
            di.dot_name  as dot_name,
            di.v_code as v_code,
            count(di.dot_name) as dot_num
        from
            worder_information wi
        left join worder_template wt on
            wi.template_id = wt.id
        left join brand b2 on
            wt.brand_id = b2.id
        left join biz_region br on
            substring_index(wi.address, '_', 1) = br.id
        left join dot_information di on
            wi.dot_id = di.dot_id
        left join biz_attendant ba on
            wi.service_id = ba.id
        WHERE
            wi.is_delete = 0 AND
            wi.worder_status <![CDATA[<]]> 3

        <if test="p.provinceName != null and p.provinceName != ''">
            and br.name = #{p.provinceName}
        </if>

        <choose>
            <when test="p.queryType != null and p.queryType == 'fp_wpd'">
                and wi.worder_exec_status = 0
                and wi.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                and wi.worder_exec_status = 0
                and wi.worder_status = 0
                and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                and wi.worder_exec_status = 0
                and wi.worder_status = 0
                and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, wi.create_time, now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_fwjlwpd'">
                and wi.worder_exec_status = 18
                and wi.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                and wi.worder_exec_status = 18
                and wi.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                (`wor`.`type` = 1 or `wor`.`type` = 2)
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[>]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                and wi.worder_exec_status = 18
                and wi.worder_status = 0
                and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`type` = 1 or `wor`.`type` = 2) and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 0.5
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_wdyjd'">
                and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                and wi.worder_status = 0
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                and wi.worder_status = 0
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.`type` = 3
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                and (wi.worder_exec_status = 1 or wi.worder_exec_status = 19)
                and wi.worder_status = 0
                and wi.create_time <![CDATA[>]]> date_format(current_date, '%Y-%m-%d 09:00:00')
                and wi.create_time <![CDATA[<]]> date_format(current_date, '%Y-%m-%d 18:00:00')
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`type` = 3 and `wor`.`affected_user_id` is not null and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkcyy'">
                and wi.worder_exec_status = 2
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                and wi.worder_exec_status = 2
                and wi.worder_status = 1
                and  TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`) from worder_operation_record `wor`
                where
                `wor`.affected_user_id is not null
                and `wor`.`type` = 3
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                and wi.worder_exec_status = 2
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.affected_user_id is not null and `wor`.`type` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 1
            </when>

            <when test="p.queryType != null and p.queryType == 'kc_mr_dkcyy'">
                and wi.worder_exec_status = 2
                and wi.worder_status = 1
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`type` = 2
                and `wor`.`affected_user_id` is not null
                and `wor`.`worder_no` = wi.worder_no
                ) <![CDATA[>]]> date_format(current_date,'%Y-%m-%d 18:00:00')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_dkc'">
                and wi.worder_exec_status = 3
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                and wi.worder_exec_status = 3
                and wi.worder_status = 1
                and wi.convey_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                and wi.worder_exec_status = 3
                and wi.worder_status = 1
                and wi.convey_appoint_time <![CDATA[>=]]> now()
                and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_mr_dkc'">
                and wi.worder_exec_status = 3
                and wi.worder_status = 1
                and wi.convey_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and wi.convey_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kc_hr_dkc'">
                and wi.worder_exec_status = 3
                and wi.worder_status = 1
                and wi.convey_appoint_time between date_format(current_date + 2,'%Y-%m-%d 00:00:00') and date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_ddcdzjpj'">
                and wi.worder_exec_status = 9
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                and wi.worder_exec_status = 9
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_dazyy'">
                and wi.worder_exec_status = 10
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                and wi.worder_exec_status = 10
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[>]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                and wi.worder_exec_status = 10
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.affected_user_id is not null and `wor`.`type` = 4)
                or (`wor`.worder_status = 1 and `wor`.worder_exec_status = 5)
                or (`wor`.worder_status = 2 and `wor`.worder_exec_status = 10)
                )
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[<=]]> 1
            </when>
            <when test="p.queryType != null and p.queryType == 'az_daz'">
                and wi.worder_exec_status = 11
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                and wi.worder_exec_status = 11
                and wi.worder_status = 2
                and wi.install_appoint_time <![CDATA[<]]> now()
            </when>
            <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                and wi.worder_exec_status = 11
                and wi.worder_status = 2
                and wi.install_appoint_time <![CDATA[>=]]> now()
                and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_mr_daz'">
                and wi.worder_exec_status = 11
                and wi.worder_status = 2
                and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 1,'%Y-%m-%d 00:00:00')
                and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 1,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'az_hr_daz'">
                and wi.worder_exec_status = 11
                and wi.worder_status = 2
                and wi.install_appoint_time <![CDATA[>=]]> date_format(current_date + 2,'%Y-%m-%d 00:00:00')
                and wi.install_appoint_time <![CDATA[<=]]> date_format(current_date + 2,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlwtj'">
                and wi.worder_exec_status = 4
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                and wi.worder_exec_status = 4
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where (`wor`.`worder_exec_status` = 1 or `wor`.`worder_exec_status` = 3) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                and wi.worder_exec_status = 4
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 3 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 6
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczltjdsh'">
                and wi.worder_exec_status = 5
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                and wi.worder_exec_status = 5
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_kczlzgz'">
                and wi.worder_exec_status = 6
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                and wi.worder_exec_status = 6
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                and wi.worder_exec_status = 6
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 1 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_dkfqr'">
                and wi.worder_exec_status = 7
                and wi.worder_status = 1
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                and wi.worder_exec_status = 7
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                and wi.worder_exec_status = 7
                and wi.worder_status = 1
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where ((`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 7) or (`wor`.`worder_status` = 1 and `wor`.`worder_exec_status` = 5)) and  `wor`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwtj'">
                and wi.worder_exec_status = 12
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                and wi.worder_exec_status = 12
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                `wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 11
                and `wor`.`worder_no` = wi.worder_no
                ), now()) <![CDATA[>]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                and wi.worder_exec_status = 12
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`wor`.`create_time`) from worder_operation_record `wor` where `wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 11 and `wor`.`worder_no` = wi.worder_no), now()) <![CDATA[<=]]> 12
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlytjdsh'">
                and wi.worder_exec_status = 13
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                and wi.worder_exec_status = 13
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlzgz'">
                and wi.worder_exec_status = 14
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                and wi.worder_exec_status = 14
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`war`.`gmt_create`) from worder_audit_result `war`
                where
                `war`.`worder_status` = 2
                and `war`.`worder_audit_status` = 21
                and `war`.`worder_no` = wi.worder_no
                ) ,now()) <![CDATA[>]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                and wi.worder_exec_status = 14
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (select max(`war`.`gmt_create`) from worder_audit_result `war` where  `war`.`worder_status` = 2 and `war`.`worder_audit_status` = 21 and  `war`.`worder_no` = wi.worder_no) ,now()) <![CDATA[<=]]> 24
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzldkfsh'">
                and wi.worder_exec_status = 15
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                and wi.worder_exec_status = 15
                and wi.worder_status = 2
                and TIMESTAMPDIFF(hour, (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2
                and `wor`.`worder_exec_status` = 15)
                )
                and  `wor`.`worder_no` = wi.worder_no
                ) ,now()) <![CDATA[>]]> 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                and wi.worder_exec_status = 15
                and wi.worder_status = 2
                and
                (
                select max(`wor`.`create_time`)
                from worder_operation_record `wor`
                where
                (
                (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 13)
                or (`wor`.`worder_status` = 2 and `wor`.`worder_exec_status` = 15)
                )
                and `wor`.`worder_no` = wi.worder_no)
                between date_format(current_date,'%Y-%m-%d 00:00:00')
                and date_format(current_date,'%Y-%m-%d 23:59:59')
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_azzlwwdsccq'">
                and wi.worder_exec_status = 16
                and wi.worder_status = 2
            </when>
            <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwwdsccq'">
                and wi.worder_exec_status = 16
                and wi.worder_status = 2
            </when>
            <otherwise></otherwise>
        </choose>

        <if test="p.createBy != null">
            and wi.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null">
            and wi.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null">
            and wi.dot_id = #{p.dotId}
        </if>

        <if test="p.serviceId != null">
            and ba.user_id = #{p.serviceId}
        </if>

        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and wt.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>

        <if test="p.areas != null and p.areas.size() > 0">
            and wi.area_id in (
            select b.id from biz_region a
            , biz_region b where b.pid=a.id
            and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and wi.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="p.userId != null">
            and exists (
            select
                1
            from
                biz_region r ,
                (
                select
                    m.group_id ,
                    m.child_group_id ,
                    GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                    GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                    GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                from
                    sys_user s
                left join manager_area_id m on
                    s.user_id = m.user_id
                left join biz_region b on
                    m.area_id = b.id
                where
                    s.user_id = #{p.userId}
                group by m.group_id,
                    m.child_group_id
                ) temp
            where
                r.regcode regexp (temp.regcodes)
                and r.type = 3
                and wi.area_id = r.id
                and find_in_set(wt.brand_id, temp.brand_ids)
                and (FIND_IN_SET(wt.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>

        group by brand_name, di.dot_name
    </select>

    <select id="queryCockpitPermission" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity">
        select
            sdd.*
        from
            sys_dictionary sd
        left join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cockpit_permission'
    </select>


    <delete id="deleteByStimulateId">
        delete from worder_child_information
        where stimulate_id = #{stimulateId}
    </delete>

    <select id="getStoreIdByWorderNo" resultType="java.lang.Integer">
        select rsbi.id from rrs_store_basic_info rsbi
        inner join rrs_user_store_relation rusr on rusr.store_id  = rsbi.id
        inner join sys_user su on su.user_id = rusr.user_id
        inner join dot_contacts dc on dc.contacts_name = su.username
        inner join dot_information di on di.dot_code = dc.dot_code
        inner join worder_information wi on wi.dot_id =di.dot_id
        inner join rrs_store_sn_info rssi on rssi.store_id = rsbi.id
        where wi.worder_no=#{worderNo}
        and rsbi.is_delete = 0
        and rssi.sn in
        <foreach collection="outSn" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>

    <select id="getStoreNameById" resultType="java.lang.String">
        select rsbi.store_name from rrs_store_basic_info rsbi
        where rsbi.id = #{storeId}
    </select>


    <select id="checkSnInfo" resultType="com.bonc.rrs.worderapp.Vo.SnStoreVo">
    select rssi.materiel_id,rssi.position_id,rssi.store_id,rssi.status,rssi.sn,rssi.goods_type from rrs_store_sn_info rssi
    inner join materiel_information mi on rssi.materiel_id = mi.id
    inner join rrs_store_basic_info rsbi on rsbi.id =rssi.store_id
    inner join rrs_user_store_relation rusr on rusr.store_id  = rsbi.id
    inner join sys_user su on su.user_id = rusr.user_id
    inner join dot_contacts dc on dc.contacts_name = su.username
    inner join dot_information di on di.dot_code = dc.dot_code
    where rssi.is_delete = 0
    and di.is_delete = 0
    and rsbi.is_delete = 0
    and di.dot_id = #{dotId}
    and rssi.sn =#{sn}
    </select>

    <select id="getStoreInfoByBrandId" resultType="com.bonc.rrs.worderapp.Vo.SnStoreVo">
    select rssg.goods_id as materiel_id,rssg.store_position_id as position_id,rssg.store_id,rssg.goods_type from rrs_store_stock_goods rssg
    inner join materiel_information mi on mi.id = rssg.goods_id
    inner join materiel_information_brand mib on mib.materiel_id = mi.id
    inner join materiel_type mt on mt.id = mi.materiel_type_id and mt.materiel_type = 1 and mt.materiel_child_type = '立柱'
    inner join rrs_store_basic_info rsbi on rsbi.id =rssg.store_id
    inner join rrs_user_store_relation rusr on rusr.store_id  = rsbi.id
    inner join sys_user su on su.user_id = rusr.user_id
    inner join dot_contacts dc on dc.contacts_name = su.username
    inner join dot_information di on di.dot_code = dc.dot_code
    where di.is_delete = 0
    and rsbi.is_delete = 0
    and rssg.real_goods_total > 0
    and rssg.goods_type = 4
    and mib.brand_id = #{brandId}
    and rsbi.id = #{storeId}
    and di.dot_id = #{dotId}
    </select>

    <select id="getExNum" resultType="java.lang.Integer">
        select real_goods_total from rrs_store_stock_goods
        where goods_id = #{materielId}
        and store_id = #{storeId}
        and store_position_id = #{positionId}
        and goods_type = 4
    </select>

    <update id="updateSnStatus">
        update rrs_store_sn_info set status = 1
        where status = 7
        and is_delete = 0
        and goods_type = 4
        and sn = #{sn}
    </update>

    <update id="updateSnUse">
        update rrs_store_sn_info set status = 3
        where status = 7
        and is_delete = 0
        and goods_type = 4
        and sn = #{sn}
    </update>

    <select id="selectSnByWorderId" resultType="java.lang.String">
        select rglr.materiel_sn from rrs_goods_leave_order rglo
        inner join rrs_goods_leave_relation rglr on rglo.id = rglr.leave_id
        left join worder_information_attribute wia on rglo.leave_num = wia.attribute_value
        left join rrs_store_sn_info rssi on rglr.materiel_sn = rssi.sn
        where wia.is_delete = 0
        and rglo.out_scenario = 3
        and rglo.go_status = 0
        and wia.worder_id =#{worderId}
        and wia.attribute_code = 'LeaveOrderNumber'
        and wia.`attribute` = 'NoOutbound'
        and rglr.materiel_sn is not null
    </select>

    <update id="updateLeaveOrder">
        update rrs_goods_leave_order set go_status = -1 where go_status = 0 and leave_num =#{leaveNum}
    </update>

    <update id="updateLeaveInfo">
        update rrs_enter_leave_info set status = -1 where status = 0 and enter_code =#{leaveNum}
    </update>

    <select id="getLeaveIdByNum" resultType="java.lang.Integer">
        select id from rrs_goods_leave_order
        where leave_num = #{leaveNum}
    </select>

    <select id="getStoreIdByNum" resultType="java.lang.Integer">
        select rglo.store_id from rrs_goods_leave_order rglo
        where rglo.leave_num = #{leaveNum}
    </select>

    <select id="checkSnStatus" resultType="com.bonc.rrs.worderapp.Vo.SnVo">
        select rssi.sn,rsbi.store_name  from rrs_store_sn_info rssi
        left join rrs_store_basic_info rsbi on rsbi.id =rssi.store_id
        inner join rrs_user_store_relation rusr on rusr.store_id  = rsbi.id
        inner join sys_user su on su.user_id = rusr.user_id
        inner join dot_contacts dc on dc.contacts_name = su.username
        inner join dot_information di on di.dot_code = dc.dot_code
        where rssi.status = 7
        and rssi.is_delete = 0
        and di.dot_id = #{dotId}
        and sn =#{sn}
    </select>

    <select id="getSnByWorderNo" resultType="java.lang.String">
        select wef.field_value from worder_ext_field wef
        left join ext_field ef on wef.field_id = ef.field_id
        where ef.field_type = 12
        and worder_no = #{worderNo}
        and ef.deleted = 0
    </select>

    <select id="getLeaveStatus" resultType="java.lang.String">
        select go_status from rrs_goods_leave_order
        where out_scenario = '3'
        and leave_num =#{leaveNum}
    </select>

    <update id="updateSnStatusUse">
        update rrs_store_sn_info set status = 7
        where status = 1
        and is_delete = 0
        and goods_type = 4
        and sn = #{sn}
    </update>

    <select id="checkSnNum" resultType="java.lang.String">
        select rssi.status from rrs_store_sn_info rssi
        inner join rrs_goods_leave_relation rglr on rglr.materiel_sn = rssi.sn
        inner join rrs_goods_leave_order rglo on rglo.id = rglr.leave_id
        inner join worder_information_attribute wia on rglo.leave_num = wia.attribute_value
        where wia.is_delete = 0
        and rglo.out_scenario = 3
        and rglo.go_status = 0
        and rssi.status =7
        and wia.worder_id =#{worderId}
        and wia.attribute_code = 'LeaveOrderNumber'
        and wia.`attribute` = 'NoOutbound'
        and rssi.sn in
        <foreach collection="outSn" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>

    <select id="getNoInstallReasonList" resultType="com.bonc.rrs.balanceprocess.vo.NoInstallReasonVo">
    select sdd.id,sdd.detail_name,sdd.detail_number,sdd.remark from sys_dictionary sd
    left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
    where sd.dic_number = 'no_install_reason'
    </select>

    <select id="getWorderLevelList" resultType="com.bonc.rrs.balanceprocess.vo.NoInstallReasonVo">
    select sdd.id,sdd.detail_name,sdd.detail_number,sdd.remark from sys_dictionary sd
    left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
    where sd.dic_number = 'worder_level'
    </select>

    <select id="isExsitWorder" resultMap="worderInformationMap">
        select  wi.* from worder_information wi, worder_ext_field wef ,ext_field ef  where  wi.worder_no=wef.worder_no and  wef.field_id =ef.field_id and ef.field_id =5 and wef.field_value = #{companyOrderNumber} and wi.is_delete = 0
    </select>

    <select id="getSatisfactionByWorderNo" resultType="com.bonc.rrs.worder.dto.vo.ClientSatisfactionVo">
        select * from client_satisfaction where worder_no = #{worderNo} order by create_time desc limit 1
    </select>

    <update id="updateClientSatisfaction">
        UPDATE client_satisfaction
        SET worder_no=#{worderNo},
            service_satisfaction=#{serviceSatisfaction},
            trouble_spot=#{troubleSpot},
            trouble_spot_content=#{troubleSpotContent},
            satisfaction_score=#{satisfactionScore},
            is_work_cloth=#{isWorkCloth},
            is_show_document=#{isShowDocument},
            is_show_agreement=#{isShowAgreement},
            is_assist_bind=#{isAssistBind},
            customer_advice=#{customerAdvice}
        WHERE id = #{id};
    </update>


    <select id="getPresurveyWorder" resultMap="worderInformationMap">
        select
            wi.worder_id,
            wi.worder_no ,
            wi.create_time,
            wi.company_order_number,
            wi.pm_id,
            wi.dot_id
        from
            worder_information wi ,
            worder_template wt ,
            worder_ext_field wef
        where
            wi.worder_no = wef.worder_no
          and wt.id = wi.template_id
          and wi.create_time > DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00'), INTERVAL 2 YEAR)
          and wi.worder_type_id = 5
          and wi.is_delete = 0
          <if test="userPhone != null and userPhone != ''">
              and wi.user_phone = #{userPhone}
          </if>
          <if test="postcode != null and postcode != ''">
              and wi.postcode = #{postcode}
          </if>
          and wt.brand_id = #{brandId}
          and wef.field_id = 1224
          and wef.field_value = '是'
        order by wi.create_time desc limit 1;
    </select>

    <select id="getCockpitCSNegativeStimulateSwitch" resultType="string">
        select
            sdd.detail_number
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cs_cockpik_negative_stimulate_config'
            and sdd.id = 764
    </select>

    <select id="getCockpitCSNegativeStimulateMoney" resultType="string">
        select
            sdd.detail_number
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cs_cockpik_negative_stimulate_config'
            and sdd.id = 765
    </select>

    <select id="getCockpitCSNegativeStimulateLink" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysDicEntity">
        select
            sdd.detail_number,
            sdd.remark
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cs_cockpik_negative_stimulate_link'
    </select>

    <select id="getCockpitCSNegativeStimulateCheckWorderType" resultType="string">
        select
            sdd.detail_name
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cs_cockpik_negative_stimulate_config'
            and sdd.detail_number = 'worder_type'
    </select>

    <select id="getCockpitCSNegativeStimulateCheckBrand" resultType="string">
        select
            sdd.detail_name
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'cs_cockpik_negative_stimulate_config'
            and sdd.detail_number = 'brand'
    </select>

    <select id="queryCockpitStatistics" resultType="com.bonc.rrs.worder.dto.dto.CockpitStatisticsDto">
        select
            <choose>
                <when test="p.timeDimension != null and p.timeDimension == 'day'">
                    DATE_FORMAT(rkor.`cycle`, '%y-%m-%d') time_slot,
                </when>
                <otherwise>
                    DATE_FORMAT(rkor.`cycle`, '%H:%i') time_slot,
                </otherwise>
            </choose>

            <choose>
                <when test="p.dimension != null and p.dimension == 'province'">
                    province_br.name catalog,
                </when>
                <when test="p.dimension != null and p.dimension == 'dot'">
                    SUBSTRING(di.dot_name, 1, 6) catalog,
                </when>
                <otherwise>
                    br.regione catalog,
                </otherwise>
            </choose>
            <choose>
                <when test="p.queryType != null and p.queryType == 'fp_cs_wpd'">
                    sum(rkor.未派单) num,
                    sum(rkor.未派单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_jr_wpd'">
                    sum(rkor.客服未派单今日总量) num,
                    sum(rkor.未派单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_cs_fwjlwpd'">
                    sum(rkor.服务经理未派单) num,
                    sum(rkor.服务经理未派单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'fp_jr_fwjlwpd'">
                    sum(rkor.项目经理未派单今日总量) num,
                    sum(rkor.服务经理未派单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_wdyjd'">
                    sum(rkor.网点已接单) num,
                    sum(rkor.网点已接单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_wdyjd'">
                    sum(rkor.网点派已接单今日总量) num,
                    sum(rkor.网点已接单总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_dkcyy'">
                    sum(rkor.待勘测预约) num,
                    sum(rkor.待勘测预约总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_dkcyy'">
                    sum(rkor.待勘测预约今日总量) num,
                    sum(rkor.待勘测预约总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_cs_dkc'">
                    sum(rkor.待勘测) num,
                    sum(rkor.待勘测总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kc_jr_dkc'">
                    sum(rkor.待勘测今日总量) num,
                    sum(rkor.待勘测总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_cs_ddcdzjpj'">
                    sum(rkor.等待充电桩及备件) num,
                    sum(rkor.等待充电桩及备件总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_ddcdzjpj'">
                    sum(rkor.等待充电桩及备件今日总量) num,
                    sum(rkor.等待充电桩及备件总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_cs_dazyy'">
                    sum(rkor.待安装预约) num,
                    sum(rkor.待安装预约总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_dazyy'">
                    sum(rkor.待安装预约今日总量) num,
                    sum(rkor.待安装预约总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_cs_daz'">
                    sum(rkor.待安装) num,
                    sum(rkor.待安装总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'az_jr_daz'">
                    sum(rkor.待安装今日总量) num,
                    sum(rkor.待安装总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlwtj'">
                    sum(rkor.勘测资料未提交) num,
                    sum(rkor.勘测资料未提交总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlwtj'">
                    sum(rkor.勘测资料未提交今日总量) num,
                    sum(rkor.勘测资料未提交总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_kczltjdsh'">
                    sum(rkor.勘测资料提交待审核) num,
                    sum(rkor.勘测资料提交待审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczltjdsh'">
                    sum(rkor.勘测资料提交待审核今日总量) num,
                    sum(rkor.勘测资料提交待审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_kczlzgz'">
                    sum(rkor.勘测资料整改中) num,
                    sum(rkor.勘测资料整改中总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_kczlzgz'">
                    sum(rkor.勘测资料整改中今日总量) num,
                    sum(rkor.勘测资料整改中总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_cs_dkfqr'">
                    sum(rkor.待客服确认) num,
                    sum(rkor.待客服确认总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'kczl_jr_dkfqr'">
                    sum(rkor.待客服确认今日总量) num,
                    sum(rkor.待客服确认总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlwtj'">
                    sum(rkor.安装资料未提交) num,
                    sum(rkor.安装资料未提交总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlwtj'">
                    sum(rkor.安装资料待提交今日总量) num,
                    sum(rkor.安装资料未提交总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlytjdsh'">
                    sum(rkor.安装资料已提交待审核) num,
                    sum(rkor.安装资料已提交待审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlytjdsh'">
                    sum(rkor.安装资料提交待审核今日总量) num,
                    sum(rkor.安装资料已提交待审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzlzgz'">
                    sum(rkor.安装资料整改中) num,
                    sum(rkor.安装资料整改中总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzlzgz'">
                    sum(rkor.安装资料待客服审核今日总量) num,
                    sum(rkor.安装资料待客服审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_cs_azzldkfsh'">
                    sum(rkor.安装资料待客服审核) num,
                    sum(rkor.安装资料待客服审核总量) total
                </when>
                <when test="p.queryType != null and p.queryType == 'azzl_jr_azzldkfsh'">
                    sum(rkor.安装资料整改中今日总量) num,
                    sum(rkor.安装资料整改中总量) total
                </when>
            </choose>
        from
        <choose>
            <when test="p.timeDimension != null and p.timeDimension == 'day'">
                rrs_kanban_operate_realdate rkor
            </when>
            <otherwise>
                rrs_kanban_operate_realtime rkor
            </otherwise>
        </choose>

        inner join biz_region br on
            rkor.area_id = br.id
            <choose>
                <when test="p.dimension != null and p.dimension == 'province'">
                    inner join biz_region city_br on
                        br.pid = city_br.id
                    inner join biz_region province_br on
                        city_br.pid = province_br.id
                </when>
                <when test="p.dimension != null and p.dimension == 'dot'">
                    inner join dot_information di on
                        rkor.dot_id = di.dot_id
                </when>
            </choose>
        where 1=1
        <choose>
            <when test="p.timeDimension != null and p.timeDimension == 'day'">
                <if test="p.dayTimeStart != null">
                    and rkor.`cycle` <![CDATA[>=]]> #{p.dayTimeStart}
                </if>
                <if test="p.dayTimeStart != null">
                    and rkor.`cycle` <![CDATA[<=]]> #{p.dayTimeEnd}
                </if>
            </when>
            <otherwise>
                and rkor.`cycle` <![CDATA[>=]]> DATE_SUb(now() , interval 24 hour)
                and rkor.`cycle` <![CDATA[<=]]> now()
            </otherwise>
        </choose>


        <if test="p.brandIds != null and p.brandIds.size() > 0">
            and rkor.brand_id in
            <foreach collection="p.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="p.worderTypes != null and p.worderTypes.size() > 0">
            and rkor.worder_type_id in
            <foreach collection="p.worderTypes" item="worderType" open="(" separator="," close=")">
                #{worderType}
            </foreach>
        </if>
        <if test="p.areas != null and p.areas.size() > 0">
            and rkor.area_id in (
                select b.id from biz_region a
                , biz_region b where b.pid=a.id
                and a.pid in
                <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size() > 0">
            and rkor.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="p.createBy != null">
            and rkor.create_by = #{p.createBy}
        </if>

        <if test="p.pmId != null">
            and rkor.pm_id = #{p.pmId}
        </if>

        <if test="p.dotId != null">
            and rkor.dot_id = #{p.dotId}
        </if>

        <if test="p.serviceId != null">
            and rkor.service_id = #{p.serviceId}
        </if>
        <if test="p.userId != null">
            and exists (
                select
                    1
                from
                    biz_region r ,
                    (
                        select
                            m.group_id ,
                            m.child_group_id ,
                            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
                            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
                            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
                        from
                            sys_user s
                        left join manager_area_id m on
                            s.user_id = m.user_id
                        left join biz_region b on
                            m.area_id = b.id
                        where
                            s.user_id = #{p.userId}
                        group by m.group_id,
                            m.child_group_id
                    ) temp
                where
                    r.regcode regexp (temp.regcodes)
                    and r.type = 3
                    and rkor.area_id = r.id
                    and find_in_set(rkor.brand_id, temp.brand_ids)
                    and (FIND_IN_SET(rkor.service_type_enum, temp.service_types) or FIND_IN_SET('0', temp.service_types))
            )
        </if>
        group by
            <choose>
                <when test="p.timeDimension != null and p.timeDimension == 'day'">
                    DATE_FORMAT(rkor.`cycle`, '%y-%m-%d'),
                </when>
                <otherwise>
                    DATE_FORMAT(rkor.`cycle`, '%H:%i'),
                </otherwise>
            </choose>

            <choose>
                <when test="p.dimension != null and p.dimension == 'province'">
                    province_br.id
                </when>
                <when test="p.dimension != null and p.dimension == 'dot'">
                    di.dot_id
                </when>
                <otherwise>
                    br.regione
                </otherwise>
            </choose>
        order by
            rkor.`cycle`
    </select>

    <update id="callGenerateKanbanRealtime" statementType="CALLABLE">
        { call GenerateKanbanRealtime }
    </update>
    <update id="updateMaterial">
        update
            worder_information wi,
            worder_information_attribute wia
        set
            wia.is_delete = 1
        where
            wia.worder_id = wi.worder_id
          and wia.`attribute` in ('NoOutbound', 'addedMaterialType', 'FixDocSubmit')
          and wi.worder_no = #{worderNo}
    </update>

    <select id="selectCaWorderInfo" resultType="java.lang.Integer">
        select wi.worder_id, attribute_code
        from worder_information wi
                 left join worder_template wt on wi.template_id = wt.id
                 left join worder_information_attribute wia on wi.worder_id = wia.worder_id and wia.attribute_code = 'push_dataCheck'
        where wi.is_delete = 0
          and wt.brand_id in (106, 104)
          and wia.id is null
        order by worder_id desc
            limit 500;
    </select>
    <select id="getDotInfoByWorderNo" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select  wi.*,di.dot_short_name from worder_information  wi
                        left join  dot_information di on di.dot_id = wi.dot_id
        where wi.is_delete = 0 and  worder_no = #{worderNo}
    </select>
    <select id="selectazjslList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId, proName , SUM(CASE WHEN t2 &lt;= 24 THEN 1 ELSE 0 END) AS azjslSum24,count(1) as azjslSum
        from (
        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,
        TIMESTAMPDIFF(HOUR, wef1.field_value,wor.create_time ) AS t2
        from worder_information wi
        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id
        inner join worder_operation_record wor on wi.worder_no = wor.worder_no
        and wor.worder_exec_status = 13
        and wor.create_time =(
        select
        max(wor2.create_time)
        from
        worder_operation_record wor2
        where
        wor2.worder_no = wor.worder_no
        and wor2.worder_exec_status = 13 )
        where wi.is_delete = 0  and br3.id is not  null  <include refid="baobiaochaxun"></include> ) a group by proId
    </select>

    <select id="selectpjazsxList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId as proId,proName as type3,
        count(1) as pjazsxSum,
        sum(t1) as pjazsxSumHours
        from (
        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,
        wef1.field_value,
        TIMESTAMPDIFF(HOUR, wef2.field_value,wef1.field_value ) AS t1
        from worder_information wi
        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN worder_ext_field wef2 on wi.worder_no = wef2.worder_no and wef2.field_id = 154

        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id

        where wi.is_delete = 0
        and wef1.field_value is not null and wef2.field_value is not null and br3.id is not  null <include refid="baobiaochaxun"></include>

        ) a group by proId
    </select>
    <select id="selectazlList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId as proId,proName,
        SUM(CASE WHEN a.worder_status = 6 THEN 1 ELSE 0 END) AS azlSumCanal,SUM(CASE WHEN a.worder_status = 4 and a.worder_exec_status = 20 THEN 1 ELSE 0 END) AS azlSumConvey,
        count(1) as azlSum
        from (

        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,wi.worder_status,wi.worder_exec_status


        from worder_information wi
        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id

        where wi.is_delete = 0   and br3.id is not  null <include refid="baobiaochaxun"></include>) a group by proId
    </select>
    <select id="selectpjazsxDotList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId as proId,proName as type1,dotId,dot_name as type2,
        count(1) as pjazsxSum,
        sum(t1) as pjazsxSumHours
        from (
        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,wi.dot_id as dotId,di.dot_name,
        wef1.field_value,
        TIMESTAMPDIFF(HOUR, wef2.field_value,wef1.field_value ) AS t1
        from worder_information wi
        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN worder_ext_field wef2 on wi.worder_no = wef2.worder_no and wef2.field_id = 154
        LEFT JOIN dot_information di on wi.dot_id = di.dot_id

        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id

        where wi.is_delete = 0
        and wef1.field_value is not null and wef2.field_value is not null and br3.id is not  null <include refid="baobiaochaxun"></include>

        ) a group by proId,dotId
    </select>
    <select id="selectazjslDotList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId as proId,proName as type1,dotId,dot_name as type2, SUM(CASE WHEN t2 &lt;= 24 THEN 1 ELSE 0 END) AS azjslSum24,count(1) as azjslSum
        from (
        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,wi.dot_id as dotId,di.dot_name,
        -- 安装资料提交时间 - 比亚迪模板中的实际安装完成时间
        TIMESTAMPDIFF(HOUR, wef1.field_value,wor.create_time ) AS t2
        from worder_information wi
        LEFT JOIN dot_information di on wi.dot_id = di.dot_id

        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id
        inner join worder_operation_record wor on wi.worder_no = wor.worder_no
        and wor.worder_exec_status = 13
        and wor.create_time =(
        select
        max(wor2.create_time)
        from
        worder_operation_record wor2
        where
        wor2.worder_no = wor.worder_no
        and wor2.worder_exec_status = 13 )
        where wi.is_delete = 0  and wi.worder_exec_status = 17   and br3.id is not  null  <include refid="baobiaochaxun"></include> ) a group by proId,dotId
    </select>
    <select id="selectazlDotList" resultType="com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult">
        SELECT proId as proId,proName as type1,dotId,dot_name as type2,
        SUM(CASE WHEN a.worder_status = 6 THEN 1 ELSE 0 END) AS azlSumCanal,SUM(CASE WHEN a.worder_status = 4 and a.worder_exec_status = 20 THEN 1 ELSE 0 END) AS azlSumConvey,
        count(1) as azlSum
        from (

        SELECT wi.worder_no,br3.id proId,br3.`name` proName ,wi.worder_status,wi.worder_exec_status,wi.dot_id as dotId,di.dot_name


        from worder_information wi
        LEFT JOIN dot_information di on wi.dot_id = di.dot_id

        LEFT JOIN worder_ext_field wef1 on wi.worder_no = wef1.worder_no and wef1.field_id = 1197
        LEFT JOIN biz_region br1 on wi.area_id = br1.id
        LEFT JOIN biz_region br2 on br2.id = br1.pid
        LEFT JOIN biz_region br3 on br3.id = br2.pid
        LEFT JOIN worder_template wt on wi.template_id = wt.id

        where wi.is_delete = 0   and br3.id is not  null <include refid="baobiaochaxun"></include>) a group by proId,dotId


    </select>

    <sql id="baobiaochaxun">
        <if test="param.brandIds != null and param.brandIds.size > 0">
            and wt.brand_id in
            <foreach collection="param.brandIds" item="brandId" open="(" separator="," close=")">
                #{brandId}
            </foreach>
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and wi.create_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and wi.create_time &lt;= #{param.endTime}
        </if>

        <if test="param.finishTimeStart != null and param.finishTimeStart != ''">
            and wef1.field_value &gt;= #{param.finishTimeStart}
        </if>
        <if test="param.finishTimeEnd != null and param.finishTimeEnd != ''">
            and wef1.field_value &lt;= #{param.finishTimeEnd}
        </if>
        <if test="param.provinceIds != null and param.provinceIds.size > 0">
            and br3.id in
            <foreach collection="param.provinceIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>
</mapper>
