<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.SysFilesMapper">
    <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.SysFile">
        <constructor>
            <idArg column="file_id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="obj_type" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="obj_value" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="old_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="new_name" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="path" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="small_path" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="file_type" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="file_size" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="file_sort" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="file_width" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="file_height" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="create_person" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        </constructor>
    </resultMap>

    <sql id="Base_Column_List">
        file_id
        , obj_type, obj_value, old_name, new_name, path, small_path, file_type, file_size,
        file_sort, file_width, file_height, create_person, create_time
    </sql>

    <insert id="addFilelist" useGeneratedKeys="true" keyProperty="id">

        insert into sys_file (obj_type, obj_value, new_name, small_path,file_code)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ObjType},#{item.ObjValue},#{item.fileName},#{item.url},#{fileCode})
        </foreach>

    </insert>


    <select id="listSysFiles" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select file_id fileId, path, old_name oldName, new_name newName
        from sys_file
        where file_id = #{fileId}
    </select>

    <insert id="saveSysFile" parameterType="com.bonc.rrs.workManager.entity.SysFileEntity" useGeneratedKeys="true"
            keyProperty="fileId">
        insert into sys_file (path, new_name, create_person, create_time, old_name,file_code, obj_type, obj_value)
        values (#{path}, #{newName}, null, now(), #{oldName},#{fileCode}, #{objType},#{objValue})
    </insert>
    <insert id="saveFile" parameterType="com.bonc.rrs.workManager.entity.SysFileEntity" useGeneratedKeys="true"
            keyProperty="fileId">
        insert into sys_file (path, new_name, create_person, create_time, old_name, obj_value,file_code)
        values (#{path}, #{newName}, null, now(), #{oldName}, #{objValue},#{fileCode})
    </insert>

    <select id="selectSysFileInfo" resultType="java.lang.String">
        select path
        from sys_file
        where file_id = #{fileId}
    </select>
    <select id="selectSysFileName" resultType="java.lang.String">
        select new_name
        from sys_file
        where file_id = #{fileId}
    </select>

    <select id="listPaths" parameterType="java.lang.String" resultType="java.lang.String">
        select path
        from sys_file
        where file_id in ${fileIds}
    </select>

    <update id="updateSysFile" parameterType="com.bonc.rrs.workManager.entity.dto.SysFileUpdateDto">
        update sys_file
        <set>
            <if test="objType != null and objType != ''">
                obj_type = #{objType},
            </if>
            <if test="objValue != null and objValue != ''">
                obj_value = #{objValue},
            </if>
        </set>
        where file_id = #{fileId}
    </update>

    <select id="listFileInFileIds" parameterType="java.lang.String"
            resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select file_id fileId, old_name oldName, path
        from sys_file
        where file_id in ${fileIds}
    </select>
    <select id="getUrl"  resultType="java.util.Map">
        select  e.field_type, e.field_purpose, s.path, temp.field_id, temp.field_name, temp.id
        from (
                 select a.field_name,
                        a.field_id,
                        a.field_value,
                        a.id,
                        substring_index(substring_index(a.field_value, ',', b.help_topic_id + 1), ',', -1) name
                 from worder_ext_field a
                          join
                      mysql.help_topic b
                      on b.help_topic_id &lt; (length(a.field_value) - length(replace(a.field_value, ',', '')) + 1)
                 where a.id in (
                     select w.id
                     from sys_file s
                              inner join worder_ext_field w on s.file_id = w.field_value
                     where a.worder_no = #{worderNo}
                 )
                   and a.id in (select max(id) from  worder_ext_field where worder_no=#{worderNo} group by field_name,field_id)
             ) temp
                 inner join sys_file s on temp.name = s.file_id
                 inner join ext_field e on temp.field_id = e.field_id
        where e.field_purpose = #{purpose}
          and e.field_type = 3
    </select>
    <select id="fingByValue"
            resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select e.file_id from worder_ext_field w inner join sys_file e on w.id=e.obj_value
        where w.worder_no=#{worderNo} and w.field_name=#{name}
    </select>
    <select id="findObjValue"
            resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select id from worder_ext_field where  worder_no=#{worderNo} and field_name=#{name}
    </select>
    <update id="updatePath">
        update sys_file set path=#{path} where file_id=#{fileId}
    </update>

    <select id="getAll" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select * from sys_file where file_code is null and file_id >= 7083080
    </select>

    <update id="updateCode">
        update sys_file set file_code=#{fileCode} where file_id=#{fileId}
    </update>

    <select id="getScoreImportFile" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select * from sys_file sf where obj_value = 15 and create_time >= MONTH(CURDATE()) and obj_type  = 15 order by create_time desc limit 1;
    </select>
    <select id="getSysFileByIds" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select * from sys_file sf where file_id in (${fileIds})
    </select>
    <select id="getPathByName" resultType="java.lang.String">
        select `path` from sys_file sf where new_name = #{name} limit 1
    </select>
</mapper>