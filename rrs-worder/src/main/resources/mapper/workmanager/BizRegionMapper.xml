<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.BizRegionMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.BizRegion">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="pid" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="type" javaType="java.lang.Byte" jdbcType="TINYINT" />
      <arg column="code" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="abbreviation" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>

  <sql id="Base_Column_List">
    id, pid, name, type, code, abbreviation
  </sql>

  <select id="selectArealist" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_region
    where `pid` = #{pid,jdbcType=INTEGER}
  </select>
  <select id="selectArealistById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from biz_region
    where `id` = #{id,jdbcType=INTEGER}
  </select>

  <select id="areaName" resultType="java.lang.String">
    select
    abbreviation
    from biz_region
    where `id` = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectAreaName" resultType="java.lang.String">
    select
    `name`
    from biz_region
    where `id` = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectRegionlist" resultType="java.util.Map">
    SELECT * FROM biz_region WHERE regcode LIKE CONCAT('%',#{code},'%')
  </select>

  <select id="selctDotFuSheInfo" resultType="java.util.Map">
    SELECT CAST(d.`area_id` as CHAR(10)) as `areaId` , b.`name` as `name`, d.group_id as groupId FROM `biz_region`
    AS b,`dot_area` AS d WHERE b.`id`=d.`area_id`
    AND d.`dot_id`=#{dotId}
  </select>
  <select id="findParentName" resultType="java.lang.String">
    select
    t.`name`
    from biz_region t
      inner  join
       biz_region t1 on t.id = t1.pid
    where t1.`id` = #{id,jdbcType=INTEGER}
  </select>

    <select id="selectAreaByUserId" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
      select distinct br.* from manager_area_id mai , biz_region br where mai.area_id = br.id and user_id = #{userId};
    </select>

  <select id="selectListByDot" resultType="com.bonc.rrs.worder.entity.BizRegionEntity">
      select br.*, da.group_id
      from dot_area da,
           biz_region br
      where da.area_id = br.id
        and da.is_delete = '0'
        and da.dot_id = #{dotId}
  </select>
</mapper>