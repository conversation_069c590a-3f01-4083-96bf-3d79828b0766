<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.BrandDotScoreMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.BrandDotScoreBean">
    <!--@mbg.generated-->
    <!--@Table `rrs_brand_dot_score`-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="cycle" jdbcType="VARCHAR" property="cycle" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="dot_id" jdbcType="INTEGER" property="dotId" />
    <result column="performance" jdbcType="DECIMAL" property="performance" />
    <result column="service_ability" jdbcType="DECIMAL" property="serviceAbility" />
    <result column="added_performance" jdbcType="DECIMAL" property="addedPerformance" />
    <result column="mission_critical" jdbcType="DECIMAL" property="missionCritical" />
    <result column="deduct" jdbcType="DECIMAL" property="deduct" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="manual_score" jdbcType="DECIMAL" property="manualScore" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_state" jdbcType="TINYINT" property="deleteState" />

    <result column="dot_name" jdbcType="VARCHAR" property="dotName" />
    <result column="dot_area" jdbcType="INTEGER" property="dotArea" />
    <result column="dot_city" jdbcType="INTEGER" property="dotCity" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
  </resultMap>

  <select id="selectDotIdsPage" resultType="java.lang.Integer">
    select
      t1.dot_id
    from
      (
        select
          dot_id,
          max(create_time) as create_time
        from
          rrs_brand_dot_score
        where
          `cycle` = #{param.cycle}
          <if test="param.brandId != null">
            and brand_id = ${param.brandId}
          </if>
          and delete_state = 0
        group by
          dot_id) t1 , dot_information t2
    where t1.dot_id = t2.dot_id
    <if test="param.dotName != null and param.dotName != ''">
      and t2.dot_name like concat('%',#{param.dotName},'%')
    </if>
    <if test="param.region != null">
      and (t2.dot_area = ${param.region} || t2.dot_city = ${param.region} || t2.dot_district = ${param.region})
    </if>
    order by
      t1.create_time desc
  </select>

  <select id="selectListByDotId" resultMap="BaseResultMap">
    select
      t1.*,
      t2.dot_name,
      t2.dot_area ,
      t2.dot_city
    from
      rrs_brand_dot_score t1,
      dot_information t2
    where
      t1.dot_id = t2.dot_id
      and t1.`cycle` = #{cycle}
      and t1.delete_state = 0
    <foreach collection="dotIds" item="dotId" open="and t2.dot_id in (" close=")" separator=",">
      ${dotId}
    </foreach>
  </select>

  <select id="selectExtportList" resultType="com.bonc.rrs.workManager.entity.vo.DotBrandScoreExport">
    select
      t1.`cycle` ,
      t2.dot_id ,
      t2.dot_name ,
      t2.dot_area ,
      t2.dot_city ,
      t2.dot_district ,
      t1.brand_name ,
      t1.performance ,
      t1.service_ability ,
      t1.added_performance ,
      t1.mission_critical ,
      t1.deduct ,
      t1.score
    from
      rrs_brand_dot_score t1 ,
      dot_information t2
    where
      t1.dot_id = t2.dot_id
      and t1.delete_state = 0
    <if test="param.cycle != null and param.cycle != ''">
      and t1.`cycle` = #{param.cycle}
    </if>
    <if test="param.brandId != null">
      and t1.brand_id = ${param.brandId}
    </if>
    <if test="param.dotName != null and param.dotName != ''">
      and t2.dot_name like concat('%',#{param.dotName},'%')
    </if>
    <if test="param.region != null">
      and (t2.dot_area = ${param.region} || t2.dot_city = ${param.region} || t2.dot_district = ${param.region})
    </if>
    order by t1.create_time desc
  </select>

  <select id="getTargetServiceAbility" resultType="java.math.BigDecimal">
    select CAST((count(*) / (DAY(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 2 MONTH))) * 3)) AS DECIMAL(10,2)) from send_worder_record where send_worder_type = 2 and operation_type = 1 and delete_state = 0 and DATE_FORMAT(create_time , '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), interval 2 month), '%Y-%m') and accept_worder_user = #{dotId}
  </select>

  <select id="getActualServiceAbility" resultType="java.util.Map">
    select di.common_car_num, (select count(*) from biz_attendant ba where ba.attendant_flag = '1' and ba.dot_id = #{dotId}) as attendant_num from dot_information di  where di.dot_id = #{dotId}
  </select>

  <select id="selectPageByDot" resultMap="BaseResultMap">
    select
      rbds.*,concat(br2.name,br.name) as city_name
    from
      rrs_brand_dot_score rbds ,
      biz_region br ,
      biz_region br2
    where
      rbds.city = br.id
      and br.pid = br2.id
      and rbds.`cycle` = #{cycle}
      and rbds.dot_id = #{dotId}
      <if test="region != null">
        and (br.id = #{region} or br2.id = #{region})
      </if>
      and rbds.delete_state = 0
    order by rbds.brand_id , rbds.city
  </select>

  <select id="selectAreaByUserId" resultType="java.lang.Long">
     select distinct  area_id  from manager_area_id mai where user_id = #{userId} and area_id is not null
  </select>

  <select id="queryList" resultMap="BaseResultMap">
    select
      t1.`cycle` ,
      t2.dot_id ,
      SUBSTR(t2.dot_name,1,6) as dot_name ,
      t1.brand_name ,
      t1.performance ,
      t1.service_ability ,
      t1.added_performance ,
      t1.mission_critical ,
      t1.deduct ,
      t1.score ,
      t1.manual_score
    from
      rrs_brand_dot_score t1 ,
      dot_information t2
    where
    t1.dot_id = t2.dot_id
    and t1.delete_state = 0
    <if test="param.cycle != null and param.cycle != ''">
      and t1.`cycle` = #{param.cycle}
    </if>
    <foreach collection="param.brandIds" item="brandId" open=" and t1.brand_id in (" close=")" separator=",">
      ${brandId}
    </foreach>
    <if test="param.city != null">
      and t1.city = ${param.city}
    </if>
  </select>

  <select id="queryDotIdsByArea" resultType="com.bonc.rrs.workManager.entity.vo.DotInfoVo">
    select
      di.dot_id,
      max(di.dot_name) as dot_name
    from
      dot_information di,
      dot_area da,
      dot_brand db,
      biz_region br,
      biz_region br2
    where
      di.dot_id = da.dot_id
      and di.dot_id = db.dot_id
      and br.id = da.area_id
      and br2.regcode like CONCAT(br.regcode, '%')
      and br2.type in (2, 3)
      and di.is_delete = 0
      <foreach collection="brandIds" item="brandId" open=" and db.brand_id in (" close=")" separator=",">
         #{brandId}
      </foreach>
      and br.regcode like CONCAT(#{regcode}, '%')
    group by di.dot_id;
  </select>

  <select id="selectPageTables" resultType="java.util.Map">
    select
      rbds.`cycle` ,
      rbds.city ,
      di.dot_name ,
      rbds.dot_id
    from
      rrs_brand_dot_score rbds ,
      dot_information di ,
      biz_region br
    where
      rbds.dot_id = di.dot_id and rbds.city = br.id and rbds.delete_state = 0
    <if test="param.cycle != null and param.cycle != ''">
      and rbds.`cycle` = #{param.cycle}
    </if>
    <if test="param.brandId != null">
      and rbds.brand_id = #{param.brandId}
      <if test="param.regione != null and param.regione != ''">
        <choose>
          <when test="param.brandId == 18">
            and br.lx_region = #{param.regione}
          </when>
          <when test="param.brandId == 14">
            and br.tsl_region = #{param.regione}
          </when>
          <otherwise>
            and br.regione = #{param.regione}
          </otherwise>
        </choose>
      </if>
    </if>
    <if test="param.dotId != null">
      and rbds.dot_id = #{param.dotId}
    </if>
    <if test="param.regionId != null and param.regionId != ''">
      and rbds.city in (${param.regionId})
    </if>
    group by
      rbds.`cycle` ,
      rbds.city ,
      rbds.dot_id
    order by rbds.create_time desc
  </select>


  <select id="selectListByRecords" resultType="com.bonc.rrs.workManager.entity.BrandDotScoreBean">
    select
      *
    from
      rrs_brand_dot_score
    where
        (`cycle` , dot_id , city) in
        <foreach collection="records" item="record" separator="," open="(" close=")" >
          (#{record.cycle}, #{record.dot_id}, #{record.city})
        </foreach>
  </select>
  <select id="queryBrandDotScoreReport" resultType="com.bonc.rrs.workManager.entity.BrandDotScoreExport">
    select
      rbds.`cycle` ,
      di.dot_name ,
      concat(br2.name, br.name) as regionName,
      rbds.brand_name ,
      rbds.performance ,
      rbds.service_ability ,
      rbds.added_performance ,
      rbds.mission_critical ,
      rbds.deduct ,
      rbds.score
    from
      rrs_brand_dot_score rbds
        left join dot_information di on
        rbds.dot_id = di.dot_id
        left join biz_region br on
        rbds.city = br.id
        left join biz_region br2 on
        br.pid = br2.id
    where
      rbds.delete_state = 0
    <if test="param.cycle != null and param.cycle != ''">
      and rbds.`cycle` = #{param.cycle}
    </if>
    <if test="param.brandId != null">
      and rbds.brand_id = #{param.brandId}
    </if>
    <if test="param.dotId != null">
      and rbds.dot_id = #{param.dotId}
    </if>
    <if test="param.regionId != null and param.regionId != ''">
      and rbds.city in (${param.regionId})
    </if>
    order by rbds.dot_id , rbds.city , rbds.brand_id
  </select>

</mapper>