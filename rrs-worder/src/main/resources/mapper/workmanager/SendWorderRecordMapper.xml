<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.SendWorderRecordMapper">

    <select id="selectCycleDotSendCount" resultType="java.util.Map">
        select
        swr.accept_worder_user as dotId,
        count(swr.accept_worder_user) as sendCount
        from send_worder_record swr
        inner join biz_region br on swr.area_id = br.id
        where swr.send_worder_type = 2
        and swr.operation_type = 1
        and send_worder_overflow = 0
        and swr.delete_state = 0
        and swr.create_time >= #{cycleTime}
        and swr.accept_worder_user in
        <foreach collection="dotIdList" item="dotId" open="(" close=")" separator=",">
            #{dotId}
        </foreach>
        and br.regcode in ${regcode}
        and swr.brand_id = #{brandId}
        group by swr.accept_worder_user
    </select>

    <select id="selectCycleDotSendList" resultType="com.bonc.rrs.workManager.entity.SendWorderRecord">
        select
            swr.*
        from send_worder_record swr
            inner join biz_region br on swr.area_id = br.id
        where swr.send_worder_type = 2
            and swr.operation_type = #{operationType}
            and send_worder_overflow = 0
            and swr.delete_state = 0
            and swr.create_time >= #{cycleTime}
            and swr.accept_worder_user in
            <foreach collection="dotIdList" item="dotId" open="(" close=")" separator=",">
                #{dotId}
            </foreach>
            and swr.city_id = ${areaId}
            and swr.brand_id = #{brandId}
    </select>


    <select id="selectCycleDotSendListByArea" resultType="com.bonc.rrs.workManager.entity.SendWorderRecord">
        select
        swr.*
        from send_worder_record swr
        inner join biz_region br on swr.area_id = br.id
        left join  worder_information wi on wi.worder_id = swr.worder_id
        where swr.send_worder_type = 2
        and swr.operation_type = #{operationType}
        and send_worder_overflow = 0
        and swr.delete_state = 0
        and wi.create_time >= #{cycleTime}
        and swr.accept_worder_user in
        <foreach collection="dotIdList" item="dotId" open="(" close=")" separator=",">
            #{dotId}
        </foreach>
        and wi.area_id = ${areaId}
        and swr.brand_id = #{brandId}
    </select>
</mapper>
