<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.SysDoloadMapper">
    <insert id="saveSysDoload" parameterType="com.bonc.rrs.workManager.entity.SysDoloadEntity" useGeneratedKeys="true" keyProperty="id">
        insert  into sys_download(name,apply_time,state,sys_file_id,uid,down_type)
        values(#{name},#{applyTime},#{state},#{sys_file_id},#{uid},#{downType})
    </insert>
    <update id="updateState" >
        update sys_download set state=#{state} where id=#{id}
    </update>
    <update id="updateSysFileId" >
        update sys_download set sys_file_id=#{sys_file_id} where id=#{id}
    </update>
    <select id="listSysDoload" resultType="com.bonc.rrs.workManager.entity.SysDoloadEntity">
        select id,`name`,apply_time as applyTime,state,down_time as downTime,down_type as downType from sys_download
        where uid=#{uid} and (down_type=2 or down_type=3)
           <if test="startDate!='' and startDate!=null and endDate!='' and endDate!=null">
               and apply_time between #{startDate} and #{endDate}
           </if>
        order by apply_time desc

    </select>
    <select id="getUrl" resultType="java.lang.String">
        select f.path from sys_download s inner join sys_file f on s.sys_file_id=f.file_id where s.id=#{id}
    </select>
    <update id="updateTime" parameterType="com.bonc.rrs.workManager.entity.SysDoloadEntity">
       update sys_download set down_time=#{downTime} where id=#{id}
    </update>
    <select id="findByState" resultType="java.util.HashMap">
        select  sd.id,sd.state
        from (
        select  a.field_name,
        a.field_id,
        a.field_value,
        a.id,
        substring_index(substring_index(a.field_value, ',', b.help_topic_id + 1), ',', -1) name
        from worder_ext_field a
        join
        mysql.help_topic b
        on b.help_topic_id &lt; (length(a.field_value) - length(replace(a.field_value, ',', '')) + 1)
        where a.id in (
        select w.id
        from sys_file s
        inner join worder_ext_field w on s.file_id = w.field_value
        where a.worder_no = #{worderNo}
        )

        and a.id in (select max(id) from  worder_ext_field where worder_no=#{worderNo} group by field_name,field_id)
        ) temp
        inner join sys_file s on temp.name = s.file_id
        inner join ext_field e on temp.field_id = e.field_id
        inner join sys_download sd on s.file_id = sd.sys_file_id
        where e.field_id=#{fileId}  order by sd.id desc
    </select>
    <select id="findBySysFile" resultType="java.util.HashMap">
        select * from sys_file where file_id in (select  substring_index(substring_index(a.field_value, ',', b.help_topic_id + 1), ',', -1) name
        from worder_ext_field a
        join
        mysql.help_topic b
        on b.help_topic_id &lt; (length(a.field_value) - length(replace(a.field_value, ',', '')) + 1)
        where worder_no=#{worderNo})
    </select>


</mapper>