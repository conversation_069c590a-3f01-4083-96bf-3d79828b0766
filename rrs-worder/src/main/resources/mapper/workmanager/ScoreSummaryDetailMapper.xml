<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.workManager.dao.RrsScoreSummaryDetailMapper">

    <select id="queryScoreReal" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
        select
        max(t0.id) as indicator_id,
        max(t0.indicator_desc) as title,
        max(t0.unit) as unit,
        max(t0.num_type) as num_type,
        count(1) as total,
        if(t0.num_type = 0 ,
            ROUND(sum(if(t1.is_rule >= 1, 1, 0)), 4) ,
            ROUND(sum(if(t1.duration >= 1, t1.duration, 0)), 2)
        ) as totalScore,
        <if test="flags == 0">
            t1.regione as name,
        </if>
        <if test="flags == 1">
            SUBSTRING(max(t3.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 2">
            SUBSTRING(max(t3.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 3">
            max(t2.name) as name,
        </if>
        if(t0.num_type = 0 ,
            ROUND(if(count(1) = 0, 0, sum(if(t1.is_rule = 1, 1, 0)) / count(1)), 4) ,
            ROUND(if(count(1) = 0, 0, sum(if(t1.duration >= 1, t1.duration, 0)) / count(1)), 2)
        ) as score
        from
        brand_per_evaluation t0 ,
        rrs_score_summary_detail t1
        <if test="flags == 3">
            left join biz_region t2 on t1.province_id = t2.id
        </if>
        <if test="flags == 1 or flags == 2">
            left join dot_information t3 on t1.dot_id = t3.dot_id and t3.is_delete = 0
        </if>
        where
        t0.id = t1.type
        and t1.query_time >= concat(#{param.startDate},' 00:00:00')
        and t1.query_time &lt;= concat(#{param.endDate},' 23:59:59')
        <foreach collection="param.brandIds" item="brandId" open=" and t1.brand_id in (" close=")" separator=",">
            #{brandId}
        </foreach>
        and t0.delete_state = 0
        <if test="flags == 1">
            and t1.city_id = #{param.city}
        </if>
        <if test="flags == 2">
            and t1.province_id = #{param.province}
        </if>
        <if test="flags == 3">
            and t1.regione = #{param.regione}
        </if>
        group by
        t1.type,
        <if test="flags == 0">
            t1.regione
        </if>
        <if test="flags == 1">
            t1.dot_id
        </if>
        <if test="flags == 2">
            t1.dot_id
        </if>
        <if test="flags == 3">
            t1.province_id
        </if>
    </select>

    <select id="queryScoreExport" resultType="com.bonc.rrs.workManager.entity.vo.DotBrandScoreExport">
        select
            max(bpe.indicator_desc) as indicator,
            max(b.brand_name) as brandName,
            max(di.dot_name) as dotName,
            group_concat(distinct br.name) as cityName,
            if(bpe.num_type = 0 ,
               sum(if(rssd.is_rule = 1, 1, 0)) ,
               sum(if(rssd.duration >= 1, rssd.duration, 0))
                ) as orders,
            count(1) as ordersCount,
            if(bpe.num_type = 0 ,
               concat(ROUND(if(count(1) = 0, 0, sum(if(rssd.is_rule = 1, 1, 0)) / count(1)), 2) * 100 , '%') ,
               ROUND(if(count(1) = 0, 0, sum(if(rssd.duration >= 1, rssd.duration, 0)) / count(1)), 2)
                ) as performance,
            bpe.unit,
        concat(if(STR_TO_DATE(#{param.startDate}, '%Y-%m-%d') >= min(rssd.start_date) , #{param.startDate},rssd.start_date), '~', if(STR_TO_DATE(#{param.endDate}, '%Y-%m-%d') &lt;= max(rssd.end_date)  , #{param.endDate},rssd.end_date)) as serviceAbility
        from
            rrs_score_summary_detail rssd ,
            dot_information di ,
            brand_per_evaluation bpe ,
            brand b ,
            biz_region br
        where
            rssd.dot_id = di.dot_id
          and rssd.brand_id = b.id
          and rssd.city_id = br.id
          and rssd.`type` = bpe.id
          and di.is_delete = 0
          and rssd.query_time >= concat(#{param.startDate},' 00:00:00')
          and rssd.query_time &lt;= concat(#{param.endDate},' 23:59:59')
        <foreach collection="param.brandIds" item="brandId" open=" and rssd.brand_id in (" close=")" separator=",">
            ${brandId}
        </foreach>
        <if test="param.city != null">
            and rssd.city_id = #{param.city}
        </if>
        <if test="param.province != null">
            and rssd.province_id = #{param.province}
        </if>
        <if test="param.regione != null">
            and rssd.regione = #{param.regione}
        </if>
        group by
            rssd.`type` ,
            rssd.dot_id ;
    </select>
</mapper>