<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.RemarkLogMapper">

    <insert id="addOperationLog">
        insert into worder_remark_log (worder_no, title, content, user_id, create_time)
        value (#{worderNo}, #{title}, #{content}, #{userId}, current_timestamp)
    </insert>

    <update id="updateWordeInfoState">
        update worder_information set survey_status=#{status},
        survey_status_value=#{statusValue}, worder_exec_status=#{execStatus},
        worder_exec_status_value=#{execValue}, `worder_status`= #{worderStatus},
        <if test="worderStatus == 5">
            `worder_status_value`='勘测结单',
            worder_set_status = 0,
            worder_set_status_value = '工单待计算',
        </if>
        <if test="worderStatus == 6">
            worder_status_value = '取消服务',
            worder_set_status = null,
            worder_set_status_value = '',
            worder_Incre_status = null,
            worder_Incre_status_value = '',
            worder_exci_status = null,
            worder_exci_status_value = '',
        </if>
        worder_finish_time = current_timestamp
        where worder_id=#{id}
    </update>

</mapper>