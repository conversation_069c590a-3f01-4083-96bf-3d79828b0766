<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.SendOrdersMapper">

    <select id="selectOrderArea" resultType="java.lang.Integer">
        SELECT area_id FROM worder_information WHERE worder_no=#{worderNo}
    </select>

    <select id="selectDotInfo" resultType="java.util.Map">
        SELECT dot_id as dotId, dot_star as dotStar, dot_name as dotName, radiate_area as radiateArea
        FROM dot_information WHERE dot_id in
        <foreach item="item" index="index" collection="dotId" open="(" separator="," close=")">
            #{item.dotId}
        </foreach> and
        dot_state=1 and is_delete=0
    </select>

    <update id="updataOrderInfo">
        UPDATE worder_information set candidate_branch=#{dotName},candidate_attendant ='', worder_exec_status=#{status},dot_id=#{dotId},service_id = null,
        worder_exec_status_value=#{record}, `worder_status`=0, `worder_status_value`='分配中'
        WHERE worder_no=#{worderNo}
    </update>

    <update id="updateOrder">
        UPDATE worder_information set candidate_branch=#{dotName},candidate_attendant ='',dot_id=#{dotId},
        service_id = null
        WHERE worder_no=#{worderNo}
    </update>

    <select id="selectServiceArmlist" resultType="java.util.Map">
        SELECT id as id, `name` as name FROM biz_attendant WHERE attendant_state = 1 and attendant_flag='1' and dot_id=#{branchId}
        and technician_level in
        <foreach item="technicianLevelId" index="index" collection="technicianLevelList" open="(" separator="," close=")">
            #{technicianLevelId}
        </foreach>
    </select>

    <select id="selectWorderServiceId" resultType="java.lang.Integer">
        SELECT dot_id FROM worder_information WHERE worder_no=#{worderNo}
    </select>

    <update id="updateOrderServiceInfo">
        UPDATE worder_information set candidate_attendant=#{serviceName}, service_id=#{serviceId},
        worder_exec_status_value=#{record}, worder_status=#{majorState}, worder_status_value=#{majorValue},
        worder_exec_status=#{status} WHERE worder_no=#{worderNo}
    </update>

    <update id="updateServiceInfo">
      UPDATE worder_information set candidate_attendant=#{serviceName}, service_id=#{serviceId}
      WHERE worder_no=#{worderNo}
    </update>

    <select id="selectOrderDotId" resultType="java.lang.Integer">
        SELECT dot_id FROM worder_information WHERE worder_no=#{worderNo}
    </select>

    <select id="selectOperatRecord" resultType="java.util.Map">
        select create_time as creatTime, record as record
        from worder_operation_record
        where worder_no=#{worderNo}
        ORDER by create_time asc
    </select>

    <select id="selectDotIdByAreaId" resultType="java.util.Map">
        select dot_id as dotId from dot_area where area_id=#{areaId}
    </select>

    <select id="selectTemplateId" resultType="java.lang.Integer" >
        select template_id from  worder_information
        where worder_no=#{worderNo}
    </select>

    <select id="selectBrandId" resultType="java.lang.Integer">
        select brand_id from worder_template
        where id=#{templateId}
    </select>

    <select id="selectMangerId" resultType="java.lang.Integer">
        SELECT worder_exec_status FROM worder_information WHERE worder_no=#{worderNo}
    </select>

    <select id="selectBrandIdByWorderNo" resultType="java.lang.Integer">
        select brand_id
        from worder_information wi
                 inner join worder_template wt on
            wi.template_id = wt.id
        where wi.worder_no = #{worderNo}
    </select>

    <select id="selectAutoSendByWorderNo" resultType="java.lang.Integer">
        select
            wt.auto_send
        from
            worder_template wt
        inner join worder_information wi on
            wt.id = wi.template_id
        where
            wi.worder_id = #{worderId}
    </select>
    <select id="selectBrandTechnicianLevelByWorderNo" resultType="string">
        select
            b.technician_level
        from
            worder_information wi
        inner join worder_template wt on
            wi.template_id = wt.id
        inner join brand b on
            wt.brand_id = b.id
        where
            wi.worder_no = #{worderNo}
    </select>
</mapper>