<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.AutidOrderMapper">

    <select id="selectFieldValue" resultType="java.util.Map">
        SELECT b.field_name   as fieldName,
               c.field_value  as fieldValue,
               c.field_value  as valueUp,
               b.is_nessary   as isNessary,
               c.field_id     as fieldId,
               filed_dic_key  AS `key`,
               c.check_status as checkStatus,
               b.field_type as fieldType,
               sf.`path` as imgPath,
               replace(b.field_desc,char(10),'\n') as remake
        FROM (select dd.*
            from worder_ext_field dd,
                (select max(wef.id) as id, wef.field_id, ef.field_name, wef.worder_no
                from worder_ext_field wef
                left join ext_field ef on wef.field_id = ef.field_id
                    where worder_no = #{worderNo}
                    group by field_id, field_name, worder_no) f
            where f.id = dd.id) c
            inner join ext_field b on c.field_id = b.field_id
            left join sys_file sf on b.file_sample =sf.file_id
        WHERE worder_no = #{worderNo}
          and b.field_purpose = #{purpose}
          and b.field_type = #{type}
    </select>

    <select id="getFieldIds" resultType="java.lang.Integer">
        select e.field_id as fieldId
        from worder_ext_field e
                 left join ext_field f on e.field_id = f.field_id
        where e.worder_no = #{worderNo}
          and f.field_purpose = #{purpose}
    </select>

    <update id="updateWorderFieldState">
        UPDATE worder_ext_field as b, ext_field as `c`
        set check_status=#{state}
        WHERE worder_no=#{worderNo}
          and
            b.field_id= c.field_id
          and
            c.field_purpose=#{purpose}
    </update>

    <update id="updateBatchWorderField">
        update worder_ext_field as b, ext_field as `c`
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="check_status = case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.fieldId!=null">
                        when b.field_id=#{i.fieldId} then #{i.state}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        c.field_purpose=#{purpose} and b.worder_no=#{worderNo}
    </update>

    <update id="updateFieldStatus">
        update worder_ext_field
        set check_status = #{checkStatus}
        where worder_no = #{worderNo}
          and field_id = #{fieldId}
    </update>

    <select id="selectWorderFieldInfolist" resultType="java.util.Map">
        SELECT b.filed_dic_key   as `key`,
               b.field_name      as `name`,
               d.detail_name,
               b.is_nessary      as isNessary,
               c.field_id        as fieldId,
               c.field_value     as fieldValue,
               c.field_value_dup as valueDup,
               d.detail_number   as `number`
        FROM ext_field as b,
             worder_ext_field as `c`,
             sys_dictionary_detail as d
        WHERE c.worder_no = #{worderNo}
          and c.field_id = b.field_id
          and b.field_type = #{fieldType}
          and d.dictionary_id = b.filed_dic_key
          and c.field_id = #{fieldId}
    </select>

    <select id="selectOddAndMuchField" resultType="java.util.Map">
        select e.filed_dic_key as `key`,
               w.field_name    as `name`,
               e.is_nessary    as isNessary,
               w.field_id      as fieldId,
               w.field_value   as fieldValue,
               w.field_value   as valueDup
        from (
                 SELECT dd.*
                 FROM worder_ext_field dd,
                      (
                          SELECT max(id) AS id,
                                 field_id,
                                 field_name,
                                 worder_no
                          FROM worder_ext_field
                          where worder_no = #{worderNo}
                          GROUP BY field_id,
                                   field_name,
                                   worder_no
                      ) f
                 WHERE f.id = dd.id) w
                 join ext_field e on w.field_id = e.field_id
        where w.worder_no = #{worderNo}
          and e.field_type = #{fieldType}
          and w.field_id = #{fieldId}
    </select>

    <select id="getSelectData" resultType="java.lang.String">
        select select_data
        from ext_field
        where field_id = #{fieldId}
          and field_type = #{fieldType}
    </select>

    <select id="selectDictInfo" resultType="java.util.Map">
        select detail_name as nameValue, detail_number as member, dictionary_id as id
        from sys_dictionary_detail
        where dictionary_id = #{key}
    </select>

    <update id="updateAppointInstall">
        update worder_information
        set worder_exec_status=#{status},
            worder_exec_status_value=#{statusValue}
        where worder_id = #{id}
    </update>

    <update id="updateWorderAutidInfo">
        update worder_ext_field as b, ext_field as c
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="field_value = case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.fieldId!=null">
                        when b.field_id=#{i.fieldId} then #{i.fieldValue}
                    </if>
                </foreach>
            </trim>
            <!--<trim prefix="field_value_dup = case" suffix="end,">-->
            <!--<foreach collection="list" item="i" index="index">-->
            <!--<if test="i.fieldId!=null">-->
            <!--when b.field_id=#{i.fieldId} then #{i.valueDup}-->
            <!--</if>-->
            <!--</foreach>-->
            <!--</trim>-->
        </trim>
        where
        b.worder_no=#{worderNo} and c.field_purpose=#{purpose}
    </update>

    <update id="updateAutidInfo">
        update worder_ext_field
        set field_value = #{fieldValue}
        where worder_no = #{worderNo}
          and field_id = #{fieldId}
    </update>
    <select id="fandByValue" resultType="java.util.HashMap">
        select e.field_value
        from worder_ext_field e
                 inner join ext_field f on e.field_id = f.field_id
        where e.worder_no = #{worderNo}
          and f.field_id = #{fieldId} order by e.id desc
    </select>

    <!--查询工单的执行状态-->
    <select id="getWorderExecStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        select worder_exec_status
        from worder_information
        where worder_no = #{worderNo}
    </select>

    <!--查询工单状态-->
    <select id="getWorderStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        select worder_status
        from worder_information
        where worder_no = #{worderNo}
    </select>

    <select id="getClientName" parameterType="java.lang.String" resultType="java.lang.String">
        select user_name as clientName
        from worder_information
        where worder_no = #{worderNo}
    </select>

    <select id="getWorderField" resultType="java.util.Map">
        select w.worder_no as worderNo, w.field_id as fieldId, w.field_name as fieldName, w.field_value as fieldValue
        from worder_ext_field w
                 left join ext_field e on w.field_id = e.field_id
        where w.worder_no = #{worderNo}
          and e.field_purpose = #{purpose}
          and w.field_id = #{fieldId}
          and w.id = (SELECT MAX(f.id) fId
                      from worder_ext_field f
                      WHERE w.worder_no = f.worder_no
                        and f.field_id = w.field_id)
    </select>
    <select id="getWorderFieldName" resultType="java.util.Map">
        select w.worder_no   as worderNo,
               w.field_id    as fieldId,
               w.field_name  as fieldName,
               w.field_value as fieldValue,
               s.old_name
        from worder_ext_field w
                 left join ext_field e on w.field_id = e.field_id
                 inner join sys_file s on w.field_value = s.file_id
        where w.worder_no = #{worderNo}
          and e.field_purpose = #{purpose}
          and w.field_id = #{fieldId}
          and w.id = (SELECT MAX(f.id) fId
                      from worder_ext_field f
                      WHERE w.worder_no = f.worder_no
                        and f.field_id = w.field_id)
    </select>

    <select id="isSelfWiringByWorderNo" parameterType="string" resultType="integer">
        select
            count(id)
        from
            worder_ext_field wef
        where
            wef.field_id = 1262
            and field_value = '用户自布线'
            and worder_no = #{worderNo}
    </select>

    <select id="queryAddMaterialConfig" parameterType="integer" resultType="map">
        select
            mttomi.materiel_id,
            wia.attribute_value as use_num
        from
            worder_information_attribute wia
        inner join (
            select
                sdd.detail_number as materiel_type_id, sdd.detail_name as materiel_id
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'added_material_type_to_material_id') mttomi on
            wia.attribute_code = mttomi.materiel_type_id
        where
            wia.`attribute` = 'addedMaterialType'
            and wia.is_delete = 0
            and wia.worder_id = #{worderId}
    </select>

    <select id="queryUsedAddedMaterielByWorderId" parameterType="integer" resultType="com.bonc.rrs.workManager.entity.vo.UsedMaterielVo">
        select
            mttomi.materiel_id ,
            sum(wum.num) used_num
        from
            worder_used_materiel wum
        inner join materiel_information mi on
            wum.materiel_id = mi.id
        inner join worder_information_attribute wia on
            wum.worder_id = wia.worder_id
            and mi.materiel_type_id = wia.attribute_code
            and wia.`attribute` = 'addedMaterialType'
            and wia.is_delete = 0
        inner join (
            select
                cast(sdd.detail_number as unsigned INTEGER) as materiel_type_id, cast(sdd.detail_name as unsigned INTEGER) as materiel_id
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'added_material_type_to_material_id') mttomi on
            mi.materiel_type_id = mttomi.materiel_type_id
        where
            wum.worder_id = #{worderId}
        group by mttomi.materiel_id
    </select>

    <select id="queryDotPositionAddedMaterielStock" parameterType="integer" resultType="com.bonc.rrs.workManager.entity.vo.DotPositionAddedMaterielStockVo">
        select
            rssg.goods_id,
            rssg.real_goods_total,
            rssg.store_id,
            rssg.store_position_id
        from
            rrs_store_stock_goods rssg
        inner join rrs_store_position_info rspi on
            rssg.store_position_id = rspi.id
        inner join rrs_store_basic_info rsbi on
            rspi.store_id = rsbi.id
        inner join rrs_user_store_relation rusr on
            rusr.store_id = rsbi.id
        inner join sys_user su on
            rusr.user_id = su.user_id
        inner join dot_contacts dc on
            su.username = dc.contacts_name
        inner join dot_information di on
            di.dot_code = dc.dot_code
        where
            rspi.`type` = 1
            and di.dot_id = #{dotId}
            and rssg.goods_type = 4
            and rssg.goods_id in
            <foreach item="v" index="index" collection="materielIdList" open="(" separator="," close=")">
                #{v}
            </foreach>


    </select>

    <select id="getOutMoveCodeAndTime" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nextval_safe('storage_out_create_no',#{currDate})
    </select>

    <select id="getReceiverInfo" resultType="com.bonc.rrs.workManager.entity.vo.ReceiverInfoVo">
        SELECT user_name   userName,
               user_phone  userPhone,
               address_dup addressDup
        FROM worder_information
        WHERE worder_id = #{worderId}
    </select>

    <update id="cancelLeaveInfo">
        update rrs_enter_leave_info set status = '-1' where enter_code = #{leaveNum}
    </update>
    <update id="cancelLeaveOrder">
        update rrs_goods_leave_order set go_status = '-1', update_time = now() where leave_num = #{leaveNum}
    </update>
</mapper>