<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bonc.rrs.workManager.dao.WorkMsgDao">

    <select id="selectMangerlist" resultType="java.util.Map">
        select b.name as username,s.user_id as userId
        from sys_user_employee s
        left join biz_employee b on s.employee_id = b.id
        where s.user_id in
        <foreach collection="roleIdlist" item="areaCode" index="index" open="(" close=")" separator=",">
            #{areaCode}
        </foreach>
    </select>

    <update id="updataOrderMager" parameterType="java.util.Map" >
        UPDATE worder_information SET candidate_pm=#{candidatepm},
        worder_exec_status=#{status}, modify_time=NOW(),pm_id=#{pmId},
        worder_exec_status_value=#{record},candidate_branch=null,candidate_attendant=null,
        dot_id=null,service_id=null
        WHERE worder_no=#{worderNo}
    </update>

    <update id="updataManager" parameterType="java.util.Map" >
        UPDATE worder_information SET candidate_pm=#{candidatepm},
        modify_time=NOW(),pm_id=#{pmId}
        WHERE worder_no=#{worderNo}
    </update>

    <update id="flowUpdataOrderMager" parameterType="java.util.Map" >
        UPDATE worder_information SET candidate_pm=#{candidatepm},
        modify_time=NOW(),pm_id=#{pmId}
        WHERE worder_no=#{worderNo}
    </update>

    <insert id="insertOperation" parameterType="com.bonc.rrs.workManager.entity.OperationRecord">
        insert into worder_operation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="operationUser != null">
                operation_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="record != null">
                record,
            </if>
            <if test="affectedUserId != null">
                affected_user_id,
            </if>
            <if test="affectedUser != null">
                affected_user,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="worderNo != null">
                worder_no,
            </if>
            <if test="acceptSend != null">
                accept_send,
            </if>
            <if test="worderStatus != null">
                worder_status,
            </if>
            <if test="worderExecStatus != null">
                worder_exec_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="operationUser != null">
                #{operationUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="record != null">
                #{record,jdbcType=VARCHAR},
            </if>
            <if test="affectedUserId != null">
                #{affectedUserId,jdbcType=BIGINT},
            </if>
            <if test="affectedUser != null">
                #{affectedUser,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="worderNo != null">
                #{worderNo,jdbcType=VARCHAR},
            </if>
            <if test="acceptSend != null">
                #{acceptSend,jdbcType=INTEGER},
            </if>
            <if test="worderStatus != null">
                #{worderStatus},
            </if>
            <if test="worderExecStatus != null">
                #{worderExecStatus},
            </if>
        </trim>
    </insert>

    <select id="selectWorderOperateListByMap" parameterType="java.util.Map" resultType="com.bonc.rrs.workManager.entity.OperationRecord">
        select worder_no worderNo, create_time createTime
        from worder_operation_record
        where 1=1
        <if test="worderStatus != null and worderStatus != ''">
            and worder_status = #{worderStatus}
        </if>
        <if test="worderExecStatus != null and worderExecStatus != ''">
            and worder_exec_status = #{worderExecStatus}
        </if>
        <if test="worderNo != null and worderNo != ''">
            and worder_no = #{worderNo}
        </if>
    </select>

    <select id="selectContactlist" resultType="java.util.Map">
        SELECT `affected_user` AS affectedUser,
        `affected_user_id` as affectId
        FROM `worder_frequent_contacts`
        WHERE `username`=#{username}
        ORDER BY `affected_nums` DESC
    </select>

    <select id="getContactNum" resultType="java.util.Map">
        SELECT `affected_nums` as nums
        FROM `worder_frequent_contacts`
        WHERE `username`=#{username}
        AND `affected_user`=#{affectName}
    </select>

    <update id="updataContactNum" >
        UPDATE `worder_frequent_contacts`
        SET `affected_nums`=#{affectNum}, `gmt_modified`=CURRENT_TIMESTAMP
        WHERE `username`=#{username} AND `affected_user`=#{affectName}
    </update>

    <insert id="addContactAffect" >
        INSERT `worder_frequent_contacts`
        (`username`,`affected_user_id`, `affected_user`, `type`, `affected_nums`,`gmt_create`)
        VALUE (#{username},#{affectUserId},#{affectUser},1,1,current_timestamp)
    </insert>

    <select id="getContactCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `worder_frequent_contacts`
        WHERE `username`=#{username}
        AND `affected_user`=#{affectName}
    </select>

    <select id="getRoleName" resultType="java.lang.String">
        SELECT ifnull(`role_name`,'') FROM `sys_user` WHERE `username`=#{username}
    </select>

    <select id="getManagerCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM `worder_operation_record`
        WHERE `operation_user`=#{username} AND `type`=#{type}
        AND `worder_no`=#{worderno}
    </select>

    <select id="selectIndustryName" resultType="java.util.Map">
        SELECT branch_name AS branchName FROM `industry_trade _info`
        WHERE 1=1
        <if test="name != null">
           and `branch_name` LIKE CONCAT('%',#{name},'%')
        </if>
        GROUP BY `branch_name`
    </select>

    <select id="listIndustryRegionIds" resultType="java.lang.Integer" >
        SELECT b.id FROM `industry_trade _info` a
		left join biz_region b on a.internat_code = b.code
        WHERE a.`branch_name` = #{brachName}
    </select>

    <update id="updateFilePath">
        UPDATE dot_information set contract_end=#{contractEnd},
        contract_url=#{filePath} WHERE dot_id=#{id}
    </update>

    <select id="selectUserId" resultType="java.lang.Integer">
        select user_id from sys_user where username=#{username}
    </select>

    <select id="selectRoleId" resultType="java.lang.Integer">
        select role_id from sys_user_role where user_id=#{userId}
    </select>

    <select id="selectUserIdlist" resultType="java.lang.Integer">
        SELECT user_id FROM sys_user_role WHERE role_id=#{roleId}
    </select>

    <select id="selectUserAreaId" resultType="java.lang.Integer">
        SELECT `area_id` FROM `manager_area_id` WHERE `user_id`=#{userId}
    </select>

    <select id="selectAreaUserIdlist" resultType="java.lang.Integer">
        select user_id from manager_area_id where area_id=#{areaId}
    </select>

    <select id="selectUserNameById" resultType="java.lang.String">
        select username from sys_user where user_id=#{id}
    </select>

    <select id="selectAreaName" resultType="java.lang.String">
        SELECT `name` FROM `biz_region` WHERE `id`=#{id}
    </select>

    <select id="selectBranchCode" resultType="java.lang.String">
        SELECT DISTINCT i.branch_code AS branch_code FROM `industry_trade _info` i where i.branch_name = #{branchName}
    </select>

    <select id="getSuperviseByWorderNo" resultType="com.bonc.rrs.supervise.entity.SuperviseInfomation">
        select si.* from supervise_infomation si
        <where>
            si.is_delete = 0
            and si.worder_no = #{worderNo}
            and si.state != 3
        </where>

    </select>

    <update id="userSuperviseTransfer">
        update supervise_infomation si
        <set>
            si.duty_peo = #{toUserId}
        </set>
        <where>
            si.state != 3
            and si.is_delete = 0
            and si.duty_peo = #{fromUserId}
            and si.worder_no = #{worderNo}
        </where>
    </update>

    <update id="userSuperviseTeleTransfer">
        update supervise_infomation si,supervise_tele_record str
        <set>
            str.duty_peo = #{toUserId}
        </set>
        <where>
            str.supervise_id = si.id
            and si.duty_peo = #{fromUserId}
            and si.is_delete = 0
            and si.state != 3
            and str.is_delete = 0
            and si.worder_no = #{worderNo}
        </where>
    </update>

    <insert id="insertSuperviseOperationRecord">
        INSERT INTO rrs_pro.supervise_operation_record
        (supervise_id, user_id, user_name, operation_type, optration_content, duty_peo, related_peo)
        VALUES(#{superviseId}, #{userId}, #{userName}, 6, #{optrationContent}, #{dutyPeo}, #{relatedPeo});

    </insert>

</mapper>