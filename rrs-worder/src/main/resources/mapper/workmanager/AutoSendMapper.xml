<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.AutoSendMapper">

    <select id="selectUserId" resultType="java.lang.Integer">
        select user_id from manager_area_id
        where area_id=#{areaId}
        and brand_id=#{brandId}
    </select>

    <select id="selectUsername" resultType="java.lang.String">
        select b.name as username
        from sys_user s
        left join sys_user_employee e on s.user_id = e.user_id
        left join biz_employee b on e.employee_id = b.id
        where s.user_id=#{userId}
    </select>

    <insert id="addNewStar" parameterType="com.bonc.rrs.workManager.entity.DotStarDesign">
        insert into dot_star_design
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="starName != null">
                star_name,
            </if>
            <if test="starMax != null">
                star_max,
            </if>
            <if test="starMin != null">
                star_min,
            </if>
            <if test="settleAccount != null">
                settle_account,
            </if>
            <if test="countPeriod != null">
                count_period,
            </if>
            <if test="sendScale != null">
                send_scale,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="starName != null">
                #{starName,jdbcType=VARCHAR},
            </if>
            <if test="starMax != null">
                #{starMax,jdbcType=INTEGER},
            </if>
            <if test="starMin != null">
                #{starMin,jdbcType=INTEGER},
            </if>
            <if test="settleAccount != null">
                #{settleAccount,jdbcType=VARCHAR},
            </if>
            <if test="countPeriod != null">
                #{countPeriod,jdbcType=VARCHAR},
            </if>
            <if test="sendScale != null">
                #{sendScale,jdbcType=REAL},
            </if>
        </trim>
    </insert>

    <update id="updataNewStar" parameterType="com.bonc.rrs.workManager.entity.DotStarDesign">
        update dot_star_design
        <set>
            <if test="starName != null">
                star_name = #{starName,jdbcType=VARCHAR},
            </if>
            <if test="starMax != null">
                star_max = #{starMax,jdbcType=INTEGER},
            </if>
            <if test="starMin != null">
                star_min = #{starMin,jdbcType=INTEGER},
            </if>
            <if test="settleAccount != null">
                settle_account = #{settleAccount,jdbcType=VARCHAR},
            </if>
            <if test="countPeriod != null">
                count_period = #{countPeriod,jdbcType=VARCHAR},
            </if>
            <if test="sendScale != null">
                send_scale = #{sendScale,jdbcType=REAL},
            </if>
        </set>
        where star_name = #{starName,jdbcType=VARCHAR}
    </update>

    <select id="selectNewStarCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT count(1) FROM dot_star_design WHERE star_name=#{starName}
    </select>

    <update id="resetCountTime">
        UPDATE period_sum set sum=1, time_start=date_add(curdate(),interval - day(curdate())+1 day) WHERE id=1
    </update>

    <select id="selectStarMaxMin" resultType="java.util.Map">
        SELECT count_period as `count`, send_scale as scale, count_period as period FROM dot_star_design WHERE star_max &gt; #{dotScore} and star_min &lt;= #{dotScore}
    </select>

    <select id="selectDotScoreByAreaId" resultType="java.util.Map">
        SELECT dot_score as dotScore, dot_id as dotId, dot_name as dotName, `count` as `count`
        FROM dot_information WHERE area_id=#{areaId} ORDER BY dot_score desc
    </select>

    <select id="selectPeriodSumInfo" resultType="java.util.Map">
        select `sum` as `sum`, time_start as timeStart from period_sum where `id`=#{id}
    </select>

    <update id="updateDotCount">
        update dot_information set `count`=#{count} where dot_id=#{dotId}
    </update>

    <update id="updataPeriodSumById">
        update period_sum set `sum`=#{sum} where id=#{id};
    </update>

    <update id="updateDotInformation">
        update dot_information set `count`=0;
    </update>

    <select id="selectTimeById" resultType="java.lang.Integer">
        select count_period from dot_star_design where id=#{id}
    </select>

    <select id="selectDotIdlist" resultType="java.lang.Integer">
        select dot_id from dot_area where area_id=#{areaId}
    </select>

    <select id="selectdotCountScorelist" resultType="java.util.Map">
        select
        dot_score as score, `count` as `count`,
        dot_name as `name`, dot_id as dotId
        from dot_information
        WHERE dot_id in
        <foreach collection="dotId" item="areaCode" index="index" open="(" close=")" separator=",">
            #{areaCode}
        </foreach>
        and dot_state=1 and is_delete=0
        order by dot_score desc
    </select>

    <update id="updateAutoSendStatus">
        UPDATE `sys_config` SET
        `status` = #{status} WHERE `param_key` = #{key}
    </update>
    <update id="updateWorderStatus">
        UPDATE worder_information wi
        left join flow_worder fw on wi.worder_id=fw.worder_id
        SET
        worder_exec_status = #{worderExecStatus} , fw.next_flow_child_code = #{code}

                                 WHERE wi.worder_id = #{worderId}
    </update>

    <select id="selectAutoSendStatus" resultType="java.lang.Integer">
        select status from sys_config where `param_key` = #{key}
    </select>

    <select id="selectAreaBrand" resultType="java.util.Map">
        select worder_id as worder_id, area_id as areaId, template_id as templateId , user_phone as userPhone, worder_type_id as worderType, postcode as postcode
        from `worder_information`
        where worder_no=#{worderNo}
        and is_delete = 0
    </select>

    <select id="selectBrandId" resultType="java.lang.Integer">
        select `brand_id` from `worder_template` where `id`=#{id}
    </select>
    <select id="selectCycleTime" resultType="java.util.Date">
        ${cycle}
    </select>

    <select id="selectAreaByUserAndAreaIds" resultType="java.lang.Integer">
        select
            distinct br.id
        from
            manager_area_id mai ,
            biz_region br
        where
            mai.area_id = br.id
          and mai.user_id = #{userId}
          and br.regcode in ${regionCodes}
        order by br.`type` asc limit 1
    </select>
    <select id="selectDotAreaByDotAndAreaIds" resultType="java.lang.Integer">
        select
            da.area_id
        from
            dot_area da ,
            biz_region br
        where
            da.area_id = br.id
          and da.dot_id = #{dotId}
          and br.regcode in ${regCode}
	      and da.is_delete = '0'
        order by br.`type` asc limit 1
    </select>
    <select id="selectWorderBySendStatus" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select * from worder_information
        where is_delete = 0 and worder_exec_status =#{worderExecStatus}
        and worder_type_id in (2,4,5,6)
          <if test="worderNo != null and worderNo != '' ">
              and worder_no like concat('%',#{worderNo},'%')
          </if>

        <if test="worderNo == null or worderNo == '' ">
           and create_time >= DATE_SUB(CURDATE(), INTERVAL ${day} DAY);
        </if>
    </select>

</mapper>