<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.DotApproveInfoMapper">


  <insert id="addDotAreaInfo" >
    insert into dot_area_approve
    (approve_id,dot_id, area_id, group_id)
    values
    <foreach collection ="arealist" item="reddemCode" index= "index" separator =",">
      (
      #{approveId},#{dotId}, #{reddemCode.areaId}, #{groupId}
      )
    </foreach >
  </insert>
  <insert id="addDotBrandInfo" >
    insert into dot_brand_approve
    (approve_id, dot_id, brand_id, group_id, date_time, service_type, child_group_id)
    values
    <foreach collection ="brandList" item="brand" index= "index" separator =",">
      (
      #{approveId}, #{dotId}, #{brand.brandId},#{brand.groupId},#{dateTime},#{brand.serviceType},#{brand.childGroupId}
      )
    </foreach >
  </insert>

  <insert id="addContact" parameterType="com.bonc.rrs.workManager.entity.DotContactsApprove">
    insert into dot_contacts_approve
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="approveId != null">
        approve_id,
      </if>
      <if test="contactsId != null">
        contacts_id,
      </if>
      <if test="dotCode != null">
        dot_code,
      </if>
      <if test="contactsName != null">
        contacts_name,
      </if>
      <if test="contactsPhone != null">
        contacts_phone,
      </if>
      <if test="contactsEmail != null">
        contacts_email,
      </if>
      create_time,
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="approveId != null">
        #{approveId,jdbcType=INTEGER},
      </if>
      <if test="contactsId != null">
        #{contactsId,jdbcType=INTEGER},
      </if>
      <if test="dotCode != null">
        #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="contactsName != null">
        #{contactsName,jdbcType=VARCHAR},
      </if>
      <if test="contactsPhone != null">
        #{contactsPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactsEmail != null">
        #{contactsEmail,jdbcType=VARCHAR},
      </if>
      current_timestamp ,
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <insert id="insertSelective"  parameterType="com.bonc.rrs.workManager.entity.DotInfoApprove">
    insert into dot_information_approve
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="approveId != null">
        approve_id,
      </if>
      <if test="dotId != null">
        dot_id,
      </if>
      <if test="dotStar != null">
        dot_star,
      </if>
      <if test="dotName != null">
        dot_name,
      </if>
      <if test="dotShortName != null">
        dot_short_name,
      </if>
      <if test="dotArea != null">
        dot_area,
      </if>
      <if test="dotCity != null">
        dot_city,
      </if>
      <if test="dotDistrict != null">
        dot_district,
      </if>
      <if test="dotAddress != null">
        dot_address,
      </if>
      <if test="dotCertificate != null">
        dot_certificate,
      </if>
      <if test="dotQuality != null">
        dot_quality,
      </if>
      <if test="dotPostcode != null">
        dot_postcode,
      </if>
      <if test="dotClass != null">
        dot_class,
      </if>
      <if test="dotFeatures != null">
        dot_features,
      </if>
      <if test="dotCreateTime != null">
        dot_create_time,
      </if>
      <if test="dotCreatePer != null">
        dot_create_per,
      </if>
      <if test="dotCode != null">
        dot_code,
      </if>
      <if test="marketLevel != null">
        market_level,
      </if>
      create_time,
      <if test="legalPersonName != null">
        legal_person_name,
      </if>
      <if test="legalPersonSex != null">
        legal_person_sex,
      </if>
      <if test="degreeEducation != null">
        degree_education,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="storageSpare != null">
        storage_spare,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="vehiclesNums != null">
        vehicles_nums,
      </if>
      <if test="contractStart != null">
        contract_start,
      </if>
      <if test="contractEnd != null">
        contract_end,
      </if>
      <if test="taxNo != null">
        tax_no,
      </if>
      <if test="companyQuality != null">
        company_quality,
      </if>
      <if test="registFund != null">
        regist_fund,
      </if>
      <if test="buseRegistTime != null">
        buse_regist_time,
      </if>
      <if test="taxRegistTime != null">
        tax_regist_time,
      </if>
      <if test="taxDeadline != null">
        tax_deadline,
      </if>
      <if test="industryDeadline != null">
        industry_deadline,
      </if>
      <if test="buseRegistNumber != null">
        buse_regist_number,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="taxPoint != null">
        tax_point,
      </if>
      <if test="radiateArea != null">
        radiate_area,
      </if>
      <if test="dotState != null">
        dot_state,
      </if>
      <if test="buseLicenseUrl != null">
        buse_license_url,
      </if>
      <if test="officeSpaceUrl != null">
        office_space_url,
      </if>
      <if test="sparesSpaceUrl != null">
        spares_space_url,
      </if>
      <if test="doorHeadUrl != null">
        door_head_url,
      </if>
      <if test="vehicleUrl != null">
        vehicle_url,
      </if>
      <if test="serviceAidUrl != null">
        service_aid_url,
      </if>
      <if test="consumableUrl != null">
        consumable_url,
      </if>
      <if test="basicDataUrl != null">
        basic_data_url,
      </if>
      <if test="houseContractUrl != null">
        house_contract_url,
      </if>
      <if test="corporateIdCard != null">
        corporate_id_card,
      </if>
      <if test="electLicense != null">
        elect_license,
      </if>
      <if test="aptitude != null">
        aptitude,
      </if>
      <if test="commonTaxPer != null">
        common_tax_per,
      </if>
      <if test="smallCarRays != null">
        small_car_rays,
      </if>
      <if test="commonCarNum != null">
        common_car_num,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="dotTelephone != null">
        dot_telephone,
      </if>
      <if test="dotEmail != null">
        dot_email,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="starId != null">
        star_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="vCode != null">
        v_code,
      </if>
      <if test="branchCode != null">
        branch_code,
      </if>
      <if test="branchName != null">
        branch_name,
      </if>
      <if test="dotBank != null">
        dot_bank,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="bankNumber != null">
        bank_number,
      </if>
      <if test="dotSignOut != null">
        dot_sign_out,
      </if>
      <if test="count">
        `count`,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="isAdvanceMoney != null">
        is_advance_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="approveId != null">
        #{approveId,jdbcType=INTEGER},
      </if>
      <if test="dotId != null">
        #{dotId,jdbcType=INTEGER},
      </if>
      <if test="dotStar != null">
        #{dotStar,jdbcType=VARCHAR},
      </if>
      <if test="dotName != null">
        #{dotName,jdbcType=VARCHAR},
      </if>
      <if test="dotShortName != null">
        #{dotShortName,jdbcType=VARCHAR},
      </if>
      <if test="dotArea != null">
        #{dotArea,jdbcType=VARCHAR},
      </if>
      <if test="dotCity != null">
        #{dotCity,jdbcType=VARCHAR},
      </if>
      <if test="dotDistrict != null">
        #{dotDistrict,jdbcType=VARCHAR},
      </if>
      <if test="dotAddress != null">
        #{dotAddress,jdbcType=VARCHAR},
      </if>
      <if test="dotCertificate != null">
        #{dotCertificate,jdbcType=VARCHAR},
      </if>
      <if test="dotQuality != null">
        #{dotQuality,jdbcType=VARCHAR},
      </if>
      <if test="dotPostcode != null">
        #{dotPostcode,jdbcType=VARCHAR},
      </if>
      <if test="dotClass != null">
        #{dotClass,jdbcType=VARCHAR},
      </if>
      <if test="dotFeatures != null">
        #{dotFeatures,jdbcType=VARCHAR},
      </if>
      <if test="dotCreateTime != null">
        #{dotCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dotCreatePer != null">
        #{dotCreatePer,jdbcType=VARCHAR},
      </if>
      <if test="dotCode != null">
        #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="marketLevel != null">
        #{marketLevel,jdbcType=VARCHAR},
      </if>
      current_timestamp ,
      <if test="legalPersonName != null">
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonSex != null">
        #{legalPersonSex,jdbcType=VARCHAR},
      </if>
      <if test="degreeEducation != null">
        #{degreeEducation,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="storageSpare != null">
        #{storageSpare,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehiclesNums != null">
        #{vehiclesNums,jdbcType=INTEGER},
      </if>
      <if test="contractStart != null">
        #{contractStart,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEnd != null">
        #{contractEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="taxNo != null">
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="companyQuality != null">
        #{companyQuality,jdbcType=VARCHAR},
      </if>
      <if test="registFund != null">
        #{registFund,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistTime != null">
        #{buseRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRegistTime != null">
        #{taxRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxDeadline != null">
        #{taxDeadline,jdbcType=VARCHAR},
      </if>
      <if test="industryDeadline != null">
        #{industryDeadline,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistNumber != null">
        #{buseRegistNumber,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxPoint != null">
        #{taxPoint,jdbcType=VARCHAR},
      </if>
      <if test="radiateArea != null">
        #{radiateArea,jdbcType=VARCHAR},
      </if>
      <if test="dotState != null">
        #{dotState,jdbcType=VARCHAR},
      </if>
      <if test="buseLicenseUrl != null">
        #{buseLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="officeSpaceUrl != null">
        #{officeSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="sparesSpaceUrl != null">
        #{sparesSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="doorHeadUrl != null">
        #{doorHeadUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUrl != null">
        #{vehicleUrl,jdbcType=VARCHAR},
      </if>
      <if test="serviceAidUrl != null">
        #{serviceAidUrl,jdbcType=VARCHAR},
      </if>
      <if test="consumableUrl != null">
        #{consumableUrl,jdbcType=VARCHAR},
      </if>
      <if test="basicDataUrl != null">
        #{basicDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="houseContractUrl != null">
        #{houseContractUrl,jdbcType=VARCHAR},
      </if>
      <if test="corporateIdCard != null">
        #{corporateIdCard,jdbcType=VARCHAR},
      </if>
      <if test="electLicense != null">
        #{electLicense,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="commonTaxPer != null">
        #{commonTaxPer,jdbcType=VARCHAR},
      </if>
      <if test="smallCarRays != null">
        #{smallCarRays,jdbcType=INTEGER},
      </if>
      <if test="commonCarNum != null">
        #{commonCarNum,jdbcType=INTEGER},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="dotTelephone != null">
        #{dotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="dotEmail != null">
        #{dotEmail,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="starId != null">
        #{starId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="vCode != null">
        #{vCode,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null">
        #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="dotBank != null">
        #{dotBank,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null">
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="dotSignOut != null">
        #{dotSignOut,jdbcType=INTEGER},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        #{bankName},
      </if>
      <if test="isAdvanceMoney != null">
        #{isAdvanceMoney},
      </if>
    </trim>
  </insert>

  <select id="selectOldDotBrandGroups" resultType="com.bonc.rrs.workManager.entity.vo.DotAreaBrandGroupsVo">
    select
      da.area_id ,
      db.brand_id ,
      db.service_type ,
      da.group_id ,
      db.child_group_id
    from
      dot_area da ,
      dot_brand db
    where
      da.dot_id = db.dot_id
      and da.group_id = db.group_id
      and da.is_delete = 0
      and da.dot_id = #{dotId};
  </select>

  <select id="selectNewDotBrandGroups" resultType="com.bonc.rrs.workManager.entity.vo.DotAreaBrandGroupsVo">
    select
      da.area_id ,
      db.brand_id ,
      db.service_type ,
      da.group_id ,
      db.child_group_id
    from
      dot_area_approve da ,
      dot_brand_approve db
    where
      da.approve_id = db.approve_id
      and da.group_id = db.group_id
      and da.is_delete = 0
      and da.approve_id = #{approveId};
  </select>

  <select id="selectListRecord" resultType="com.bonc.rrs.workManager.entity.DotApproveInfo">
    select
      dai.*
    from
      dot_approve_info dai
    where dai.delete_state = 0
      <if test="query.vCode != null and query.vCode != ''">
        and dai.v_code like concat('%',#{query.vCode},'%')
      </if>
      <if test="query.dotName != null and query.dotName != ''">
        and dai.dot_name like concat('%',#{query.dotName},'%')
      </if>
      <if test="query.type != null">
        and dai.`type` = #{query.type}
      </if>
      <if test="query.status != null">
        and dai.status = #{query.status}
      </if>
      <if test="query.regionId != null">
        and (dai.dot_area = #{query.regionId} or dai.dot_city = #{query.regionId} or dai.dot_district = #{query.regionId})
      </if>
      <if test="query.startDate != null and query.endDate != null">
        and dai.create_time >= concat(#{query.startDate},' 00:00:00') and dai.create_time &lt;= concat(#{query.endDate},' 23:59:59')
      </if>
      <if test="query.contactsName != null">
        and dai.contacts_name = #{query.contactsName}
      </if>
      <if test="query.areaIds != null and query.areaIds != ''">
        and (
            dai.dot_area in (${query.areaIds})
            or dai.dot_city in (${query.areaIds})
            or dai.dot_district in (${query.areaIds})
        )
      </if>
    order by dai.update_time desc
  </select>

  <select id="selectListPage" resultType="com.bonc.rrs.workManager.entity.DotApproveInfo">
      select
        dai.*
      from
        dot_approve_info dai
      where dai.delete_state = 0
      <if test="query.vCode != null and query.vCode != ''">
          and dai.v_code like concat('%',#{query.vCode},'%')
      </if>
      <if test="query.dotName != null and query.dotName != ''">
          and dai.dot_name like concat('%',#{query.dotName},'%')
      </if>
      <if test="query.type != null">
          and dai.`type` = #{query.type}
      </if>
      <if test="query.status != null">
          and dai.status = #{query.status}
      </if>
      <if test="query.regionId != null">
          and (dai.dot_area = #{query.regionId} or dai.dot_city = #{query.regionId} or dai.dot_district = #{query.regionId})
      </if>
      <if test="query.startDate != null and query.endDate != null">
        and dai.create_time >= concat(#{query.startDate},' 00:00:00') and dai.create_time &lt;= concat(#{query.endDate},' 23:59:59')
      </if>
      <if test="query.contactsName != null">
        and dai.contacts_name = #{query.contactsName}
      </if>
      <if test="query.areaIds != null and query.areaIds != ''">
        and (
          dai.dot_area in (${query.areaIds})
          or dai.dot_city in (${query.areaIds})
          or dai.dot_district in (${query.areaIds})
        )
      </if>
      order by dai.update_time desc
  </select>

  <insert id="insertDotInfo">
    insert
    into
      dot_information (dot_star,
                       dot_name,
                       dot_short_name,
                       dot_area,
                       dot_city,
                       dot_district,
                       dot_address,
                       dot_certificate,
                       dot_quality,
                       dot_postcode,
                       dot_class,
                       dot_features,
                       dot_create_time,
                       dot_create_per,
                       dot_code,
                       dot_score,
                       dot_sign_out,
                       market_level,
                       create_time,
                       legal_person_name,
                       legal_person_sex,
                       degree_education,
                       telephone,
                       storage_spare,
                       modify_time,
                       vehicles_nums,
                       contract_start,
                       contract_end,
                       tax_no,
                       company_quality,
                       regist_fund,
                       buse_regist_time,
                       tax_regist_time,
                       tax_deadline,
                       industry_deadline,
                       buse_regist_number,
                       approve_time,
                       tax_point,
                       radiate_area,
                       dot_state,
                       buse_license_url,
                       office_space_url,
                       spares_space_url,
                       door_head_url,
                       vehicle_url,
                       service_aid_url,
                       consumable_url,
                       basic_data_url,
                       house_contract_url,
                       contract_url,
                       corporate_id_card,
                       elect_license,
                       aptitude,
                       common_tax_per,
                       small_car_rays,
                       common_car_num,
                       contact_name,
                       dot_telephone,
                       dot_email,
                       is_delete,
                       star_id,
                       customer_code,
                       v_code,
                       branch_code,
                       branch_name,
                       dot_bank,
                       bank_account,
                       bank_number,
                       count,
                       company_id,
                       company_type,
                       bank_name,
                       modify_score_user_id,
                       is_advance_money)
    select
      dot_star,
      dot_name,
    dot_short_name,
      dot_area,
      dot_city,
      dot_district,
      dot_address,
      dot_certificate,
      dot_quality,
      dot_postcode,
      dot_class,
      dot_features,
      dot_create_time,
      dot_create_per,
      dot_code,
      dot_score,
      dot_sign_out,
      market_level,
      create_time,
      legal_person_name,
      legal_person_sex,
      degree_education,
      telephone,
      storage_spare,
      modify_time,
      vehicles_nums,
      contract_start,
      contract_end,
      tax_no,
      company_quality,
      regist_fund,
      buse_regist_time,
      tax_regist_time,
      tax_deadline,
      industry_deadline,
      buse_regist_number,
      approve_time,
      tax_point,
      radiate_area,
      dot_state,
      buse_license_url,
      office_space_url,
      spares_space_url,
      door_head_url,
      vehicle_url,
      service_aid_url,
      consumable_url,
      basic_data_url,
      house_contract_url,
      contract_url,
      corporate_id_card,
      elect_license,
      aptitude,
      common_tax_per,
      small_car_rays,
      common_car_num,
      contact_name,
      dot_telephone,
      dot_email,
      is_delete,
      star_id,
      customer_code,
      v_code,
      branch_code,
      branch_name,
      dot_bank,
      bank_account,
      bank_number,
      count,
      company_id,
      company_type,
      bank_name,
      modify_score_user_id,
      is_advance_money
    from
      dot_information_approve where approve_id = #{approveId}
  </insert>

  <update id="updateDotInfo">
    update
      dot_information t1 , dot_information_approve t2
    set
      t1.dot_star = t2.dot_star,
      t1.dot_name = t2.dot_name,
      t1.dot_short_name = t2.dot_short_name,
      t1.dot_area = t2.dot_area,
      t1.dot_city = t2.dot_city,
      t1.dot_district = t2.dot_district,
      t1.dot_address = t2.dot_address,
      t1.dot_certificate = t2.dot_certificate,
      t1.dot_quality = t2.dot_quality,
      t1.dot_postcode = t2.dot_postcode,
      t1.dot_class = t2.dot_class,
      t1.dot_features = t2.dot_features,
      t1.dot_create_time = t2.dot_create_time,
      t1.dot_create_per = t2.dot_create_per,
      t1.dot_code = t2.dot_code,
      t1.dot_score = t2.dot_score,
      t1.dot_sign_out = t2.dot_sign_out,
      t1.market_level = t2.market_level,
      t1.create_time = t2.create_time,
      t1.legal_person_name = t2.legal_person_name,
      t1.legal_person_sex = t2.legal_person_sex,
      t1.degree_education = t2.degree_education,
      t1.telephone = t2.telephone,
      t1.storage_spare = t2.storage_spare,
      t1.modify_time = t2.modify_time,
      t1.vehicles_nums = t2.vehicles_nums,
      t1.contract_start = t2.contract_start,
      t1.contract_end = t2.contract_end,
      t1.tax_no = t2.tax_no,
      t1.company_quality = t2.company_quality,
      t1.regist_fund = t2.regist_fund,
      t1.buse_regist_time = t2.buse_regist_time,
      t1.tax_regist_time = t2.tax_regist_time,
      t1.tax_deadline = t2.tax_deadline,
      t1.industry_deadline = t2.industry_deadline,
      t1.buse_regist_number = t2.buse_regist_number,
      t1.approve_time = t2.approve_time,
      t1.tax_point = t2.tax_point,
      t1.radiate_area = t2.radiate_area,
      t1.dot_state = t2.dot_state,
      t1.buse_license_url = t2.buse_license_url,
      t1.office_space_url = t2.office_space_url,
      t1.spares_space_url = t2.spares_space_url,
      t1.door_head_url = t2.door_head_url,
      t1.vehicle_url = t2.vehicle_url,
      t1.service_aid_url = t2.service_aid_url,
      t1.consumable_url = t2.consumable_url,
      t1.basic_data_url = t2.basic_data_url,
      t1.house_contract_url = t2.house_contract_url,
      t1.contract_url = t2.contract_url,
      t1.corporate_id_card = t2.corporate_id_card,
      t1.elect_license = t2.elect_license,
      t1.aptitude = t2.aptitude,
      t1.common_tax_per = t2.common_tax_per,
      t1.small_car_rays = t2.small_car_rays,
      t1.common_car_num = t2.common_car_num,
      t1.contact_name = t2.contact_name,
      t1.dot_telephone = t2.dot_telephone,
      t1.dot_email = t2.dot_email,
      t1.is_delete = t2.is_delete,
      t1.star_id = t2.star_id,
      t1.customer_code = t2.customer_code,
      t1.v_code = t2.v_code,
      t1.branch_code = t2.branch_code,
      t1.branch_name = t2.branch_name,
      t1.dot_bank = t2.dot_bank,
      t1.bank_account = t2.bank_account,
      t1.bank_number = t2.bank_number,
      t1.count = t2.count,
      t1.company_id = t2.company_id,
      t1.company_type = t2.company_type,
      t1.bank_name = t2.bank_name,
      t1.modify_score_user_id = t2.modify_score_user_id,
      t1.is_advance_money = t2.is_advance_money
    where
      t1.dot_id = t2.dot_id
      and t2.approve_id = #{approveId}
  </update>

  <insert id="insertDotContacts">
    insert
    into
      dot_contacts
    (contacts_id,
     dot_code,
     contacts_name,
     contacts_phone,
     contacts_email,
     create_time,
     modify_time,
     is_delete)
    select
      #{userId} as contacts_id,
      dot_code,
      contacts_name,
      contacts_phone,
      contacts_email,
      create_time,
      modify_time,
      is_delete
    from
      dot_contacts_approve
    where approve_id = #{approveId}
  </insert>

  <select id="selectDotContacts" resultType="com.bonc.rrs.workManager.entity.DotContactsApprove">
    select * from dot_contacts_approve dca where approve_id = #{approveId} limit 1
  </select>

  <insert id="insertDotArea">
    insert
    into
      dot_area
    (dot_id,
     area_id,
     is_delete,
     group_id)
    select
      #{dotId} as dot_id,
      area_id,
      is_delete,
      group_id
    from
      dot_area_approve
    where approve_id = #{approveId}
  </insert>

  <insert id="insertDotBrand">
    insert
    into
      dot_brand
    (dot_id,
     brand_id,
     service_type,
     date_time,
     group_id,
     child_group_id)
    select
      #{dotId} as dot_id,
      brand_id,
      service_type,
      date_time,
      group_id,
      child_group_id
    from
      dot_brand_approve
    where approve_id = #{approveId}
  </insert>

  <update id="updateDotContacts">
    update
      dot_contacts t1 , dot_contacts_approve t2
    set
      t1.dot_code = t2.dot_code,
      t1.contacts_name = t2.contacts_name,
      t1.contacts_phone = t2.contacts_phone,
      t1.contacts_email = t2.contacts_email,
      t1.create_time = t2.create_time,
      t1.modify_time = t2.modify_time,
      t1.is_delete = t2.is_delete
    where
      t1.contacts_id = t2.contacts_id and t2.approve_id = #{approveId}
  </update>

  <select id="checkRepeatContacts" resultType="int">
    select
      count(1)
    from
      dot_contacts_approve  dc ,
      sys_user su
    where
      dc.contacts_name = su.username
      <if test="type == 1 or type == 4">
        and su.user_id != dc.contacts_id
        and su.status != 0
      </if>
      and dc.approve_id = #{approveId}
  </select>

  <select id="checkRepeatDotName" resultType="int">
    select
      count(1)
    from
      dot_approve_info dai ,
      dot_information di
    where
      dai.dot_name = di.dot_name
      <if test="type == 1 or type == 4">
        and dai.dot_id != di.dot_id
        and di.is_delete != 1
      </if>
      and dai.id = #{approveId}
  </select>

  <select id="checkRepeatContactsPhone" resultType="int">
    select
        count(1)
    from
      dot_contacts_approve  dc ,
      sys_user su
    where
    dc.contacts_phone = su.mobile
    <if test="type == 1 or type == 4">
      and su.user_id != dc.contacts_id
      and su.status != 0
    </if>
    and dc.approve_id = #{approveId}
  </select>

  <select id="checkRepeatDotCode" resultType="int">
    select
        count(1)
    from
      dot_approve_info dai ,
      dot_information di
    where
        dai.dot_code = di.dot_code
        <if test="type == 1 or type == 4">
          and dai.dot_id != di.dot_id
          and di.is_delete != 1
        </if>
        and dai.id = #{approveId};
  </select>

  <select id="checkRepeatDotVCode" resultType="int">
    select
        count(1)
    from
      dot_approve_info dai ,
      dot_information di
    where
      dai.v_code = di.v_code
      <if test="type == 1 or type == 4">
        and dai.dot_id != di.dot_id
        and di.is_delete != 1
      </if>
      and dai.id = #{approveId};
  </select>

  <select id="selectDotIdByVCode" resultType="java.lang.Integer">
    select dot_id from dot_information where v_code = #{vCode} limit 1
  </select>

  <select id="selectOldDrivingPermitGroups" resultType="com.bonc.rrs.balanceprocess.entity.DrivingPermitEntity">
    select * from driving_permit where dot_id = #{dotId} and is_delete = 0
  </select>

  <select id="selectNewDrivingPermitGroups" resultType="com.bonc.rrs.balanceprocess.entity.DrivingPermitEntity">
    select * from driving_permit_approve where approve_id = #{approveId} and is_delete = 0
  </select>

  <select id="selectOldDotBankGroups" resultType="com.bonc.rrs.workManager.entity.DotBank">
    select * from dot_bank where dot_id = #{dotId} and is_delete = 0
  </select>

  <select id="selectNewDotBankGroups" resultType="com.bonc.rrs.workManager.entity.DotBank">
    select * from dot_bank_approve where approve_id = #{approveId} and is_delete = 0
  </select>

  <insert id="insertDrivingPermit">
    insert
    into
      driving_permit
    (dot_id,
     `type`,
     name,
     car_no,
     img)
    select
      #{dotId} as dot_id,
      `type`,
      name,
      car_no,
      img
    from
      driving_permit_approve
    where
      approve_id = #{approveId}
      and driving_id is null
  </insert>

  <insert id="insertDotBank">
    insert
    into
    dot_bank(dot_id, dot_bank, bank_number, bank_account, bank_name, tax_no)
    select
      #{dotId} as dot_id,
      dot_bank,
      bank_number,
      bank_account,
      bank_name,
      tax_no
    from
      dot_bank_approve
    where
      approve_id = #{approveId}
      and bank_id is null
  </insert>

  <update id="updateDrivingPermit">
    update
      driving_permit t1 ,
      driving_permit_approve t2
    set t1.name = t2.name,
        t1.car_no = t2.car_no,
        t1.img = t2.img,
        t1.is_delete = 0
    where
      t1.id = t2.driving_id
      and t1.dot_id = t2.dot_id
      and t2.approve_id = #{approveId}
      and t2.dot_id = #{dotId}
  </update>

  <update id="updateDotBank">
    update
      dot_bank t1 ,
      dot_bank_approve t2
    set t1.dot_bank = t2.dot_bank,
        t1.bank_number = t2.bank_number,
        t1.bank_account = t2.bank_account,
        t1.bank_name = t2.bank_name,
        t1.tax_no = t2.tax_no,
        t1.is_delete = 0
    where
      t1.id = t2.bank_id
      and t1.dot_id = t2.dot_id
      and t2.approve_id = #{approveId}
      and t2.dot_id = #{dotId}
  </update>

</mapper>