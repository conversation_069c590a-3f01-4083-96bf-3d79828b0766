<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.ScoreSummaryMapper">

  <select id="statisticsCycleList" resultType="com.bonc.rrs.workManager.entity.ScoreSummary">
    select
      brand_id ,
      dot_id ,
      br.id as city ,
      sum(score) as score
    from
      rrs_score_summary rs ,
      biz_region br
    where
      rs.regcode = br.regcode
      and rs.`cycle` = #{cycle}
      and rs.is_delete = 0
    group by
      rs.brand_id ,
      rs.dot_id ,
      br.id
  </select>

  <select id="queryScoreByRole" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
    select
    max(t0.id) as indicator_id,
    max(t0.indicator_desc) as title,
    max(t0.unit) as unit,
    max(t0.num_type) as num_type,
    (case
        t0.num_type when 0 then ROUND(sum(t1.orders_count), 4)
      else ROUND(sum(t1.orders_count), 2)
    end) as total,
    (case
        t0.num_type when 0 then ROUND(sum(t1.orders), 4)
      else ROUND(sum(t1.orders), 2)
    end) as totalScore,
    <if test="flags == 0">
      <choose>
        <when test="param.brand == 18">
          t2.lx_region as name ,
        </when>
        <when test="param.brand == 14">
          t2.tsl_region as name ,
        </when>
        <otherwise>
          t2.regione as name,
        </otherwise>
      </choose>
      (case t0.num_type
        when 0 then ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 4)
        else ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 2)
       end) as score
    </if>
    <if test="flags == 1">
      SUBSTRING(max(t3.dot_name),1,6) as name,
      t1.dot_id,
      (case t0.num_type
      when 0 then ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 4)
      else ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 2)
      end) as score
    </if>
    <if test="flags == 2">
      SUBSTRING(max(t3.dot_name),1,6) as name,
      t1.dot_id,
      (case t0.num_type
      when 0 then ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 4)
      else ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 2)
      end) as score
    </if>
    <if test="flags == 3">
      max(t1.province_name) as name,
      (case t0.num_type
      when 0 then ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 4)
      else ROUND(IF(sum(t1.orders_count) = 0,0,sum(t1.orders) / sum(t1.orders_count)), 2)
      end) as score
    </if>
    from
      brand_per_evaluation t0 ,
      rrs_score_summary t1 ,
      biz_region t2 ,
      dot_information t3
    where
      t0.id = t1.type
      and t1.regcode = t2.regcode
      and t1.dot_id = t3.dot_id
      and t0.delete_state = 0
      and t1.is_delete = 0
      and t1.orders_count > 0
      and t1.`cycle` = #{param.cycle}
      <foreach collection="param.brandIds" item="brandId" open=" and t1.brand_id in (" close=")" separator=",">
        #{brandId}
      </foreach>
    <if test="flags == 1">
      and t2.id = #{param.city}
    </if>
    <if test="flags == 2">
      and t1.province_id = #{param.province}
    </if>
    <if test="flags == 3">
      and t1.regione = #{param.regione}
    </if>
    group by
    t1.type,
    <if test="flags == 0">
      <choose>
        <when test="param.brand == 18">
          t2.lx_region
        </when>
        <when test="param.brand == 14">
          t2.tsl_region
        </when>
        <otherwise>
          t2.regione
        </otherwise>
      </choose>
    </if>
    <if test="flags == 1">
      t1.dot_id
    </if>
    <if test="flags == 2">
      t1.dot_id
    </if>
    <if test="flags == 3">
      t1.province_id
    </if>
  </select>
</mapper>