<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.AutidResultMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.AutidResult">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="purpose" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="worder_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="username" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="user_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="result" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="modificate_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, purpose, worder_no, username, user_id, result, type, create_time, modificate_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from worder_autid_result
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from worder_autid_result
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.workManager.entity.AutidResult">
    insert into worder_autid_result (id, purpose, worder_no, 
      username, user_id, result, 
      type, create_time, modificate_time
      )
    values (#{id,jdbcType=INTEGER}, #{purpose,jdbcType=INTEGER}, #{worderNo,jdbcType=VARCHAR}, 
      #{username,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, #{result,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{modificateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bonc.rrs.workManager.entity.AutidResult">
    insert into worder_autid_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purpose != null">
        purpose,
      </if>
      <if test="worderNo != null">
        worder_no,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="type != null">
        type,
      </if>
        create_time,
      <if test="modificateTime != null">
        modificate_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purpose != null">
        #{purpose,jdbcType=INTEGER},
      </if>
      <if test="worderNo != null">
        #{worderNo,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
        current_timestamp,
      <if test="modificateTime != null">
        #{modificateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.workManager.entity.AutidResult">
    update worder_autid_result
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
        modificate_time = current_timestamp,
    </set>
    where worder_no = #{worderNo,jdbcType=VARCHAR}
    and purpose = #{purpose,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.workManager.entity.AutidResult">
    update worder_autid_result
    set purpose = #{purpose,jdbcType=INTEGER},
      worder_no = #{worderNo,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modificate_time = #{modificateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getCountAutidResult" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM worder_autid_result
    WHERE worder_no=#{worderNo} and purpose=#{purpose}
  </select>

  <update id="updataWorderInformation">
    UPDATE worder_information set worder_exec_status=#{status},worder_exec_status_value=#{statusValue}, modify_time=current_timestamp
    WHERE worder_no=#{worderNo}
  </update>

  <update id="updateWorderTime">
    update worder_information set modify_time = current_timestamp
    where worder_no =#{worderNo}
  </update>

  <select id="selectAutidType" resultType="java.lang.Integer">
    select `type` from worder_autid_result
    WHERE worder_no=#{worderNo} and purpose=#{purpose}
  </select>

  <update id="updateWorderMajorState">
    UPDATE worder_information set worder_status=#{majorState}, worder_status_value=#{record},
    modify_time=current_timestamp
    WHERE worder_no=#{worderNo}
  </update>

  <update id="updateConfirmTime">
    update worder_information
    set confirm_completion_time = current_timestamp
    where worder_no=#{worderNo}
  </update>

</mapper>