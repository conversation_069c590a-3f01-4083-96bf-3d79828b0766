<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.DotAreaMapper">


    <select id="selectByDotValue" resultType="com.bonc.rrs.workManager.entity.DotArea">
        select da.*
        from dot_information di
        inner join dot_area da on
        di.dot_id = da.dot_id and da.is_delete = '0' and di.dot_state = '1'
        inner join biz_region br on br.id = da.area_id
        left join dot_brand db on db.dot_id = di.dot_id and da.group_id = db.group_id
        <if test="brandId != null">
            and db.brand_id = #{brandId}
        </if>
        where 1=1
        <if test="type != null">
            br.type >= #{type}
        </if>
        <if test="dotId != null">
            and di.dot_id = #{dotId}
        </if>
        <if test="areaIds != null and areaIds != ''">
            and ${areaIds}
        </if>
    </select>

    <select id="selectByAreaId" resultType="java.lang.Integer">
        select distinct da.dot_id
        from dot_area da
            inner join dot_brand db on
                da.dot_id = db.dot_id and da.group_id = db.group_id
            inner join dot_information di on
                da.dot_id = di.dot_id and di.is_delete = 0 and da.is_delete = '0' and di.dot_state = '1'
            and da.area_id in
        <foreach collection="areaIds" item="areaId" open="(" close=")" separator=",">
            #{areaId}
        </foreach>
        <if test="param.brandId != null">
            and db.brand_id = #{param.brandId}
        </if>
    </select>

    <select id="selectListByDotBrand" resultType="com.bonc.rrs.workManager.entity.DotArea">
        select
            da.*
        from
            dot_area da ,
            dot_brand db
        where
            da.dot_id = db.dot_id
          and da.group_id = db.group_id
          and da.is_delete = '0'
          and da.dot_id = #{dotId}
          and db.brand_id = #{brandId}
    </select>

    <select id="selectListByDotsBrand" resultType="com.bonc.rrs.workManager.entity.DotArea">
        select
            da.*
        from
            dot_area da ,
            dot_brand db
        where
            da.dot_id = db.dot_id
          and da.group_id = db.group_id
          and da.is_delete = '0'
          and db.brand_id = #{brandId}
          <foreach collection="dotIds" item="dotId" open="and da.dot_id in (" close=")" separator=",">
              #{dotId}
          </foreach>
          <if test="serviceType != null">
            and (db.service_type = #{serviceType} or db.service_type = 0)
          </if>
    </select>
</mapper>