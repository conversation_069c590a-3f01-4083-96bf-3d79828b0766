<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.DotInfoMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.DotInfo">
    <constructor>
      <idArg column="dot_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="dot_star" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_short_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_area" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_city" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_district" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_address" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_certificate" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_quality" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_postcode" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_class" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_features" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="dot_create_per" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_code" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_score" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="dot_sign_out" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="market_level" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="legal_person_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="legal_person_sex" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="degree_education" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="telephone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="storage_spare" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="modify_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="vehicles_nums" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="contract_start" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="contract_end" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="tax_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="company_quality" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="regist_fund" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="buse_regist_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="tax_regist_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="tax_deadline" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="industry_deadline" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="buse_regist_number" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="approve_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="tax_point" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="radiate_area" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_state" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="buse_license_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="office_space_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="spares_space_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="door_head_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="vehicle_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="service_aid_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="consumable_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="basic_data_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="house_contract_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contract_url" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="corporate_id_card" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="elect_license" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="aptitude" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="common_tax_per" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="small_car_rays" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="common_car_num" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="contact_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_telephone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_email" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="is_delete" javaType="java.lang.Byte" jdbcType="TINYINT" />
      <arg column="star_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="customer_code" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="v_code" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="branch_code" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="branch_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="dot_bank" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="bank_account" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="bank_number" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="count" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="company_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="company_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="bank_name" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    dot_id, dot_star, dot_name, dot_short_name,dot_area, dot_city, dot_district, dot_address, dot_certificate,
    dot_quality, dot_postcode, dot_class, dot_features, dot_create_time, dot_create_per, 
    dot_code, dot_score, dot_sign_out, market_level, create_time, legal_person_name,
    legal_person_sex, degree_education, telephone, storage_spare, modify_time, vehicles_nums,
    contract_start, contract_end, tax_no, company_quality, regist_fund, buse_regist_time,
    tax_regist_time, tax_deadline, industry_deadline, buse_regist_number, approve_time,
    tax_point, radiate_area, dot_state, buse_license_url, office_space_url, spares_space_url,
    door_head_url, vehicle_url, service_aid_url, consumable_url, basic_data_url, house_contract_url,
    contract_url, corporate_id_card, elect_license, aptitude, common_tax_per, small_car_rays,
    common_car_num, contact_name, dot_telephone, dot_email, is_delete, star_id, customer_code,
    v_code, branch_code, branch_name, dot_bank, bank_account, bank_number, count, company_id,
    company_type, bank_name,is_advance_money
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dot_information
    where dot_id = #{dotId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from dot_information
    where dot_id = #{dotId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.workManager.entity.DotInfo">
    insert into dot_information (dot_id, dot_star, dot_name, 
      dot_area, dot_city, dot_district, 
      dot_address, dot_certificate, dot_quality, 
      dot_postcode, dot_class, dot_features, 
      dot_create_time, dot_create_per, dot_code, 
      market_level, create_time, legal_person_name, 
      legal_person_sex, degree_education, telephone, 
      storage_spare, modify_time, vehicles_nums, 
      contract_start, contract_end, tax_no, 
      company_quality, regist_fund, buse_regist_time, 
      tax_regist_time, tax_deadline, industry_deadline, 
      buse_regist_number, approve_time, tax_point, 
      radiate_area, dot_state, buse_license_url, 
      office_space_url, spares_space_url, door_head_url, 
      vehicle_url, service_aid_url, consumable_url, 
      basic_data_url, house_contract_url, corporate_id_card, 
      elect_license, aptitude, common_tax_per, 
      small_car_rays, common_car_num, contact_name, 
      dot_telephone, dot_email, is_delete, 
      star_id, customer_code, v_code, 
      branch_code, branch_name, dot_bank, 
      bank_account, bank_number)
    values (#{dotId,jdbcType=INTEGER}, #{dotStar,jdbcType=VARCHAR}, #{dotName,jdbcType=VARCHAR}, 
      #{dotArea,jdbcType=VARCHAR}, #{dotCity,jdbcType=VARCHAR}, #{dotDistrict,jdbcType=VARCHAR}, 
      #{dotAddress,jdbcType=VARCHAR}, #{dotCertificate,jdbcType=VARCHAR}, #{dotQuality,jdbcType=VARCHAR}, 
      #{dotPostcode,jdbcType=VARCHAR}, #{dotClass,jdbcType=VARCHAR}, #{dotFeatures,jdbcType=VARCHAR}, 
      #{dotCreateTime,jdbcType=TIMESTAMP}, #{dotCreatePer,jdbcType=VARCHAR}, #{dotCode,jdbcType=VARCHAR}, 
      #{marketLevel,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{legalPersonName,jdbcType=VARCHAR}, 
      #{legalPersonSex,jdbcType=VARCHAR}, #{degreeEducation,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{storageSpare,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{vehiclesNums,jdbcType=INTEGER}, 
      #{contractStart,jdbcType=TIMESTAMP}, #{contractEnd,jdbcType=TIMESTAMP}, #{taxNo,jdbcType=VARCHAR}, 
      #{companyQuality,jdbcType=VARCHAR}, #{registFund,jdbcType=VARCHAR}, #{buseRegistTime,jdbcType=TIMESTAMP}, 
      #{taxRegistTime,jdbcType=TIMESTAMP}, #{taxDeadline,jdbcType=VARCHAR}, #{industryDeadline,jdbcType=VARCHAR}, 
      #{buseRegistNumber,jdbcType=VARCHAR}, #{approveTime,jdbcType=TIMESTAMP}, #{taxPoint,jdbcType=VARCHAR}, 
      #{radiateArea,jdbcType=VARCHAR}, #{dotState,jdbcType=VARCHAR}, #{buseLicenseUrl,jdbcType=VARCHAR}, 
      #{officeSpaceUrl,jdbcType=VARCHAR}, #{sparesSpaceUrl,jdbcType=VARCHAR}, #{doorHeadUrl,jdbcType=VARCHAR}, 
      #{vehicleUrl,jdbcType=VARCHAR}, #{serviceAidUrl,jdbcType=VARCHAR}, #{consumableUrl,jdbcType=VARCHAR}, 
      #{basicDataUrl,jdbcType=VARCHAR}, #{houseContractUrl,jdbcType=VARCHAR}, #{corporateIdCard,jdbcType=VARCHAR}, 
      #{electLicense,jdbcType=VARCHAR}, #{aptitude,jdbcType=VARCHAR}, #{commonTaxPer,jdbcType=VARCHAR}, 
      #{smallCarRays,jdbcType=INTEGER}, #{commonCarNum,jdbcType=INTEGER}, #{contactName,jdbcType=VARCHAR}, 
      #{dotTelephone,jdbcType=VARCHAR}, #{dotEmail,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, 
      #{starId,jdbcType=INTEGER}, #{customerCode,jdbcType=VARCHAR}, #{vCode,jdbcType=VARCHAR}, 
      #{branchCode,jdbcType=VARCHAR}, #{branchName,jdbcType=VARCHAR}, #{dotBank,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{bankNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyColumn="dot_id" keyProperty="dotId" parameterType="com.bonc.rrs.workManager.entity.DotInfo">
    insert into dot_information
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dotId != null">
        dot_id,
      </if>
      <if test="dotStar != null">
        dot_star,
      </if>
      <if test="dotName != null">
        dot_name,
      </if>
      <if test="dotArea != null">
        dot_area,
      </if>
      <if test="dotCity != null">
        dot_city,
      </if>
      <if test="dotDistrict != null">
        dot_district,
      </if>
      <if test="dotAddress != null">
        dot_address,
      </if>
      <if test="dotCertificate != null">
        dot_certificate,
      </if>
      <if test="dotQuality != null">
        dot_quality,
      </if>
      <if test="dotPostcode != null">
        dot_postcode,
      </if>
      <if test="dotClass != null">
        dot_class,
      </if>
      <if test="dotFeatures != null">
        dot_features,
      </if>
      <if test="dotCreateTime != null">
        dot_create_time,
      </if>
      <if test="dotCreatePer != null">
        dot_create_per,
      </if>
      <if test="dotCode != null">
        dot_code,
      </if>
      <if test="marketLevel != null">
        market_level,
      </if>
        create_time,
      <if test="legalPersonName != null">
        legal_person_name,
      </if>
      <if test="legalPersonSex != null">
        legal_person_sex,
      </if>
      <if test="degreeEducation != null">
        degree_education,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="storageSpare != null">
        storage_spare,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="vehiclesNums != null">
        vehicles_nums,
      </if>
      <if test="contractStart != null">
        contract_start,
      </if>
      <if test="contractEnd != null">
        contract_end,
      </if>
      <if test="taxNo != null">
        tax_no,
      </if>
      <if test="companyQuality != null">
        company_quality,
      </if>
      <if test="registFund != null">
        regist_fund,
      </if>
      <if test="buseRegistTime != null">
        buse_regist_time,
      </if>
      <if test="taxRegistTime != null">
        tax_regist_time,
      </if>
      <if test="taxDeadline != null">
        tax_deadline,
      </if>
      <if test="industryDeadline != null">
        industry_deadline,
      </if>
      <if test="buseRegistNumber != null">
        buse_regist_number,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="taxPoint != null">
        tax_point,
      </if>
      <if test="radiateArea != null">
        radiate_area,
      </if>
      <if test="dotState != null">
        dot_state,
      </if>
      <if test="buseLicenseUrl != null">
        buse_license_url,
      </if>
      <if test="officeSpaceUrl != null">
        office_space_url,
      </if>
      <if test="sparesSpaceUrl != null">
        spares_space_url,
      </if>
      <if test="doorHeadUrl != null">
        door_head_url,
      </if>
      <if test="vehicleUrl != null">
        vehicle_url,
      </if>
      <if test="serviceAidUrl != null">
        service_aid_url,
      </if>
      <if test="consumableUrl != null">
        consumable_url,
      </if>
      <if test="basicDataUrl != null">
        basic_data_url,
      </if>
      <if test="houseContractUrl != null">
        house_contract_url,
      </if>
      <if test="corporateIdCard != null">
        corporate_id_card,
      </if>
      <if test="electLicense != null">
        elect_license,
      </if>
      <if test="aptitude != null">
        aptitude,
      </if>
      <if test="commonTaxPer != null">
        common_tax_per,
      </if>
      <if test="smallCarRays != null">
        small_car_rays,
      </if>
      <if test="commonCarNum != null">
        common_car_num,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="dotTelephone != null">
        dot_telephone,
      </if>
      <if test="dotEmail != null">
        dot_email,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="starId != null">
        star_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="vCode != null">
        v_code,
      </if>
      <if test="branchCode != null">
        branch_code,
      </if>
      <if test="branchName != null">
        branch_name,
      </if>
      <if test="dotBank != null">
        dot_bank,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="bankNumber != null">
        bank_number,
      </if>
      <if test="dotSignOut != null">
        dot_sign_out,
      </if>
      <if test="count">
        `count`,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="isAdvanceMoney != null">
        is_advance_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dotId != null">
        #{dotId,jdbcType=INTEGER},
      </if>
      <if test="dotStar != null">
        #{dotStar,jdbcType=VARCHAR},
      </if>
      <if test="dotName != null">
        #{dotName,jdbcType=VARCHAR},
      </if>
      <if test="dotArea != null">
        #{dotArea,jdbcType=VARCHAR},
      </if>
      <if test="dotCity != null">
        #{dotCity,jdbcType=VARCHAR},
      </if>
      <if test="dotDistrict != null">
        #{dotDistrict,jdbcType=VARCHAR},
      </if>
      <if test="dotAddress != null">
        #{dotAddress,jdbcType=VARCHAR},
      </if>
      <if test="dotCertificate != null">
        #{dotCertificate,jdbcType=VARCHAR},
      </if>
      <if test="dotQuality != null">
        #{dotQuality,jdbcType=VARCHAR},
      </if>
      <if test="dotPostcode != null">
        #{dotPostcode,jdbcType=VARCHAR},
      </if>
      <if test="dotClass != null">
        #{dotClass,jdbcType=VARCHAR},
      </if>
      <if test="dotFeatures != null">
        #{dotFeatures,jdbcType=VARCHAR},
      </if>
      <if test="dotCreateTime != null">
        #{dotCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dotCreatePer != null">
        #{dotCreatePer,jdbcType=VARCHAR},
      </if>
      <if test="dotCode != null">
        #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="marketLevel != null">
        #{marketLevel,jdbcType=VARCHAR},
      </if>
        current_timestamp ,
      <if test="legalPersonName != null">
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonSex != null">
        #{legalPersonSex,jdbcType=VARCHAR},
      </if>
      <if test="degreeEducation != null">
        #{degreeEducation,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="storageSpare != null">
        #{storageSpare,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehiclesNums != null">
        #{vehiclesNums,jdbcType=INTEGER},
      </if>
      <if test="contractStart != null">
        #{contractStart,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEnd != null">
        #{contractEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="taxNo != null">
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="companyQuality != null">
        #{companyQuality,jdbcType=VARCHAR},
      </if>
      <if test="registFund != null">
        #{registFund,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistTime != null">
        #{buseRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRegistTime != null">
        #{taxRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxDeadline != null">
        #{taxDeadline,jdbcType=VARCHAR},
      </if>
      <if test="industryDeadline != null">
        #{industryDeadline,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistNumber != null">
        #{buseRegistNumber,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxPoint != null">
        #{taxPoint,jdbcType=VARCHAR},
      </if>
      <if test="radiateArea != null">
        #{radiateArea,jdbcType=VARCHAR},
      </if>
      <if test="dotState != null">
        #{dotState,jdbcType=VARCHAR},
      </if>
      <if test="buseLicenseUrl != null">
        #{buseLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="officeSpaceUrl != null">
        #{officeSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="sparesSpaceUrl != null">
        #{sparesSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="doorHeadUrl != null">
        #{doorHeadUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUrl != null">
        #{vehicleUrl,jdbcType=VARCHAR},
      </if>
      <if test="serviceAidUrl != null">
        #{serviceAidUrl,jdbcType=VARCHAR},
      </if>
      <if test="consumableUrl != null">
        #{consumableUrl,jdbcType=VARCHAR},
      </if>
      <if test="basicDataUrl != null">
        #{basicDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="houseContractUrl != null">
        #{houseContractUrl,jdbcType=VARCHAR},
      </if>
      <if test="corporateIdCard != null">
        #{corporateIdCard,jdbcType=VARCHAR},
      </if>
      <if test="electLicense != null">
        #{electLicense,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="commonTaxPer != null">
        #{commonTaxPer,jdbcType=VARCHAR},
      </if>
      <if test="smallCarRays != null">
        #{smallCarRays,jdbcType=INTEGER},
      </if>
      <if test="commonCarNum != null">
        #{commonCarNum,jdbcType=INTEGER},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="dotTelephone != null">
        #{dotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="dotEmail != null">
        #{dotEmail,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="starId != null">
        #{starId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="vCode != null">
        #{vCode,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null">
        #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="dotBank != null">
        #{dotBank,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null">
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="dotSignOut != null">
        #{dotSignOut,jdbcType=INTEGER},
      </if>
<!--      <if test="areaId != null">-->
<!--        #{areaId,jdbcType=INTEGER},-->
<!--      </if>-->
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="bankName != null">
        #{bankName},
      </if>
      <if test="isAdvanceMoney != null">
        #{isAdvanceMoney},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.workManager.entity.DotInfo">
    update dot_information
    <set>
      <if test="dotStar != null">
        dot_star = #{dotStar,jdbcType=VARCHAR},
      </if>
      <if test="dotName != null">
        dot_name = #{dotName,jdbcType=VARCHAR},
      </if>
      <if test="dotArea != null">
        dot_area = #{dotArea,jdbcType=VARCHAR},
      </if>
      <if test="dotCity != null">
        dot_city = #{dotCity,jdbcType=VARCHAR},
      </if>
      <if test="dotDistrict != null">
        dot_district = #{dotDistrict,jdbcType=VARCHAR},
      </if>
      <if test="dotAddress != null">
        dot_address = #{dotAddress,jdbcType=VARCHAR},
      </if>
      <if test="dotCertificate != null">
        dot_certificate = #{dotCertificate,jdbcType=VARCHAR},
      </if>
      <if test="dotQuality != null">
        dot_quality = #{dotQuality,jdbcType=VARCHAR},
      </if>
      <if test="dotPostcode != null">
        dot_postcode = #{dotPostcode,jdbcType=VARCHAR},
      </if>
      <if test="dotClass != null">
        dot_class = #{dotClass,jdbcType=VARCHAR},
      </if>
      <if test="dotFeatures != null">
        dot_features = #{dotFeatures,jdbcType=VARCHAR},
      </if>
      <if test="dotCreateTime != null">
        dot_create_time = #{dotCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dotCreatePer != null">
        dot_create_per = #{dotCreatePer,jdbcType=VARCHAR},
      </if>
      <if test="dotCode != null">
        dot_code = #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="marketLevel != null">
        market_level = #{marketLevel,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="legalPersonName != null">
        legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonSex != null">
        legal_person_sex = #{legalPersonSex,jdbcType=VARCHAR},
      </if>
      <if test="degreeEducation != null">
        degree_education = #{degreeEducation,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="storageSpare != null">
        storage_spare = #{storageSpare,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehiclesNums != null">
        vehicles_nums = #{vehiclesNums,jdbcType=INTEGER},
      </if>
      <if test="contractStart != null">
        contract_start = #{contractStart,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEnd != null">
        contract_end = #{contractEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="taxNo != null">
        tax_no = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="companyQuality != null">
        company_quality = #{companyQuality,jdbcType=VARCHAR},
      </if>
      <if test="registFund != null">
        regist_fund = #{registFund,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistTime != null">
        buse_regist_time = #{buseRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRegistTime != null">
        tax_regist_time = #{taxRegistTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxDeadline != null">
        tax_deadline = #{taxDeadline,jdbcType=VARCHAR},
      </if>
      <if test="industryDeadline != null">
        industry_deadline = #{industryDeadline,jdbcType=VARCHAR},
      </if>
      <if test="buseRegistNumber != null">
        buse_regist_number = #{buseRegistNumber,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        approve_time = #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxPoint != null">
        tax_point = #{taxPoint,jdbcType=VARCHAR},
      </if>
      <if test="radiateArea != null">
        radiate_area = #{radiateArea,jdbcType=VARCHAR},
      </if>
      <if test="dotState != null">
        dot_state = #{dotState,jdbcType=VARCHAR},
      </if>
      <if test="buseLicenseUrl != null">
        buse_license_url = #{buseLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="officeSpaceUrl != null">
        office_space_url = #{officeSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="sparesSpaceUrl != null">
        spares_space_url = #{sparesSpaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="doorHeadUrl != null">
        door_head_url = #{doorHeadUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUrl != null">
        vehicle_url = #{vehicleUrl,jdbcType=VARCHAR},
      </if>
      <if test="serviceAidUrl != null">
        service_aid_url = #{serviceAidUrl,jdbcType=VARCHAR},
      </if>
      <if test="consumableUrl != null">
        consumable_url = #{consumableUrl,jdbcType=VARCHAR},
      </if>
      <if test="basicDataUrl != null">
        basic_data_url = #{basicDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="houseContractUrl != null">
        house_contract_url = #{houseContractUrl,jdbcType=VARCHAR},
      </if>
      <if test="corporateIdCard != null">
        corporate_id_card = #{corporateIdCard,jdbcType=VARCHAR},
      </if>
      <if test="electLicense != null">
        elect_license = #{electLicense,jdbcType=VARCHAR},
      </if>
      <if test="aptitude != null">
        aptitude = #{aptitude,jdbcType=VARCHAR},
      </if>
      <if test="commonTaxPer != null">
        common_tax_per = #{commonTaxPer,jdbcType=VARCHAR},
      </if>
      <if test="smallCarRays != null">
        small_car_rays = #{smallCarRays,jdbcType=INTEGER},
      </if>
      <if test="commonCarNum != null">
        common_car_num = #{commonCarNum,jdbcType=INTEGER},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="dotTelephone != null">
        dot_telephone = #{dotTelephone,jdbcType=VARCHAR},
      </if>
      <if test="dotEmail != null">
        dot_email = #{dotEmail,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="starId != null">
        star_id = #{starId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="vCode != null">
        v_code = #{vCode,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        branch_code = #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null">
        branch_name = #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="dotBank != null">
        dot_bank = #{dotBank,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null">
        bank_number = #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name= #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="dotSignOut != null">
        dot_sign_out = #{dotSignOut,jdbcType=INTEGER},
      </if>
      <if test="isAdvanceMoney != null">
        is_advance_money = #{isAdvanceMoney,jdbcType=INTEGER},
      </if>
    </set>
    where dot_id = #{dotId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.workManager.entity.DotInfo">
    update dot_information
    set dot_star = #{dotStar,jdbcType=VARCHAR},
      dot_name = #{dotName,jdbcType=VARCHAR},
      dot_area = #{dotArea,jdbcType=VARCHAR},
      dot_city = #{dotCity,jdbcType=VARCHAR},
      dot_district = #{dotDistrict,jdbcType=VARCHAR},
      dot_address = #{dotAddress,jdbcType=VARCHAR},
      dot_certificate = #{dotCertificate,jdbcType=VARCHAR},
      dot_quality = #{dotQuality,jdbcType=VARCHAR},
      dot_postcode = #{dotPostcode,jdbcType=VARCHAR},
      dot_class = #{dotClass,jdbcType=VARCHAR},
      dot_features = #{dotFeatures,jdbcType=VARCHAR},
      dot_create_time = #{dotCreateTime,jdbcType=TIMESTAMP},
      dot_create_per = #{dotCreatePer,jdbcType=VARCHAR},
      dot_code = #{dotCode,jdbcType=VARCHAR},
      market_level = #{marketLevel,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      legal_person_sex = #{legalPersonSex,jdbcType=VARCHAR},
      degree_education = #{degreeEducation,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      storage_spare = #{storageSpare,jdbcType=VARCHAR},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      vehicles_nums = #{vehiclesNums,jdbcType=INTEGER},
      contract_start = #{contractStart,jdbcType=TIMESTAMP},
      contract_end = #{contractEnd,jdbcType=TIMESTAMP},
      tax_no = #{taxNo,jdbcType=VARCHAR},
      company_quality = #{companyQuality,jdbcType=VARCHAR},
      regist_fund = #{registFund,jdbcType=VARCHAR},
      buse_regist_time = #{buseRegistTime,jdbcType=TIMESTAMP},
      tax_regist_time = #{taxRegistTime,jdbcType=TIMESTAMP},
      tax_deadline = #{taxDeadline,jdbcType=VARCHAR},
      industry_deadline = #{industryDeadline,jdbcType=VARCHAR},
      buse_regist_number = #{buseRegistNumber,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      tax_point = #{taxPoint,jdbcType=VARCHAR},
      radiate_area = #{radiateArea,jdbcType=VARCHAR},
      dot_state = #{dotState,jdbcType=VARCHAR},
      buse_license_url = #{buseLicenseUrl,jdbcType=VARCHAR},
      office_space_url = #{officeSpaceUrl,jdbcType=VARCHAR},
      spares_space_url = #{sparesSpaceUrl,jdbcType=VARCHAR},
      door_head_url = #{doorHeadUrl,jdbcType=VARCHAR},
      vehicle_url = #{vehicleUrl,jdbcType=VARCHAR},
      service_aid_url = #{serviceAidUrl,jdbcType=VARCHAR},
      consumable_url = #{consumableUrl,jdbcType=VARCHAR},
      basic_data_url = #{basicDataUrl,jdbcType=VARCHAR},
      house_contract_url = #{houseContractUrl,jdbcType=VARCHAR},
      corporate_id_card = #{corporateIdCard,jdbcType=VARCHAR},
      elect_license = #{electLicense,jdbcType=VARCHAR},
      aptitude = #{aptitude,jdbcType=VARCHAR},
      common_tax_per = #{commonTaxPer,jdbcType=VARCHAR},
      small_car_rays = #{smallCarRays,jdbcType=INTEGER},
      common_car_num = #{commonCarNum,jdbcType=INTEGER},
      contact_name = #{contactName,jdbcType=VARCHAR},
      dot_telephone = #{dotTelephone,jdbcType=VARCHAR},
      dot_email = #{dotEmail,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT},
      star_id = #{starId,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      v_code = #{vCode,jdbcType=VARCHAR},
      branch_code = #{branchCode,jdbcType=VARCHAR},
      branch_name = #{branchName,jdbcType=VARCHAR},
      dot_bank = #{dotBank,jdbcType=VARCHAR},
      bank_account = #{bankAccount,jdbcType=VARCHAR},
      bank_number = #{bankNumber,jdbcType=VARCHAR}
    where dot_id = #{dotId,jdbcType=INTEGER}
  </update>

  <select id="selectDotInfo" resultType="com.bonc.rrs.workManager.entity.SelectDot">
    SELECT c.`dot_id` AS dotId,c.dot_state AS dotState, c.dot_name AS dotName,c.dot_short_name dotShortName, c.v_code AS vCode,
    c.dot_star AS dotStar, c.dot_address AS dotAddress,
    c.contact_name AS contactsName FROM `dot_information` AS c
    where 1=1
    and `is_delete`=0
    <if test="code == 1 and nameCode != null and nameCode != ''">
      and dot_name like CONCAT('%',#{nameCode},'%')
    </if>
    <if test="code == 2 and nameCode != null and nameCode != ''">
      and dot_code like CONCAT('%',#{nameCode},'%')
    </if>
    <if test="contactName != null and contactName != ''">
      AND contact_name like CONCAT('%',#{contactName},'%')
    </if>
    <if test="vCode != null and vCode != ''">
      AND v_code like CONCAT('%',#{vCode},'%')
    </if>
    <if test="dotStar != null and dotStar != ''">
      AND dot_star=#{dotStar}
    </if>
    <if test="dotState != null and dotState != ''">
      AND dot_state = #{dotState}
    </if>
    <if test="userId != null">
      and dot_id in (select
        distinct
        dga.dot_id
      from
      (
        select
            di.dot_id,
            di.dot_name,
            GROUP_CONCAT(distinct(concat('-', db.brand_id , '-')) separator '|') brand_ids,
            GROUP_CONCAT(distinct(
            case db.service_type
                when 0 then '1|2|3'
                else db.service_type
            end
            ) separator '|') service_types,
            GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
        from dot_information di
        left join dot_area da on da.dot_id = di.dot_id
        left join dot_brand db on da.dot_id = db.dot_id and da.group_id = db.group_id
        left join biz_region da_br on da.area_id = da_br.id
        group by di.dot_id, db.group_id, db.child_group_id ) dga
    left join
      (
      select
        mai.user_id,
        GROUP_CONCAT(distinct(mai.brand_id) separator '|') brand_ided,
        GROUP_CONCAT(distinct(concat('-', mai.brand_id , '-')) separator '|') brand_ids,
        GROUP_CONCAT(distinct(
        case mai.service_type
        when 0 then '1|2|3'
        else mai.service_type
        end
        ) separator '|') service_types,
        GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
      from
        manager_area_id mai
      inner join biz_region mai_br on
        mai.area_id = mai_br.id
      and mai.user_id = #{userId}
        group by
            mai.user_id,
            mai.group_id,
            mai.child_group_id
      ) mga
      on
      (dga.regcodes regexp mga.regcodes
        and dga.brand_ids regexp mga.brand_ids
        and dga.service_types regexp mga.service_types)
        or (mga.regcodes regexp dga.regcodes
        and mga.brand_ids regexp dga.brand_ids
        and mga.service_types regexp dga.service_types)
        or (dga.regcodes is null or dga.brand_ids is null)
      )
    </if>
<!--    <if test="areaIds != null and areaIds.size>0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_area in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="areaIds != null and areaIds.size>0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_city in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="areaIds != null and areaIds.size>0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_district in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
    order by dot_id desc
    LIMIT #{currentIndex}, #{pageSize}
  </select>

  <select id="getDotInfoCount" resultType="java.lang.Integer">
    SELECT count(1) FROM `dot_information`
    where 1=1
    and `is_delete`=0
    <if test="code == 1">
      and dot_name=#{nameCode}
    </if>
    <if test="code == 2">
      and dot_code=#{nameCode}
    </if>
    <if test="contactName != null">
      AND contact_name=#{contactName}
    </if>
    <if test="vCode != null">
      AND v_code=#{contactName}
    </if>
    <if test="dotStar != null">
      AND dot_star=#{dotStar}
    </if>
    <if test="dotState != null and dotState != ''">
      AND dot_state = #{dotState}
    </if>
    <if test="userId != null">
      and dot_id in (select
        distinct
        dga.dot_id
      from
      (
      select
      di.dot_id,
      di.dot_name,
      GROUP_CONCAT(distinct(concat('-', db.brand_id , '-')) separator '|') brand_ids,
      GROUP_CONCAT(distinct(
      case db.service_type
      when 0 then '1|2|3'
      else db.service_type
      end
      ) separator '|') service_types,
      GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
      from dot_information di
      left join dot_area da on da.dot_id = di.dot_id
      left join dot_brand db on da.dot_id = db.dot_id and da.group_id = db.group_id
      left join biz_region da_br on da.area_id = da_br.id
      group by di.dot_id, db.group_id, db.child_group_id ) dga
      left join
      (
      select
      mai.user_id,
      GROUP_CONCAT(distinct(mai.brand_id) separator '|') brand_ided,
      GROUP_CONCAT(distinct(concat('-', mai.brand_id , '-')) separator '|') brand_ids,
      GROUP_CONCAT(distinct(
      case mai.service_type
      when 0 then '1|2|3'
      else mai.service_type
      end
      ) separator '|') service_types,
      GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
      from
      manager_area_id mai
      inner join biz_region mai_br on
      mai.area_id = mai_br.id
      and mai.user_id = #{userId}
      group by
      mai.user_id,
      mai.group_id,
      mai.child_group_id
      ) mga
      on
      (dga.regcodes regexp mga.regcodes
      and dga.brand_ids regexp mga.brand_ids
      and dga.service_types regexp mga.service_types)
      or (mga.regcodes regexp dga.regcodes
      and mga.brand_ids regexp dga.brand_ids
      and mga.service_types regexp dga.service_types)
      or (dga.regcodes is null or dga.brand_ids is null)
      )
    </if>
<!--    <if test="areaIds != null and areaIds.size >0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_area in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="areaIds != null and areaIds.size>0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_city in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="areaIds != null and areaIds.size>0">-->
<!--      <foreach collection="areaIds" item="id" open="and dot_district in (" close=")" separator=",">-->
<!--        #{id}-->
<!--      </foreach>-->
<!--    </if>-->
  </select>

  <update id="deleteFlagDot" >
    UPDATE `dot_information` SET `is_delete`=1 WHERE `dot_id`=#{id}
  </update>

  <update id="updataFlagDot">
    UPDATE `dot_information` SET `dot_state`=#{status} WHERE `dot_id`=#{id}
  </update>

  <update id="updataScoreDot">
    UPDATE `dot_information` SET `dot_star`=#{star}, `dot_score`=#{score} WHERE `dot_id`= #{id}
  </update>

  <insert id="addUserManager" useGeneratedKeys="true" keyColumn="user_id" keyProperty="userId" parameterType="com.bonc.rrs.workManager.entity.SysUser">
    insert into sys_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
        create_time,
      <if test="employeeNumber != null">
        employee_number,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
        current_timestamp,
      <if test="employeeNumber != null">
        #{employeeNumber,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="addContact" parameterType="com.bonc.rrs.workManager.entity.DotContacts">
    insert into dot_contacts
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contactsId != null">
        contacts_id,
      </if>
      <if test="dotCode != null">
        dot_code,
      </if>
      <if test="contactsName != null">
        contacts_name,
      </if>
      <if test="contactsPhone != null">
        contacts_phone,
      </if>
      <if test="contactsEmail != null">
        contacts_email,
      </if>
        create_time,
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contactsId != null">
        #{contactsId,jdbcType=INTEGER},
      </if>
      <if test="dotCode != null">
        #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="contactsName != null">
        #{contactsName,jdbcType=VARCHAR},
      </if>
      <if test="contactsPhone != null">
        #{contactsPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactsEmail != null">
        #{contactsEmail,jdbcType=VARCHAR},
      </if>
      current_timestamp ,
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>

  <update id="deleteDrivingPermitStatus">
       update driving_permit set is_delete = 1 where is_delete = 0 and dot_id = #{dotId}
  </update>

  <update id="deleteDrivingPermitApproveStatus">
    update driving_permit set is_delete = 1 where is_delete = 0 and dot_id = #{dotId}
  </update>

  <update id="deleteDotBankStatus">
       update dot_bank set is_delete = 1 where is_delete = 0 and dot_id = #{dotId}
  </update>

  <update id="deleteDotBankApproveStatus">
        update dot_bank_approve set is_delete = 1 where is_delete = 0 and dot_id = #{dotId}
  </update>

  <update id="updateDrivingPermitStatus">
       update driving_permit set is_delete = 0 where dot_id = #{dotId}
       and id in <foreach collection ="ids" item="id" separator ="," open="(" close=")">#{id}</foreach >
  </update>

  <update id="updateDrivingPermitApproveStatus">
       update driving_permit_approve set is_delete = 0 where approve_id = #{approveId}
       and driving_id in <foreach collection ="ids" item="id" separator ="," open="(" close=")">#{id}</foreach >
  </update>

  <update id="updateDotBankStatus">
    update dot_bank set is_delete = 0 where dot_id = #{dotId}
    and id in <foreach collection ="ids" item="id" separator ="," open="(" close=")">#{id}</foreach >
  </update>

  <update id="updateDotBankApproveStatus">
    update dot_Bank_approve set is_delete = 0 where approve_id = #{approveId}
    and bank_id in <foreach collection ="ids" item="id" separator ="," open="(" close=")">#{id}</foreach >
  </update>


  <select id="selectCarNo" resultType="java.lang.Integer">
    select count(1) from driving_permit where is_delete = 0 and car_no = #{carNo}
  </select>

  <select id="selectCarNoById" resultType="java.lang.Integer">
    select count(1) from driving_permit where is_delete = 0 and car_no = #{carNo} and id!= #{id}
  </select>

  <select id="selectBankAccount" resultType="java.lang.Integer">
    select count(1) from dot_bank db inner join dot_information di on di.dot_id = db.dot_id  where db.is_delete = 0 and di.is_delete =0 and db.bank_account = #{bankAccount}
  </select>

  <select id="selectBankAccountById" resultType="java.lang.Integer">
    select count(1) from dot_bank db inner join dot_information di on di.dot_id = db.dot_id  where db.is_delete = 0 and di.is_delete =0 and db.bank_account = #{bankAccount} and db.id != #{id}
  </select>

  <insert id="addDrivingPermit" >
    insert into driving_permit
    (dot_id, type , name , car_no,img)
    values
    <foreach collection ="drivingList" item="driving" index= "index" separator =",">
      (
      #{driving.dotId}, #{driving.type}, #{driving.name},#{driving.carNo},#{driving.img}
      )
    </foreach >
  </insert>

  <insert id="addDrivingPermitApprove" >
    insert into driving_permit_approve
    (approve_id,driving_id, dot_id, type , name , car_no, img)
    values
    <foreach collection ="drivingList" item="driving" index= "index" separator =",">
      (
        #{driving.approveId}, #{driving.drivingId}, #{driving.dotId}, #{driving.type}, #{driving.name},#{driving.carNo},#{driving.img}
      )
    </foreach >
  </insert>

  <insert id="addDotBank" >
    insert into dot_bank
    (id, dot_id, dot_bank, bank_number, bank_account, bank_name, tax_no)
    values
    <foreach collection ="bankList" item="bank" index= "index" separator =",">
      (
      #{bank.id}, #{bank.dotId}, #{bank.dotBank}, #{bank.bankNumber},#{bank.bankAccount},#{bank.bankName},#{bank.taxNo}
      )
    </foreach >
  </insert>

  <insert id="addDotBankApprove" >
    insert into dot_bank_approve
    (approve_id, bank_id, dot_id, dot_bank, bank_number, bank_account, bank_name, tax_no)
    values
    <foreach collection ="bankList" item="bank" index= "index" separator =",">
      (
      #{bank.approveId}, #{bank.bankId}, #{bank.dotId}, #{bank.dotBank}, #{bank.bankNumber},#{bank.bankAccount},#{bank.bankName},#{bank.taxNo}
      )
    </foreach >
  </insert>

  <update id="updateDrivingPermit" >
    update driving_permit set name = #{drivingPermitEntity.name},car_no = #{drivingPermitEntity.carNo},img= #{drivingPermitEntity.img} where id = #{drivingPermitEntity.id}
  </update>

  <update id="updateDrivingPermitApprove" >
    update driving_permit_approve set name = #{drivingPermitEntity.name},car_no = #{drivingPermitEntity.carNo},img= #{drivingPermitEntity.img} where id = #{drivingPermitEntity.id}
  </update>

  <update id="updateDotBankApprove" >
    update dot_bank_approve set name = #{dotBankApproveEntity.name},car_no = #{drivingPermitEntity.carNo},img= #{drivingPermitEntity.img} where id = #{drivingPermitEntity.id}
  </update>

  <insert id="addDotAreaInfo" >
    insert into dot_area
        (dot_id, area_id, group_id)
    values
    <foreach collection ="arealist" item="reddemCode" index= "index" separator =",">
    (
        #{dotId}, #{reddemCode.areaId}, #{groupId}
    )
    </foreach >
  </insert>
  <insert id="addDotBrandInfo" >
    insert into dot_brand
        (dot_id, brand_id, group_id, date_time, service_type, child_group_id)
    values
    <foreach collection ="brandList" item="brand" index= "index" separator =",">
    (
        #{dotId}, #{brand.brandId},#{brand.groupId},#{dateTime},#{brand.serviceType},#{brand.childGroupId}
    )
    </foreach >
  </insert>

  <select id="selectDotCodeById" resultType="java.lang.String">
    select dot_code from dot_information where dot_id=#{id}
  </select>

  <update id="updateAreaDot">
      update dot_area
      <set >
        <if test="areaId != null" >
          area_id = #{areaId}
        </if>
      </set>
      where dot_id = #{id}
  </update>

  <update id="updateSysUserName">
    update sys_user set username=#{username}
    where user_id=#{id}
  </update>

  <!--<select id="selectFilelist" resultType="com.bonc.rrs.workManager.entity.DotInfo">-->
     <!--select-->
  <!--</select>-->

  <select id="selectDotInfobydotId" resultType="com.bonc.rrs.workManager.entity.SelectDot">
    SELECT c.`dot_id` AS dotId, c.dot_name AS dotName,
    c.dot_star AS dotStar, c.dot_address AS dotAddress,
    c.contact_name AS contactsName FROM `dot_information` AS c
    where  dot_id in
    <foreach collection="dotIds" item="dotId" separator ="," open="(" close=")">
      #{dotId}
    </foreach>
  </select>

  <update id="updateEmployee">
    update biz_employee set user_id=#{userId} where id=#{id}
  </update>

  <select id="selectDictDetaillist" resultType="java.util.Map">
    SELECT c.`id` AS id, b.`detail_number` AS number, b.`detail_name` AS `name`
    FROM `sys_dictionary` AS c, `sys_dictionary_detail` AS b
    WHERE c.`id`=b.`dictionary_id` AND c.`dic_number`=#{key}
  </select>

  <select id="selectContactId" resultType="java.lang.Integer">
    SELECT b.`contacts_id` FROM `dot_information` AS d,
    `dot_contacts` AS b WHERE d.`dot_code`=b.`dot_code`
    AND `dot_id`=#{id} limit 1
  </select>

  <update id="updateSysContactState">
    UPDATE `sys_user` SET `status`=0 WHERE `user_id`=#{id}
  </update>

  <update id="updateCarNumByDotId">
        update dot_information di set di.vehicles_nums = #{number},di.common_car_num = #{number} where dot_id = #{dotId}
  </update>

  <update id="updateApproveCarNumById">
        update dot_information_approve di set di.vehicles_nums = #{number},di.common_car_num = #{number} where di.approve_id = #{id}
  </update>
  <select id="selectDotAreaId" resultType="java.lang.Integer">
    select `id` from `dot_area` where `dot_id`=#{dotId}
  </select>

  <update id="updateDotAreaAreaId">
    update dot_area
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="area_id = case" suffix="end,">
        <foreach collection="list" item="i" index="index">
          <if test="i.id != null">
            when id=#{i.id} then #{i.areaId}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    dot_id=#{dotId}
  </update>

  <select id="selectWorderAreaDot" parameterType="java.lang.String" resultType="com.bonc.rrs.workManager.entity.WorderAreaDotEntity">
    select
      distinct
      a.id areaId,
      b.dot_id dotId,
      c.dot_name dotName
    from
      biz_region a
        inner join dot_area b on
            a.id = b.area_id
        inner join dot_information c on
            b.dot_id = c.dot_id and b.is_delete = '0' and c.dot_state = '1' and c.is_delete = 0
        inner join baidu_grid_basis bgb on
            bgb.region_id = a.id and  bgb.state = 0
    where
      c.dot_id is not null
      and a.regcode in ${regcode}
  </select>

  <!--自动派单根据工单区域的regcode和工单的品牌筛选网格启用状态的辐射区域覆盖此工单的服务经理-->
  <select id="selectWorderAreaManager" resultType="java.lang.Integer">
    select distinct(m.user_id) as user_id
      from manager_area_id m
        inner join baidu_grid_basis bgb on
            m.area_id = bgb.region_id and bgb.state = 0
        left join biz_region b on
            m.area_id = b.id
        left join sys_user s on
            m.user_id = s.user_id and s.status = 1
        join sys_user_role r on
            s.user_id = r.user_id
      and r.role_id = 3
      where b.regcode in ${regcode}
      and m.brand_id = #{brandId}
    <if test="serviceType != null">
      and (m.service_type = #{serviceType} or m.service_type = 0)
    </if>
  </select>

  <!--自动派单根据工单区域的regcode和工单的品牌筛选网格启用状态的辐射区域覆盖此工单的服务经理-->
  <select id="selectListWorderAreaManager" resultType="java.lang.Integer">
    select distinct(t.user_id)
    from (
           select distinct(m.user_id) as user_id
           from manager_area_id m
                  inner join baidu_grid_basis bgb on
             m.area_id = bgb.region_id and bgb.state = 0
                  left join biz_region b on
             m.area_id = b.id
                  left join sys_user s on
             m.user_id = s.user_id and s.status = 1
                  join sys_user_role r on
                 s.user_id = r.user_id
               and r.role_id = 3
           where b.regcode in ${regcode}
             and m.brand_id = #{brandId}
           union all
           select m.user_id as user_id
           from manager_area_id m
                  inner join baidu_grid_basis bgb on
             m.area_id = bgb.region_id and bgb.state = 0
                  left join biz_region b on
             m.area_id = b.id
                  left join sys_user s on
                 m.user_id = s.user_id
               and s.status = 1
                  join sys_user_role r on
                 s.user_id = r.user_id
               and r.role_id = 3
           where b.regcode in ${regcode}
           group by m.user_id
           having count(m.brand_id) &lt;= 0) t
  </select>

  <!--手动派单根据工单区域的regcode筛选辐射区域覆盖此工单的服务经理-->
  <select id="selectManagerArea" resultType="java.lang.Integer">
    select distinct(m.user_id)
    from manager_area_id m
    left join biz_region b on m.area_id = b.id
    left join sys_user s on m.user_id = s.user_id and s.status = 1
    join sys_user_role r on s.user_id = r.user_id and r.role_id = 3
    join baidu_grid_basis bgb on bgb.region_id = b.id and bgb.state = 0
    where b.regcode in ${regcode}
  </select>

  <!--手动派单根据工单区域的regcode筛选辐射区域覆盖此工单的服务经理-->
  <select id="updateManagerBrand" resultType="java.lang.Integer">
    select
        distinct(m.user_id) as user_id
    from manager_area_id m
        left join sys_user s on
            m.user_id = s.user_id and s.status = 1
        join sys_user_role r on
            s.user_id = r.user_id and r.role_id = 3
        where m.brand_id = #{brandId}
      <if test="userId != null">
        and s.user_id != #{userId}
      </if>
      <if test="serviceType != null">
        and (m.service_type = #{serviceType}  or m.service_type = 0)
      </if>
  </select>

  <!--手动派单根据工单区域的regcode筛选辐射区域覆盖此工单的服务经理-->
  <select id="updateManagerArea" resultType="java.lang.Integer">
    select distinct(m.user_id) as user_id
      from manager_area_id m
        inner join baidu_grid_basis bgb on
            m.area_id = bgb.region_id and bgb.state = 0
        left join biz_region b on
            m.area_id = b.id
        left join sys_user s on
            m.user_id = s.user_id and s.status = 1
        join sys_user_role r on
            s.user_id = r.user_id
      and r.role_id = 3
      where b.regcode in ${regcode}
      and m.brand_id = #{brandId}
    <if test="userId != null">
      and s.user_id != #{userId}
    </if>
    <if test="serviceType != null">
      and (m.service_type = #{serviceType}  or m.service_type = 0)
    </if>
  </select>

  <select id="export" resultMap="BaseResultMap">
    SELECT di.*  ,br.an,br.bn,br.cn
    FROM dot_information di, dot_area da ,
    (SELECT a.id aid,a.name an,b.id bid,b.name bn,c.id cid,c.name cn
    FROM biz_region a , biz_region b,biz_region c
    where a.id=b.pid AND b.id=c.pid
     ) br
    where di.dot_id=da.dot_id AND da.area_id=br.cid
    AND di.dot_state=1 and di.is_delete=0
    UNION
    SELECT di.*  ,br.an,br.bn,br.cn
    FROM dot_information di, dot_area da ,
    (SELECT a.id aid,a.name an,b.id bid,b.name bn,c.id cid,c.name cn
    FROM biz_region a , biz_region b,biz_region c
    where a.id=b.pid AND b.id=c.pid
     ) br
    where di.dot_id=da.dot_id AND da.area_id=br.bid
    AND di.dot_state=1 and di.is_delete=0
    UNION
    SELECT di.*  ,br.an,br.bn,br.cn
    FROM dot_information di, dot_area da ,
    (SELECT a.id aid,a.name an,b.id bid,b.name bn,c.id cid,c.name cn
    FROM biz_region a , biz_region b,biz_region c
    where a.id=b.pid AND b.id=c.pid
     ) br
    where di.dot_id=da.dot_id AND da.area_id=br.aid
    AND di.dot_state=1 and di.is_delete=0
  </select>


  <select id="getDotByCurrentUser" resultType="com.bonc.rrs.workManager.entity.dto.DotInfoDto">
        select c.dot_id from sys_user a
        left join dot_contacts b on a.user_id = b.contacts_id
        left join dot_information c on b.dot_code = c.dot_code
        where a.user_id = #{userId}
    </select>
  <delete id="deleteDotArea">
    delete from dot_area where dot_id=#{dotId}
  </delete>
  <delete id="deleteDotBrand">
    delete from dot_brand where dot_id=#{dotId}
  </delete>
  <select id="findByBrand" resultType="java.util.HashMap">
    select brand_id as brandId, group_id as groupId, service_type as serviceType, child_group_id as childGroupId from dot_brand where dot_id=#{dotId}
  </select>

  <select id="selectWorderAreaBrandDot" resultType="com.bonc.rrs.workManager.entity.WorderAreaDotEntity">
    select
      a.id areaId,
      b.dot_id dotId,
      c.dot_name dotName
    from
      biz_region a
        inner join dot_area b on
        a.id = b.area_id
        inner join dot_information c on
        b.dot_id = c.dot_id and c.is_delete = '0'
        inner join dot_brand db on
        db.dot_id = c.dot_id and db.group_id = b.group_id
        inner join baidu_grid_basis bgb on
        bgb.region_id = a.id and  bgb.state = 0
    where
      c.dot_id is not null
      <if test="brandId != null">
        db.brand_id = #{brandId}
      </if>
      and a.regcode in ${regcode}
  </select>
  <select id="selectWorderAreaDotByBrand" resultType="java.lang.Integer">
    select
        distinct
        di.dot_id
    from
        dot_information di,
      (
        select
            da.dot_id
        from
          biz_region a,
          dot_area da,
          dot_brand db,
          baidu_grid_basis bgb
        where
          bgb.region_id = a.id
          and da.group_id = db.group_id
          and da.dot_id = db.dot_id
          and da.area_id = a.id
          and bgb.state = 0
          and db.brand_id = #{brandId}
          and (a.regcode = #{provinceRegcode} or a.regcode like concat(#{cityRegcode},'%'))
        <if test="serviceType != null">
          and (db.service_type = #{serviceType} or db.service_type = 0)
        </if>
      ) t
    where
    di.dot_id = t.dot_id
    and di.dot_state = '1'
    and di.is_delete = 0
  </select>

  <select id="selectWorderAreaDotByRegcode" resultType="java.lang.Integer">
    select
      distinct
      di.dot_id
    from
      dot_information di,
      (
        select
          da.dot_id
        from
          biz_region a,
          dot_area da,
          dot_brand db,
          baidu_grid_basis bgb
        where
          bgb.region_id = a.id
          and da.group_id = db.group_id
          and da.dot_id = db.dot_id
          and da.area_id = a.id
          and bgb.state = 0
          and db.brand_id = #{brandId}
          and a.regcode in ${regcode}
        <if test="serviceType != null">
          and (db.service_type = #{serviceType}  or db.service_type = 0)
        </if>
      ) t
    where
      di.dot_id = t.dot_id
      and di.dot_state = '1'
      and di.is_delete = 0
    <if test="dotId != null">
      and di.dot_id != #{dotId}
    </if>
  </select>

  <select id="selectDot" resultType="com.bonc.rrs.workManager.entity.DotInfoExport" parameterType="java.util.Map">
    select
      di.*
    from
      dot_information di
    where di.is_delete = 0
    <if test="nameCode != null and nameCode != ''">
        and di.dot_name like concat('%',#{nameCode},'%')
    </if>
    <if test="contactName != null and contactName != ''">
      and di.contact_name = #{contactName}
    </if>
    <if test="vCode != null and vCode != ''">
      and di.v_code = #{vCode}
    </if>
    <if test="userId != null">
      and dot_id in (
        select
          distinct
          dga.dot_id
        from
        (
          select
            di.dot_id,
            di.dot_name,
            GROUP_CONCAT(distinct(concat('-', db.brand_id , '-')) separator '|') brand_ids,
            GROUP_CONCAT(distinct(
              case db.service_type when 0 then '1|2|3'
                else db.service_type
              end
            ) separator '|') service_types,
            GROUP_CONCAT(distinct(concat(da_br.regcode, '.*')) separator '|') regcodes
            from dot_information di
            left join dot_area da on da.dot_id = di.dot_id
            left join dot_brand db on da.dot_id = db.dot_id and da.group_id = db.group_id
            left join biz_region da_br on da.area_id = da_br.id
            group by di.dot_id, db.group_id, db.child_group_id ) dga
            left join
        (
        select
          mai.user_id,
          GROUP_CONCAT(distinct(mai.brand_id) separator '|') brand_ided,
          GROUP_CONCAT(distinct(concat('-', mai.brand_id , '-')) separator '|') brand_ids,
          GROUP_CONCAT(distinct(
          case mai.service_type
            when 0 then '1|2|3'
            else mai.service_type
          end
          ) separator '|') service_types,
            GROUP_CONCAT(distinct(concat(mai_br.regcode, '.*')) separator '|') regcodes
        from
            manager_area_id mai
        inner join biz_region mai_br on
            mai.area_id = mai_br.id
            and mai.user_id = #{userId}
        group by
          mai.user_id,
          mai.group_id,
          mai.child_group_id
        ) mga
        on
        (dga.regcodes regexp mga.regcodes
          and dga.brand_ids regexp mga.brand_ids
          and dga.service_types regexp mga.service_types)
        or (mga.regcodes regexp dga.regcodes
          and mga.brand_ids regexp dga.brand_ids
          and mga.service_types regexp dga.service_types)
        or (dga.regcodes is null or dga.brand_ids is null)
      )
    </if>
  </select>

  <select id="selectDotDriving" resultType="com.bonc.rrs.workManager.entity.DrivingExport" parameterType="java.util.Map">
    select di.dot_name,dp.`type`,dp.name,dp.car_no,dp.img
    from driving_permit dp
    left join dot_information di on di.dot_id = dp.dot_id
    where dp.is_delete = 0
    and dp.dot_id = #{dotId}
  </select>

  <select id="selectDotAreaBrand" resultType="com.bonc.rrs.workManager.entity.DotAreaBrand" parameterType="java.util.Map">
      select
          distinct
          di.dot_name ,
          di.v_code ,
          di.branch_code ,
          di.branch_name ,
          br.an province,
          br.bn city,
          br.cn district,
          b2.brand_name ,
          db.service_type as serviceTypeName,
          da.group_id ,
          db.child_group_id
      from
          dot_information di,
          dot_area da,
          (
              select
                  a.id aid,
                  a.name an,
                  b.id bid,
                  b.name bn,
                  c.id cid,
                  c.name cn
              from
                  biz_region a ,
                  biz_region b,
                  biz_region c
              where
                  a.id = b.pid
                and b.id = c.pid
          ) br,
          dot_brand db,
          brand b2
      where
          di.dot_state = 1
        and di.is_delete = 0
        and di.dot_id = da.dot_id
        and da.is_delete = 0
        and (da.area_id = br.cid or da.area_id = br.aid or da.area_id = br.bid)
        and db.dot_id = di.dot_id
        and db.brand_id = b2.id
        and da.group_id = db.group_id
      <if test="nameCode != null and nameCode != ''">
        and di.dot_name like concat('%',#{nameCode},'%')
      </if>
      <if test="contactName != null and contactName != ''">
        and di.contact_name = #{contactName}
      </if>
      <if test="vCode != null and vCode != ''">
        and di.v_code = #{vCode}
      </if>
  </select>

  <select id="queryAddedMaterialTypePreempCountByWorderId" resultType="integer" parameterType="integer">
    select
        count(*)
    from
        worder_information_attribute wia
    where
        `attribute` = 'addedMaterialType'
        and is_delete = 0
        and wia.worder_id = #{worderId}
  </select>

  <select id="queryTemplateAddedMaterialTypeByTemplateId" resultType="com.bonc.rrs.worder.entity.WorderTemplateAddedMaterialTypeEntity" parameterType="integer">
    select
        wtamt.*,
        mt.materiel_child_type materielTypeName
    from
        worder_template_added_material_type wtamt
    inner join materiel_type mt on
        wtamt.materiel_type_id = mt.id
    where
        wtamt.template_id = #{templateId}
  </select>

  <select id="queryPmMobileByWorderId" resultType="string" parameterType="integer">
    select
        su.mobile
    from
        worder_information wi
    inner join sys_user su on
        wi.pm_id = su.user_id
    where
        su.status = 1
        and wi.worder_id = #{worderId}
    limit 1
  </select>

  <select id="queryDotAddedMaterialTypeRealStock" resultType="com.bonc.rrs.workManager.entity.vo.DotAddedMaterialTypeRealStockVo">
    select
        mttomi.materiel_type_id,
        sum(rssg.real_goods_total) goods_total,
        rssg.store_id,
        rssg.store_position_id,
        di.dot_name
    from
        rrs_store_stock_goods rssg
    inner join rrs_store_position_info rspi on
        rssg.store_position_id = rspi.id
    inner join rrs_store_basic_info rsbi on
        rspi.store_id = rsbi.id
    inner join rrs_user_store_relation rusr on
        rusr.store_id = rsbi.id
    inner join sys_user su on
        rusr.user_id = su.user_id
    inner join dot_contacts dc on
        su.username = dc.contacts_name
    inner join dot_information di on
        di.dot_code = dc.dot_code
    inner join (
        select
            cast(sdd.detail_number as unsigned INTEGER) as materiel_type_id, cast(sdd.detail_name as unsigned INTEGER) as materiel_id
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = 'added_material_type_to_material_id') mttomi on
        rssg.goods_id = mttomi.materiel_id
    where
        rspi.`type` = 1
        and rsbi.is_delete = 0
        and di.dot_id = #{dotId}
        and rssg.goods_type = 4
        and mttomi.materiel_type_id in
        <foreach item="v" collection="materielTypeIdList" open="(" separator="," close=")">
            #{v}
        </foreach>
    group by mttomi.materiel_type_id
  </select>

  <select id="getAddMaterialStockThreshold" resultType="integer">
    select
        cast(sdd.detail_number as unsigned INTEGER) as threshold
    from
        sys_dictionary sd
    inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
    where
        sd.dic_number = 'add_material_stock_threshold'
  </select>

  <select id="getDotMobileByDotId" resultType="string" parameterType="integer">
    select
        dc.contacts_phone
    from
        dot_contacts dc
    inner join dot_information di on
        dc.dot_code = di.dot_code
    where
        di.is_delete = 0
        and di.dot_state = 1
        and di.dot_id = #{dotId}
  </select>

  <select id="getDotSmsMessage" resultType="string">
    select
        sdd.remark
    from
        sys_dictionary sd
    inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
    where
        sd.dic_number = 'auth_code'
        and sdd.detail_number = '3'
  </select>

  <select id="getPmSmsMessage" resultType="string">
    select
        sdd.remark
    from
        sys_dictionary sd
    inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
    where
        sd.dic_number = 'auth_code'
        and sdd.detail_number = '4'
  </select>

  <select id="getDotNameByDotId" resultType="string" parameterType="integer">
    select
        di.dot_name
    from
        dot_information di
    where
        di.dot_id = #{dotId}
  </select>
</mapper>