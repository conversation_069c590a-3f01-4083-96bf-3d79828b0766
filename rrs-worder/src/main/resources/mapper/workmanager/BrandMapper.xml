<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.BrandMapper">

    <select id="selectBrandCarNamelist" resultType="java.util.Map">
        SELECT b.`brand_name` AS brandName, d.`id` AS id,
        `car_type_name` AS typeName FROM `brand` AS b,
        `brand_car_type` AS c, `car_type` AS d WHERE
        b.`id`=c.`brand_id` AND c.`car_type_id`=d.`id`
        <if test="brandName != null">
            AND b.`brand_name`=#{brandName}
        </if>
    </select>
    <select id="selectBrandCarName" resultType="java.util.Map">
        select b.brand_name,i.company_order_number
        from worder_information i
                 inner join worder_template t on i.template_id = t.id
                 inner join brand b on t.brand_id = b.id
        where i.worder_no = #{worderNo}
    </select>
    <select id="selectBrandCarID" resultType="java.lang.Integer">
        select b.id
        from worder_information i
                 inner join worder_template t on i.template_id = t.id
                 inner join brand b on t.brand_id = b.id
        where i.worder_no = #{worderNo}
    </select>

</mapper>