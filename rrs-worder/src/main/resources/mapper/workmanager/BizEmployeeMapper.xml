<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.BizEmployeeMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.BizEmployee">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="gender" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contact" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="id_card" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="department_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="address" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="zip_code" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="gender_value" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="user_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="isdelete" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="status" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>

  <sql id="Base_Column_List">
    id, name, gender, contact, id_card, department_id, address, zip_code, gender_value,status,
    user_id , isdelete
  </sql>

  <select id="selectEmployee" resultMap="BaseResultMap">
    select
    *
    from biz_employee
    where 1=1
    <if test="code == 1">
      and name=#{nameId}
    </if>
    <if test="code == 2">
      and user_id=#{nameId}
    </if>
    LIMIT #{currentIndex}, #{pageSize}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_employee
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.workManager.entity.BizEmployee">
    insert into biz_employee (id, name, gender,
      contact, id_card, department_id,
      address, zip_code, gender_value,
      user_id, postcode, isdelete
      )
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR},
      #{contact,jdbcType=VARCHAR}, #{idCard,jdbcType=VARCHAR}, #{departmentId,jdbcType=INTEGER},
      #{address,jdbcType=VARCHAR}, #{zipCode,jdbcType=INTEGER}, #{genderValue,jdbcType=VARCHAR},
      #{userId,jdbcType=INTEGER}, #{postcode,jdbcType=VARCHAR},#{isdelete,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bonc.rrs.workManager.entity.BizEmployee">
    insert into biz_employee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="genderValue != null">
        gender_value,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="isdelete != null">
        isdelete,
      </if>
      <!--<if test="departmentName != null">-->
        <!--department_name,-->
      <!--</if>-->
    </trim>
    <trim prefix=" values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=INTEGER},
      </if>
      <if test="genderValue != null">
        #{genderValue,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="isdelete != null">
        #{isdelete,jdbcType=VARCHAR},
      </if>
      <!--<if test="departmentName != null">-->
        <!--#{departmentName,jdbcType=VARCHAR},-->
      <!--</if>-->
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.workManager.entity.BizEmployee">
    update biz_employee
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        id_card = #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=INTEGER},
      </if>
      <if test="genderValue != null">
        gender_value = #{genderValue,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="isdelete != null">
        isdelete = #{isdelete,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.workManager.entity.BizEmployee">
    update biz_employee
    set name = #{name,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      id_card = #{idCard,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=INTEGER},
      address = #{address,jdbcType=VARCHAR},
      zip_code = #{zipCode,jdbcType=INTEGER},
      gender_value = #{genderValue,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      isdelete = #{isdelete,jdbcType=VARCHAR}
--       department_name = #{departmentName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from biz_employee
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectDepatInfo" resultType="java.util.Map">
    select ID as id, departname as departname from sys_depart
  </select>

  <select id="getDeptInfo" resultType="com.bonc.rrs.workManager.entity.dto.SysDeptDto">
    select ID as id, departname as departName,parentdepartid  as parentDepartId  from sys_depart
    where parentdepartid = 0;
  </select>

  <select id="getDepartByParentId" resultType="com.bonc.rrs.workManager.entity.dto.SysDeptDto">
    select ID as id, departname as departName,parentdepartid  as parentDepartId  from sys_depart
    where parentdepartid = #{parentId};
  </select>

</mapper>