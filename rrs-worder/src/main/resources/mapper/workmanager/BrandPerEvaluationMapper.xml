<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.BrandPerEvaluationMapper">
    <update id="callProcedure" statementType="CALLABLE">
        { call ${procedureName} }
    </update>

    <select id="queryLxMonthlyFixTime" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
        select
        sum(t1.orders) as total,
        ROUND(sum(t1.duration), 2) as totalScore,
        <if test="flags == 0">
            t1.regione as name,
        </if>
        <if test="flags == 1 or flags == 2">
            SUBSTRING(max(di.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 3">
            max(br.name) as name,
        </if>
        ROUND(if(sum(t1.orders) = 0, 0, sum(t1.duration) / sum(t1.orders)), 2) as score
        from
        GetMonthlyData t1
        <if test="flags == 1 or flags == 2">
            left join dot_information di on t1.dot_id = di.dot_id and di.is_delete = 0
        </if>
        <if test="flags == 3">
            left join biz_region br on t1.province_id = br.id
        </if>
        where t1.create_time >= concat(#{param.startDate},' 00:00:00')
        and t1.create_time &lt;= concat(#{param.endDate},' 23:59:59')
        <if test="param.city != null">
            and t1.city_id = #{param.city}
        </if>
        <if test="param.province != null">
            and t1.province_id = #{param.province}
        </if>
        <if test="param.regione != null">
            and t1.regione = #{param.regione}
        </if>
        <if test="userId != null">
            and exists(
            select 1 from
            (select
            distinct SUBSTR(br2.regcode, 1, 6) as regcode
            from
            manager_area_id mai
            inner join biz_region br on
            mai.area_id = br.id
            inner join biz_region br2 on
            br2.regcode like CONCAT(br.regcode, '%') and br2.type in (2,3) and mai.user_id = #{userId}
            ) r where r.regcode = t1.regcode
            )
        </if>
        group by
        <if test="flags == 0">
            t1.regione
        </if>
        <if test="flags == 1 or flags == 2">
            t1.dot_id
        </if>
        <if test="flags == 3">
            t1.province_id
        </if>
    </select>

    <select id="queryLxQuarterly30dayFix" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
        select
        ROUND(sum(t1.duration), 2) as total,
        sum(t1.orders) as totalScore,
        <if test="flags == 0">
            t1.regione as name,
        </if>
        <if test="flags == 1 or flags == 2">
            SUBSTRING(max(di.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 3">
            max(br.name) as name,
        </if>
        ROUND(if(sum(t1.duration) = 0, 0, sum(t1.orders) / sum(t1.duration)), 2) as score
        from
        GetQuarterlyData t1
        <if test="flags == 1 or flags == 2">
            left join dot_information di on t1.dot_id = di.dot_id and di.is_delete = 0
        </if>
        <if test="flags == 3">
            left join biz_region br on t1.province_id = br.id
        </if>
        where t1.create_time >= concat(#{param.startDate},' 00:00:00')
        and t1.create_time &lt;= concat(#{param.endDate},' 23:59:59')
        <if test="param.city != null">
            and t1.city_id = #{param.city}
        </if>
        <if test="param.province != null">
            and t1.province_id = #{param.province}
        </if>
        <if test="param.regione != null">
            and t1.regione = #{param.regione}
        </if>
        <if test="userId != null">
            and exists(
            select 1 from
            (select
            distinct SUBSTR(br2.regcode, 1, 6) as regcode
            from
            manager_area_id mai
            inner join biz_region br on
            mai.area_id = br.id
            inner join biz_region br2 on
            br2.regcode like CONCAT(br.regcode, '%') and br2.type in (2,3) and mai.user_id = #{userId}
            ) r where r.regcode = t1.regcode
            )
        </if>
        group by
        <if test="flags == 0">
            t1.regione
        </if>
        <if test="flags == 1 or flags == 2">
            t1.dot_id
        </if>
        <if test="flags == 3">
            t1.province_id
        </if>
    </select>

    <select id="queryLxMonthlyClientSatisfy" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
        select
        sum(t1.orders) as total,
        ROUND(sum(t1.satisfaction_score), 2) as totalScore,
        <if test="flags == 0">
            t1.regione as name,
        </if>
        <if test="flags == 1 or flags == 2">
            SUBSTRING(max(di.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 3">
            max(br.name) as name,
        </if>
        ROUND(if(sum(t1.orders) = 0, 0, sum(t1.satisfaction_score) / sum(t1.orders)), 2) as score
        from
        GetMonthlySatisfyData t1
        <if test="flags == 1 or flags == 2">
            left join dot_information di on t1.dot_id = di.dot_id and di.is_delete = 0
        </if>
        <if test="flags == 3">
            left join biz_region br on t1.province_id = br.id
        </if>
        where t1.create_time >= concat(#{param.startDate},' 00:00:00')
        and t1.create_time &lt;= concat(#{param.endDate},' 23:59:59')
        <if test="param.city != null">
            and t1.city_id = #{param.city}
        </if>
        <if test="param.province != null">
            and t1.province_id = #{param.province}
        </if>
        <if test="param.regione != null">
            and t1.regione = #{param.regione}
        </if>
        <if test="userId != null">
            and exists(
            select 1 from
            (select
            distinct SUBSTR(br2.regcode, 1, 6) as regcode
            from
            manager_area_id mai
            inner join biz_region br on
            mai.area_id = br.id
            inner join biz_region br2 on
            br2.regcode like CONCAT(br.regcode, '%') and br2.type in (2,3) and mai.user_id = #{userId}
            ) r where r.regcode = t1.regcode
            )
        </if>
        group by
        <if test="flags == 0">
            t1.regione
        </if>
        <if test="flags == 1 or flags == 2">
            t1.dot_id
        </if>
        <if test="flags == 3">
            t1.province_id
        </if>
    </select>

    <select id="queryLxRepair5dayFix" resultType="com.bonc.rrs.workManager.entity.vo.ScoreVo">
        select
        sum(t1.orders) as total,
        ROUND(sum(t1.duration), 2) as totalScore,
        <if test="flags == 0">
            t1.regione as name,
        </if>
        <if test="flags == 1 or flags == 2">
            SUBSTRING(max(di.dot_name),1,6) as name,
            t1.dot_id,
        </if>
        <if test="flags == 3">
            max(br.name) as name,
        </if>
        ROUND(if(sum(t1.orders) = 0, 0, sum(t1.duration) / sum(t1.orders)), 2) as score
        from
        GetMonthlyRepairData t1
        <if test="flags == 1 or flags == 2">
            left join dot_information di on t1.dot_id = di.dot_id and di.is_delete = 0
        </if>
        <if test="flags == 3">
            left join biz_region br on t1.province_id = br.id
        </if>
        where t1.create_time >= concat(#{param.startDate},' 00:00:00')
        and t1.create_time &lt;= concat(#{param.endDate},' 23:59:59')
        <if test="param.city != null">
            and t1.city_id = #{param.city}
        </if>
        <if test="param.province != null">
            and t1.province_id = #{param.province}
        </if>
        <if test="param.regione != null">
            and t1.regione = #{param.regione}
        </if>
        <if test="userId != null">
            and exists(
            select 1 from
            (select
            distinct SUBSTR(br2.regcode, 1, 6) as regcode
            from
            manager_area_id mai
            inner join biz_region br on
            mai.area_id = br.id
            inner join biz_region br2 on
            br2.regcode like CONCAT(br.regcode, '%') and br2.type in (2,3) and mai.user_id = #{userId}
            ) r where r.regcode = t1.regcode
            )
        </if>
        group by
        <if test="flags == 0">
            t1.regione
        </if>
        <if test="flags == 1 or flags == 2">
            t1.dot_id
        </if>
        <if test="flags == 3">
            t1.province_id
        </if>
    </select>
</mapper>