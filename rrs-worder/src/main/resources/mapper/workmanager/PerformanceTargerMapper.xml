<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.workManager.dao.PerformanceTargerMapper">

    <select id="selectListPage" resultType="com.bonc.rrs.workManager.entity.PerformanceTargerEntity">
        select
            pt.*,b.brand_name , br.name as province_name, bpe.indicator_desc, bpe.unit
        from
            performance_targer pt ,
            brand b ,
            brand_per_evaluation bpe ,
            biz_region br
        where
            pt.brand_id = b.id
          and pt.province_id = br.id
          and pt.indicator_id = bpe.id
          and pt.delete_state = 0
        <if test="performanceTargerVo.cycle != null and performanceTargerVo.cycle != ''">
            and pt.cycle = #{performanceTargerVo.cycle}
        </if>
        <if test="performanceTargerVo.brandId != null">
            and pt.brand_id = #{performanceTargerVo.brandId}
        </if>
        <if test="performanceTargerVo.provinceId != null">
            and pt.province_id = #{performanceTargerVo.provinceId}
        </if>
        <if test="performanceTargerVo.indicatorId != null">
            and pt.indicator_id = #{performanceTargerVo.indicatorId}
        </if>
        <if test="userId != null">
            and exists(
                select 1 from
                    (select
                            distinct SUBSTR(br2.regcode, 1, 3) as regcode ,
                            mai.brand_id
                        from
                        manager_area_id mai
                        inner join biz_region br on
                        mai.area_id = br.id
                        inner join biz_region br2 on
                        br2.regcode like CONCAT(br.regcode, '%') and br2.type in (1,2,3) where mai.user_id = #{userId}
                    ) r where r.regcode = br.regcode and pt.brand_id = r.brand_id )
        </if>
        <if test="dotId != null">
            and exists(
                select 1 from
                (
                    select
                        distinct SUBSTR(br2.regcode, 1, 3) as regcode ,
                        db.brand_id
                    from
                        dot_area da
                    inner join dot_brand db on
                        da.dot_id = db.dot_id and da.group_id = db.group_id
                    inner join biz_region br on
                        da.area_id = br.id
                    inner join biz_region br2 on
                        br2.regcode like CONCAT(br.regcode, '%') and br2.type in (1,2,3)
                    where da.dot_id = #{dotId}
                ) r where r.regcode = br.regcode and pt.brand_id = r.brand_id )
        </if>
        order by pt.id
    </select>
    <select id="getAreaAndBrandByUser" resultType="java.util.Map">
        select
            t1.id,
            t1.name ,
            t1.regcode ,
            t2.brand_id ,
            t3.brand_name
        from
            biz_region t1,
            (
                select
                    distinct SUBSTR(br2.regcode, 1, 3) as regcode ,
                             mai.brand_id
                from
                    manager_area_id mai
                        inner join biz_region br on
                        mai.area_id = br.id
                        inner join biz_region br2 on
                                br2.regcode like CONCAT(br.regcode, '%')
                            and br2.type in (1, 2, 3)
                where
                    mai.user_id = #{userId}) t2 ,
            brand t3
        where
            t1.regcode = t2.regcode and t2.brand_id = t3.id
    </select>
    <select id="getAreaAndBrandByDot" resultType="java.util.Map">
        select
            distinct t1.id,
                     t1.name ,
                     t1.regcode ,
                     t2.brand_id ,
                     t3.brand_name
        from
            biz_region t1,
            (
                select
                    distinct SUBSTR(br2.regcode, 1, 3) as regcode ,
                             db.brand_id
                from
                    dot_area da
                        inner join dot_brand db on
                                da.dot_id = db.dot_id
                            and da.group_id = db.group_id
                        inner join biz_region br on
                        da.area_id = br.id
                        inner join biz_region br2 on
                                br2.regcode like CONCAT(br.regcode, '%')
                            and br2.type in (1, 2, 3)
                where
                    da.dot_id = #{dotId}) t2 ,
            brand t3
        where
            t1.regcode = t2.regcode and t2.brand_id = t3.id
    </select>
</mapper>