<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.AttendantSendsRecordMapper">
    <select id="findByOverflow" resultType="java.util.HashMap">
        select d.detail_number, d.detail_name
        from sys_dictionary s
                 inner join sys_dictionary_detail d on s.id = d.dictionary_id
        where dic_number = #{overflow}
    </select>
    <select id="listAttendantNum" resultType="com.bonc.rrs.workManager.entity.AttendantSendsRecord">
        select * from attendant_sends_record where service_id=#{serviceId} and present_date &gt;= #{currentDate}
    </select>
    <update id="updateOverflow">
        update attendant_sends_record set overflow=#{overflow},send_num=#{sendNum} where service_id=#{serviceId} and present_date &gt;= #{currentDate}
    </update>
    <update id="updateSendNum">
        update attendant_sends_record set send_num=#{sendNum} where service_id=#{serviceId} and present_date &gt;= #{currentDate}
    </update>
    <select id="findByServiceId" resultType="java.lang.Long">
        select service_id from worder_information where worder_id=#{worderId}
    </select>
    <select id="getDotNextAttendanSendCount" resultType="java.lang.Integer">
        select
            sum(asr.send_num)
        from
            attendant_sends_record asr ,
            biz_attendant ba
        where
            asr.service_id = ba.id
          and asr.present_date = curdate()
          and ba.attendant_state  = '1'
          and ba.dot_id = #{dotId}
    </select>
</mapper>
