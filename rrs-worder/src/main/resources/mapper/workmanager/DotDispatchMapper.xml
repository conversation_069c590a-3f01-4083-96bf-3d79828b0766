<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.DotDispatchMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.DotDispatch">
    <constructor>
      <idArg column="rule_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="ser_num" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="dispatch_ratio" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="is_delete" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="brand_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="city_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
    </constructor>
  </resultMap>

  <sql id="Base_Column_List">
    rule_id, ser_num, dispatch_ratio, is_delete, create_time
  </sql>

  <select id="selectDispatchRatioById" resultType="java.lang.String">
    select dispatch_ratio from dot_dispatch_rule
    where ser_num=#{serNum}
    <if test="brandId != null">
      and brand_id=#{brandId}
    </if>
    limit 1
  </select>

  <select id="selectDispatchRatio" resultType="java.lang.String">
    select dispatch_ratio from dot_dispatch_rule
    where ser_num=#{serNum}
    <if test="brandId != null">
      and brand_id=#{brandId}
    </if>
    <if test="brandId != null">
      and city_id=#{cityId}
    </if>
    limit 1
  </select>
</mapper>