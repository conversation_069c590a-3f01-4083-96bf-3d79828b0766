<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.workManager.dao.DotContactsMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.workManager.entity.DotContacts">
    <constructor>
      <idArg column="contacts_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="dot_code" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contacts_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contacts_phone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contacts_email" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="modify_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="is_delete" javaType="java.lang.Byte" jdbcType="TINYINT" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    contacts_id, dot_code, contacts_name, contacts_phone, contacts_email, create_time,
    modify_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.bonc.rrs.workManager.entity.DotContacts">
    select 
    <include refid="Base_Column_List" />
    from dot_contacts
    where dot_code = #{dotCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from dot_contacts
    where contacts_id = #{contactsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.workManager.entity.DotContacts">
    insert into dot_contacts (contacts_id, dot_code, contacts_name, 
      contacts_phone, contacts_email, create_time, 
      modify_time, is_delete)
    values (#{contactsId,jdbcType=INTEGER}, #{dotCode,jdbcType=VARCHAR}, #{contactsName,jdbcType=VARCHAR}, 
      #{contactsPhone,jdbcType=VARCHAR}, #{contactsEmail,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bonc.rrs.workManager.entity.DotContacts">
    insert into dot_contacts
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contactsId != null">
        contacts_id,
      </if>
      <if test="dotCode != null">
        dot_code,
      </if>
      <if test="contactsName != null">
        contacts_name,
      </if>
      <if test="contactsPhone != null">
        contacts_phone,
      </if>
      <if test="contactsEmail != null">
        contacts_email,
      </if>
        create_time,
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contactsId != null">
        #{contactsId,jdbcType=INTEGER},
      </if>
      <if test="dotCode != null">
        #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="contactsName != null">
        #{contactsName,jdbcType=VARCHAR},
      </if>
      <if test="contactsPhone != null">
        #{contactsPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactsEmail != null">
        #{contactsEmail,jdbcType=VARCHAR},
      </if>
        current_timestamp ,
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.workManager.entity.DotContacts">
    update dot_contacts
    <set>
      <if test="dotCode != null">
        dot_code = #{dotCode,jdbcType=VARCHAR},
      </if>
      <if test="contactsName != null">
        contacts_name = #{contactsName,jdbcType=VARCHAR},
      </if>
      <if test="contactsPhone != null">
        contacts_phone = #{contactsPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactsEmail != null">
        contacts_email = #{contactsEmail,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where contacts_id = #{contactsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.workManager.entity.DotContacts">
    update dot_contacts
    set dot_code = #{dotCode,jdbcType=VARCHAR},
      contacts_name = #{contactsName,jdbcType=VARCHAR},
      contacts_phone = #{contactsPhone,jdbcType=VARCHAR},
      contacts_email = #{contactsEmail,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where contacts_id = #{contactsId,jdbcType=INTEGER}
  </update>

  <insert id="addManagerRoleId">
    insert into sys_user_role (user_id, role_id) value (#{userId}, #{roleId})
  </insert>

  <select id="selectContactsId" resultType="java.lang.Integer">
    select contacts_id
    from dot_contacts where dot_code=#{dotCode}
  </select>

</mapper>