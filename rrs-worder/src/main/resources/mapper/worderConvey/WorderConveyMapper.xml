<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worderConvey.dao.WorderConveyMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.worderConvey.pojo.WorderConvey">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="worder_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="oppointment_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="oppointment_modified_reason" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="convey_status" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="is_sign_on" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="sign_on_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="gmt_create" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="gmt_modified" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="build_type" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="developers" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="property_company" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="property_person" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contact_number" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="contact_email" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="car_type" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="park_location" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="property_attitude" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="remark_info" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="transformer_rate" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="main_capacity" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="circuit_capacity" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="measured_current" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="measured_voltage" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="actual_temperature" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="wire_diameter" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="charger_current" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="power_mode" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="customer_feedback" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="reconnaiss_report" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="construct_plan" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="park_space" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="site_environment" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="source_figure" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="audit_result" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="audit_state" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, worder_no, oppointment_time, oppointment_modified_reason, convey_status, is_sign_on,
    sign_on_time, gmt_create, gmt_modified, build_type, developers, property_company, 
    property_person, contact_number, contact_email, car_type, park_location, property_attitude, 
    remark_info, transformer_rate, main_capacity, circuit_capacity, measured_current, 
    measured_voltage, actual_temperature, wire_diameter, charger_current, power_mode, 
    customer_feedback, reconnaiss_report, construct_plan, park_space, site_environment, 
    source_figure, audit_result, audit_state
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from worder_convey
    where worder_no = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from worder_convey
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.worderConvey.pojo.WorderConvey">
    insert into worder_convey (id, worder_id, oppointment_time, 
      oppointment_modified_reason, convey_status, 
      is_sign_on, sign_on_time, gmt_create, 
      gmt_modified, build_type, developers, 
      property_company, property_person, contact_number, 
      contact_email, car_type, park_location, 
      property_attitude, remark_info, transformer_rate, 
      main_capacity, circuit_capacity, measured_current, 
      measured_voltage, actual_temperature, wire_diameter, 
      charger_current, power_mode, customer_feedback, 
      reconnaiss_report, construct_plan, park_space, 
      site_environment, source_figure, audit_result, 
      audit_state)
    values (#{id,jdbcType=INTEGER}, #{worderId,jdbcType=INTEGER}, #{oppointmentTime,jdbcType=TIMESTAMP}, 
      #{oppointmentModifiedReason,jdbcType=VARCHAR}, #{conveyStatus,jdbcType=VARCHAR}, 
      #{isSignOn,jdbcType=BIT}, #{signOnTime,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{buildType,jdbcType=VARCHAR}, #{developers,jdbcType=VARCHAR}, 
      #{propertyCompany,jdbcType=VARCHAR}, #{propertyPerson,jdbcType=VARCHAR}, #{contactNumber,jdbcType=VARCHAR}, 
      #{contactEmail,jdbcType=VARCHAR}, #{carType,jdbcType=VARCHAR}, #{parkLocation,jdbcType=VARCHAR}, 
      #{propertyAttitude,jdbcType=VARCHAR}, #{remarkInfo,jdbcType=VARCHAR}, #{transformerRate,jdbcType=VARCHAR}, 
      #{mainCapacity,jdbcType=VARCHAR}, #{circuitCapacity,jdbcType=VARCHAR}, #{measuredCurrent,jdbcType=VARCHAR}, 
      #{measuredVoltage,jdbcType=VARCHAR}, #{actualTemperature,jdbcType=VARCHAR}, #{wireDiameter,jdbcType=VARCHAR}, 
      #{chargerCurrent,jdbcType=VARCHAR}, #{powerMode,jdbcType=VARCHAR}, #{customerFeedback,jdbcType=VARCHAR}, 
      #{reconnaissReport,jdbcType=VARCHAR}, #{constructPlan,jdbcType=VARCHAR}, #{parkSpace,jdbcType=VARCHAR}, 
      #{siteEnvironment,jdbcType=VARCHAR}, #{sourceFigure,jdbcType=VARCHAR}, #{auditResult,jdbcType=VARCHAR}, 
      #{auditState,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bonc.rrs.worderConvey.pojo.WorderConvey">
    insert into worder_convey
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="worderId != null">
        worder_id,
      </if>
      <if test="oppointmentTime != null">
        oppointment_time,
      </if>
      <if test="oppointmentModifiedReason != null">
        oppointment_modified_reason,
      </if>
      <if test="conveyStatus != null">
        convey_status,
      </if>
      <if test="isSignOn != null">
        is_sign_on,
      </if>
      <if test="signOnTime != null">
        sign_on_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="buildType != null">
        build_type,
      </if>
      <if test="developers != null">
        developers,
      </if>
      <if test="propertyCompany != null">
        property_company,
      </if>
      <if test="propertyPerson != null">
        property_person,
      </if>
      <if test="contactNumber != null">
        contact_number,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="carType != null">
        car_type,
      </if>
      <if test="parkLocation != null">
        park_location,
      </if>
      <if test="propertyAttitude != null">
        property_attitude,
      </if>
      <if test="remarkInfo != null">
        remark_info,
      </if>
      <if test="transformerRate != null">
        transformer_rate,
      </if>
      <if test="mainCapacity != null">
        main_capacity,
      </if>
      <if test="circuitCapacity != null">
        circuit_capacity,
      </if>
      <if test="measuredCurrent != null">
        measured_current,
      </if>
      <if test="measuredVoltage != null">
        measured_voltage,
      </if>
      <if test="actualTemperature != null">
        actual_temperature,
      </if>
      <if test="wireDiameter != null">
        wire_diameter,
      </if>
      <if test="chargerCurrent != null">
        charger_current,
      </if>
      <if test="powerMode != null">
        power_mode,
      </if>
      <if test="customerFeedback != null">
        customer_feedback,
      </if>
      <if test="reconnaissReport != null">
        reconnaiss_report,
      </if>
      <if test="constructPlan != null">
        construct_plan,
      </if>
      <if test="parkSpace != null">
        park_space,
      </if>
      <if test="siteEnvironment != null">
        site_environment,
      </if>
      <if test="sourceFigure != null">
        source_figure,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditState != null">
        audit_state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="worderId != null">
        #{worderId,jdbcType=INTEGER},
      </if>
      <if test="oppointmentTime != null">
        #{oppointmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oppointmentModifiedReason != null">
        #{oppointmentModifiedReason,jdbcType=VARCHAR},
      </if>
      <if test="conveyStatus != null">
        #{conveyStatus,jdbcType=VARCHAR},
      </if>
      <if test="isSignOn != null">
        #{isSignOn,jdbcType=BIT},
      </if>
      <if test="signOnTime != null">
        #{signOnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="buildType != null">
        #{buildType,jdbcType=VARCHAR},
      </if>
      <if test="developers != null">
        #{developers,jdbcType=VARCHAR},
      </if>
      <if test="propertyCompany != null">
        #{propertyCompany,jdbcType=VARCHAR},
      </if>
      <if test="propertyPerson != null">
        #{propertyPerson,jdbcType=VARCHAR},
      </if>
      <if test="contactNumber != null">
        #{contactNumber,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=VARCHAR},
      </if>
      <if test="parkLocation != null">
        #{parkLocation,jdbcType=VARCHAR},
      </if>
      <if test="propertyAttitude != null">
        #{propertyAttitude,jdbcType=VARCHAR},
      </if>
      <if test="remarkInfo != null">
        #{remarkInfo,jdbcType=VARCHAR},
      </if>
      <if test="transformerRate != null">
        #{transformerRate,jdbcType=VARCHAR},
      </if>
      <if test="mainCapacity != null">
        #{mainCapacity,jdbcType=VARCHAR},
      </if>
      <if test="circuitCapacity != null">
        #{circuitCapacity,jdbcType=VARCHAR},
      </if>
      <if test="measuredCurrent != null">
        #{measuredCurrent,jdbcType=VARCHAR},
      </if>
      <if test="measuredVoltage != null">
        #{measuredVoltage,jdbcType=VARCHAR},
      </if>
      <if test="actualTemperature != null">
        #{actualTemperature,jdbcType=VARCHAR},
      </if>
      <if test="wireDiameter != null">
        #{wireDiameter,jdbcType=VARCHAR},
      </if>
      <if test="chargerCurrent != null">
        #{chargerCurrent,jdbcType=VARCHAR},
      </if>
      <if test="powerMode != null">
        #{powerMode,jdbcType=VARCHAR},
      </if>
      <if test="customerFeedback != null">
        #{customerFeedback,jdbcType=VARCHAR},
      </if>
      <if test="reconnaissReport != null">
        #{reconnaissReport,jdbcType=VARCHAR},
      </if>
      <if test="constructPlan != null">
        #{constructPlan,jdbcType=VARCHAR},
      </if>
      <if test="parkSpace != null">
        #{parkSpace,jdbcType=VARCHAR},
      </if>
      <if test="siteEnvironment != null">
        #{siteEnvironment,jdbcType=VARCHAR},
      </if>
      <if test="sourceFigure != null">
        #{sourceFigure,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.worderConvey.pojo.WorderConvey">
    update worder_convey
    <set>
      <if test="worderId != null">
        worder_id = #{worderId,jdbcType=INTEGER},
      </if>
      <if test="oppointmentTime != null">
        oppointment_time = #{oppointmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oppointmentModifiedReason != null">
        oppointment_modified_reason = #{oppointmentModifiedReason,jdbcType=VARCHAR},
      </if>
      <if test="conveyStatus != null">
        convey_status = #{conveyStatus,jdbcType=VARCHAR},
      </if>
      <if test="isSignOn != null">
        is_sign_on = #{isSignOn,jdbcType=BIT},
      </if>
      <if test="signOnTime != null">
        sign_on_time = #{signOnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="buildType != null">
        build_type = #{buildType,jdbcType=VARCHAR},
      </if>
      <if test="developers != null">
        developers = #{developers,jdbcType=VARCHAR},
      </if>
      <if test="propertyCompany != null">
        property_company = #{propertyCompany,jdbcType=VARCHAR},
      </if>
      <if test="propertyPerson != null">
        property_person = #{propertyPerson,jdbcType=VARCHAR},
      </if>
      <if test="contactNumber != null">
        contact_number = #{contactNumber,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=VARCHAR},
      </if>
      <if test="parkLocation != null">
        park_location = #{parkLocation,jdbcType=VARCHAR},
      </if>
      <if test="propertyAttitude != null">
        property_attitude = #{propertyAttitude,jdbcType=VARCHAR},
      </if>
      <if test="remarkInfo != null">
        remark_info = #{remarkInfo,jdbcType=VARCHAR},
      </if>
      <if test="transformerRate != null">
        transformer_rate = #{transformerRate,jdbcType=VARCHAR},
      </if>
      <if test="mainCapacity != null">
        main_capacity = #{mainCapacity,jdbcType=VARCHAR},
      </if>
      <if test="circuitCapacity != null">
        circuit_capacity = #{circuitCapacity,jdbcType=VARCHAR},
      </if>
      <if test="measuredCurrent != null">
        measured_current = #{measuredCurrent,jdbcType=VARCHAR},
      </if>
      <if test="measuredVoltage != null">
        measured_voltage = #{measuredVoltage,jdbcType=VARCHAR},
      </if>
      <if test="actualTemperature != null">
        actual_temperature = #{actualTemperature,jdbcType=VARCHAR},
      </if>
      <if test="wireDiameter != null">
        wire_diameter = #{wireDiameter,jdbcType=VARCHAR},
      </if>
      <if test="chargerCurrent != null">
        charger_current = #{chargerCurrent,jdbcType=VARCHAR},
      </if>
      <if test="powerMode != null">
        power_mode = #{powerMode,jdbcType=VARCHAR},
      </if>
      <if test="customerFeedback != null">
        customer_feedback = #{customerFeedback,jdbcType=VARCHAR},
      </if>
      <if test="reconnaissReport != null">
        reconnaiss_report = #{reconnaissReport,jdbcType=VARCHAR},
      </if>
      <if test="constructPlan != null">
        construct_plan = #{constructPlan,jdbcType=VARCHAR},
      </if>
      <if test="parkSpace != null">
        park_space = #{parkSpace,jdbcType=VARCHAR},
      </if>
      <if test="siteEnvironment != null">
        site_environment = #{siteEnvironment,jdbcType=VARCHAR},
      </if>
      <if test="sourceFigure != null">
        source_figure = #{sourceFigure,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        audit_state = #{auditState,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.worderConvey.pojo.WorderConvey">
    update worder_convey
    set worder_id = #{worderId,jdbcType=INTEGER},
      oppointment_time = #{oppointmentTime,jdbcType=TIMESTAMP},
      oppointment_modified_reason = #{oppointmentModifiedReason,jdbcType=VARCHAR},
      convey_status = #{conveyStatus,jdbcType=VARCHAR},
      is_sign_on = #{isSignOn,jdbcType=BIT},
      sign_on_time = #{signOnTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      build_type = #{buildType,jdbcType=VARCHAR},
      developers = #{developers,jdbcType=VARCHAR},
      property_company = #{propertyCompany,jdbcType=VARCHAR},
      property_person = #{propertyPerson,jdbcType=VARCHAR},
      contact_number = #{contactNumber,jdbcType=VARCHAR},
      contact_email = #{contactEmail,jdbcType=VARCHAR},
      car_type = #{carType,jdbcType=VARCHAR},
      park_location = #{parkLocation,jdbcType=VARCHAR},
      property_attitude = #{propertyAttitude,jdbcType=VARCHAR},
      remark_info = #{remarkInfo,jdbcType=VARCHAR},
      transformer_rate = #{transformerRate,jdbcType=VARCHAR},
      main_capacity = #{mainCapacity,jdbcType=VARCHAR},
      circuit_capacity = #{circuitCapacity,jdbcType=VARCHAR},
      measured_current = #{measuredCurrent,jdbcType=VARCHAR},
      measured_voltage = #{measuredVoltage,jdbcType=VARCHAR},
      actual_temperature = #{actualTemperature,jdbcType=VARCHAR},
      wire_diameter = #{wireDiameter,jdbcType=VARCHAR},
      charger_current = #{chargerCurrent,jdbcType=VARCHAR},
      power_mode = #{powerMode,jdbcType=VARCHAR},
      customer_feedback = #{customerFeedback,jdbcType=VARCHAR},
      reconnaiss_report = #{reconnaissReport,jdbcType=VARCHAR},
      construct_plan = #{constructPlan,jdbcType=VARCHAR},
      park_space = #{parkSpace,jdbcType=VARCHAR},
      site_environment = #{siteEnvironment,jdbcType=VARCHAR},
      source_figure = #{sourceFigure,jdbcType=VARCHAR},
      audit_result = #{auditResult,jdbcType=VARCHAR},
      audit_state = #{auditState,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByWorderno">
     update worder_convey
     <set>
       <if test="result != null">
         audit_result = #{result},
       </if>
       <if test="state != null">
         audit_state = #{state},
       </if>
         audit_time=CURRENT_TIMESTAMP
     </set>
    where worder_no = #{worderno}
  </update>

</mapper>