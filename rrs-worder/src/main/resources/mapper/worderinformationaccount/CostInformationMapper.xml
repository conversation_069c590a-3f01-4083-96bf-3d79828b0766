<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinformationaccount.dao.CostInformationDao">


    <select id="getCostByWorderId" resultType="com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity">
        select bci.ywms,bci.balance_id from balance_cost_information bci
        inner join worder_child_information wci on bci.balance_id = wci.id
        where wci.id = #{id}
    </select>

    <select id="getCostByIncreId" resultType="com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity">
        select bci.ywms,bci.incre_id from balance_cost_information bci
        where bci.incre_id = #{id}
    </select>

    <select id="getCostByStimulateId" resultType="com.bonc.rrs.worderinformationaccount.entity.CostInformationEntity">
        select bci.ywms,bci.balance_id from balance_cost_information bci
        inner join worder_child_information wci on bci.balance_id = wci.id
        where wci.id = #{id}
    </select>
</mapper>