<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinformationaccount.dao.InvoiceBusinessTypeDetailDao">


    <select id="queryByHeaderId" resultType="com.bonc.rrs.worderinformationaccount.entity.InvoiceBusinessTypeDetailEntity">
        select
            *
        from
            invoice_business_type_detail
        where
            enterprises_header_id = #{enterprisesHeaderId}
        order by
            create_time desc
        limit 1
    </select>

    <select id="queryByInvoiceId" resultType="com.bonc.rrs.worderinformationaccount.entity.InvoiceBusinessTypeDetailEntity">
        select
            *
        from
            invoice_business_type_detail
        where
            invoice_id = #{invoiceId}
        order by
            create_time desc
        limit 1
    </select>

    <delete id="deleteByInvoiceId">
        delete from invoice_business_type_detail where invoice_id = #{invoiceId}
    </delete>
</mapper>