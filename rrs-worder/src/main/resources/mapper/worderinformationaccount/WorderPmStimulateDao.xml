<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao">
    <resultMap id="stimulateMap" type="com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity">
        <id property="id" column="id"/>
        <result property="worderId" column="worder_id"/>
        <result property="dotId" column="dot_id"/>
        <result property="stimulateType" column="stimulate_type"/>
        <result property="status" column="status"/>
        <result property="stimulateReason" column="stimulate_reason"/>
        <result property="stimulateFee" column="stimulate_fee"/>
        <result property="createTime" column="create_time"/>
        <result property="storageTax" column="storage_tax"/>
        <result property="priceTax" column="price_tax"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="costId" column="cost_id"/>
        <result property="priceType" column="price_type"/>
    </resultMap>
    <select id="selectNotBalanceStimulateOnBalanceWorderByCompanyId" parameterType="java.lang.Integer" resultMap="stimulateMap">
        SELECT a.* FROM worder_pm_stimulate a, worder_information b, worder_template c, worder_ext_field d
        WHERE a.worder_id=b.worder_id AND b.template_id=c.id AND a.stimulate_type=11 AND a.status=18
        AND b.worder_set_status>1 AND b.worder_no=d.worder_no AND d.field_id = c.settle_way+100
        AND d.field_value=#{companyId} and a.is_delete = 0
    </select>
    <select id="queryDotStimulateForBalance" resultMap="stimulateMap">
        SELECT a.* FROM worder_pm_stimulate a, worder_information b
        WHERE a.worder_id=b.worder_id AND a.stimulate_type=10 AND a.status=19 AND b.worder_set_status in (3,4,5,7,11,12,13,14) and a.is_delete = 0
    </select>

    <select id="selectByStimulateId" resultMap="stimulateMap">
        select a.*,d.price_type  from worder_pm_stimulate a
        left join worder_information b on a.worder_id = b.worder_id
        left join worder_template c on b.template_id = c.id
        left join balance_rule d on c.company_balance_rule_id = d.id
        where a.id = #{stimulateId}
    </select>


    <select id="listStimulateCompanyNotAccount" resultMap="stimulateMap">
        select a.id, a.stimulate_fee,a.worder_id, e.price_type  from worder_pm_stimulate a
        left join worder_wait_account b on a.id = b.stimulate_id
        left join worder_information c on a.worder_id = c.worder_id
        left join worder_template d on c.template_id = d.id
        left join balance_rule e on d.company_balance_rule_id = e.id
        where a.stimulate_type = 11 and a.status = 18 and b.id is null and a.is_delete = 0
    </select>

    <insert id="addStimulateFile" parameterType="com.bonc.rrs.worder.entity.StimulateFileEntity">
        insert into stimulate_file (stimulate_id, file_id) values (#{stimulateId}, #{fileId})
    </insert>

    <select id="findStimulateFileByStimulateId" resultType="com.bonc.rrs.worder.entity.StimulateFileEntity" parameterType="java.lang.Integer">
        select a.id, a.stimulate_id, a.file_id from stimulate_file a where stimulate_id = #{stimulateId}
    </select>

    <update id="updatePublishStatus">
        update worder_pm_stimulate set publish_status=null,status=22  where id=#{id}
    </update>
    <update id="updatePmStatus">
        update worder_pm_stimulate set publish_status=#{status}  where id=#{id}
    </update>

</mapper>