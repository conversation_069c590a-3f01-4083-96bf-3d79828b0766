<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinformationaccount.dao.BalanceEnterprisesDetailRecordDao">


    <select id="listByInvoiceId" parameterType="java.util.List"
            resultType="com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity">
        select b.* from company_invoice a
        left join balance_enterprises_detail_record b on a.company_invoice_no = b.company_invoice_no
        <where>
            a.id in
            <foreach collection="list" item="invoiceId" open="(" separator="," close=")">
                invoiceId
            </foreach>
        </where>
        order by a.id
    </select>


    <select id="listSumByInvoiceId" parameterType="java.util.List"
            resultType="com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity">
        select sum(no_tax_amount) invoiceNoTaxFeeSum, sum(tax_amount) invoiceTaxFeeSum, sum(tex_forehead) invoiceTaxSum,
        a.id invoiceId
        from company_invoice a
        left join balance_enterprises_detail_record b on a.company_invoice_no = b.company_invoice_no
        <where>
            a.id in
            <foreach collection="list" item="invoiceId" open="(" separator="," close=")">
                #{invoiceId}
            </foreach>
        </where>
        group by a.id
    </select>
</mapper>