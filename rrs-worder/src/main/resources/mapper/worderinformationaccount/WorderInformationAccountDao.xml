<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao">

    <select id="queryWorderIdList" parameterType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO"
            resultType="map">
        SELECT
        a.id, a.stimulate_id stimulateId, a.worder_invoice_type worderInvoiceType,
        i.company_order_number companyOrderNumber,
        i.worder_id worderId
        FROM
        worder_wait_account a
        left join worder_information i on a.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = a.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        WHERE 1 = 1 and a.status = 0
        <choose>
            <when test="worderMainStatus == 0 ">
            </when>
            <otherwise>
                AND i.worder_status = #{worderMainStatus}
            </otherwise>
        </choose>
        <if test="companyOrderNumber != null and companyOrderNumber != '' ">
            AND i.company_order_number = #{companyOrderNumber}
        </if>
        <if test="companyId != null and companyId != '' ">
            AND a.company_id = #{companyId}
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
        <if test="companyOrderNumbers != null and companyOrderNumbers != '' ">
            AND i.company_order_number in ${companyOrderNumbers}
        </if>
    </select>

<!--    <select id="queryWorderInformationNotAccountList" parameterType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO" resultType="map">-->
<!--        SELECT-->
<!--        i.company_order_number companyOrderNumber,-->
<!--        i.worder_id worderId,-->
<!--        i.worder_no worderNo,-->
<!--        b.name AS worderTypeName,-->
<!--        c.company_name companyBrand,-->
<!--        i.company_balance_fee companyBalanceFee,-->
<!--        i.company_balance_fee_sum companyBalanceSum,-->
<!--        i.company_balance_fee_tax companyBalanceFeetax,-->
<!--        #{taxRate} taxRate,-->
<!--        i.user_name userName,-->
<!--        d.dot_name dotName,-->
<!--        i.worder_status_value worderStatusValue,-->
<!--        DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d %H:%i:%S') worderFinishTime,-->
<!--        i.dot_balance_fee dotBalanceFee,-->
<!--        c.company_id companyId,-->
<!--        (CASE WHEN i.worder_set_status = 0 THEN '首次审核'-->
<!--        WHEN i.worder_set_status = 8 THEN '二次审核'-->
<!--        WHEN i.worder_set_status = 1 THEN '开票'-->
<!--        WHEN i.worder_set_status = 9 THEN '提交审核'-->
<!--        END) operation-->
<!--        &lt;!&ndash;<if test="isAduit == 1 ">&ndash;&gt;-->

<!--        &lt;!&ndash;</if>&ndash;&gt;-->
<!--        &lt;!&ndash;<if test="isAduit == 0 ">&ndash;&gt;-->
<!--            &lt;!&ndash;(&ndash;&gt;-->
<!--            &lt;!&ndash;END) operation&ndash;&gt;-->
<!--        &lt;!&ndash;</if>&ndash;&gt;-->
<!--        FROM-->
<!--        worder_information i-->
<!--        LEFT JOIN worder_template t ON i.template_id = t.id-->
<!--        LEFT JOIN worder_type b ON i.worder_type_id=b.id-->
<!--        LEFT JOIN worder_ext_field e ON e.worder_no = i.worder_no AND e.field_id = t.settle_way+100-->
<!--        left join ext_field ef on ef.field_id = e.field_id-->
<!--        LEFT JOIN company_information c ON c.company_id = e.field_value-->
<!--        LEFT JOIN dot_information d ON d.dot_id = i.dot_id-->
<!--        WHERE 1 = 1-->
<!--            AND i.worder_set_status  = 1-->
<!--        <choose>-->
<!--            <when test="worderMainStatus == 0 ">-->
<!--                AND i.worder_status IN (3,5)-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                AND i.worder_status = #{worderMainStatus}-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        <if test="companyOrderNumber != null and companyOrderNumber != '' ">-->
<!--            AND i.company_order_number = #{companyOrderNumber}-->
<!--        </if>-->
<!--        <if test="companyId != null and companyId != '' ">-->
<!--            AND c.company_id = #{companyId}-->
<!--        </if>-->
<!--        <if test="startTime != null  ">-->
<!--            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')-->
<!--        </if>-->
<!--        <if test="endTime != null  ">-->
<!--            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')-->
<!--        </if>-->
<!--        <if test="companyOrderNumbers != null and companyOrderNumbers != '' ">-->
<!--            AND i.company_order_number in ${companyOrderNumbers}-->
<!--        </if>-->
<!--        ORDER BY i.worder_finish_time ASC-->
<!--        <if test="page != null and limit != null ">-->
<!--            limit #{page},#{limit}-->
<!--        </if>-->
<!--    </select>-->

    <select id="queryWorderInformationNotAccountList" parameterType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO" resultType="map">
        SELECT
        a.id, a.stimulate_id stimulateId, a.worder_invoice_type worderInvoiceType,
        a.company_balance_fee companyBalanceFee,
        a.company_balance_fee_sum companyBalanceSum,
        a.company_balance_fee_tax companyBalanceFeetax,
        i.company_order_number companyOrderNumber,
        i.worder_id worderId,
        i.worder_no worderNo,
        b.name AS worderTypeName,
        c.company_name companyBrand,
        br.balance_tax_rate_value taxRate,
        i.user_name userName,
        d.dot_name dotName,
        i.worder_status_value worderStatusValue,
        DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d %H:%i:%S') worderFinishTime,
        i.dot_balance_fee dotBalanceFee,
        c.company_id companyId,
        (CASE WHEN i.worder_set_status = 0 THEN '首次审核'
        WHEN i.worder_set_status = 8 THEN '二次审核'
        WHEN i.worder_set_status = 1 THEN '开票'
        WHEN i.worder_set_status = 9 THEN '提交审核'
        END) operation
        FROM
        worder_wait_account a
        left join worder_information i on a.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = a.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        LEFT JOIN balance_rule br ON t.company_balance_rule_id = br.id
        WHERE 1 = 1 and a.status = 0
        <choose>
            <when test="worderMainStatus == 0 ">
            </when>
            <otherwise>
                AND i.worder_status = #{worderMainStatus}
            </otherwise>
        </choose>
        <if test="companyOrderNumber != null and companyOrderNumber != '' ">
            AND i.company_order_number like concat(#{companyOrderNumber},'%')
        </if>
        <if test="companyId != null and companyId != '' ">
            AND a.company_id = #{companyId}
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
        <if test="companyOrderNumbers != null and companyOrderNumbers != '' ">
            AND i.company_order_number in ${companyOrderNumbers}
        </if>
        ORDER BY i.worder_finish_time ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryWorderInformationAllFlowOrdersList" parameterType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO" resultType="map">
        SELECT distinct
        wci.balance_source worderInvoiceType,
        i.company_order_number companyOrderNumber,
        i.worder_id worderId,
        i.worder_no worderNo,
        b.name AS worderTypeName,
        (select  company_name  from company_information ci where ci.company_id=wef.field_value) companyBrand,
        i.user_name userName,
        d.dot_name dotName,
        (select sdd.detail_name from sys_dictionary sd left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id where sd.dic_number ='worder_exec_status' and sdd.detail_number = i.worder_exec_status) worderStatusValue,
        DATE_FORMAT(i.worder_finish_time, '%Y-%m-%d %H:%i:%S') worderFinishTime,
        i.dot_balance_fee dotBalanceFee,
        (CASE when wap.is_delete = 0 then '已付款' when bcb.status_flag in(5) then '已确认' when bcb.status_flag in(6) then '已开票' when bcb.status_flag in(8) then '已入账' when bcb.status_flag in(9) then '待付款已传BCC' when bcb.status_flag in(10) then '已付款' when bcb.status_flag in(11) then '发票已邮寄' else '' END) as statusFlag,
        (CASE when i.worder_set_status in (99) then '不结算' when cr.status in (4) then '已核销' when cr.status in (-2,3) then '财务审核中'  when cr.status in (-1,1,2) then '核销待一次审核' when ci.status in (7) then '记收暂估完成'
        when ci.status in (4,5,6,9) then '开票中' when ci.status in (-2,3,8) then '财务审核中' when ci.status in (-1,2) then '开票待一次审核'  when ci.status in (1) then '等待开票'
        when i.worder_set_status in (2) then '等待开票' when i.worder_set_status in (0,1) then '待结算' end)as companyStatus
        FROM worder_information i
        force index(worder_information_worder_finish_time_IDX)
        left join worder_child_information wci on wci.worder_id = i.worder_id and wci.balance_source = 0
        left join worder_advance_puiblish wap on wap.worder_no = i.worder_no and wap.is_delete = 0
        left join biz_attendant ba on i.service_id = ba.id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id = b.id
        LEFT JOIN company_information c ON c.company_id = i.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        LEFT JOIN balance_rule br ON t.company_balance_rule_id = br.id
        left join balance_publish bp on wci.publish_id = bp.id
        left join balance_cvp_bill bcb on bp.cvp_bill_id = bcb.id
        left join worder_ext_field wef on wef.worder_no=i.worder_no and wef.field_id =concat('10',t.settle_way)
        left join company_invoice ci on i.invoice_id = ci.id
        left join company_receivable_record crr on crr.invoice_id = ci.id and crr.`type` = 1 and crr.delete_state = 0
        left join company_receivable cr on cr.company_receivable_no = crr.company_receivable_no
        <if test="p.brandFlag == 1">
            inner join (
            select DISTINCT(m.brand_id)
            from manager_area_id m
            left join sys_user s on m.user_id = s.user_id
            left join biz_region b on m.area_id = b.id
            where s.user_id = #{p.userId}
            <if test="p.brandId !=null and p.brandId != ''">
                AND m.brand_id = #{p.brandId}
            </if>
            ) b on t.brand_id=b.brand_id
        </if>
        WHERE 1 = 1
        and i.worder_exec_status in (15,16,17)
        and i.is_delete = 0
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '0'.toString() ">
            AND i.worder_set_status in (0,1)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '1'.toString() ">
            AND (i.worder_set_status in (2) or ci.status in (1))
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '2'.toString() ">
            AND ci.status in (-1,2)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '3'.toString() ">
            AND ci.status in (-2,3,8)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '4'.toString() ">
            AND ci.status in (4,5,6,9)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '5'.toString() ">
            AND ci.status = 7
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '6'.toString() ">
            AND cr.status in (-1,1,2)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '7'.toString() ">
            AND cr.status in (-2,3)
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '8'.toString() ">
            AND cr.status = 4
        </if>
        <if test="p.companyStatus != null and p.companyStatus != '' and p.companyStatus == '99'.toString() ">
            AND i.worder_set_status = 99
        </if>
        <if test="p.serviceId != null and p.serviceId != '' ">
            AND ba.user_id = #{p.serviceId}
        </if>
        <if test="p.pmId != null and p.pmId != '' ">
            AND i.pm_id = #{p.pmId}
        </if>
        <if test="p.brandId != null and p.brandId != '' ">
            AND t.brand_id = #{p.brandId}
        </if>
        <if test="p.companyOrderNumber != null and p.companyOrderNumber != '' ">
            AND i.company_order_number like concat(#{p.companyOrderNumber},'%')
        </if>
        <if test="p.worderNo != null and p.worderNo != '' ">
            AND i.worder_no = #{p.worderNo}
        </if>
        <if test="p.dotId != null and p.dotId != '' ">
            AND i.dot_id = #{p.dotId}
        </if>
        <if test="p.userName != null and p.userName != '' ">
            AND i.user_name like concat(#{p.userName},'%')
        </if>
        <if test="p.status != null and p.status == '10'.toString() ">
            AND (bcb.status_flag = #{p.status} or wap.is_delete = 0)
        </if>
        <if test="p.status != null and p.status != '10'.toString() ">
            AND bcb.status_flag = #{p.status}
        </if>
        <if test="p.areas != null and p.areas.size()>0">
            And i.area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="p.areas" item="area" open="(" separator="," close=")">
                #{area}
            </foreach>
            )
        </if>
        <if test="p.areaIds != null and p.areaIds.size()>0">
            And i.area_id in
            <foreach collection="p.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="p.startTime != null and p.startTime != ''  ">
            AND DATE_FORMAT(i.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{p.startTime},'%Y-%m-%d')
        </if>
        <if test="p.endTime != null and p.endTime != ''  ">
            AND DATE_FORMAT(#{p.endTime},'%Y-%m-%d') >= DATE_FORMAT(i.create_time,'%Y-%m-%d')
        </if>
        <if test="p.createBy != null and  p.createBy !=''">
            and i.create_by = #{p.createBy}
        </if>
        <if test="p.startFinishTime != null  and p.startFinishTime != '' ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{p.startFinishTime},'%Y-%m-%d')
        </if>
        <if test="p.endFinishTime != null  and p.endFinishTime != '' ">
            AND DATE_FORMAT(#{p.endFinishTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
        <if test="p.areaFlag == 1">
            and exists (select 1 from biz_region r ,
            (select
            m.group_id ,
            m.child_group_id ,
            GROUP_CONCAT(distinct(b.regcode) separator '|') as regcodes ,
            GROUP_CONCAT(distinct(m.brand_id) separator ',') as brand_ids ,
            GROUP_CONCAT(distinct(m.service_type) separator ',') as service_types
            from
            sys_user s
            left join manager_area_id m on
            s.user_id = m.user_id
            left join biz_region b on
            m.area_id = b.id
            where
            s.user_id = #{p.userId}
            group by m.group_id,m.child_group_id
            ) temp
            where r.regcode regexp (temp.regcodes)
            and r.type = 3
            and i.area_id = r.id
            and find_in_set(t.brand_id, temp.brand_ids)
            and (FIND_IN_SET(t.service_type_enum, temp.service_types) OR FIND_IN_SET('0', temp.service_types))
            )
        </if>
        ORDER BY i.worder_finish_time desc
    </select>

    <select id="queryWorderInformationNotAccountCount" parameterType="com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO" resultType="Integer">
        SELECT
        count(i.worder_id)
        FROM
        worder_wait_account a
        left join worder_information i on a.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = a.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        WHERE 1 = 1 and a.status = 0
        <choose>
            <when test="worderMainStatus == 0 ">
            </when>
            <otherwise>
                AND i.worder_status = #{worderMainStatus}
            </otherwise>
        </choose>
        <if test="companyOrderNumber != null and companyOrderNumber != '' ">
            AND i.company_order_number = #{companyOrderNumber}
        </if>
        <if test="companyId != null and companyId != '' ">
            AND a.company_id = #{companyId}
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
        <if test="companyOrderNumbers != null and companyOrderNumbers != '' ">
            AND i.company_order_number in ${companyOrderNumbers}
        </if>
    </select>

    <select id="queryWorderInformationAllFlowOrdersCount" parameterType="com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO" resultType="Integer">
        SELECT
        count(i.worder_id)
        FROM worder_information i
        inner join worder_child_information wci on wci.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = i.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        LEFT JOIN balance_rule br ON t.company_balance_rule_id = br.id
        left join balance_publish bp on wci.publish_id = bp.id
        left join balance_cvp_bill bcb on bp.cvp_bill_id = bcb.id
        WHERE 1 = 1 and wci.balance_source = 0
        and i.worder_exec_status in (15,16,17)
        and i.is_delete = 0
        <if test="companyOrderNumber != null and companyOrderNumber != '' ">
            AND i.company_order_number like concat(#{companyOrderNumber},'%')
        </if>
        <if test="worderNo != null and worderNo != '' ">
            AND i.worder_no = #{worderNo}
        </if>
        <if test="brandId != null and brandId != '' ">
            AND t.brand_id = #{brandId}
        </if>
        <if test="dotId != null and dotId != '' ">
            AND i.dot_id = #{dotId}
        </if>
        <if test="userName != null and userName != '' ">
            AND i.user_name like concat(#{userName},'%')
        </if>
        <if test="status != null and status != '' ">
            AND bcb.status_flag = #{status}
        </if>
        <if test="area != null and area.size()>0">
            And i.area_id in (
            select b.id from biz_region a,biz_region b  where b.pid=a.id  and a.pid in
            <foreach collection="area" item="a" open="(" separator="," close=")">
                #{a}
            </foreach>
            )
        </if>
        <if test="areaId != null and areaId.size()>0">
            And i.area_id in
            <foreach collection="areaId" item="a" open="(" separator="," close=")">
                #{a}
            </foreach>
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(i.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.create_time,'%Y-%m-%d')
        </if>
        <if test="startFinishTime != null  ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startFinishTime},'%Y-%m-%d')
        </if>
        <if test="endFinishTime != null  ">
            AND DATE_FORMAT(#{endFinishTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
    </select>

    <select id="queryWorderInformationNotAccountExcel" parameterType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountVO" resultType="com.bonc.rrs.worderinformationaccount.vo.WorderInformationAccountExcelVO">
        SELECT
        a.id, a.stimulate_id stimulateId, (CASE WHEN a.worder_invoice_type = 0 THEN '工单'
        WHEN a.worder_invoice_type = 1 THEN '激励'
        END)  worderInvoiceTypeName,
        a.company_balance_fee companyBalanceFee,
        a.company_balance_fee_sum companyBalanceSum,
        a.company_balance_fee_tax companyBalanceFeetax,
        i.company_order_number companyOrderNumber,
        (CASE WHEN a.worder_invoice_type = 1 THEN i.worder_id
        END)  worderId,
        i.worder_no worderNo,
        b.name AS worderTypeName,
        c.company_name companyBrand,
        #{taxRate} taxRate,
        i.user_name userName,
        d.dot_name dotName,
        i.worder_status_value worderStatusValue,
        DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d %H:%i:%S') worderFinishTime,
        i.dot_balance_fee,
        i.dot_balance_fee_sum as dotBalanceFeeSum,
        i.dot_balance_fee_tax,
        c.company_id companyId,
        (CASE WHEN i.worder_set_status = 0 THEN '首次审核'
        WHEN i.worder_set_status = 8 THEN '二次审核'
        WHEN i.worder_set_status = 1 THEN '开票'
        WHEN i.worder_set_status = 9 THEN '提交审核'
        END) operation
        FROM
        worder_wait_account a
        left join worder_information i on a.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = a.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        WHERE 1 = 1 and a.status = 0
        <choose>
            <when test="worderMainStatus == 0 ">
            </when>
            <otherwise>
                AND i.worder_status = #{worderMainStatus}
            </otherwise>
        </choose>
        <if test="companyOrderNumber != null and companyOrderNumber != '' ">
            AND i.company_order_number like concat(#{companyOrderNumber},'%')
        </if>
        <if test="companyId != null and companyId != '' ">
            AND a.company_id = #{companyId}
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d')
        </if>
        <if test="companyOrderNumbers != null and companyOrderNumbers != '' ">
            AND i.company_order_number in ${companyOrderNumbers}
        </if>
        ORDER BY i.worder_finish_time ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>
    <select id="getCompanyById" parameterType="java.lang.Integer" resultType="java.util.Map">
        select * from company_information where company_id = #{companyId}
    </select>
    <select id="getInvoiceCustomerByInvoiceId" parameterType="java.lang.Integer" resultType="java.util.Map">
        select
            distinct behr.customer_name as company_name,
            behr.customer_heading as tax_no,
            behr.customer_bank as company_bank,
            behr.customer_bank_no as bank_account,
            behr.customer_mobile as company_mobile,
            behr.customer_address as address_dup
        from
            balance_enterprises_header_record behr
        left join company_invoice ci on
            ci.company_invoice_no = behr.company_invoice_no
        where
            ci.id = #{invoiceId}
    </select>

    <select id="listWorderByIds" parameterType="java.util.List" resultType="java.util.Map">
        select a.*, b.full_name as worder_name
        from worder_information a
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        where a.worder_id IN
        <foreach collection="list" separator="," open="(" close=")" item="e">
            #{e}
        </foreach>
    </select>
    <select id="getWorderAndDotByWorderId" parameterType="java.lang.Integer" resultType="java.util.Map">
        select a.*, b.full_name as worder_name, c.*, e.balance_tax_rate_value as dot_tax_rate, t1.name as dotAreaName, t2.name as dotCityName
        from worder_information a
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        LEFT JOIN dot_information c ON a.dot_id=c.dot_id
        LEFT join biz_region t1 on c.dot_area = t1.id
        LEFT join biz_region t2 on c.dot_city = t2.id
        LEFT JOIN worder_template d ON a.template_id = d.id
        LEFT JOIN balance_rule e ON d.dot_balance_rule_id = e.id
        where a.worder_id = #{worderId}
    </select>
    <select id="getWorderAndDotByInvoiceId" parameterType="java.lang.Integer" resultType="java.util.Map">
        select a.*, b.full_name as worder_name, c.*, e.balance_tax_rate_value as dot_tax_rate,  f.balance_tax_rate_value as dot_incre_tax_rate, t1.name as dotAreaName, t2.name as dotCityName
        from worder_information a
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        LEFT JOIN dot_information c ON a.dot_id=c.dot_id
        LEFT join biz_region t1 on c.dot_area = t1.id
        LEFT join biz_region t2 on c.dot_city = t2.id
        LEFT JOIN worder_template d ON a.template_id = d.id
        LEFT JOIN balance_rule e ON d.dot_balance_rule_id = e.id
        LEFT JOIN balance_rule f ON d.dot_incre_balance_rule_id = f.id
        where a.invoice_id = #{invoiceId}
    </select>

    <select id="listCompanyInvoiceByInvoiceId" parameterType="java.lang.Integer" resultType="java.util.Map">
        select a.worder_invoice_type, a.worder_id, c.full_name, b.dot_balance_fee_sum, b.dot_balance_fee_tax,
        b.dot_id, d.customer_code, d.v_code, d.dot_name, t1.name as dotAreaName, t2.name as dotCityName
        from worder_wait_account a
        left join worder_information b on a.worder_id = b.worder_id
        LEFT JOIN worder_type c ON b.worder_type_id=c.id
        left join dot_information d on b.dot_id = d.dot_id
        LEFT join biz_region t1 on d.dot_area = t1.id
        LEFT join biz_region t2 on d.dot_city = t2.id
        where a.invoice_id = #{invoiceId} AND a.worder_invoice_type = 0
    </select>
    <update id="updateSetStatusByInvoiceIds" parameterType="java.util.List">
        update worder_information set worder_set_status=3, worder_set_status_value='已开票' where invoice_id in
        <foreach collection="list" separator="," open="(" close=")" item="e">
            #{e}
        </foreach>
    </update>

    <!-- 网点工单结算费用审核列表查询-->
    <select id="queryWorderInfoAduitList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="map">
        select * from (
        SELECT distinct
        wci.id worderChildId,
        wci.balance_no balanceNo,
        w.worder_id worderId,
        wci.id id,
        w.worder_no worderNo,
        '' stimulateId,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        wci.balance_set_status_value worderStatusValue,
        '网点工单费用' balanceTypeValue,
        '33' balanceType,
        w.worder_finish_time worderFinishTime,
        wci.dot_balance_fee dotIncreBalanceFee,
        wci.dot_balance_fee_sum dotBalanceFeeSum,
        wci.dot_balance_fee_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (CASE WHEN wci.balance_set_status = 4 THEN '首次审核'
        WHEN wci.balance_set_status = 11 THEN '二次审核'
        WHEN wci.balance_set_status = 12 THEN '三次审核'
        WHEN wci.balance_set_status = 13 THEN '发布'
        WHEN wci.balance_set_status = 9 THEN '提交审核'
        END) operation,
        w.dot_id  dotId,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        FROM
        worder_child_information wci
        left join worder_information w on wci.worder_id = w.worder_id
        LEFT JOIN worder_type t ON t.id = w.worder_type_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        LEFT JOIN dot_information d ON d.dot_id = w.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo) on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1=1
        and wci.balance_source = 0
        and wci.`type` = 0
        <if test="isAduit == 1 ">
            AND wci.balance_set_status = 4
        </if>
        <if test="isAduit == 0 ">
            AND wci.balance_set_status = 9
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        ) a
        ORDER BY a.worderFinishTime ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>


    <!-- 网点激励结算费用审核列表查询-->
    <select id="queryStimulateInfoAduitList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="map">
        select distinct
            wci.id worderChildId,
            wci.balance_no balancdNo,
            w.worder_id worderId,
            wci.id id,
            w.worder_no worderNo,
            wps.id stimulateId,
            t.full_name fullName,
            d.dot_name dotName,
            d.dot_star dotStar,
            '记账成功' worderStatusValue,
            '网点激励费用' balanceTypeValue,
            '37' balanceType,
            w.worder_finish_time worderFinishTime,
            wci.dot_balance_fee dotIncreBalanceFee,
            wci.dot_balance_fee_sum dotBalanceFeeSum,
            wci.dot_balance_fee_tax dotBalanceFeetax,
            wps.tax_point taxPoint,
            d.dot_state dotState,
            wt.brand_id brandId,
            b.brand_name brandName,
            (case
            when wci.balance_set_status = 22 then '网点激励记账成功'
            end) operation,
            w.dot_id  dotId,
            ci.company_invoice_no companyInvoiceNo,
            war.create_time receivableTime
            from worder_child_information wci
            left join worder_information w on wci.worder_id = w.worder_id
            left join worder_template wt on w.template_id = wt.id
            inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
            left join worder_type t on t.id = w.worder_type_id
            left join dot_information d on wps.dot_id = d.dot_id
            LEFT JOIN brand b ON b.id = wt.brand_id
            left join company_receivable cr on wci.receivable_id = cr.id
            left join company_invoice ci on w.invoice_id = ci.id
            left join worder_audit_record war  FORCE INDEX(worder_audit_record_applyNo)  on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
            where
            1 = 1
            and wci.balance_source = 1
            and wci.balance_set_status = 22 and wps.stimulate_type = '10'
            <if test="dotId != null ">
                AND wps.dot_id = #{dotId}
            </if>
            <if test="brandId != null ">
                AND wt.brand_id = #{brandId}
            </if>
            <if test="receivableStartTime != null and receivableStartTime != '' ">
                and war.create_time <![CDATA[>=]]> #{receivableStartTime}
            </if>
            <if test="receivableEndTime != null and receivableEndTime != '' ">
                and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
            </if>
        ORDER BY w.worder_finish_time ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <!-- 网点工单结算费用-新资金 审核列表查询-->
    <select id="queryBalanceAdwanceMoneyWorderInfoAduitList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="map">
        select
            wci.id worderChildId,
            wci.balance_no balanceNo,
            w.worder_id worderId,
            wci.id id,
            w.worder_no worderNo,
            t.full_name fullName,
            d.dot_name dotName,
            d.dot_star dotStar,
            wci.balance_set_status_value worderStatusValue,
            '网点工单费用' balanceTypeValue,
            '36' balanceType,
            w.worder_finish_time worderFinishTime,
            wci.dot_balance_fee dotIncreBalanceFee,
            wci.dot_balance_fee_sum dotBalanceFeeSum,
            wci.dot_balance_fee_tax dotBalanceFeetax,
            d.tax_point taxPoint,
            d.dot_state dotState,
            wt.brand_id brandId,
            b.brand_name brandName,
            (case
            when wci.balance_set_status = 4 then '首次审核'
            when wci.balance_set_status = 11 then '二次审核'
            when wci.balance_set_status = 12 then '三次审核'
            when wci.balance_set_status = 13 then '发布'
            when wci.balance_set_status = 9 then '提交审核'
            end) operation, w.dot_id dotId,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        from
            worder_child_information wci
        inner join
        (
            select
                wci2.id
            from
                worder_child_information wci2
            inner join worder_information wi2 on
                wci2.worder_id = wi2.worder_id
            inner join company_invoice ci2 on
                wi2.invoice_id = ci2.id
                and ci2.cav_state = 2
                and ci2.status in (6, 7)
            inner join
            (
                select
                    wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
                from
                    worder_child_information wci
                inner join worder_information wi on
                    wci.worder_id = wi.worder_id
                inner join company_invoice ci on
                    wi.invoice_id = ci.id
                    and ci.cav_state = 2
                    and ci.status in (6, 7)
                where
                    wci.`type` = 1
                    and wci.balance_set_status = 4
                    and ci.cav_state = 2
                    and ci.status in (6, 7)
                group by
                    wi.dot_id
            ) dot_balance_fee_sum_worder_child on
                wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        where
            wci2.`type` = 1
            and wci2.balance_set_status = 4
            and ci2.cav_state = 2
            and ci2.status in (6, 7)

        ) dwci on
            wci.id = dwci.id
        left join worder_information w on
            wci.worder_id = w.worder_id
        left join dot_information di on
            w.dot_id = di.dot_id
        left join worder_type t on
            t.id = w.worder_type_id
        left join worder_template wt on
            w.template_id = wt.id
        left join dot_information d on
            d.dot_id = w.dot_id
        left join brand b on
            b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo) on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        where
            di.is_advance_money = 1
            <if test="dotId != null ">
                AND w.dot_id = #{dotId}
            </if>
            <if test="brandId != null ">
                AND wt.brand_id = #{brandId}
            </if>
            <if test="receivableStartTime != null and receivableStartTime != '' ">
                and war.create_time <![CDATA[>=]]> #{receivableStartTime}
            </if>
            <if test="receivableEndTime != null and receivableEndTime != '' ">
                and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
            </if>
        order by
        w.worder_finish_time asc
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <!-- 网点工单结算费用-新资金 审核列表查询-->
    <select id="queryBalanceAdwanceMoneyWorderInfoAduitCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="Integer">
        select
        count(*)
        from
        worder_child_information wci
        inner join
        (
        select
        wci2.id
        from
        worder_child_information wci2
        inner join worder_information wi2 on
        wci2.worder_id = wi2.worder_id
        inner join company_invoice ci2 on
        wi2.invoice_id = ci2.id
        and ci2.cav_state = 2
        and ci2.status in (6, 7)
        inner join
        (
        select
        wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
        from
        worder_child_information wci
        inner join worder_information wi on
        wci.worder_id = wi.worder_id
        inner join company_invoice ci on
        wi.invoice_id = ci.id
        and ci.cav_state = 2
        and ci.status in (6, 7)
        where
        wci.`type` = 1
        and wci.balance_set_status = 4
        group by
        wi.dot_id
        ) dot_balance_fee_sum_worder_child on
        wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        where
        wci2.`type` = 1
        and wci2.balance_set_status = 4
        ) dwci on
        wci.id = dwci.id
        left join worder_information w on
        wci.worder_id = w.worder_id
        left join dot_information di on
        w.dot_id = di.dot_id
        left join worder_type t on
        t.id = w.worder_type_id
        left join worder_template wt on
        w.template_id = wt.id
        left join dot_information d on
        d.dot_id = w.dot_id
        left join brand b on
        b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        where
        di.is_advance_money = 1
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>

    </select>

    <select id="queryStimulateInfoAduitCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="Integer">
        select
        count(*)
        from worder_child_information wci
        left join worder_information w on wci.worder_id = w.worder_id
        left join worder_template wt on w.template_id = wt.id
        inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
        left join worder_type t on t.id = w.worder_type_id
        left join dot_information d on wps.dot_id = d.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        where
        1 = 1
        and wci.balance_source = 1
        and wci.balance_set_status = 22 and wps.stimulate_type = '10'
        <if test="dotId != null ">
            AND wps.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>

    </select>
    <select id="queryWorderInfoAduitCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="Integer">
        select count(1) from (
            SELECT distinct
            wci.id worderChildId,
            wci.balance_no balanceNo,
            w.worder_id worderId,
            wci.id id,
            w.worder_no worderNo,
            '' stimulateId,
            t.full_name fullName,
            d.dot_name dotName,
            d.dot_star dotStar,
            wci.balance_set_status_value worderStatusValue,
            '网点工单费用' balanceTypeValue,
            '33' balanceType,
            w.worder_finish_time worderFinishTime,
            wci.dot_balance_fee dotIncreBalanceFee,
            wci.dot_balance_fee_sum dotBalanceFeeSum,
            wci.dot_balance_fee_tax dotBalanceFeetax,
            d.tax_point taxPoint,
            d.dot_state dotState,
            wt.brand_id brandId,
            b.brand_name brandName,
            (CASE WHEN wci.balance_set_status = 4 THEN '首次审核'
            WHEN wci.balance_set_status = 11 THEN '二次审核'
            WHEN wci.balance_set_status = 12 THEN '三次审核'
            WHEN wci.balance_set_status = 13 THEN '发布'
            WHEN wci.balance_set_status = 9 THEN '提交审核'
            END) operation,
            w.dot_id  dotId,
            ci.company_invoice_no companyInvoiceNo,
            war.create_time receivableTime
            FROM
            worder_child_information wci
            left join worder_information w on wci.worder_id = w.worder_id
            LEFT JOIN worder_type t ON t.id = w.worder_type_id
            LEFT JOIN worder_template wt ON w.template_id = wt.id
            LEFT JOIN dot_information d ON d.dot_id = w.dot_id
            LEFT JOIN brand b ON b.id = wt.brand_id
            left join company_receivable cr on wci.receivable_id = cr.id
            left join company_invoice ci on w.invoice_id = ci.id
            left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
            WHERE 1=1
            and wci.balance_source = 0
            and wci.`type` = 0
            <if test="isAduit == 1 ">
                AND wci.balance_set_status = 4
            </if>
            <if test="isAduit == 0 ">
                AND wci.balance_set_status = 9
            </if>
            <if test="dotId != null ">
                AND w.dot_id = #{dotId}
            </if>
            <if test="brandId != null ">
                AND wt.brand_id = #{brandId}
            </if>
            <if test="receivableStartTime != null and receivableStartTime != '' ">
                and war.create_time <![CDATA[>=]]> #{receivableStartTime}
            </if>
            <if test="receivableEndTime != null and receivableEndTime != '' ">
                and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
            </if>
        ) a

    </select>

    <!--激励金额审核list-->
    <select id="queryWorderStimulateList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO" resultType="map">
        SELECT * FROM (
        SELECT
        w.worder_id worderId,
        p.id id,
        w.worder_no worderNo,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        (CASE WHEN p.status = 12 THEN '激励待结算'
        WHEN p.status = 17 THEN '激励首次审核通过'
        WHEN p.status = 18 THEN '激励二次审核通过'
        WHEN p.status = 19 THEN '激励三次审核通过'
        WHEN p.status = 20 THEN '激励审核不通过'
        END
        ) worderStatusValue,
        '网点奖惩金额' balanceTypeValue,
        '37' balanceType,
        p.create_time worderFinishTime,
        p.stimulate_fee dotIncreBalanceFee,
        p.price_tax dotBalanceFeeSum,
        p.storage_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_id dotId,
        d.dot_state dotState,
        wt.brand_id brandId,
        (CASE WHEN p.status = 12 THEN '首次审核'
        WHEN p.status = 17 THEN '二次审核'
        WHEN p.status = 18 THEN '三次审核'
        WHEN p.status = 19 THEN '发布'
        WHEN p.status = 20 THEN '提交审核'
        END
        ) operation
        FROM
        worder_pm_stimulate p
        LEFT JOIN worder_information w ON w.worder_id = p.worder_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        LEFT JOIN worder_type t ON t.id = w.worder_type_id
        LEFT JOIN dot_information d ON d.dot_id = w.dot_id
        WHERE p.stimulate_type = 10 and w.worder_exec_status = 17 and p.is_delete = 0
        <if test="isAduit == 1 ">
            AND p.status IN (12,17,18,19)
        </if>
        <if test="isAduit == 0 ">
            AND p.status = 20
        </if>
<!--        UNION ALL-->
<!--        SELECT-->
<!--        w.worder_id worderId,-->
<!--        p.id id,-->
<!--        w.worder_no worderNo,-->
<!--        t.full_name fullName,-->
<!--        d.dot_name dotName,-->
<!--        d.dot_star dotStar,-->
<!--        (CASE WHEN p.status = 12 THEN '激励待结算'-->
<!--        WHEN p.status = 17 THEN '激励首次审核通过'-->
<!--        WHEN p.status = 18 THEN '激励二次审核通过'-->
<!--        WHEN p.status = 20 THEN '激励审核不通过'-->
<!--        END-->
<!--        ) worderStatusValue,-->
<!--        '日日顺奖惩金额' balanceTypeValue,-->
<!--        '35' balanceType,-->
<!--        p.create_time worderFinishTime,-->
<!--        p.stimulate_fee dotIncreBalanceFee,-->
<!--        p.price_tax dotBalanceFeeSum,-->
<!--        p.storage_tax dotBalanceFeetax,-->
<!--        #{taxRate} taxPoint,-->
<!--        d.dot_id dotId,-->
<!--        d.dot_state dotState,-->
<!--        wt.brand_id brandId,-->
<!--        (CASE WHEN p.status = 12 THEN '首次审核'-->
<!--        WHEN p.status = 17 THEN '二次审核审核'-->
<!--        WHEN p.status = 18 THEN '发布'-->
<!--        WHEN p.status = 20 THEN '提交审核'-->
<!--        END-->
<!--        ) operation-->
<!--        FROM-->
<!--        worder_pm_stimulate p-->
<!--        LEFT JOIN worder_information w ON w.worder_id = p.worder_id-->
<!--        LEFT JOIN worder_template wt ON w.template_id = wt.id-->
<!--        LEFT JOIN worder_type t ON t.id = w.worder_type_id-->
<!--        LEFT JOIN dot_information d ON d.dot_id = w.dot_id-->
<!--        WHERE p.stimulate_type = 11-->
<!--        <if test="isAduit == 1 ">-->
<!--            AND p.status IN (12,17,18)-->
<!--        </if>-->
<!--        <if test="isAduit == 0 ">-->
<!--            AND p.status = 20-->
<!--        </if>-->
        ) a
        <if test="dotId != null and dotId != null and dotId != 0">
            WHERE a.dotId = #{dotId}
        </if>
        <if test="brandId != null and brandId != '' and brandId != 0">
            AND a.brandId = #{brandId}
        </if>
        ORDER BY a.worderFinishTime ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryWorderStimulateCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO" resultType="Integer">
        SELECT SUM(a.num) FROM (
        SELECT
        COUNT(p.id) num
        FROM
        worder_pm_stimulate p
        LEFT JOIN worder_information w ON w.worder_id = p.worder_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        WHERE p.stimulate_type = 10 and w.worder_exec_status = 17 and p.is_delete = 0
        <if test="isAduit == 1 ">
            AND p.status IN (12,17,18,19)
        </if>
        <if test="isAduit == 0 ">
            AND p.status = 20
        </if>
        <if test="dotId != null and dotId != null ">
            AND p.dot_id = #{dotId}
        </if>
        <if test="brandId != null and brandId != '' ">
            AND wt.brand_id = #{brandId}
        </if>
<!--        UNION ALL-->
<!--        SELECT-->
<!--        COUNT(p.id) num-->
<!--        FROM-->
<!--        worder_pm_stimulate p-->
<!--        LEFT JOIN worder_information w ON w.worder_id = p.worder_id-->
<!--        LEFT JOIN worder_template wt ON w.template_id = wt.id-->
<!--        WHERE p.stimulate_type = 11-->
<!--        <if test="isAduit == 1 ">-->
<!--            AND p.status IN (12,17,18)-->
<!--        </if>-->
<!--        <if test="isAduit == 0 ">-->
<!--            AND p.status = 20-->
<!--        </if>-->
<!--        <if test="dotId != null and dotId != null ">-->
<!--            AND p.dot_id = #{dotId}-->
<!--        </if>-->
<!--        <if test="brandId != null and brandId != '' ">-->
<!--            AND wt.brand_id = #{brandId}-->
<!--        </if>-->
        ) a

    </select>

    <!--网点增项费用审核-->
    <select id="querydotIncreBalanceAduitList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="map">
        select * from (
        SELECT
        '' worderChildId,
        '' balanceNo,
        w.worder_id worderId,
        w.worder_id id,
        w.worder_no worderNo,
        '' stimulateId,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        w.worder_Incre_status_value worderStatusValue,
        '网点增项费用' balanceTypeValue,
        '34' balanceType,
        w.worder_finish_time worderFinishTime,
        w.dot_incre_balance_fee dotIncreBalanceFee,
        w.dot_incre_balance_fee_sum dotBalanceFeeSum,
        w.dot_incre_balance_fee_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (CASE WHEN w.worder_Incre_status = 1 THEN '首次审核'
        WHEN w.worder_Incre_status = 5 THEN '二次审核'
        WHEN w.worder_Incre_status = 6 THEN '三次审核'
        WHEN w.worder_Incre_status = 7 THEN '发布'
        WHEN w.worder_Incre_status = 8 THEN '提交审核'
        END) operation,
        w.dot_id dotId,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        FROM
        worder_information w
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        LEFT JOIN worder_type t ON t.id = w.worder_type_id
        LEFT JOIN dot_information d ON d.dot_id = w.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable_worder crw on w.worder_id = crw.worder_id
        left join company_receivable cr on crw.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo) on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1 = 1 and w.worder_exec_status = 17
        <if test="isAduit == 1 ">
            AND w.worder_Incre_status = 1
        </if>
        <if test="isAduit == 0 ">
            AND w.worder_Incre_status = 8
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        ) a

        ORDER BY a.worderFinishTime ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="querydotIncreBalanceAduitCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="Integer">
        select count(1) from (
            select
            distinct
            *
            from
            (   select
                '' worderChildId,
                '' balanceNo,
                w.worder_id worderId,
                w.worder_no worderNo,
                null stimulateId,
                t.full_name fullName,
                d.dot_name dotName,
                d.dot_star dotStar,
                w.worder_Incre_status_value worderStatusValue,
                '网点增项费用' balanceTypeValue,
                w.worder_finish_time worderFinishTime,
                w.dot_incre_balance_fee dotBalanceFee,
                w.dot_incre_balance_fee_sum dotBalanceFeeSum,
                w.dot_incre_balance_fee_tax dotBalanceFeetax,
                w.user_balance_fee_sum   userBalanceFeeSum,
                w.user_actual_cost       userActualCost,
                d.tax_point taxPoint,
                d.dot_id    dotId,
                wt.template_name  templateName,
                (
                    case
                    when w.worder_Incre_status = 1 then '增项待结算'
                    when w.worder_Incre_status = 5 then '网点增项首次审核通过'
                    when w.worder_Incre_status = 6 then '网点增项二次审核通过'
                    when w.worder_Incre_status = 7 then '网点增项三次审核通过'
                    when w.worder_Incre_status = 8 then '网点增项审核不通过'
                end)    operation,
                wt.brand_id brandId,
                ci.company_invoice_no companyInvoiceNo,
                war.create_time receivableTime
                from
                worder_information w
                left join worder_template wt on w.template_id = wt.id
                left join worder_type t on t.id = w.worder_type_id
                left join dot_information d on d.dot_id = w.dot_id
                left join company_receivable_record crr on crr.invoice_id = w.invoice_id
                left join company_receivable cr on cr.company_receivable_no = crr.company_receivable_no
                left join company_invoice ci on w.invoice_id = ci.id
                left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
                where
                1 = 1
                and w.worder_exec_status = 17
                <if test="isAduit == 1 ">
                    AND w.worder_Incre_status = 1
                </if>
                <if test="isAduit == 0 ">
                    AND w.worder_Incre_status = 8
                </if>
                <if test="dotId != null ">
                    AND w.dot_id = #{dotId}
                </if>
                <if test="brandId != null ">
                    AND wt.brand_id = #{brandId}
                </if>
                <if test="receivableStartTime != null and receivableStartTime != '' ">
                    and war.create_time <![CDATA[>=]]> #{receivableStartTime}
                </if>
                <if test="receivableEndTime != null and receivableEndTime != '' ">
                    and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
                </if>
            )a
        )a
    </select>

    <!--汇总-->
    <select id="queryTotalAduitList" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO"  resultType="map">
        SELECT * FROM (
        SELECT
        wci.id worderChildId,
        wci.balance_no balanceNo,
        w.worder_id worderId,
        wci.id id,
        w.worder_no worderNo,
        '' stimulateId,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        wci.balance_set_status_value worderStatusValue,
        '网点工单费用' balanceTypeValue,
        '33' balanceType,
        w.worder_finish_time worderFinishTime,
        wci.dot_balance_fee dotIncreBalanceFee,
        wci.dot_balance_fee_sum dotBalanceFeeSum,
        wci.dot_balance_fee_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (CASE WHEN wci.balance_set_status = 4 THEN '首次审核'
        WHEN wci.balance_set_status = 11 THEN '二次审核'
        WHEN wci.balance_set_status = 12 THEN '三次审核'
        WHEN wci.balance_set_status = 13 THEN '发布'
        WHEN wci.balance_set_status = 9 THEN '提交审核'
        END) operation,
        w.dot_id  dotId,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        FROM
        worder_child_information wci
        left join worder_information w on wci.worder_id = w.worder_id
        LEFT JOIN worder_type t ON t.id = w.worder_type_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        LEFT JOIN dot_information d ON d.dot_id = w.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo) on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1=1
        and wci.balance_source = 0
        <if test="isAduit == 1 ">
            AND wci.balance_set_status = 4
        </if>
        <if test="isAduit == 0 ">
            AND wci.balance_set_status = 9
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        UNION ALL
        SELECT
        '' worderChildId,
        '' balanceNo,
        w.worder_id worderId,
        w.worder_id id,
        w.worder_no worderNo,
        '' stimulateId,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        w.worder_Incre_status_value worderStatusValue,
        '网点增项费用' balanceTypeValue,
        '34' balanceType,
        w.worder_finish_time worderFinishTime,
        w.dot_incre_balance_fee dotIncreBalanceFee,
        w.dot_incre_balance_fee_sum dotBalanceFeeSum,
        w.dot_incre_balance_fee_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (CASE WHEN w.worder_Incre_status = 1 THEN '首次审核'
        WHEN w.worder_Incre_status = 5 THEN '二次审核'
        WHEN w.worder_Incre_status = 6 THEN '三次审核'
        WHEN w.worder_Incre_status = 7 THEN '发布'
        WHEN w.worder_Incre_status = 8 THEN '提交审核'
        END) operation,
        w.dot_id dotId,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        FROM
        worder_information w
        inner join worder_child_information wci on wci.worder_id = w.worder_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        LEFT JOIN worder_type t ON t.id = w.worder_type_id
        LEFT JOIN dot_information d ON d.dot_id = w.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_invoice ci on w.invoice_id = ci.id
        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo)  on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1 = 1 and w.worder_exec_status = 17
        <if test="isAduit == 1 ">
            AND w.worder_Incre_status = 1
        </if>
        <if test="isAduit == 0 ">
            AND w.worder_Incre_status = 8
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>

        <if test="isAduit == 1 ">
            union all
            select
            wci.id worderChildId,
            wci.balance_no balancdNo,
            w.worder_id worderId,
            wci.id id,
            w.worder_no worderNo,
            wps.id stimulateId,
            t.full_name fullName,
            d.dot_name dotName,
            d.dot_star dotStar,
            '记账成功' worderStatusValue,
            '网点激励费用' balanceTypeValue,
            '33' balanceType,
            w.worder_finish_time worderFinishTime,
            wci.dot_balance_fee dotIncreBalanceFee,
            wci.dot_balance_fee_sum dotBalanceFeeSum,
            wci.dot_balance_fee_tax dotBalanceFeetax,
            wps.tax_point taxPoint,
            d.dot_state dotState,
            wt.brand_id brandId,
            b.brand_name brandName,
            (case
            when wci.balance_set_status = 22 then '网点激励记账成功'
            end) operation,
            w.dot_id  dotId,
            ci.company_invoice_no companyInvoiceNo,
            war.create_time receivableTime
            from worder_child_information wci
            left join worder_information w on wci.worder_id = w.worder_id
            left join worder_template wt on w.template_id = wt.id
            inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
            left join worder_type t on t.id = w.worder_type_id
            left join dot_information d on wps.dot_id = d.dot_id
            LEFT JOIN brand b ON b.id = wt.brand_id
            left join company_receivable cr on wci.receivable_id = cr.id
            left join company_invoice ci on w.invoice_id = ci.id
            left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
            where
            1 = 1
            and wci.balance_source = 1
            and wci.balance_set_status = 22 and wps.stimulate_type = '10'
            <if test="receivableStartTime != null and receivableStartTime != '' ">
                and war.create_time <![CDATA[>=]]> #{receivableStartTime}
            </if>
            <if test="receivableEndTime != null and receivableEndTime != '' ">
                and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
            </if>
        </if>
        ) a
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="dotId != null ">
                a.dotId = #{dotId}
            </if>
            <if test="brandId != null ">
                AND a.brandId = #{brandId}
            </if>
        </trim>
        ORDER BY a.worderFinishTime ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryTotalAduitCount" parameterType="com.bonc.rrs.worderinformationaccount.vo.ReceivableFeeAduitVO" resultType="Integer">
        SELECT
        SUM(a.num)
        FROM
        (
        SELECT
        COUNT(wci.id) num
        FROM
        worder_child_information wci
        left join worder_information w on wci.worder_id = w.worder_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1=1
        and wci.balance_source = 0
        <if test="isAduit == 1 ">
            AND wci.balance_set_status = 4
        </if>
        <if test="isAduit == 0 ">
            AND wci.balance_set_status = 9
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        UNION ALL
        SELECT
        COUNT(w.worder_id) num
        FROM
        worder_information w
        left join worder_child_information wci on wci.worder_id = w.worder_id
        LEFT JOIN worder_template wt ON w.template_id = wt.id
        left join company_receivable cr on wci.receivable_id = cr.id
        left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        WHERE 1 = 1 and w.worder_exec_status = 17
        <if test="isAduit == 1 ">
            AND w.worder_Incre_status = 1
        </if>
        <if test="isAduit == 0 ">
            AND w.worder_Incre_status = 8
        </if>
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>

        <if test="isAduit == 1 ">
            union all
            select
            count(wci.id) num
            from worder_child_information wci
            left join worder_information w on wci.worder_id = w.worder_id
            left join worder_template wt on w.template_id = wt.id
            inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
            left join worder_type t on t.id = w.worder_type_id
            left join dot_information d on wps.dot_id = d.dot_id
            left join company_receivable cr on wci.receivable_id = cr.id
            left join worder_audit_record war on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
            where
            1 = 1
            and wci.balance_source = 1
            and wci.balance_set_status = 22 and wps.stimulate_type = '10'
            <if test="dotId != null ">
                AND wps.dot_id = #{dotId}
            </if>
            <if test="brandId != null ">
                AND wt.brand_id = #{brandId}
            </if>
            <if test="receivableStartTime != null and receivableStartTime != '' ">
                and war.create_time <![CDATA[>=]]> #{receivableStartTime}
            </if>
            <if test="receivableEndTime != null and receivableEndTime != '' ">
                and war.create_time <![CDATA[<]]> DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
            </if>
        </if>
        ) a

    </select>

    <select id="getDotInfoList" resultType="java.util.List">
        SELECT d.dot_id dotId, d.dot_name dotName FROM dot_information d
    </select>

    <select id="getDotAllInfoByDotIds"  parameterType="java.util.Collection" resultType="map">
        SELECT d.*, t1.name as dotAreaName, t2.name as dotCityName FROM dot_information d
        LEFT join biz_region t1 on d.dot_area = t1.id
        LEFT join biz_region t2 on d.dot_city = t2.id
        WHERE dot_id in
        <foreach collection="dotIds" open="(" close=")" separator="," item="dotId">${dotId}</foreach>
    </select>

    <select id="getOneDotAllInfoByDotId"  parameterType="java.lang.Integer" resultType="map">
        SELECT d.*, t1.name as dotAreaName, t2.name as dotCityName, s2.detail_name as dotTaxPoint
        FROM dot_information d
        LEFT join biz_region t1 on d.dot_area = t1.id
        LEFT join biz_region t2 on d.dot_city = t2.id
        LEFT join sys_dictionary s1 on s1.dic_number='tax_point'
        LEFT join sys_dictionary_detail s2 on s1.id=s2.dictionary_id and s2.detail_number=d.tax_point
        WHERE dot_id = #{dotId}
    </select>
    <select id="getAllDotList" resultType="java.util.Map">
        SELECT d.dot_id, s2.detail_name as dotTaxPoint
        FROM dot_information d
        LEFT join biz_region t1 on d.dot_area = t1.id
        LEFT join biz_region t2 on d.dot_city = t2.id
        LEFT join sys_dictionary s1 on s1.dic_number='tax_point'
        LEFT join sys_dictionary_detail s2 on s1.id=s2.dictionary_id and s2.detail_number=d.tax_point
        where d.tax_point is not null
    </select>

    <select id="queryUserInvoice" parameterType="java.lang.Integer" resultType="map">
        SELECT a.worder_id, a.worder_no, b.area_name, c.invoice_code, c.pay_type_code, c.pay_type, c.pay_order_no,
        b.order_items_amount, b.order_items_tax_rate, a.user_name, a.user_phone, e.dot_id, e.v_code, f.transaction_id
        FROM worder_information a, worder_invoice_order_items b, worder_invoice_record c, dot_information e, worder_order_log f
        WHERE a.worder_no=b.order_items_code AND b.invoice_no=c.invoice_no and a.dot_id=e.dot_id and c.pay_order_no=f.order_no AND a.worder_id=#{worderId}
    </select>

    <select id="queryWorderByIds" parameterType="java.util.List" resultType="map">
        select a.*, b.name as typeName, b.full_name as typeFullName, c.company_id as companyId, c.company_id as companyId,
        c.company_no as companyNo, c.company_name as companyName
        from worder_information a
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = a.worder_no AND e.field_id = t.settle_way+100
        LEFT JOIN company_information c ON c.company_id = e.field_value
        , worder_type b
        where b.id = a.worder_type_id and a.worder_id in
        <foreach collection="list" open="(" close=")" separator="," item="worderId">${worderId}</foreach>
    </select>

    <select id="queryIncreWorderByIds" parameterType="java.util.List" resultType="map">
        select a.*, a.worder_no as item_code, b.name as typeName, b.full_name as typeFullName, c.company_id as companyId, c.company_id as companyId,
        c.company_no as companyNo, c.company_name as companyName,ci.company_invoice_no as invoiceNo
        from worder_information a
        left join company_invoice ci on a.invoice_id = ci.id
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = a.worder_no AND e.field_id = t.settle_way+100
        LEFT JOIN company_information c ON c.company_id = e.field_value
        , worder_type b
        where b.id = a.worder_type_id and a.worder_id in
        <foreach collection="list" open="(" close=")" separator="," item="worderId">${worderId}</foreach>
    </select>

    <select id="queryWorderByStimulateIds" parameterType="java.util.List" resultType="map">
        select a.worder_no, a.user_name, user_phone, b.name as typeName, b.full_name as typeFullName, p.*,
        d.detail_name as stimulateReason, c.company_id as companyId, c.company_no as companyNo, c.company_name as companyName
        from worder_information a
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = a.worder_no AND e.field_id = t.settle_way+100
        LEFT JOIN company_information c ON c.company_id = e.field_value,
        worder_type b, worder_pm_stimulate p
        left join sys_dictionary_detail d on d.dictionary_id=36 and d.detail_number=p.stimulate_reason
        where b.id = a.worder_type_id and p.worder_id=a.worder_id and p.is_delete = 0 and  p.id in
        <foreach collection="list" open="(" close=")" separator="," item="stimulateId">${stimulateId}</foreach>
    </select>

    <select id="queryWorderAndStimulateForPublish" resultType="map">
        SELECT a.dot_id, a.worder_id AS data_id, 1 AS data_type, c.brand_id FROM worder_information a, worder_template c WHERE a.template_id=c.id AND worder_set_status=13
        UNION
        SELECT a.dot_id, a.worder_id AS data_id, 2 AS data_type, c.brand_id FROM worder_information a, worder_template c WHERE a.template_id=c.id AND worder_Incre_status=7 AND worder_status in (3,4,5)
        UNION
        SELECT b.dot_id, a.id AS data_id, 3 AS data_type, c.brand_id FROM worder_pm_stimulate a, worder_information b, worder_template c
        WHERE a.worder_id=b.worder_id AND b.template_id=c.id AND a.stimulate_type=10 AND a.status=22 AND b.worder_set_status IN (5,7,13) and a.is_delete = 0
    </select>

    <select id="queryCheckedWorderAndStimulateForPublish" parameterType="com.bonc.rrs.worderinformationaccount.dto.PublishDetail" resultType="map">
        SELECT a.dot_id, a.worder_id AS data_id, 1 AS data_type, c.brand_id FROM worder_information a, worder_template c
        WHERE a.template_id=c.id AND worder_set_status=13 and a.worder_id in
        <foreach collection="worderIds" open="(" close=")" separator="," item="e1">${e1}</foreach>
        UNION
        SELECT a.dot_id, a.worder_id AS data_id, 2 AS data_type, c.brand_id FROM worder_information a, worder_template c
        WHERE a.template_id=c.id AND worder_Incre_status=7 AND worder_status in (3,4,5) and a.worder_id in
        <foreach collection="increIds" open="(" close=")" separator="," item="e2">${e2}</foreach>
        UNION
        SELECT b.dot_id, a.id AS data_id, 3 AS data_type, c.brand_id FROM worder_pm_stimulate a, worder_information b, worder_template c
        WHERE a.worder_id=b.worder_id AND b.template_id=c.id AND a.stimulate_type=10 AND a.status=22 AND b.worder_set_status IN (5,7,13) and a.is_delete = 0 and a.id in
        <foreach collection="stimulateIds" open="(" close=")" separator="," item="e3">${e3}</foreach>
    </select>
    <select id="queryChildWorderByBalanceIds" resultType="java.util.Map">
        select
            wci.id as balance_id,
	    wci.balance_type as balance_type,
            wci.balance_no as item_code,
            a.user_name ,
            a.user_phone ,
            a.worder_no as balance_no,
            wci.create_time as create_time,
            wci.company_balance_fee ,
            wci.company_balance_fee_sum,
            a.user_actual_cost,
            b.name as typeName,
            b.full_name as typeFullName,
            c.company_id as companyId,
            c.company_id as companyId,
            c.company_no as companyNo,
            c.company_name as companyName,
            ci.company_invoice_no as invoiceNo
        from
            worder_child_information wci
                left join worder_information a on
                wci.worder_id = a.worder_id
                left join company_invoice ci on
                a.invoice_id = ci.id
                left join worder_template t on
                a.template_id = t.id
                left join worder_ext_field e on
                        e.worder_no = a.worder_no
                    and e.field_id = t.settle_way + 100
                left join company_information c on
                c.company_id = e.field_value,
            worder_type b
        where
            b.id = a.worder_type_id
          and wci.id in
        <foreach collection="balanceIds" open="(" close=")" separator="," item="balanceId">${balanceId}</foreach>
    </select>

    <select id="getDotId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select i.dot_id
        from dot_contacts d
        left join dot_information i on i.dot_code = d.dot_code
        where d.contacts_id = #{userId}
    </select>

    <select id="queryChildStimulateWorderByBalanceIds" resultType="java.util.Map">
        select
            wci.id as balanceId,
            wci.balance_no as item_code,
            a.worder_no as balanceNo,
            a.user_name,
            a.user_phone,
            b.name as typeName,
            b.full_name as typeFullName,
            p.*,
            d.detail_name as stimulateReason,
            c.company_id as companyId,
            c.company_no as companyNo,
            c.company_name as companyName
        from
            worder_child_information wci
                left join worder_information a on
                wci.worder_id = a.worder_id
                left join worder_template t on
                a.template_id = t.id
                left join worder_ext_field e on
                        e.worder_no = a.worder_no
                    and e.field_id = t.settle_way + 100
                left join company_information c on
                c.company_id = e.field_value,
            worder_type b,
            worder_pm_stimulate p
                left join sys_dictionary_detail d on
                        d.dictionary_id = 36
                    and d.detail_number = p.stimulate_reason
        where
            b.id = a.worder_type_id
          and p.worder_id = a.worder_id
          and p.is_delete = 0
          and wci.id in
        <foreach collection="balanceIds" open="(" close=")" separator="," item="balanceId">${balanceId}</foreach>
    </select>

    <update id="updateStimulateStatusToPublishByIds" parameterType="java.util.List">
        update worder_pm_stimulate set status=32 where id in
        <foreach collection="list" separator="," open="(" close=")" item="stimulateId">
            #{stimulateId}
        </foreach>
    </update>
    <update id="updateWorderChildToPublishByIds">
        update worder_child_information set balance_set_status=#{status}, balance_set_status_value=#{statusValue} where id in
        <foreach collection="worderChildIdList" separator="," open="(" close=")" item="worderChildId">
            #{worderChildId}
        </foreach>
    </update>
    <update id="updateStimulateStatusToAccountByIds" parameterType="java.util.List">
        update worder_pm_stimulate set status=22 where id in
        <foreach collection="list" separator="," open="(" close=")" item="stimulateId">
            #{stimulateId}
        </foreach>
    </update>

</mapper>