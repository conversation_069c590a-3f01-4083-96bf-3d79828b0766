<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyReceivableCapitalpoolDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyReceivableCapitalpoolEtity"
               id="companyReceivableCapitalpoolMap">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="voucherNo" column="voucher_no"/>
        <result property="balance" column="balance"/>
        <result property="availableBalance" column="available_balance"/>
        <result property="deleteState" column="delete_state"/>
        <result property="createTime" column="create_time"/>
        <result property="createOper" column="create_oper"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateOper" column="update_oper"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="selectBalanceByCompanyId" resultType="java.util.Map">
        select sum(balance) as balance, sum(available_balance) as available_balance
        from company_receivable_capitalpool
        where company_id = #{companyId}
          and delete_state = 0;
    </select>

    <select id="selectEntityByCompanyIdAndVoucherNo"
            resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableCapitalpoolEtity">
        select * from company_receivable_capitalpool where company_id = #{companyId} and voucher_no = #{voucherNo}
    </select>




</mapper>