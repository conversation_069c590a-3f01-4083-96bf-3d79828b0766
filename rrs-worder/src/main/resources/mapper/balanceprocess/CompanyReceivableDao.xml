<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyReceivableDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity" id="companyReceivableMap">
        <result property="id" column="id"/>
        <result property="companyReceivableNo" column="company_receivable_no"/>
        <result property="companyId" column="company_id"/>
        <result property="cavFee" column="cav_fee"/>
        <result property="receivableFee" column="receivable_fee"/>
        <result property="noPaymentFee" column="no_payment_fee"/>
        <result property="worderIds" column="worder_ids"/>
        <result property="status" column="status"/>
        <result property="receivableTime" column="receivable_time"/>
        <result property="voucherNo" column="voucher_no"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <sql id="selectCompanyReceivablecolumn">
        select id,
               company_receivable_no,
               company_id,
               receivable_fee,
               no_payment_fee,
               worder_ids,
               stimulate_ids,
               status,
               receivable_time,
               voucher_no,
               create_time
        from company_receivable
    </sql>
    <select id="queryCompanySurplus" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
        select sum(no_payment_fee)
        from company_receivable
        where company_id = #{companyId}
          and no_payment_fee >= 0
    </select>
    <select id="queryWorderByIds" parameterType="java.util.List"
            resultType="com.bonc.rrs.balanceprocess.vo.ReceivableWorder">
        select
        a.worder_id,
        a.worder_no,
        wci.id as balanceWorderId,
        wci.balance_no ,
        wci.balance_source ,
        wci.balance_type ,
        a.worder_finish_time,
        wci.company_balance_fee_sum as companyFee,
        wci.dot_balance_fee_sum  as dotFee,
        i.invoice_code,
        c.company_id,
        c.company_name,
        d.dot_name
        from worder_child_information wci
        left join worder_information a on
        wci.worder_id = a.worder_id
        left join company_invoice i on
        i.id = a.invoice_id
        left join worder_template t on
        a.template_id = t.id
        left join worder_ext_field e on
        e.worder_no = a.worder_no
        and e.field_id = t.settle_way + 100
        left join company_information c on
        c.company_id = e.field_value
        left join dot_information d on
        d.dot_id = a.dot_id
        where wci.id in
        <foreach collection="balanceIds" open="(" close=")" separator="," item="id">${id}</foreach>
        ORDER BY a.worder_finish_time ASC
    </select>
    <select id="queryWorderByCompanyId" parameterType="java.util.Map"
            resultType="com.bonc.rrs.balanceprocess.vo.ReceivableWorder">
        select a.worder_id,
        a.worder_no,
        a.worder_finish_time,
        (
            case
                when i.price_type = 0 then a.company_balance_fee_sum
                when i.price_type = 1 then a.company_balance_fee
            end
        ) as companyFee,
        (
            case
                when i.price_type = 0 then a.dot_balance_fee_sum
                when i.price_type = 1 then a.dot_balance_fee
            end
        )  as dotFee,
        c.company_id,
        c.company_name,
        d.dot_name,
        d.dot_state,
        i.price_type,
        i.company_invoice_no
        from worder_information a
        LEFT JOIN company_invoice i ON i.id = a.invoice_id
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = a.worder_no AND e.field_id = t.settle_way + 100
        LEFT JOIN company_information c ON c.company_id = e.field_value
        LEFT JOIN dot_information d ON d.dot_id = a.dot_id
        where c.company_id = #{w.companyId}
        and a.worder_set_status = 3
        <if test="w.invoiceId != null and w.invoiceId != ''">
            and a.invoice_id = #{w.invoiceId}
        </if>
        ORDER BY a.worder_finish_time ASC
    </select>

    <select id="queryReceivable" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity">
        SELECT DISTINCT
        c.*
        FROM
        company_receivable c
        LEFT JOIN worder_audit_record w ON c.company_receivable_no = w.apply_no
        <trim prefix="where" prefixOverrides="AND|OR">
            <if test="q.companyId != null">
                AND c.company_id = #{q.companyId}
            </if>
            <if test="q.businessNo != null and q.businessNo != ''">
                AND c.company_receivable_no Like CONCAT('%',#{q.businessNo},'%')
            </if>
            <if test="q.voucherNo != null and q.voucherNo != ''">
                AND c.voucher_no like CONCAT('%',#{q.voucherNo},'%')
            </if>
            <if test="q.status != null and q.status != ''">
                AND c.status = #{q.status}
            </if>
            <if test="q.queryType == 1">
                AND (c.creator = #{q.userId} or w.audit_user_id = #{q.userId})
            </if>
            <if test="q.queryType == 2">
                AND c.`status` = #{q.queryType}
            </if>
            <if test="q.queryType == 3">
                AND c.`status` = #{q.queryType}
            </if>
            <if test="q.receiveType == 0">
                AND c.`type` = 0
            </if>
            <if test="q.receiveType == 1">
                AND c.`type` = 1
            </if>
        </trim>
        order by c.create_time desc
    </select>

    <select id="queryWorderSumFeeByInvoiceId" resultType="java.math.BigDecimal">
        select ci.invoice_fee
        from company_invoice ci
        where ci.id = #{invoiceId}
    </select>

    <select id="querySumBalanceMoneyByInvoiceId" resultType="java.math.BigDecimal">
        select
            sum(a.publish_fee)
        from
            (
            select
                distinct bp.*
            from
                balance_publish bp
            inner join worder_child_information wci on
                bp.id = wci.publish_id
            inner join worder_information wi on
                wci.worder_id = wi.worder_id
            inner join company_invoice ci on
                wi.invoice_id = ci.id
            where
                ci.type = 1
                and ci.status = 11
                and ci.cav_state = 2
                and wi.invoice_id = #{invoiceId}) a
    </select>

    <select id="queryWorderCompanyReceivableSumFeeByInvoiceId" resultType="java.math.BigDecimal">

    select
        sum(a.cav_fee) as cavFee
    from
        (
            select
                distinct cr.*
            from
                worder_information wi
            inner join worder_child_information wci on
                wi.worder_id = wci.worder_id
            inner join company_receivable cr on
                find_in_set(concat(wci.id), concat(cr.balance_ids))
            inner join company_invoice ci on
                ci.id = wi.invoice_id
            where
                ci.id = #{invoiceId}
                and ci.company_id = cr.company_id
        ) a

    </select>

    <select id="excelWordersByCompanyId" parameterType="java.util.Map"
            resultType="com.bonc.rrs.balanceprocess.vo.ReceivableWorderExcelProperty">
        select  worder_information.worder_id,
        worder_child_information.id as balance_worder_id,
        worder_information.worder_no,
        worder_child_information.balance_no ,
        worder_child_information.balance_source ,
        worder_child_information.balance_type ,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum as companyFee,
        worder_child_information.dot_balance_fee_sum as dotFee,
        company_invoice.invoice_code,
        company_information.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        where company_information.company_id = #{companyId}
        and worder_child_information.balance_type = #{balanceType}
        <if test="invoiceId != null and invoiceId != ''">
            and company_invoice.id = #{invoiceId}
        </if>
        <if test="worderId != null">
            and worder_information.worder_id = #{worderId}
        </if>
        and (worder_child_information.balance_set_status = 3  or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0

        union

        select  worder_information.worder_id,
        worder_child_information.id as balance_worder_id,
        worder_information.worder_no,
        worder_child_information.balance_no ,
        worder_child_information.balance_source ,
        worder_child_information.balance_type ,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum as companyFee,
        worder_child_information.dot_balance_fee_sum as dotFee,
        company_invoice.invoice_code,
        company_information.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id  AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        where company_information.company_id = #{companyId}
        and worder_child_information.balance_type = #{balanceType}
        <if test="invoiceId != null and invoiceId != ''">
            and company_invoice.id = #{invoiceId}
        </if>
        <if test="worderId != null">
            and worder_information.worder_id = #{worderId}
        </if>
        and (worder_child_information.balance_set_status = 3  or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0

    </select>

    <select id="queryWorderSumByByInvoiceId" parameterType="java.util.Map" resultType="java.lang.String">
        select group_concat(wci.id)
        from worder_information wi,
             worder_child_information wci
        where wci.worder_id = wi.worder_id
          and wci.balance_type = #{balanceType}
          and wi.invoice_id = #{invoiceId}
          and wi.worder_set_status = 3
          and wci.balance_set_status = 3
    </select>

    <select id="countRecWorderByInvoiceId" resultType="java.lang.Integer">
        select
        count(1)
        from (
        select  worder_information.worder_id,
        worder_child_information.id as balance_worder_id,
        worder_information.worder_no,
        worder_child_information.balance_no ,
        worder_child_information.balance_source ,
        worder_child_information.balance_type ,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum as companyFee,
        worder_child_information.dot_balance_fee_sum as dotFee,
        company_invoice.invoice_code,
        company_information.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_type = 0 AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        where worder_child_information.id in
        <foreach collection="balanceWorderIds" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
        and (worder_child_information.balance_set_status = 3  or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0

        union

        select  worder_information.worder_id,
        worder_child_information.id as balance_worder_id,
        worder_information.worder_no,
        worder_child_information.balance_no ,
        worder_child_information.balance_source ,
        worder_child_information.balance_type ,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum as companyFee,
        worder_child_information.dot_balance_fee_sum as dotFee,
        company_invoice.invoice_code,
        company_information.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id  AND
        worder_child_information.balance_type = 0 AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        where  worder_child_information.id in
        <foreach collection="balanceWorderIds" separator="," item="id" open="(" close=")">
            #{id}
        </foreach>
        and (worder_child_information.balance_set_status = 3  or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0
         ) a
    </select>
    <select id="findByInvoiceCode" resultType="java.util.HashMap">
        select invoice_code from company_invoice_query_result where company_invoice_no=#{companyInvoiceNo}
    </select>
    <select id="findByInvoiceCodes"
            resultType="com.bonc.rrs.worderinformationaccount.entity.CompanyInvoiceQueryResultEntity">
        select
               invoice_code,company_invoice_no
        from company_invoice_query_result
        where company_invoice_no in
        <foreach collection="companyInvoiceNos" item="companyInvoiceNo" open="(" close=")" separator=",">
            #{companyInvoiceNo}
        </foreach>
    </select>
    <select id="queryStimulateSumFeeByInvoiceId" resultType="java.math.BigDecimal">
        select sum(case
                       when ci.price_type = 0 then wci.company_balance_fee_sum
                       when ci.price_type = 1 then wci.company_balance_fee
            end)
        from company_invoice ci,
             worder_pm_stimulate wps ,
             worder_child_information wci
        where ci.id = wps.invoice_id and wci.stimulate_id = wps.id
          <if test="balanceType != null">
              and wci.balance_type = #{balanceType}
          </if>
          and wps.invoice_id = #{invoiceId}
          and wci.balance_set_status = 18
          and wps.stimulate_type = '11'
    </select>

    <select id="queryReceivableByInvoiceId" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity">
        select
            *
        from
            company_receivable cr
        where
            cr.type = 0
            and cr.company_receivable_no in (
            select
                crr.company_receivable_no
            from
                company_receivable_record crr
            where
                crr.type = 1
                and crr.delete_state = 0
                and crr.invoice_id = #{invoiceId})
    </select>

    <select id="queryinvoiceFeeByInvoiceId" resultType="java.math.BigDecimal">
        select
            ci.invoice_fee
        from
            company_invoice ci
        where
            ci.id = #{invoiceId}
    </select>


    <select id="queryInvoiceByInvoiceId" resultType="java.math.BigDecimal">
        select sum(a.companyFee) from (
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = 0
        <if test="invoiceId != null">
            and company_invoice.id = #{invoiceId}
        </if>
        and worder_child_information.is_delete = 0

        UNION
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = 0
        <if test="invoiceId != null">
            and company_invoice.id = #{invoiceId}
        </if>
        and worder_child_information.is_delete = 0
        ) as a
    </select>

    <select id="queryBalanceTypeByOne" resultType="java.lang.Integer">
        select count(1) from (
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = 1
        <if test="invoiceId != null">
            and company_invoice.id = #{invoiceId}
        </if>
        and worder_child_information.is_delete = 0

        UNION
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = 1
        <if test="invoiceId != null">
            and company_invoice.id = #{invoiceId}
        </if>
        and worder_child_information.is_delete = 0
        ) as a
    </select>

    <select id="queryWorderChildInfoByInvoiceId" resultType="com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity">
        select
            wci.*
        from
            worder_child_information wci
        left join worder_information wi on
            wi.worder_id = wci.worder_id
        where
            wi.invoice_id = #{invoiceId}
        and wci.is_delete = 0
    </select>

    <select id="queryVoucherList" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableNoticeEntity">
        select
            distinct
            crn.id,
            crn.receivable_voucher_no,
            crn.receivable_price,
            DATE_FORMAT(crn.receivable_time, '%Y-%m-%d %T') as receivable_time
        from
            company_receivable_notice crn
        inner join company_information ci on
            crn.company_eight_code = ci.company_code
        where
            ci.company_id = #{companyId}
            and crn.status = 0
            and crn.is_delete = 0
        order by
            crn.receivable_price
    </select>

    <select id="queryReceivableByCompanyIdAndVoucherNo" resultType="java.lang.Integer">
        select
            count(*)
        from
            company_receivable cr
        inner join company_receivable_record crr on
            cr.company_receivable_no = crr.company_receivable_no
        where
            cr.company_id = #{companyId}
            and crr.`type` = 0
            and crr.voucher_no = #{voucherNo}
            and crr.delete_state = 0
    </select>

    <update id="upateVoucherStatus">
        update company_receivable_notice set status = #{status}
                where id = #{id} and is_delete = 0
    </update>
    <update id="upateVoucherStatusByVoucherNo">
        update company_receivable_notice set status = #{status}
                where receivable_voucher_no = #{voucherNo} and is_delete = 0 and status != 2
    </update>
    <select id="queryCompanyInvoiceByCompanyReceivableNo" resultType="com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity">
        select
            distinct ci.*
        from
            company_receivable cr
        inner join company_receivable_record crr on
            crr.company_receivable_no = cr.company_receivable_no
        inner join company_invoice ci on
            crr.invoice_id = ci.id
        where
            cr.company_receivable_no = #{companyReceivableNo}
    </select>

    <select id="queryCompanyInvoiceSurplusCavFee" resultType="java.math.BigDecimal">
        select sum(a.cav_fee) as cav_fee from
        (
            select
                crr.cav_fee
            from
                company_invoice ci
            inner join company_receivable_record crr on
                ci.id = crr.invoice_id
            inner join company_receivable cr on
                crr.company_receivable_no = cr.company_receivable_no
            where
                ci.id = #{invoiceId}
                and crr.`type` = 1
                and crr.company_receivable_no != #{companyReceivableNo}
        ) a
    </select>


    <select id="queryCompanyInvoiceSurplusCavFeeHis" resultType="java.math.BigDecimal">
        select sum(a.cav_fee) as cav_fee from
        (
            select
                crr.cav_fee
            from
                company_invoice ci
            inner join company_receivable_record crr on
                ci.id = crr.invoice_id
            inner join company_receivable cr on
                crr.company_receivable_no = cr.company_receivable_no
            where
                ci.id = #{invoiceId}
                and crr.`type` = 1
                and crr.company_receivable_no = #{companyReceivableNo}
        ) a
    </select>

    <select id="querySumCavFee" resultType="java.math.BigDecimal">
        select sum(a.cav_fee) as cav_fee from
        (
            select
                crr.cav_fee
            from
                company_receivable_record crr
            where
                crr.`type` = 1
                and crr.voucher_no = #{voucherNo}
                and crr.delete_state = '0'
        ) a
    </select>

    <select id="getCavState" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity">
        select cr.* from company_receivable cr
        inner join company_receivable_record crr on cr.company_receivable_no = crr.company_receivable_no
        where crr.delete_state = 0
        and crr.`type` = 1
        and crr.invoice_id = #{invoiceId}
        limit 1
    </select>

    <select id="getAllReceivableIdByBalanceId" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity">
        select distinct cr.* from worder_child_information wci
        left join company_receivable cr on wci.receivable_id = cr.id
        left join company_receivable_record crr on cr.company_receivable_no = crr.company_receivable_no and delete_state = 0
        where wci.is_delete = 0
        and wci.id in <foreach collection="balanceIds" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
    </select>

    <select id="querySumMoney" resultType="java.math.BigDecimal">
        select sum(wci.dot_balance_fee_sum) from worder_child_information wci
        where wci.is_delete =0
        and wci.publish_id = #{publishId} and wci.receivable_id != #{receivableId}
    </select>
    <select id="queryChildMoney" resultType="java.math.BigDecimal">
        select sum(wci.dot_balance_fee_sum) from worder_child_information wci
        where wci.is_delete =0
        and wci.publish_id = #{publishId} and wci.receivable_id = #{receivableId}
    </select>

</mapper>