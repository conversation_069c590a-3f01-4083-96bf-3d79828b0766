<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyInvoiceDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity" id="companyInvoiceMap">
        <result property="id" column="id" />
        <result property="companyInvoiceNo" column="company_invoice_no" />
        <result property="companyId" column="company_id" />
        <result property="goodsName" column="goods_name" />
        <result property="goodsModel" column="goods_model" />
        <result property="goodsUnit" column="goods_unit" />
        <result property="goodsNum" column="goods_num" />
        <result property="taxPrice" column="tax_price" />
        <result property="invoiceFee" column="invoice_fee" />
        <result property="noTaxPrice" column="no_tax_price" />
        <result property="taxRate" column="tax_rate" />
        <result property="noTaxFee" column="no_tax_fee" />
        <result property="remark" column="remark" />
        <result property="adjustedWorderId" column="adjusted_worder_id" />
        <result property="adjustedDesc" column="adjusted_desc" />
        <result property="invoiceOrderNo" column="invoice_order_no" />
        <result property="status" column="status" />
        <result property="invoiceCode" column="invoice_code" />
        <result property="rn" column="rn" />
        <result property="flag" column="flag" />
        <result property="kprq" column="kprq" />
        <result property="notaxamount" column="notaxamount" />
        <result property="taxamount" column="taxamount" />
        <result property="totalamount" column="totalamount" />
        <result property="drawer" column="drawer" />
        <result property="dmgs" column="dmgs" />
    </resultMap>
    <sql id="selectCompanyInvoicecolumn">
        select id, company_invoice_no, company_id, goods_name, goods_model, goods_unit, goods_num, tax_price, invoice_fee, no_tax_price, tax_rate, no_tax_fee, remark, adjusted_worder_id, adjusted_desc, invoice_order_no, status, invoice_code, rn, flag, kprq, notaxamount, taxamount, totalamount, drawer, dmgs from company_invoice
    </sql>
    <select id="queryInvocice" resultType="com.bonc.rrs.balanceprocess.vo.CompanyInvoiceVO">
        SELECT
	    c.*,
        max(w1.create_time) as first_audit_time,
        max(w2.create_time) as second_audit_time
    FROM
	    company_invoice c
    LEFT JOIN worder_audit_record w ON c.company_invoice_no = w.apply_no
    left join worder_audit_record w1 on w.apply_no = w1.apply_no and w1.audit_status in (1, -1)
    left join worder_audit_record w2 on w.apply_no = w2.apply_no and w2.audit_status in (2, -2)
    <trim prefix="where" prefixOverrides="AND|OR">
        <if test="q.companyId != null">
            AND c.company_id = #{q.companyId}
        </if>
        <if test="q.businessNo != null and q.businessNo != ''">
            AND c.company_invoice_no Like CONCAT('%',#{q.businessNo},'%')
        </if>
        <if test="q.status != null and q.status != ''">
            AND c.status = #{q.status}
        </if>
        <if test="q.queryType == 1">
            AND (c.creator = #{q.userId} or  w.audit_user_id = #{q.userId})
        </if>
        <if test="q.queryType == 2">
            AND c.`status` = #{q.queryType}
        </if>
        <if test="q.queryType == 3">
            AND c.`status` = #{q.queryType}
        </if>

    </trim>
    group by c.company_invoice_no
    order by c.submit_time desc
    </select>

    <select id="selectCompanyInvoicePage"
            resultType="com.bonc.rrs.balanceprocess.vo.CompanyInvoiceVO">
        select
            cin.*,
            GROUP_CONCAT(cin.file_name) as old_name
        from
        (
            select
                distinct
                ci.*,
                sf.old_name as file_name
            from
                company_invoice ci
                left join worder_information wi on
                ci.id = wi.invoice_id
                left join worder_child_information wci on
                wi.worder_id = wci.worder_id
                left join company_invoice_file cif on
                ci.id = cif.invoice_id
                left join sys_file sf on
                cif.file_id = sf.file_id
        where
                ci.status in (6, 7)
                and wci.balance_set_status = 3
                and ci.cav_state in (0, 1, 6)
                <if test="q.businessNo != null and q.businessNo != ''">
                    and ci.company_invoice_no = #{q.businessNo}
                </if>
                <if test="q.companyId != null">
                    and ci.company_id = #{q.companyId}
                </if>
            union all
            select
                distinct
                ci.*,
                sf.old_name as file_name
            from
                company_invoice ci
            left join worder_information wi on
                ci.id = wi.invoice_id
            left join worder_child_information wci on
                wi.worder_id = wci.worder_id
            left join company_invoice_file cif on
                ci.id = cif.invoice_id
            left join sys_file sf on
                cif.file_id = sf.file_id
            where
                ci.status in (7,11)
                and ci.cav_state in(2,6)
                and ci.type = 1
                <if test="q.businessNo != null and q.businessNo != ''">
                    and ci.company_invoice_no = #{q.businessNo}
                </if>
                <if test="q.companyId != null">
                    and ci.company_id = #{q.companyId}
                </if>
            union all
            select
                distinct
                ci.*,
                sf.old_name as file_name
            from
                company_invoice ci
                left join worder_pm_stimulate wps on
                ci.id = wps.invoice_id
                left join worder_child_information wci on
                wps.id = wci.stimulate_id
                left join company_invoice_file cif on
                ci.id = cif.invoice_id
                left join sys_file sf on
                cif.file_id = sf.file_id
            where
                ci.status in (6, 7)
                and wci.balance_set_status = 3
                and ci.cav_state in (0, 1, 6)
                <if test="q.businessNo != null and q.businessNo != ''">
                    and ci.company_invoice_no = #{q.businessNo}
                </if>
                <if test="q.companyId != null">
                    and ci.company_id = #{q.companyId}
                </if>
        ) cin
            group by cin.id
            order by
            cin.cav_state,
            cin.create_time desc
    </select>

    <select id="queryProductTaxCodeList" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity">
        select
            sdd.*
        from
            sys_dictionary_detail sdd
        left join sys_dictionary sd on
            sdd.dictionary_id = sd.id
        where
            sd.dic_number = 'product_tax_code'
            and sdd.detail_number = #{taxRate}
    </select>

    <select id="queryBrandByInvoiceId" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity">
        select b2.* from brand b2
            inner join worder_template wt on b2.id = wt.brand_id
            inner join worder_information wi on wt.id = wi.template_id
        where wi.invoice_id = #{invoiceId} limit 1
    </select>

    <select id="queryDotByWorderId" resultType="com.bonc.rrs.worder.entity.DotInformationEntity">
        select
            di.dot_id,
            di.dot_name,
            di.v_code,
            di.branch_code,
            di.branch_name,
            di.tax_no,
            t1.name as dotAreaName, t2.name as dotCityName
        from
            dot_information di
        inner join worder_information wi on
            di.dot_id = wi.dot_id
        LEFT join biz_region t1 on di.dot_area = t1.id
        LEFT join biz_region t2 on di.dot_city = t2.id
        where
            wi.worder_id = #{worderId}
    </select>

    <select id="queryDotByDotId" resultType="com.bonc.rrs.worder.entity.DotInformationEntity">
        select
            di.dot_id,
            di.dot_name,
            di.v_code,
            di.branch_code,
            di.branch_name,
            di.tax_no,
            t1.name as dotAreaName,
            t2.name as dotCityName
        from
            dot_information di
        left join biz_region t1 on
            di.dot_area = t1.id
        left join biz_region t2 on
            di.dot_city = t2.id
        where
            di.dot_id = #{dotId}
    </select>

    <select id="queryDotTaxPointByDotId" resultType="java.math.BigDecimal">
        select
            a.taxPoint
        from
            dot_information di
        inner join (
            select
                sdd.detail_number , cast(sdd.detail_name as DECIMAL(10, 2)) * 100 as taxPoint
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = "balance_tax_rate" ) a on
            di.tax_point = a.detail_number
        where di.dot_id = #{dotId}
    </select>

    <select id="queryFileByInvoiceId" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select
            sf.old_name,
            sf.path
        from
            company_invoice ci
        inner join company_invoice_file cif on
            ci.id = cif.invoice_id
        inner join sys_file sf on
            cif.file_id = sf.file_id
        where
            ci.id = #{invoiceId}
    </select>


    <select id="queryCompanyTaxRateByWorderId" resultType="java.lang.Integer">
        select
            sdd.detail_name * 100
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id and sd.dic_number = 'balance_tax_rate'
        inner join balance_rule br on
            sdd.detail_number = br.balance_tax_rate
        inner join worder_template wt on
            wt.company_balance_rule_id = br.id
        inner join worder_information wi on
            wt.id = wi.template_id
        where
            wi.worder_id = #{worderId} limit 1
    </select>


    <select id="queryDictionaryGoodsNameList" resultType="com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryDetailEntity">
        select
            sdd.*
        from
            sys_dictionary_detail sdd
        left join sys_dictionary sd on
            sdd.dictionary_id = sd.id
        where
            sd.dic_number = 'invoice_goods_name'
    </select>
    <select id="queryInvoiceVoucherDetail"
            resultType="com.bonc.rrs.invoice.enterprises.util.InvoiceVoucherDetail">
        select
            ci.company_invoice_no,
            behr.company,
            ciqr.invoice_id,
            if(ciqr.invoice_code, substring(ciqr.invoice_code, -8), '') as invoice_code,
            baas.account_code,
            baas.account_date
        from
            company_invoice ci
        inner join balance_enterprises_header_record behr on
            ci.invoice_order_no = behr.invoice_id
        inner join balance_acs_accounting_status baas on
            ci.invoice_order_no = baas.row_id
        inner join company_invoice_query_result ciqr on
            ci.company_invoice_no = ciqr.company_invoice_no
        where
            ci.id > 1625
            and ci.is_archive = 0
            and baas.flag = 'S'
            and baas.account_flag = '1'
    </select>

    <select id="getWorderTypeNameById"
            resultType="string" parameterType="integer">
        select wt.name from worder_type wt where wt.id = #{worderTypeId}
    </select>

    <select id="queryAccountWorderPmStimulateByInvoiceId"
            resultType="com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity" parameterType="integer">
        select
            wps.*
        from
            worder_wait_account wwa
        inner join worder_pm_stimulate wps on
            wwa.stimulate_id = wps.id
        where
            wwa.invoice_id = #{invoiceId}
            and wwa.status = 1
            and wwa.deleted = 0
            and wps.is_delete = 0
    </select>

    <select id="getWorderPmStimulateReason"
            resultType="string" parameterType="string">
        select
            sdd.detail_name
        from
            sys_dictionary sd
        inner join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id
        where
            sd.dic_number = #{dicNumber}
            and sdd.detail_number = #{detailNumber}
    </select>

    <select id="queryByReceivableNo" resultType="com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity">
        select ci.* from company_receivable_record crr
        left join company_invoice ci on crr.invoice_id = ci.id
        where crr.delete_state = 0
        and crr.type = 1
        and crr.company_receivable_no =#{companyReceivableNo}
    </select>

    <select id="getFileUrlByInvoiceId" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select sf.`path` from company_invoice_file cif
        left join sys_file sf on cif.file_id =sf.file_id
        where cif.invoice_id = #{invoiceId}
        order by sf.create_time desc limit 1
    </select>

    <select id="getFileUrlByReceivableId" resultType="com.bonc.rrs.workManager.entity.SysFileEntity">
        select sf.`path` from company_receivable_file crf
        left join sys_file sf on crf.file_id =sf.file_id
        where crf.receivable_id = #{receivableId}
        order by sf.create_time desc limit 1
    </select>
    <select id="queryDotByWorderIdList" resultType="com.bonc.rrs.worder.entity.DotInformationEntity">
        select
        di.dot_id,
        di.dot_name,
        di.v_code,
        di.branch_code,
        di.branch_name,
        di.tax_no,
        t1.name as dotAreaName, t2.name as dotCityName,
        wi.worder_id worderId
        from
        dot_information di
        inner join worder_information wi on
        di.dot_id = wi.dot_id
        LEFT join biz_region t1 on di.dot_area = t1.id
        LEFT join biz_region t2 on di.dot_city = t2.id
        where wi.worder_id in
        <foreach collection="worderIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>