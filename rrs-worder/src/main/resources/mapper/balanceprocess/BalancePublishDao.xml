<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.BalancePublishDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.BalancePublishEntity" id="balancePublishMap">
        <result property="id" column="id" />
        <result property="balancePublishNo" column="balance_publish_no" />
        <result property="dotId" column="dot_id" />
        <result property="brandId" column="brand_id" />
        <result property="worderIds" column="worder_ids" />
        <result property="increIds" column="incre_ids" />
        <result property="stimulateIds" column="stimulate_ids" />
        <result property="status" column="status" />
        <result property="createTime" column="create_time" />
    </resultMap>
    <sql id="selectBalancePublishcolumn">
        select id, balance_publish_no, dot_id, brand_id, worder_ids, incre_ids, stimulate_ids, status, create_time from balance_publish
    </sql>

    <insert id="saveBatchFile">
        insert into publish_file (batch_no,url) values (#{batchNo},#{importFileUrl})
    </insert>

    <select id="checkAdvanceMoneyMinLimit" parameterType="java.util.List" resultType="map">
        select
            wci2.id ,
            di.dot_name dotName
        from
            worder_child_information wci2
        inner join worder_information wi2 on
            wci2.worder_id = wi2.worder_id
        inner join dot_information di on
            wi2.dot_id = di.dot_id
        inner join company_invoice ci2 on
            wi2.invoice_id = ci2.id
            and ci2.cav_state = 2
            and ci2.status in (6, 7)
        inner join (
            select
                wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
            from
                worder_child_information wci
            inner join worder_information wi on
                wci.worder_id = wi.worder_id
            inner join company_invoice ci on
                wi.invoice_id = ci.id
                and ci.cav_state = 2
                and ci.status in (6, 7)
            where
                    wci.`type` = 1
                and wci.balance_set_status = 4
                and wci.id in
                <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
            group by wi.dot_id
        ) dot_balance_fee_sum_worder_child on
            wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        inner join (
            select
                CAST(sdd.detail_number AS SIGNED) advance_money_min_limit
            from
                sys_dictionary sd
            inner join sys_dictionary_detail sdd on
                sd.id = sdd.dictionary_id
            where
                sd.dic_number = 'advance_money_min_limit'
        ) dot_min_limit on
            dot_balance_fee_sum_worder_child.dot_balance_fee_sum <![CDATA[ < ]]> dot_min_limit.advance_money_min_limit
        where
            wci2.`type` = 1
            and wci2.balance_set_status = 4
            and wci2.id in
            <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>

    </select>

    <select id="checkBankMoneyMinLimit" parameterType="java.util.List" resultType="map">
        select
        wci2.id ,
        di.dot_name dotName
        from
        worder_child_information wci2
        inner join worder_information wi2 on
        wci2.worder_id = wi2.worder_id
        inner join dot_information di on
        wi2.dot_id = di.dot_id
        inner join company_invoice ci2 on
        wi2.invoice_id = ci2.id
        and ci2.cav_state = 2
        and ci2.status in (6, 7)
        inner join (
        select
        wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
        from
        worder_child_information wci
        inner join worder_information wi on
        wci.worder_id = wi.worder_id
        inner join company_invoice ci on
        wi.invoice_id = ci.id
        and ci.cav_state = 2
        and ci.status in (6, 7)
        where
        wci.`type` = 1
        and wci.balance_set_status = 4
        and wci.id in
        <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
        group by wi.dot_id
        ) dot_balance_fee_sum_worder_child on
        wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        inner join (
        select
        CAST(sdd.detail_number AS SIGNED) bank_money_min_limit
        from
        sys_dictionary sd
        inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
        where
        sd.dic_number = 'bank_money_min_limit'
        ) dot_min_limit on
        dot_balance_fee_sum_worder_child.dot_balance_fee_sum <![CDATA[ < ]]> dot_min_limit.bank_money_min_limit
        where
        wci2.`type` = 1
        and wci2.balance_set_status = 4
        and wci2.id in
        <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>

    </select>

    <select id="getDotByChilds" resultType="java.lang.Integer">
        select distinct wi.dot_id from worder_information wi inner join worder_child_information wci on wi.worder_id = wci.worder_id
        where wci.id in <foreach collection="ids" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
    </select>

    <select id="checkDotBank" resultType="java.lang.Integer">
        select db.dot_id dotId from dot_bank db
        where db.is_delete = 0 and db.dot_bank like CONCAT('%',#{bankAccount},'%') and db.dot_id in (
            select wi.dot_id from worder_information wi inner join worder_child_information wci on wi.worder_id = wci.worder_id
            where wci.id in <foreach collection="ids" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
        )
    </select>

    <select id="checkDotBankFirst" resultType="java.lang.Integer">
        select distinct db.dot_id dotId from dot_bank db
        where db.is_delete = 0 and db.dot_id in (
        select wi.dot_id from worder_information wi inner join worder_child_information wci on wi.worder_id = wci.worder_id
        where wci.id in <foreach collection="ids" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
        )
    </select>

    <select id="listWorder" parameterType="java.util.List" resultType="com.bonc.rrs.balanceprocess.vo.PublishWorder">
        select wci.id worderChildId, wci.balance_no, a.worder_id, a.worder_no, t.brand_id, a.dot_id, d.dot_name, s.star_name as dot_astar_name, b.full_name as worder_type_name, '网点工单费用' as balance_type_name,
        wci.dot_balance_fee_sum as fee, wci.dot_balance_fee_tax as tax, wci.dot_balance_fee as no_tax_fee, s2.detail_name as dot_tax_point, i.invoice_code
        from worder_child_information wci
        left join worder_information a on wci.worder_id = a.worder_id
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        LEFT JOIN company_invoice i ON i.id = a.invoice_id
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN dot_information d ON d.dot_id = a.dot_id
        LEFT JOIN dot_star_design s ON s.id = d.star_id
        LEFT join sys_dictionary s1 on s1.dic_number='tax_point'
        LEFT join sys_dictionary_detail s2 on s1.id=s2.dictionary_id and s2.detail_number=d.tax_point
        where wci.id in
        <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
    </select>
    <select id="listWorderPayNo" parameterType="java.util.List" resultType="map">
        select a.worder_id, c.voucher_no
        from worder_information a, company_receivable_worder b, company_receivable c
        where a.worder_id=b.worder_id and b.receivable_id=c.id and a.worder_id in
        <foreach collection="list" open="(" close=")" separator="," item="worderId">${worderId}</foreach>
    </select>
    <select id="listIncreWorder" parameterType="java.util.List" resultType="com.bonc.rrs.balanceprocess.vo.PublishWorder">
        select a.worder_id, a.worder_no, t.brand_id, a.dot_id, d.dot_name, s.star_name as dot_star_name, b.full_name as worder_type_name, '网点增项费用' as balance_type_name,
        a.dot_incre_balance_fee_sum as fee, a.dot_incre_balance_fee_tax as tax, a.dot_incre_balance_fee as no_tax_fee, s2.detail_name as dot_tax_point, i2.invoice_code, i3.transaction_id as pay_no
        from worder_information a
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN dot_information d ON d.dot_id = a.dot_id
        LEFT JOIN dot_star_design s ON s.id = d.star_id
        LEFT join sys_dictionary s1 on s1.dic_number='tax_point'
        LEFT join sys_dictionary_detail s2 on s1.id=s2.dictionary_id and s2.detail_number=d.tax_point
        LEFT JOIN worder_invoice_order_items i1 on a.worder_no=i1.order_items_code
        LEFT JOIN worder_invoice_record i2 on i1.invoice_no=i2.invoice_no
        LEFT JOIN worder_order_log i3 on i2.pay_order_no=i3.order_no
        where a.worder_id in
        <foreach collection="list" open="(" close=")" separator="," item="worderId">${worderId}</foreach>
    </select>
    <select id="listStimulateWorder" parameterType="java.util.List" resultType="com.bonc.rrs.balanceprocess.vo.PublishWorder">
        select wci.id worderChildId, wci.balance_no, a.worder_id, a.worder_no, wps.id as stimulateId, t.brand_id, a.dot_id, d.dot_name, s.star_name as dot_star_name, b.full_name as worder_type_name, '网点激励费用' as balance_type_name,
        wci.dot_balance_fee_sum as fee, wci.dot_balance_fee_tax as tax, wci.dot_balance_fee as no_tax_fee, wps.tax_point as dot_tax_point,wps.id as pmId
        from worder_child_information wci
        left join worder_information a on wci.worder_id = a.worder_id
        inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
        LEFT JOIN worder_type b ON a.worder_type_id=b.id
        LEFT JOIN worder_template t ON a.template_id = t.id
        LEFT JOIN dot_information d ON d.dot_id = wps.dot_id
        LEFT JOIN dot_star_design s ON s.id = d.star_id
        where wci.id in
        <foreach collection="list" open="(" close=")" separator="," item="worderChildId">${worderChildId}</foreach>
    </select>

    <select id="queryPublish" resultType="com.bonc.rrs.balanceprocess.entity.BalancePublishEntity">
        SELECT DISTINCT
        b.*
        FROM
        balance_publish b
        LEFT JOIN worder_audit_record w ON b.balance_publish_no = w.apply_no
    <trim prefix="where" prefixOverrides="AND|OR">
        <if test="q.dotId != null">
            AND b.dot_id = #{q.dotId}
        </if>
        <if test="q.balancePublishNo != null and q.balancePublishNo != ''">
            AND b.balance_publish_no Like CONCAT('%',#{q.balancePublishNo},'%')
        </if>
        <if test="q.status != null and q.status != ''">
            AND b.status = #{q.status}
        </if>
        <if test="q.brandId != null">
            AND b.brand_id = #{q.brandId}
        </if>
        <if test="q.queryType == 1">
            AND (b.creator = #{q.userId} or w.audit_user_id = #{q.userId})
        </if>
        <if test="q.queryType == 2">
            AND b.`status` = #{q.queryType}
        </if>
        <if test="q.queryType == 3">
            AND b.`status` = #{q.queryType}
        </if>

    </trim>
    </select>

    <select id="queryInvoiceIdByWorderChildId" resultType="Integer">
        select
            wi.invoice_id
        from
            worder_information wi
        inner join worder_child_information wci on
            wi.worder_id = wci.worder_id
        where
            wci.id = #{worderChildId}
        limit 1
    </select>

    <update id="deletePublishUpdateWorderChildInformation">
        update
            worder_child_information
        set
            balance_set_status = 4,
            balance_set_status_value = '车企已回款',
            publish_id = null
        where
            is_delete = 0
            and id in
            <foreach collection="worderChildIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </update>

    <select id="queryPublishGroupBatch" resultType="com.bonc.rrs.balanceprocess.entity.BalancePublishEntity">
        select
            a.batch_no,
            a.dot_id,
            a.brand_id ,
            sum(a.publish_fee) as publish_fee ,
            a.publish_type,
            a.batch_status,
            a.create_time ,
            a.creator ,
            a.creator_name ,
            a.submit_time ,
            a.worder_ids,
            a.stimulate_ids
        from
        (
            SELECT
            distinct
                b.batch_no,
                b.dot_id,
                b.brand_id ,
                b.publish_fee,
                b.publish_type,
                b.batch_status,
                b.create_time ,
                b.creator ,
                b.creator_name ,
                b.submit_time ,
                b.worder_ids,
                b.stimulate_ids
            FROM
                balance_publish b
            LEFT JOIN worder_audit_record w ON b.balance_publish_no = w.apply_no
            <trim prefix="where" prefixOverrides="AND|OR">
                <if test="q.dotId != null">AND b.dot_id = #{q.dotId}
                </if>
                <if test="q.batchNo != null and q.batchNo != ''">
                    AND b.batch_no Like CONCAT('%',#{q.batchNo},'%')
                </if>
                <if test="q.balancePublishNo != null and q.balancePublishNo != ''">
                    AND b.balance_publish_no Like CONCAT('%',#{q.balancePublishNo},'%')
                </if>
                <if test="q.status != null and q.status != ''">
                    AND b.status = #{q.status}
                </if>
                <if test="q.batchStatus != null">
                    AND b.batch_status = #{q.batchStatus}
                </if>
                <if test="q.publishType != null">
                    AND b.publish_type = #{q.publishType}
                </if>
                <if test="q.brandId != null">
                    AND b.brand_id = #{q.brandId}
                </if>
                <if test="q.queryType == 1">
                    AND (b.creator = #{q.userId} or w.audit_user_id = #{q.userId})
                </if>
            </trim>

        ) a group by a.batch_no order by a.create_time desc
    </select>

    <!-- 网点激励结算费用审核列表查询-->
    <select id="queryStimulateInfoAduitList" resultType="com.bonc.rrs.balanceprocess.vo.PublishCompanyExcelProperty">
        select
        distinct
        wci.id worderChildId,
        wci.balance_no balancdNo,
        w.worder_id worderId,
        wci.id id,
        w.worder_no worderNo,
        wps.id stimulateId,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        '记账成功' worderStatusValue,
        '网点激励费用' balanceTypeValue,
        '33' balanceType,
        w.worder_finish_time worderFinishTime,
        wci.dot_balance_fee dotBalanceFee,
        wci.dot_balance_fee_sum dotBalanceFeeSum,
        wci.dot_balance_fee_tax dotBalanceFeetax,
        wps.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (case
        when wci.balance_set_status = 22 then '网点激励记账成功'
        end) operation,
        w.dot_id  dotId,
        '37' publishType,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        from worder_child_information wci
        left join worder_information w on wci.worder_id = w.worder_id
        left join worder_template wt on w.template_id = wt.id
        inner join worder_pm_stimulate wps on wci.stimulate_id = wps.id
        left join worder_type t on t.id = w.worder_type_id
        left join dot_information d on wps.dot_id = d.dot_id
        LEFT JOIN brand b ON b.id = wt.brand_id
        left join company_receivable_record crr on
        crr.invoice_id = w.invoice_id
        left join company_receivable cr on
        wci.receivable_id = cr.id
        left join company_invoice ci on
        w.invoice_id = ci.id
        left join worder_audit_record war  FORCE INDEX(worder_audit_record_applyNo)  on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        where
        1 = 1
        and wci.balance_source = 1
        and wci.balance_set_status = 22 and wps.stimulate_type = '10'
        <if test="dotId != null ">
            AND wps.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]>  DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        ORDER BY w.worder_finish_time ASC
    </select>

    <!-- 网点工单结算费用-新资金 审核列表查询-->
    <select id="queryBalanceAdwanceMoneyWorderInfoAduitList" resultType="com.bonc.rrs.balanceprocess.vo.PublishCompanyExcelProperty">
        select
        wci.id worderChildId,
        wci.balance_no balanceNo,
        w.worder_id worderId,
        wci.id id,
        w.worder_no worderNo,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        wci.balance_set_status_value worderStatusValue,
        '网点工单费用' balanceTypeValue,
        w.worder_finish_time worderFinishTime,
        wci.dot_balance_fee dotBalanceFee,
        wci.dot_balance_fee_sum dotBalanceFeeSum,
        wci.dot_balance_fee_tax dotBalanceFeetax,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        (case
        when wci.balance_set_status = 4 then '首次审核'
        when wci.balance_set_status = 11 then '二次审核'
        when wci.balance_set_status = 12 then '三次审核'
        when wci.balance_set_status = 13 then '发布'
        when wci.balance_set_status = 9 then '提交审核'
        end) operation, w.dot_id dotId,
        '36' publishType
        from
        worder_child_information wci
        inner join
        (
        select
        wci2.id
        from
        worder_child_information wci2
        inner join worder_information wi2 on
        wci2.worder_id = wi2.worder_id
        inner join company_invoice ci2 on
        wi2.invoice_id = ci2.id
        and ci2.cav_state = 2
        and ci2.status in (6, 7)
        inner join
        (
        select
        wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
        from
        worder_child_information wci
        inner join worder_information wi on
        wci.worder_id = wi.worder_id
        inner join company_invoice ci on
        wi.invoice_id = ci.id
        and ci.cav_state = 2
        and ci.status in (6, 7)
        where
        wci.`type` = 1
        and wci.balance_set_status = 4
        and ci.company_invoice_no = #{companyInvoiceNo}
        and ci.cav_state = 2
        and ci.status in (6, 7)
        group by
        wi.dot_id
        ) dot_balance_fee_sum_worder_child on
        wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        inner join
        (
        select
        CAST(sdd.detail_number AS SIGNED) advance_money_min_limit
        from
        sys_dictionary sd
        inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
        where
        sd.dic_number = 'advance_money_min_limit'
        ) dot_min_limit on
        dot_balance_fee_sum_worder_child.dot_balance_fee_sum >= dot_min_limit.advance_money_min_limit
        where
        wci2.`type` = 1
        and wci2.balance_set_status = 4
        and ci2.company_invoice_no = #{companyInvoiceNo}
        and ci2.cav_state = 2
        and ci2.status in (6, 7)
        ) dwci on
        wci.id = dwci.id
        left join worder_information w on
        wci.worder_id = w.worder_id
        left join dot_information di on
        w.dot_id = di.dot_id
        left join worder_type t on
        t.id = w.worder_type_id
        left join worder_template wt on
        w.template_id = wt.id
        left join dot_information d on
        d.dot_id = w.dot_id
        left join brand b on
        b.id = wt.brand_id
        where
        di.is_advance_money = 1
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        order by
        w.worder_finish_time asc
    </select>

    <select id="queryBalanceAdwanceMoneyWorderInfoExport"  resultType="com.bonc.rrs.balanceprocess.vo.PublishCompanyExcelProperty">
        select distinct
        wci.id worderChildId,
        wci.balance_no balanceNo,
        w.worder_id worderId,
        wci.id id,
        w.worder_no worderNo,
        t.full_name fullName,
        d.dot_name dotName,
        d.dot_star dotStar,
        (case
        WHEN wci.balance_set_status = 4 THEN
        '新资金待发布'
        WHEN wci.balance_set_status = 11 THEN
        '新资金发布已提交'
        WHEN wci.balance_set_status = 12 THEN
        '新资金二次审核通过'
        WHEN wci.balance_set_status = 13 THEN
        '新资金三次审核通过'
        WHEN wci.balance_set_status = 14 THEN
        '新资金审核不通过'
        end) worderStatusValue,
        '网点工单费用-新资金' balanceTypeValue,
        w.worder_finish_time worderFinishTime,
        wci.dot_balance_fee dotBalanceFee,
        wci.dot_balance_fee_sum dotBalanceFeeSum,
        wci.dot_balance_fee_tax dotBalanceFeetax,
        w.company_balance_fee_sum companyBalanceFeeSum,
        d.tax_point taxPoint,
        d.dot_state dotState,
        wt.brand_id brandId,
        b.brand_name brandName,
        wt.template_name  templateName,
        (case
        WHEN wci.balance_set_status = 4 THEN
        '新资金待发布'
        WHEN wci.balance_set_status = 11 THEN
        '新资金发布已提交'
        WHEN wci.balance_set_status = 12 THEN
        '新资金二次审核通过'
        WHEN wci.balance_set_status = 13 THEN
        '新资金三次审核通过'
        WHEN wci.balance_set_status = 14 THEN
        '新资金审核不通过'
        end) operation, w.dot_id dotId,
        '36' publishType,
        ci.company_invoice_no companyInvoiceNo,
        war.create_time receivableTime
        from
        worder_child_information wci
        inner join
        (
        select
        wci2.id
        from
        worder_child_information wci2
        inner join worder_information wi2 on
        wci2.worder_id = wi2.worder_id
        inner join company_invoice ci2 on
        wi2.invoice_id = ci2.id
        and ci2.cav_state = 2
        and ci2.status in (6, 7)
        inner join
        (
        select
        wi.dot_id, sum(wci.dot_balance_fee_sum) dot_balance_fee_sum
        from
        worder_child_information wci
        inner join worder_information wi on
        wci.worder_id = wi.worder_id
        inner join company_invoice ci on
        wi.invoice_id = ci.id
        and ci.cav_state = 2
        and ci.status in (6, 7)
        where
        wci.`type` = 1
        and wci.balance_set_status = 4
        group by
        wi.dot_id
        ) dot_balance_fee_sum_worder_child on
        wi2.dot_id = dot_balance_fee_sum_worder_child.dot_id
        inner join
        (
        select
        CAST(sdd.detail_number AS SIGNED) advance_money_min_limit
        from
        sys_dictionary sd
        inner join sys_dictionary_detail sdd on
        sd.id = sdd.dictionary_id
        where
        sd.dic_number = 'advance_money_min_limit'
        ) dot_min_limit on
        dot_balance_fee_sum_worder_child.dot_balance_fee_sum >= dot_min_limit.advance_money_min_limit
        where
        wci2.`type` = 1
        and wci2.balance_set_status = 4
        ) dwci on
        wci.id = dwci.id
        left join worder_information w on
        wci.worder_id = w.worder_id
        left join dot_information di on
        w.dot_id = di.dot_id
        left join worder_type t on
        t.id = w.worder_type_id
        left join worder_template wt on
        w.template_id = wt.id
        left join dot_information d on
        d.dot_id = w.dot_id
        left join brand b on
        b.id = wt.brand_id
        left join company_receivable_record crr on
        crr.invoice_id = w.invoice_id
        left join company_receivable cr on
        wci.receivable_id = cr.id
        left join company_invoice ci on
        w.invoice_id = ci.id
        left join worder_audit_record war  FORCE INDEX(worder_audit_record_applyNo)  on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
        where
        di.is_advance_money = 1
        <if test="dotId != null ">
            AND w.dot_id = #{dotId}
        </if>
        <if test="brandId != null ">
            AND wt.brand_id = #{brandId}
        </if>
        <if test="receivableStartTime != null and receivableStartTime != '' ">
            and war.create_time <![CDATA[>=]]> #{receivableStartTime}
        </if>
        <if test="receivableEndTime != null and receivableEndTime != '' ">
            and war.create_time <![CDATA[<]]>  DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
        </if>
        order by
        w.worder_finish_time asc
    </select>

    <select id="selectCompanyWorders" resultType="com.bonc.rrs.balanceprocess.vo.PublishCompanyExcelProperty">
        select
        distinct
        *
        from
        (
            select
                wci.id worderChildId,
                wci.balance_no balanceNo,
                w.worder_id worderId,
                w.worder_no worderNo,
                null stimulateId,
                t.full_name fullName,
                d.dot_name dotName,
                d.dot_star dotStar,
                w.worder_set_status_value worderStatusValue,
                '网点工单费用' balanceTypeValue,
                w.worder_finish_time worderFinishTime,
                wci.dot_balance_fee dotBalanceFee,
                wci.dot_balance_fee_sum dotBalanceFeeSum,
                wci.dot_balance_fee_tax dotBalanceFeetax,
                w.company_balance_fee_sum companyBalanceFeeSum,
                d.tax_point taxPoint,
                d.dot_id    dotId,
                cr.voucher_no voucherNo,
                wt.template_name  templateName,
                (
                CASE
                WHEN wci.balance_set_status = 4 THEN
                '网点工单已回款待发布'
                WHEN wci.balance_set_status = 11 THEN
                '网点工单发布已提交'
                WHEN wci.balance_set_status = 12 THEN
                '网点工单二次审核通过'
                WHEN wci.balance_set_status = 13 THEN
                '网点工单三次审核通过'
                WHEN wci.balance_set_status = 14 THEN
                '网点工单审核不通过'
                END
                )  operation,
                wt.brand_id brandId,
                ci.company_invoice_no companyInvoiceNo,
                war.create_time receivableTime
            from
                worder_child_information wci
                left join worder_information w on wci.worder_id = w.worder_id
                left join worder_type t on
                t.id = w.worder_type_id
                left join worder_template wt on
                w.template_id = wt.id
                left join dot_information d on
                d.dot_id = w.dot_id
                left join company_receivable cr on
                wci.receivable_id = cr.id
                left join company_receivable_record crr on
                crr.invoice_id = w.invoice_id
                left join company_invoice ci on
                w.invoice_id = ci.id
                left join worder_audit_record war  FORCE INDEX(worder_audit_record_applyNo)  on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
            where
                1 = 1
                and wci.`type` = 0
                and wci.balance_source = 0
                and wci.balance_set_status = 4
                <if test="receivableStartTime != null and receivableStartTime != '' ">
                    and war.create_time <![CDATA[>=]]> #{receivableStartTime}
                </if>
                <if test="receivableEndTime != null and receivableEndTime != '' ">
                    and war.create_time <![CDATA[<]]>  DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
                </if>
                ) a
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="brandId != null">
                and a.brandId = #{brandId}
            </if>
            <if test="dotId != null">
                and a.dotId = #{dotId}
            </if>
        </trim>
        order by
        a.worderFinishTime asc
    </select>

    <select id="selectPublishIncreWorders"
            resultType="com.bonc.rrs.balanceprocess.vo.PublishIncreExcelProperty">
        select
        distinct
            *
        from
            (
                select
                    '' worderChildId,
                    '' balanceNo,
                    w.worder_id worderId,
                    w.worder_no worderNo,
                    null stimulateId,
                    t.full_name fullName,
                    d.dot_name dotName,
                    d.dot_star dotStar,
                    w.worder_Incre_status_value worderStatusValue,
                    '网点增项费用' balanceTypeValue,
                    w.worder_finish_time worderFinishTime,
                    w.dot_incre_balance_fee dotBalanceFee,
                    w.dot_incre_balance_fee_sum dotBalanceFeeSum,
                    w.dot_incre_balance_fee_tax dotBalanceFeetax,
                    w.user_balance_fee_sum   userBalanceFeeSum,
                    w.user_actual_cost       userActualCost,
                    d.tax_point taxPoint,
                    d.dot_id    dotId,
                    wt.template_name  templateName,
                    (
                    case
                    when w.worder_Incre_status = 1 then '增项待结算'
                    when w.worder_Incre_status = 5 then '网点增项首次审核通过'
                    when w.worder_Incre_status = 6 then '网点增项二次审核通过'
                    when w.worder_Incre_status = 7 then '网点增项三次审核通过'
                    when w.worder_Incre_status = 8 then '网点增项审核不通过'
                    end)    operation,
                    wt.brand_id brandId,
                    ci.company_invoice_no companyInvoiceNo,
                    war.create_time receivableTime
                from
                    worder_information w
                        left join worder_template wt on
                        w.template_id = wt.id
                        left join worder_type t on
                        t.id = w.worder_type_id
                        left join dot_information d on
                        d.dot_id = w.dot_id
                        left join company_receivable_record crr on
                        crr.invoice_id = w.invoice_id
                        left join company_receivable cr on
                        cr.company_receivable_no = crr.company_receivable_no
                        left join company_invoice ci on
                        w.invoice_id = ci.id
                        left join worder_audit_record war FORCE INDEX(worder_audit_record_applyNo) on war.apply_no = cr.company_receivable_no and war.audit_type = 2 and war.audit_status = 2
                where
                    1 = 1
                  and w.worder_exec_status = 17
                    and w.worder_Incre_status in (1, 8)
                <if test="receivableStartTime != null and receivableStartTime != '' ">
                    and war.create_time <![CDATA[>=]]> #{receivableStartTime}
                </if>
                <if test="receivableEndTime != null and receivableEndTime != '' ">
                    and war.create_time <![CDATA[<]]>  DATE_ADD(#{receivableEndTime}, INTERVAL 1 DAY)
                </if>
                ) a
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="dotId != null and dotId != '' ">
                a.dotId = #{dotId}
            </if>
            <if test="brandId != null and brandId != '' ">
                AND a.brandId = #{brandId}
            </if>

        </trim>
        order by
            a.worderFinishTime asc
    </select>
    <select id="selectCompanyMaterial"
            resultType="com.bonc.rrs.balanceprocess.vo.PublishMaterialExcelProperty">
        select
            worder_information.worder_id,
            worder_information.worder_no,
            materiel_information.materiel_name,
            worder_used_materiel.materiel_spec,
            worder_used_materiel.num,
            materiel_information.materiel_brand_value
        from
            worder_information
                inner join worder_used_materiel on
                worder_information.worder_id = worder_used_materiel.worder_id
                left join materiel_information on
                worder_used_materiel.materiel_id = materiel_information.id
        where
            worder_information.worder_id in
            <foreach collection="worderIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        order by
            worder_information.worder_id asc,
            worder_used_materiel.id asc
    </select>

    <select id="queryAdvanceCompanyNo"
            resultType="string">
        select
            distinct
            ci.company_invoice_no
        from
            worder_child_information wci
        inner join worder_information w on
            wci.worder_id = w.worder_id
        inner join company_invoice ci on
                w.invoice_id = ci.id
        where
            wci.`type` = 1
            and wci.balance_set_status = 4
            and ci.cav_state = 2
            and ci.status in (6, 7)
            order by w.worder_finish_time asc

    </select>

    <select id="queryPayFormList" resultType="java.util.Map">
        select sdd.detail_number as payFormCode,sdd.detail_name as payFormName from sys_dictionary sd
        left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
        where sd.dic_number  = 'pay_form'
    </select>

    <select id="queryPayBankList" resultType="java.util.Map">
        select sdd.detail_number as payBankCode,sdd.detail_name as payBankName from sys_dictionary sd
        left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
        where sd.dic_number  = 'pay_bank'
    </select>

    <select id="queryOutBankList" resultType="java.util.Map">
        select sdd.detail_number as payBankCode,sdd.detail_name as payBankName from sys_dictionary sd
        left join sys_dictionary_detail sdd on sd.id = sdd.dictionary_id
        where sd.dic_number  = 'out_bank'
    </select>


    <select id="selectPublishByBatchNo"
            resultType="com.bonc.rrs.balanceprocess.vo.BalancePublishExcelProperty">
        select bp.batch_no           batchNo,
               bp.balance_publish_no balancePublishNo,
               di.dot_name           dotName,
               b.brand_name          brandName,
               case
                   bp.publish_type
                   when 33 then '工单结算类型'
                   when 34 then '增项结算类型'
                   end               publishType,
               bp.publish_fee        publishFee,
               case
                   bp.status
                   when 1 then '暂存'
                   when 2 then '提交'
                   when 3 then '第一次审核通过'
                   when 4 then '第二次审核通过'
                   when 5 then '已发布'
                   when -1 then '第一次审核不通过'
                   when -2 then '第二次审核不通过'
                   end               status,
               bp.submit_time        submiTime,
               bcb.bill_no           billNo,
               case
                   bcb.status_flag
                   when '0' then '未确认'
                   when '1' then '已确认'
                   when '2' then '已开票'
                   when 'C' then '已作废'
                   when 'R' then '已入账'
                   when 'G' then '已传BCC付款'
                   when 'P' then '付款完成'
                   end               statusFlag,
               wi.worder_no worderNo,
               wt.full_name          worderTypeName,
               case bp.publish_type
                   when 33 then wi.dot_balance_fee_sum
                   when 34 then wi.dot_incre_balance_fee_sum
                   end               fee,
               case bp.publish_type
                   when 33 then wi.dot_balance_fee
                   when 34 then wi.dot_incre_balance_fee
                   end               noTaxFee,
               case bp.publish_type
                   when 33 then wi.dot_balance_fee_tax
                   when 34 then wi.dot_incre_balance_fee_tax
                   end               tax,
               sdd.detail_name       dotTaxPoint,
               case bp.publish_type
                   when 33 then ci.invoice_code
                   when 34 then i2.invoice_code
                   end               invoiceCode,
               case bp.publish_type
                   when 33 then (select group_concat(cr.voucher_no)
                                 from company_receivable cr
                                 where find_in_set(concat(wi.worder_id), cr.worder_ids) > 0)
                   when 34 then i3.transaction_id
                   end               payNo,
               '' pmId
        from worder_information wi
        		left join worder_child_information wci on
        	wi.worder_id = wci.worder_id
                 left join balance_publish bp on
            find_in_set(concat(wci.id), concat(bp.worder_ids)) or find_in_set(concat(wi.worder_id), concat(bp.incre_ids))
                 left join worder_type wt on
            wi.worder_type_id = wt.id
                 left join dot_information di on
            bp.dot_id = di.dot_id
                 left join brand b on
            bp.brand_id = b.id
                 left join balance_cvp_bill bcb on
            bp.cvp_bill_id = bcb.id
                 left join company_invoice ci on
            ci.id = wi.invoice_id
                 left join worder_invoice_order_items i1 on
            wi.worder_no = i1.order_items_code
                 left join worder_invoice_record i2 on
            i1.invoice_no = i2.invoice_no
                 left join worder_order_log i3 on
            i2.pay_order_no = i3.order_no
                 left join sys_dictionary sd on
            sd.dic_number = 'tax_point'
                 left join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id and sdd.detail_number = di.tax_point
        where bp.batch_no = #{batchNo}

        union

        select bp.batch_no           batchNo,
               bp.balance_publish_no balancePublishNo,
               di.dot_name           dotName,
               b.brand_name          brandName,
               '激励类型'              publishType,
               wps.stimulate_fee        publishFee,
               case
                   bp.status
                   when 1 then '暂存'
                   when 2 then '提交'
                   when 3 then '第一次审核通过'
                   when 4 then '第二次审核通过'
                   when 5 then '已发布'
                   when -1 then '第一次审核不通过'
                   when -2 then '第二次审核不通过'
                   end               status,
               bp.submit_time        submiTime,
               bcb.bill_no           billNo,
               case
                   bcb.status_flag
                   when '0' then '未确认'
                   when '1' then '已确认'
                   when '2' then '已开票'
                   when 'C' then '已作废'
                   when 'R' then '已入账'
                   when 'G' then '已传BCC付款'
                   when 'P' then '付款完成'
                   end               statusFlag,
               wi.worder_no worderNo,
               wt.full_name          worderTypeName,
               wps.balance_fee_tax              fee,
               wps.balance_fee           noTaxFee,
               wps.fee_tax              tax,
               wps.tax_point       dotTaxPoint,
               ''             invoiceCode,
               ''              payNo,
               wps.id pmId
        from  worder_pm_stimulate wps
        		left join worder_child_information wci on
        	wps.id = wci.stimulate_id
                  left  join balance_publish bp on
            find_in_set(concat(wci.id), concat(bp.stimulate_ids))
                  left join worder_information wi  on
            wps.worder_id=wi.worder_id
                  left join worder_type wt on
            wi.worder_type_id = wt.id
                  left join dot_information di on
            bp.dot_id = di.dot_id
                  left join brand b on
            bp.brand_id = b.id
                  left join balance_cvp_bill bcb on
            bp.cvp_bill_id = bcb.id
                  left join company_invoice ci on
            ci.id = wi.invoice_id
                  left join worder_invoice_order_items i1 on
            wi.worder_no = i1.order_items_code
                  left join worder_invoice_record i2 on
            i1.invoice_no = i2.invoice_no
                  left join worder_order_log i3 on
            i2.pay_order_no = i3.order_no
                  left join sys_dictionary sd on
            sd.dic_number = 'tax_point'
                  left join sys_dictionary_detail sdd on
            sd.id = sdd.dictionary_id and sdd.detail_number = di.tax_point
        where bp.batch_no = #{batchNo}
    </select>


    <select id="selectCompanyInvoiceByBatchNo"
            resultType="com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity">
        select
        distinct
            ci.*
        from
            worder_child_information wci
        inner join balance_publish bp on
            wci.publish_id = bp.id
        inner join worder_information wi on
            wci.worder_id = wi.worder_id
        inner join company_invoice ci on
            ci.id = wi.invoice_id
        where
            wi.is_delete = 0
            and bp.batch_no = #{batchNo}
    </select>

    <select id="selectWorderByBatchNo"
            resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select
            distinct
            wi.*
        from
            worder_child_information wci
        inner join balance_publish bp on
            wci.publish_id = bp.id
        inner join worder_information wi on
            wci.worder_id = wi.worder_id
        where
            wi.is_delete = 0
            and bp.batch_no = #{batchNo}
    </select>

    <select id="selectCompanyReceivableByInvoiceId"
            resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableEntity">
        select
            cr.*
        from
            company_receivable cr
        inner join company_receivable_record crr on
            cr.company_receivable_no = crr.company_receivable_no
        where
            crr.invoice_id = #{invoiceId}
        limit 1
    </select>

    <select id="selectOrderCostAmountByInvoiceId"
            resultType="java.math.BigDecimal">
        select
            sum(dot_balance_fee_sum) orderCostAmount
        from
            worder_information wi
        where
            wi.is_delete = 0
            and wi.invoice_id = #{invoiceId}
    </select>

    <select id="selectOrderPayAmountByInvoiceId"
            resultType="java.math.BigDecimal">
        select
            sum(company_balance_fee_sum) orderCostAmount
        from
            worder_information wi
        where
            wi.is_delete = 0
            and wi.invoice_id = #{invoiceId}
    </select>

    <select id="selectSettleAmountTaxExcludedByBatchNo"
            resultType="java.math.BigDecimal">
        select
            sum(a.dot_balance_fee) settleAmountTaxExcluded
        from
            (
            select
                distinct wci.id, wci.worder_id , wci.dot_balance_fee
            from
                worder_child_information wci
            inner join balance_publish bp on
                wci.publish_id = bp.id
            where
                bp.batch_no = #{batchNo}
        ) a
    </select>
    <select id="selectSettleAmountTaxExcludedByPublishId"
            resultType="java.math.BigDecimal">
        select
            sum(a.dot_balance_fee) settleAmountTaxExcluded
        from
            (
            select
                distinct wci.id, wci.worder_id , wci.dot_balance_fee
            from
                worder_child_information wci
            inner join balance_publish bp on
                wci.publish_id = bp.id
            where
                bp.id = #{publishId}
        ) a
    </select>

    <select id="selectCompanyWorderChildInfoByPublishId"
            resultType="com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity">
        select
            wci.*
        from
            worder_child_information wci
        inner join worder_information wi on
            wci.worder_id = wi.worder_id and wi.is_delete = 0
        inner join balance_publish bp on
            wci.publish_id = bp.id
        where
            wi.invoice_id = #{invoiceId}
            and wci.balance_source = 0 and wci.is_delete = 0
            and bp.batch_no = #{batchNo}
    </select>

    <select id="getBank" resultType="com.bonc.rrs.workManager.entity.DotBank">
        select * from dot_bank where is_delete = 0 and dot_id = #{dotId} and dot_bank like CONCAT('%',#{bankAccount},'%') limit 1
    </select>

    <select id="getBankFirst" resultType="com.bonc.rrs.workManager.entity.DotBank">
        select * from dot_bank where is_delete = 0 and dot_id = #{dotId} limit 1
    </select>
</mapper>