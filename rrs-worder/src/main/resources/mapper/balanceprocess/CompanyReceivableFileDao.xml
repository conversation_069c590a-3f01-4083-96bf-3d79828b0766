<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyReceivableFileDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyReceivableFileEntity" id="companyReceivableFileMap">
        <result property="id" column="id" />
        <result property="receivableId" column="receivable_id" />
        <result property="fileId" column="file_id" />
    </resultMap>
    <sql id="selectCompanyReceivableFilecolumn">
        select id, receivable_id, file_id from company_receivable_file
    </sql>

</mapper>