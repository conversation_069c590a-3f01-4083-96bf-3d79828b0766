<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.BalancePublishFileDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.BalancePublishFileEntity" id="balancePublishFileMap">
        <result property="id" column="id" />
        <result property="publishId" column="publish_id" />
        <result property="fileId" column="file_id" />
    </resultMap>
    <sql id="selectBalancePublishFilecolumn">
        select id, publish_id, file_id from balance_publish_file
    </sql>

</mapper>