<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyReceivableNoticeDao">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyReceivableNoticeEntity" id="companyReceivableNoticeMap">
        <result property="id" column="id"/>
        <result property="companyEightCode" column="company_eight_code"/>
        <result property="companyName" column="company_name"/>
        <result property="receivableVoucherNo" column="receivable_voucher_no"/>
        <result property="receivableTime" column="receivable_time"/>
        <result property="receivablePrice" column="receivable_price"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


    <select id="queryByVoucherNoEightCode" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableNoticeEntity">
        select * from company_receivable_notice
        where receivable_voucher_no = #{receivableVoucherNo}
        and company_eight_code = #{companyEightCode}
        and status != 2 and is_delete != 1
    </select>



    <select id="queryStatusByVoucherNoEightCode" resultType="com.bonc.rrs.balanceprocess.entity.CompanyReceivableNoticeEntity">
        select * from company_receivable_notice
        where receivable_voucher_no = #{receivableVoucherNo}
        and company_eight_code = #{companyEightCode}
        and status not in (2,1) and is_delete != 1
        limit 1
    </select>

    <select id="queryReceivablePriceByVoucherNo" resultType="java.math.BigDecimal" parameterType="string">
        select crn.receivable_price from company_receivable_notice crn where is_delete = 0 and crn.status != 2 and crn.receivable_voucher_no = #{voucherNo} limit 1
    </select>
</mapper>