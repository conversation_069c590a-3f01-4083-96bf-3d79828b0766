<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyReceivableStimulateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyReceivableStimulateEntity" id="companyReceivableStimulateMap">
        <result property="id" column="id" />
        <result property="receivableId" column="receivable_id" />
        <result property="stimulateId" column="stimulate_id" />
        <result property="fee" column="fee" />
        <result property="createTime" column="create_time" />
    </resultMap>
    <sql id="selectCompanyReceivableStimulatecolumn">
        select id, receivable_id, stimulate_id, fee, create_time from company_receivable_stimulate
    </sql>

</mapper>