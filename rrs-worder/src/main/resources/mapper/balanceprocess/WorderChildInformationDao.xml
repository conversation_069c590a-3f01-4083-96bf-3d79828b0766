<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.balanceprocess.dao.WorderChildInformationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity">
        <id column="id" property="id"/>
        <result column="worder_id" property="worderId"/>
        <result column="stimulate_id" property="stimulateId"/>
        <result column="balance_no" property="balanceNo"/>
        <result column="balance_type" property="balanceType"/>
        <result column="balance_source" property="balanceSource"/>
        <result column="balance_set_status" property="balanceSetStatus"/>
        <result column="balance_set_status_value" property="balanceSetStatusValue"/>
        <result column="company_balance_fee" property="companyBalanceFee"/>
        <result column="company_balance_fee_tax" property="companyBalanceFeeTax"/>
        <result column="company_balance_fee_sum" property="companyBalanceFeeSum"/>
        <result column="dot_balance_fee" property="dotBalanceFee"/>
        <result column="dot_balance_fee_tax" property="dotBalanceFeeTax"/>
        <result column="dot_balance_fee_sum" property="dotBalanceFeeSum"/>
        <result column="cav_time" property="cavTime"/>
        <result column="publish_time" property="publishTime"/>
        <result column="create_time" property="createTime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , worder_id, stimulate_id, balance_no, balance_type, balance_source, balance_set_status, balance_set_status_value, company_balance_fee, company_balance_fee_tax, company_balance_fee_sum, dot_balance_fee, dot_balance_fee_tax, dot_balance_fee_sum, cav_time, publish_time, create_time, is_delete
    </sql>
    <select id="selectListByInvoiceIds"
            resultType="com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity">
        select
            ci.id as invoice_id,
            wci.*
        from
            company_invoice ci ,
            worder_information wi ,
            worder_child_information wci
        where ci.id = wi.invoice_id
        and wi.worder_id = wci.worder_id
        and ci.id in
        <foreach collection="invoiceIdList" item="invoiceId" open="(" close=")" separator=",">
            #{invoiceId}
        </foreach>
    </select>
    <select id="queryWorderByCompanyId" resultType="com.bonc.rrs.balanceprocess.vo.ReceivableWorder">
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
            and company_invoice.id = #{params.invoiceId}
        </if>
        and (worder_child_information.balance_set_status = 3  or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0

        UNION
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
           and company_invoice.id = #{params.invoiceId}
        </if>
        and (worder_child_information.balance_set_status = 3 or ( (worder_child_information.balance_set_status = 4 or worder_child_information.balance_set_status = 5) and worder_child_information.type = 1))
        and worder_child_information.is_delete = 0
    </select>

    <select id="queryWorderByCompanyId2" resultType="com.bonc.rrs.balanceprocess.vo.ReceivableWorder">
        SELECT
        worder_wait_account.worder_id,
        worder_child_information.id AS balance_worder_id,
        worder_child_information.stimulate_id AS stimulate_id,
        worder_information.worder_no,
        worder_child_information.balance_no,
        worder_child_information.balance_source,
        worder_information.worder_finish_time,
        worder_child_information.company_balance_fee_sum AS companyFee,
        worder_child_information.company_balance_fee AS companyFeeNoTax,
        worder_child_information.dot_balance_fee_sum AS dotFee,
        worder_child_information.dot_balance_fee AS dotFeeNoTax,
        company_invoice.company_id,
        company_information.company_name,
        dot_information.dot_name,
        dot_information.dot_state,
        company_invoice.price_type,
        company_invoice.company_invoice_no
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
            and company_invoice.id = #{params.invoiceId}
        </if>
        and worder_child_information.is_delete = 0
    </select>

    <select id="queryBalanceIds" resultType="java.lang.Integer">
        SELECT
        worder_child_information.id
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
            and company_invoice.id= #{params.invoiceId}
        </if>
        and worder_child_information.balance_set_status = 3

        UNION
        SELECT
        worder_child_information.id
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 1
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.stimulate_id = worder_child_information.stimulate_id AND
        worder_child_information.balance_source = 1
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
            and company_invoice.id= #{params.invoiceId}
        </if>
        and worder_child_information.balance_set_status = 3
    </select>


    <select id="queryBalanceIds2" resultType="java.lang.Integer">
        SELECT
        worder_child_information.id
        FROM
        company_invoice
        INNER JOIN
        worder_wait_account
        ON
        company_invoice.id = worder_wait_account.invoice_id AND
        worder_wait_account.worder_invoice_type = 0
        INNER JOIN
        worder_child_information
        ON
        worder_wait_account.worder_id = worder_child_information.worder_id AND
        worder_child_information.balance_source = 0
        INNER JOIN
        worder_information
        ON
        worder_wait_account.worder_id = worder_information.worder_id
        LEFT JOIN
        company_information
        ON
        company_invoice.company_id = company_information.company_id
        LEFT JOIN
        dot_information
        ON
        worder_information.dot_id = dot_information.dot_id
        WHERE
        worder_child_information.balance_type = #{params.balanceType}
        <if test="params.invoiceId != null">
            and company_invoice.id= #{params.invoiceId}
        </if>
        and worder_child_information.balance_type = 0

    </select>
    <select id="selectWorderIdsByStatus" resultType="java.lang.Integer">

    </select>

    <select id="selectWorderIdsByWorderChildIds" resultType="java.lang.Integer">
        select worder_id from worder_child_information where id in
        <foreach collection="worderChildIds" separator="," open="(" close=")" item="worderChildId">
            #{worderChildId}
        </foreach>
    </select>
    <select id="selectStimulateIdsByWorderChildIds" resultType="java.lang.Integer">
        select stimulate_id from worder_child_information where id in
        <foreach collection="worderChildIds" separator="," open="(" close=")" item="worderChildId">
            #{worderChildId}
        </foreach>
    </select>

    <select id="selectChildWorderCompanyFeeByWorderChildIds" resultType="com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity">
        select balance_no, company_balance_fee, company_balance_fee_sum,dot_balance_fee,dot_balance_fee_sum,worder_id,type,publish_id from worder_child_information where id in
        <foreach collection="worderChildIds" separator="," open="(" close=")" item="worderChildId">
            #{worderChildId}
        </foreach>
    </select>
    <select id="selectFeeByWorderIds" resultType="java.math.BigDecimal">
        select SUM(dot_balance_fee) from worder_child_information where id in
        <foreach collection="worderIds" separator="," open="(" close=")" item="worderId">
            #{worderId}
        </foreach>
        and balance_source = 0
    </select>

    <select id="selectFeeByWorderId" resultType="java.math.BigDecimal">
        select dot_balance_fee from worder_child_information where id = #{worderId} and balance_source = 0
    </select>

    <select id="selectFeeByStimulateIds" resultType="java.math.BigDecimal">
        select SUM(dot_balance_fee_sum) from worder_child_information where id in
        <foreach collection="stimulateIds" separator="," open="(" close=")" item="stimulateId">
            #{stimulateId}
        </foreach>
        and balance_source = 1
    </select>

    <select id="selectFeeByStimulateId" resultType="java.math.BigDecimal">
        select dot_balance_fee_sum from worder_child_information where id = #{stimulateId}
        and balance_source = 1
    </select>

    <select id="getPaidAmount" resultType="java.math.BigDecimal">
        select SUM(wci.dot_balance_fee_sum) from worder_information wi
        inner join worder_child_information wci on wci.worder_id = wi.worder_id and wci.balance_source = 0 and wci.publish_id is not null
        where wi.is_delete = 0 and wci.is_delete = 0 and wi.invoice_id= #{invoiceId}
    </select>

    <update id="emptyReceivableId">
        update worder_child_information set receivable_id = null where receivable_id = #{receivableId}
    </update>

    <update id="updateChildInfoStatus">
        update worder_child_information set balance_set_status = #{balanceSetStatus}, balance_set_status_value = #{balanceSetStatusValue} where id = #{id}
    </update>


    <update id="updateByInvoiceId">
        update worder_child_information wci,worder_information wi
        set wci.balance_set_status = 3,wci.balance_set_status_value = '已开票'
        where wci.worder_id = wi.worder_id
        and wi.is_delete = 0
        and wci.balance_source = 0
        and wi.invoice_id = #{invoiceId}
    </update>
</mapper>
