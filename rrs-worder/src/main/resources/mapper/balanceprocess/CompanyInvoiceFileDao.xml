<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balanceprocess.dao.CompanyInvoiceFileDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.bonc.rrs.balanceprocess.entity.CompanyInvoiceFileEntity" id="companyInvoiceFileMap">
        <result property="id" column="id" />
        <result property="invoiceId" column="invoice_id" />
        <result property="fileId" column="file_id" />
    </resultMap>
    <sql id="selectCompanyInvoiceFilecolumn">
        select id, invoice_id, file_id from company_invoice_file
    </sql>

</mapper>