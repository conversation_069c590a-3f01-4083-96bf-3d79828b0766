<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.FlowWorderDao">
    <insert id="addFlowWorder" parameterType="com.bonc.rrs.worder.entity.FlowWorderEntity">
        INSERT INTO flow_worder(flow_id, worder_id, next_flow_child_code, is_delete)
        VALUES(#{flowId}, #{worderId}, #{nextFlowChildCode}, 0)
    </insert>
</mapper>