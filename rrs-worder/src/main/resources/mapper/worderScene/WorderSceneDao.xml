<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderSceneDao">
    <insert id="addSceneWorder" parameterType="com.bonc.rrs.worder.entity.SceneWorderEntity">
        insert into scene_worder(scene_id, worder_id, is_delete) values (#{sceneId}, #{worderId}, 0)
    </insert>

    <select id="getSceneIdBySceneRules" parameterType="java.util.Map"
            resultType="java.lang.Long">
        select sc.scene_id from (
            select
                count(s.scene_id) num,
                s.scene_id
            from
                scene s
            inner join scene_param sp on
                s.scene_id = sp.scene_id
            inner join scene_param_info spi on
                sp.scene_param_id = spi.query_id
            where
                sp.param_code = 'scene_factor'
                and s.is_delete = 0
                and sp.is_delete = 0
                and spi.is_delete = 0
                and sp.scene_param_id not in (
                    select
                        spp.scene_param_id
                    from
                        scene_param spp
                    inner join scene_param_info spii on
                        spp.scene_param_id = spii.scene_param_id
                    where
                        spp.param_code = 'scene_factor'
                        and spp.is_delete = 0
                        and spii.is_delete = 0
                        and (
                            <trim prefixOverrides="or">
                                <foreach collection="sceneRulesMap" index="key" item="val">
                                    or (spii.type_code = #{key} and spii.type_info_code != #{val})
                                </foreach>
                            </trim>
                        )
                )
                and (
                    <trim prefixOverrides="or">
                        <foreach collection="sceneRulesMap" index="key" item="val">
                            or (spi.type_code = #{key} and spi.type_info_code = #{val})
                        </foreach>
                    </trim>
                )
            group by
                s.scene_id
            order by
                num desc
            limit 1
        ) sc
    </select>

    <select id="getSceneCodeBySceneId" parameterType="java.lang.Long"
            resultType="java.lang.String">
        select s.scene_code
        from scene s
        where
        s.is_delete = 0
        and s.scene_id = #{sceneId}
    </select>

    <select id="findSceneInfoByWorderIds" parameterType="java.util.List"
            resultType="com.bonc.rrs.worder.entity.po.SceneInfoPo">
        select s.scene_id, s.scene_code, s.scene_name, sw.worder_id
        from scene s
        inner join scene_worder sw on s.scene_id = sw.scene_id
        where
        s.is_delete = 0
        and sw.is_delete = 0
        and sw.worder_id in
        <foreach collection="worderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findSceneInfoByWorderId" parameterType="java.lang.Integer"
            resultType="com.bonc.rrs.worder.entity.po.SceneInfoPo">
        select s.scene_id, s.scene_code, s.scene_name, sw.worder_id
        from scene s
        inner join scene_worder sw on s.scene_id = sw.scene_id
        where
        sw.is_delete = 0
        and sw.worder_id = #{worderId}
    </select>

    <select id="findSceneInfoByWorderNo" parameterType="java.lang.String"
            resultType="com.bonc.rrs.worder.entity.po.SceneInfoPo">
        select s.scene_id, s.scene_code, s.scene_name, sw.worder_id
        from scene s
        inner join scene_worder sw on s.scene_id = sw.scene_id
        inner join worder_information w on w.worder_id = sw.worder_id
        where
        sw.is_delete = 0
        and w.is_delete = 0
        and w.worder_no = #{worderNo}
    </select>

    <select id="updateFlowStatus">
        update flow_worder set next_flow_child_code = #{nextFlowCode}
        where is_delete = 0 and worder_id= #{worderId}
    </select>
</mapper>