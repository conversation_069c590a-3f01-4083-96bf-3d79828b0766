<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.vendorbalancemanage.dao.ReceivableDaysDao">

    <select id="queryReceivableDaysList" parameterType="com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO" resultType="map">
        SELECT
        v.id,
        v.days,
        v.vendor_id companyId,
        c.company_name companyBrand
        FROM
        vendor_receivable_days v
        LEFT JOIN company_information c ON v.vendor_id = c.company_id
        WHERE 1 = 1
        <if test="companyId != null and companyId != '' ">
            AND v.vendor_id = #{companyId}
        </if>
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryReceivableDaysTotalCount" parameterType="com.bonc.rrs.vendorbalancemanage.vo.ReceivableDaysVO" resultType="Integer">
        SELECT
        count(v.id)
        FROM
        vendor_receivable_days v
        WHERE 1 = 1
        <if test="companyId != null and companyId != '' ">
            AND v.vendor_id = #{companyId}
        </if>
    </select>

    <select id="companyInfoList" resultType="map">
        SELECT c.company_id companyId, c.company_name companyBrand FROM company_information c where c.is_delete=0 order by convert(c.company_name USING gbk)
    </select>
</mapper>