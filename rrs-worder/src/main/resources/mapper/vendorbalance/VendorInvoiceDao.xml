<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.vendorbalancemanage.dao.VendorInvoiceDao">

    <select id="queryVendorInvoicList" parameterType="com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO" resultType="map">
        SELECT
        v.id,
        v.invoice_code invoiceCode,
        v.invoice_fee invoiceFee,
        v.no_payment_fee noPaymentFee,
        v.no_receivable_fee noReceivableFee,
        v.payment_fee paymentFee,
        v.receivable_fee receivableFee,
        c.company_name companyBrand
        FROM
        vendor_invoice v
        LEFT JOIN company_information c ON v.vendor_id = c.company_id
        WHERE v.status = 2
        <if test="invoiceCode != null and invoiceCode != '' ">
            AND v.invoice_code LIKE CONCAT('%',#{invoiceCode},'%')
        </if>
        <if test="companyId != null and companyId != '' ">
            AND v.vendor_id = #{companyId}
        </if>
        <if test="startTime != null  ">
            AND DATE_FORMAT(v.create_time,'%Y-%m-%d') >= DATE_FORMAT(#{startTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null  ">
            AND DATE_FORMAT(#{endTime},'%Y-%m-%d') >= DATE_FORMAT(v.create_time,'%Y-%m-%d')
        </if>
        ORDER BY v.create_time ASC
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryVendorInvoicTotalCount" parameterType="com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO" resultType="Integer">
        SELECT
        count(v.id)
        FROM
        vendor_invoice v
        LEFT JOIN company_information c ON v.vendor_id = c.company_id
        WHERE invoice_code
        <if test="invoiceCode != null and invoiceCode != '' ">
            AND v.invoice_code LIKE CONCAT('%',#{invoiceCode},'%')
        </if>
        <if test="companyId != null and companyId != '' ">
            AND v.vendor_id = #{companyId}
        </if>
    </select>

    <select id="queryWorderInfoListByInvoiceId" resultType="map">
        SELECT
        i.worder_no worderNo,
        c.company_name companyBrand,
        i.company_balance_fee_sum companyBalanceFee,
        d.dot_name dotName,
        i.worder_set_status_value worderSetValue,
        i.dot_balance_fee_sum dotBalanceFee,
        s.star_name starName,
        i.worder_id id
        FROM
        worder_information i
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = i.worder_no AND e.field_id = t.settle_way+100
        LEFT JOIN company_information c ON c.company_id = e.field_value
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        LEFT JOIN dot_star_design s ON s.id = d.star_id
        WHERE
        i.invoice_id = #{id}
        <if test="page != null and limit != null ">
            limit #{page},#{limit}
        </if>
    </select>

    <select id="queryWorderInfoCountByInvoiceId" resultType="Integer">
        SELECT
        count(i.worder_no)
        FROM
        worder_information i
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_ext_field e ON e.worder_no = i.worder_no AND e.field_id = t.settle_way+100
        LEFT JOIN company_information c ON c.company_id = e.field_value
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        LEFT JOIN dot_star_design s ON s.id = d.star_id
        WHERE
        i.invoice_id = #{id}
    </select>

    <select id="getReceivableInvoiceList" parameterType="com.bonc.rrs.vendorbalancemanage.vo.VendorInvoiceVO" resultMap="invoiceMap">
        SELECT
            v.id,v.invoice_code invoiceCode,v.no_receivable_fee noReceivableFee,c.company_name companyName ,v.create_time createTime
        FROM vendor_invoice v
        LEFT JOIN company_information c ON c.company_id = v.vendor_id
        WHERE v.status = 2
        AND v.no_receivable_fee > 0 AND v.vendor_id = #{companyId}
        ORDER BY v.create_time ASC
    </select>

    <resultMap id="invoiceMap" type="Map">
        <id column="id" property="id" ></id>
        <collection property="worderList" select="getWorderList" javaType="List" ofType="Map" column="{id=id}" >
        </collection>
    </resultMap>

    <select id="getWorderList" resultType="Map">
        SELECT
        w.worder_id worderId, CONCAT('工单号：',w.worder_no) worderNo, w.company_balance_fee companyBalanceFee, b.full_name fullName, w.create_time createTime
        FROM worder_information w
        LEFT JOIN worder_type b ON w.worder_type_id = b.id
        WHERE w.invoice_id = #{id} AND w.worder_set_status = 3
        ORDER BY w.create_time ASC
    </select>

    <update id="updateWorderSetStatusByInvoiceId">
        UPDATE worder_information w SET w.worder_set_status = 4,w.worder_set_status_value = '车企已回款'
        WHERE 1=1
        <if test="typeId == 1  ">
            AND w.invoice_id = #{id};
        </if>
        <if test="typeId == 2  ">
            AND w.worder_id = #{id};
        </if>
    </update>
</mapper>