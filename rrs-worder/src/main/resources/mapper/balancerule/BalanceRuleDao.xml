<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.balancerule.dao.BalanceRuleDao">
    <resultMap id="balanceRuleEntityMap" type="com.bonc.rrs.balancerule.entity.BalanceRuleEntity">
        <id property="id" column="rule_id"/>
        <result property="name" column="name"/>
        <result property="balanceType" column="balance_type"/>
        <result property="balanceTarget" column="balance_target"/>
        <result property="balanceTaxRate" column="balance_tax_rate"/>
        <result property="balanceTargetValue" column="balance_target_value"/>
        <result property="balanceTaxRateValue" column="balance_tax_rate_value"/>
        <result property="balanceTypeValue" column="balance_type_value"/>
        <result property="priceType" column="price_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <collection property="balanceItemList" ofType="com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity">
            <id property="id" column="id"/>
            <result property="ruleId" column="rule_id"/>
            <result property="materielId" column="materiel_id"/>
            <result property="isSuite" column="is_suite"/>
            <result property="startNum" column="start_num"/>
            <result property="endNum" column="end_num"/>
            <result property="price" column="price"/>
            <result property="judgeProject" column="judge_project"/>
            <result property="judgeConditions" column="judge_conditions"/>
            <result property="judgeValue" column="judge_value"/>
            <result property="materielTypeId" column="materiel_type_id"/>
            <result property="materielNo" column="materiel_no"/>
            <result property="materielSpec" column="materiel_spec"/>
            <result property="materielUnitValue" column="materiel_unit_value"/>
            <result property="materielBrandValue" column="materiel_brand_value"/>
            <result property="materielComment" column="materiel_comment"/>
            <result property="materielType" column="materiel_type"/>
            <result property="isSuite" column="is_suite"/>
        </collection>
    </resultMap>
    <select id="queryAllBalanceRuleDetails" resultMap="balanceRuleEntityMap">
        SELECT a.name,a.balance_type,a.balance_target,a.balance_tax_rate,a.balance_type_value,a.balance_target_value,
          a.balance_tax_rate_value,b.id,b.rule_id,b.materiel_id,b.is_suite,b.start_num,b.end_num,b.price,b.judge_project,b.judge_conditions,b.judge_value
        FROM balance_rule a, balance_rule_detail b
        WHERE a.id=b.rule_id
        ORDER BY b.rule_id,b.materiel_id,b.start_num
    </select>
    <select id="queryBatchBalanceRuleDetailsByRuleIds" parameterType="java.util.Collection"
            resultMap="balanceRuleEntityMap">
        SELECT a.name,a.balance_type,a.balance_target,a.balance_tax_rate,a.balance_type_value,a.balance_target_value,
        a.balance_tax_rate_value,a.price_type,b.id,b.rule_id,b.materiel_id,b.is_suite,b.start_num,b.end_num,b.price,b.judge_project,b.judge_conditions,b.judge_value
        FROM balance_rule a, balance_rule_detail b
        WHERE a.id=b.rule_id
        <if test="ruleIds != null and ruleIds.size > 0">
            and a.id in
            <foreach collection="ruleIds" item="ruleId" separator="," open="(" close=")">
                ${ruleId}
            </foreach>
        </if>
        ORDER BY b.rule_id,b.materiel_id,b.start_num
    </select>

    <select id="queryBatchBalanceRuleDetailsByRuleId" parameterType="java.util.Collection" resultMap="balanceRuleEntityMap">
        SELECT a.name,a.balance_type,a.balance_target,a.balance_tax_rate,a.balance_type_value,a.balance_target_value,
        a.balance_tax_rate_value,a.price_type,b.id,b.rule_id,b.materiel_id,b.is_suite,b.start_num,b.end_num,b.price,b.judge_project,b.judge_conditions,b.judge_value
        FROM balance_rule a, balance_rule_detail b
        WHERE a.id=b.rule_id
        and a.id = #{ruleId}
    </select>

    <select id="selectRuleByWorderTemplateId" resultMap="balanceRuleEntityMap">
        select
            br.*
        from
            worder_template wt
                left join balance_rule br on
                wt.company_balance_rule_id = br.id
        where
            wt.is_delete = 0
          and wt.id = #{templateId};
    </select>

    <select id="selectListByWorderIds" resultMap="balanceRuleEntityMap">
        select
            br.*
        from
            worder_information wi ,
            worder_template wt ,
            balance_rule br
        where
            wi.template_id = wt.id
          and wt.company_balance_rule_id = br.id
          and br.balance_target = 0
          <foreach collection="worderIds" item="worderId" open="and wi.worder_id in (" close=")" separator=",">
              #{worderId}
          </foreach>
    </select>
</mapper>