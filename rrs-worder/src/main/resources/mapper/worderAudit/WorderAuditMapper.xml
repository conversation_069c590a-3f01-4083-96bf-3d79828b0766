<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.rrs.worderConvey.dao.WorderAuditMapper">
  <resultMap id="BaseResultMap" type="com.bonc.rrs.worderConvey.pojo.WorderAudit">
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="worder_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="status" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="result" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, worder_no, status, result
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from worder_audit_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from worder_audit_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.bonc.rrs.worderConvey.pojo.WorderAudit">
    insert into worder_audit_log (id, worder_no, status, 
      result)
    values (#{id,jdbcType=BIGINT}, #{worderNo,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective">
    insert into worder_audit_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="worderno != null">
        worder_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="result != null">
        result,
      </if>
      create_time
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="worderno != null">
        #{worderno,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      CURRENT_TIMESTAMP
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bonc.rrs.worderConvey.pojo.WorderAudit">
    update worder_audit_log
    <set>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
    </set>
    where worder_no = #{worderno}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bonc.rrs.worderConvey.pojo.WorderAudit">
    update worder_audit_log
    set worder_no = #{worderNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>