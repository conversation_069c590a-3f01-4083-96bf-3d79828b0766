<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinvoice.dao.WorderWaitAccountDao">

    <select id="listInfoByIds" parameterType="list" resultType="com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity">
        select a.*, d.price_type from worder_wait_account a
        left join worder_information b on a.worder_id = b.worder_id
        left join worder_template c on b.template_id = c.id
        left join balance_rule d on c.company_balance_rule_id = d.id
        <where>
            <foreach collection="list" item="id" open="a.id in(" separator="," close=")" >
                #{id}
            </foreach>
        </where>
    </select>

    <select id="listNotAccount" resultType="com.bonc.rrs.worder.entity.WorderInformationEntity">
        select a.worder_id worderId, a.worder_status worderStatus, a.worder_set_status worderSetStatus
        from worder_information a
        left join worder_wait_account b on a.worder_id = b.worder_id and b.worder_invoice_type = 0
        where b.id is null and a.worder_set_status in (0, 1) AND a.worder_status IN (3,5)
    </select>

    <select id="queryWorderCompanyAccountList" resultType="com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity">
        SELECT
		i.worder_type_id worderTypeId, i.worder_publish_time worderPublishTime,
		i.worder_set_status_value worderSetStatusValue,
        a.id waitAccountId, a.stimulate_id stimulateId, a.worder_invoice_type worderInvoiceType,
        a.company_balance_fee companyBalanceFee,
        a.company_balance_fee_sum companyBalanceFeeSum,
        a.company_balance_fee_tax companyBalanceFeetax,
        a.status companyAccountStatus,a.status_value companyAccountStatusValue,
        i.company_order_number companyOrderNumber,
        i.worder_id worderId,
        i.worder_no worderNo,
        b.name AS worderTypeName,
        c.company_name companyName,
        i.user_name userName,
        d.dot_name dotName,
        i.worder_status_value worderStatusValue,
        DATE_FORMAT(i.worder_finish_time,'%Y-%m-%d %H:%i:%S') worderFinishTime,
        i.dot_balance_fee dotBalanceFee,
        c.company_id companyId
        FROM
        worder_wait_account a
        left join worder_information i on a.worder_id = i.worder_id
        LEFT JOIN worder_template t ON i.template_id = t.id
        LEFT JOIN worder_type b ON i.worder_type_id=b.id
        LEFT JOIN company_information c ON c.company_id = a.company_id
        LEFT JOIN dot_information d ON d.dot_id = i.dot_id
        WHERE a.invoice_id = #{invoiceId}
    </select>

</mapper>