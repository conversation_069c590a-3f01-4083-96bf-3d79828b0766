<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinvoice.dao.WorderBalanceDao">



    <select id="listWorderByInvoiceId" parameterType="java.util.List"
            resultType="com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity">
        select a.template_id, a.worder_id, a.invoice_id, a.worder_no, a.company_balance_fee, a.company_balance_fee_sum, a.company_balance_fee_tax
        from worder_information a
        <where>
            a.invoice_id in
            <foreach collection="list" item="invoiceId" open="(" separator="," close=")">
                #{invoiceId}
            </foreach>
        </where>
    </select>


</mapper>