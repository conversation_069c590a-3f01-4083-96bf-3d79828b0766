<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderinvoice.dao.WorderInvoiceMapper">
    <update id="updateByInvoiceId">
        update worder_invoice_record set
        <if test="dto.customerName != null and dto.customerName != ''">customer_name = #{dto.customerName},</if>
        <if test="dto.customerCode != null and dto.customerCode != ''">customer_code = #{dto.customerCode}</if>

        where invoice_id = #{dto.invoiceId}
    </update>

    <select id="listInvoiceStatusInfoByWorderNo" parameterType="com.bonc.rrs.worderinvoice.entity.query.WorderInvoiceQuery"
            resultType="com.bonc.rrs.worderinvoice.entity.WorderInvoiceEntity">
        select a.order_no orderNo, a.email, b.billing_id billingId, b.serial_no serialNo,
         b.billing_status billingStatus,wi.app_invoice_view_url receiptUrl
        , d.invoice_code invoiceCode, d.customer_code customerCode, d.customer_name customerName
        ,case when b.billing_status = 3 then d.update_time else '' end updateTime
        ,d.invoice_type invoiceType, wi.worder_Incre_status worderIncreStatus
        from worder_billing_order_record a
        left join worder_billing_recode b on a.serial_no = b.serial_no
        left join worder_invoice_order_items c on a.order_no = c.order_items_code
        left join worder_invoice_record d on c.invoice_no = d.invoice_no
        left join worder_information wi on a.order_no = wi.worder_no
        where a.order_no = #{worderNo}
    </select>
    <select id="listIncreInfoByWorderNo" resultType="com.bonc.rrs.pay.entity.entity.IncreDto">
        SELECT wir.invoice_id  invoiceId,wbord.order_no AS worderNo, wir.customer_code AS customerCode, wir.customer_name AS customerName
        FROM worder_invoice_record wir
                 INNER JOIN worder_billing_order_record wbord ON wir.serial_no = wbord.serial_no
        WHERE wbord.order_no in
        <foreach collection="worderNos" item="item"  open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <!--    <select id="getCount" resultType="java.lang.Integer">-->
<!--        select count(1)-->
<!--        from worder_billing_order_record a-->
<!--        left join worder_billing_recode b on a.serial_no = b.serial_no-->
<!--        left join worder_invoice_order_items c on a.order_no = c.order_items_code-->
<!--        left join worder_invoice_record d on c.invoice_no = d.invoice_no-->
<!--        left join worder_information wi on a.order_no = wi.worder_no-->
<!--    </select>-->

</mapper>