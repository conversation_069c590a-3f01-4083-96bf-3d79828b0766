<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worderapp.dao.WorderAppInfoDao">

    <select id="queryWorderInfo" resultType="map">
        SELECT name,gender,contact,
        id_card AS idCard,
        id_card_img as idCardImg,
        id_card_img_back as idCardImgBack,
        id_card_effective_time as idCardEffectiveTime,
        electrician_certificate as electricianCertificate,
        electrician_certificate_time as electricianEffectiveTime,
        work_contract as workContract,
        work_effective_time as workEffectiveTime,
        insurance_contract as insuranceContract,
        insurance_amount as insuranceAmount,
        insurance_effective_time as insuranceEffectiveTime,
        branch_id as branchId
        branch_contract as branchContract
        electrician_certificate_contrary as electricianCertificateContrary
        one_inch_photo=oneInchPhoto
        FROM biz_attendant WHERE id = #{worderId}
    </select>
 <select id="queryWorderBranchInfo" resultType="map">
        SELECT name,license,address,
        contact_name as contactName,
        contact_phone as contactPhone,
        contact_mail as contactMail,
        pm_manager as pmManager,
        address_dup as addressDup,
        manager_id as managerId
        FROM biz_branch WHERE id = #{branchId}
    </select>

    <update id="updateWorderInfo">
        update  biz_attendant set
        <if test="name!=null and name!=''">
              name = #{name}
        </if>
        <if test="gender!=null and gender!=''">
            , gender = #{gender}
        </if>
        <if test="contact!=null and contact!=''">
            , contact = #{contact}
        </if>
        <if test="idCard!=null and idCard!=''">
            ,  id_card = #{idCard}
        </if>
        <if test="idCardImg!=null and idCardImg!=''">
            ,id_card_img = #{idCardImg}
        </if>
        <if test="idCardImgBack!=null and idCardImgBack!=''">
            ,  id_card_img_back = #{idCardImgBack}
        </if>
        <if test="idCardEffectiveTime!=null and idCardEffectiveTime!=''">
            ,  id_card_effective_time = #{idCardEffectiveTime}
        </if>
        <if test="electricianCertificate!=null and electricianCertificate!=''">
            , electrician_certificate = #{electricianCertificate}
        </if>
        <if test="electricianEffectiveTime!=null and electricianEffectiveTime!=''">
            ,  electrician_certificate_time = #{electricianEffectiveTime}
        </if>
        <if test="workContract!=null and workContract!=''">
            ,  work_contract = #{workContract}
        </if>
        <if test="workEffectiveTime!=null and workEffectiveTime!=''">
            ,  work_effective_time = #{workEffectiveTime}
        </if>
        <if test="insuranceContract!=null and insuranceContract!=''">
            ,  insurance_contract = #{insuranceContract}
        </if>
        <if test="insuranceAmount!=null and insuranceAmount!=''">
            ,  insurance_amount = #{insuranceAmount}
        </if>
        <if test="insuranceEffectiveTime!=null and insuranceEffectiveTime!=''">
            ,  insurance_effective_time = #{insuranceEffectiveTime}
        </if>
        <if test="dotId!=null and dotId!=''">
            ,  dot_id = #{dotId}
        </if>
        <if test="branchContract!=null and branchContract!=''">
            ,  branch_contract = #{branchContract}
        </if>
        <if test="electricianCertificateContrary!=null and electricianCertificateContrary!=''">
            ,  electrician_certificate_contrary = #{electricianCertificateContrary}
        </if>
        <if test="oneInchPhoto!=null and oneInchPhoto!=''">
            ,  one_inch_photo = #{oneInchPhoto}
        </if>
        <if test="electricianCertificateNumber!=null and electricianCertificateNumber!=''">
            ,  electrician_certificate_number = #{electricianCertificateNumber}
        </if>
        <if test="electricianEffectiveStart!=null and electricianEffectiveStart!=''">
            ,  electrician_effective_start = #{electricianEffectiveStart}
        </if>
        <if test="electricianEffectiveEnd!=null and electricianEffectiveEnd!=''">
            ,  electrician_effective_end = #{electricianEffectiveEnd}
        </if>
        where contact = #{contact}
    </update>


    <insert id="saveAttendant" parameterType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        insert biz_attendant (name, gender, contact, id_card, dot_id, technician_level, technician_level_value, electrician_certificate
        , electrician_effective_time, id_card_img, id_card_img_back, id_card_effective_time
        , branch_contract, branch_effective_time, insurance_contract, insurance_amount
        , insurance_effective_time, gender_value, user_id
        , work_contract, work_effective_time, region_id, electrician_certificate_contrary, one_inch_photo,create_time,work_license_no,
        electrician_certificate_number, electrician_effective_start, electrician_effective_end
        )
        values (#{name}, #{gender}, #{contact}, #{idCard}, #{dotId}, #{technicianLevel}, #{technicianLevelValue}, #{electricianCertificate}
        , #{electricianEffectiveTime}, #{idCardImg}, #{idCardImgBack}, #{idCardEffectiveTime}
        , #{branchContract}, #{branchEffectiveTime}, #{insuranceContract}, #{insuranceAmount}
        , #{insuranceEffectiveTime}, #{genderValue}, #{userId}
        , #{workContract}, #{workEffectiveTime}, #{regionId}, #{electricianCertificateContrary}, #{oneInchPhoto},#{createTime}, #{workLicenseNo}
        , #{electricianCertificateNumber}, #{electricianEffectiveStart}, #{electricianEffectiveEnd}
        )
    </insert>

    <update id="updateAttendant" parameterType="com.bonc.rrs.worder.entity.BizAttendantEntity">
        update biz_attendant set
        name = #{name}
        , gender = #{gender}
        , contact = #{contact}
        , id_card = #{idCard}
        , dot_id = #{dotId}
        , technician_level = #{technicianLevel}
        , technician_level_value = #{technicianLevelValue}
        , electrician_certificate = #{electricianCertificate}
        , electrician_effective_time = #{electricianEffectiveTime}
        , id_card_img = #{idCardImg}
        , id_card_img_back = #{idCardImgBack}
        , id_card_effective_time = #{idCardEffectiveTime}
        , branch_contract = #{branchContract}
        , branch_effective_time = #{branchEffectiveTime}
        , insurance_contract = #{insuranceContract}
        , insurance_amount = #{insuranceAmount}
        , insurance_effective_time = #{insuranceEffectiveTime}
        , gender_value = #{genderValue}
        , user_id = #{userId}
        , work_contract = #{workContract}
        , work_effective_time = #{workEffectiveTime}
        , region_id = #{regionId}
        , electrician_certificate_contrary = #{electricianCertificateContrary}
        , one_inch_photo = #{oneInchPhoto}
        , update_time = now()
        , attendant_state = #{attendantState}
        , electrician_certificate_number = #{electricianCertificateNumber}
        , electrician_effective_start = #{electricianEffectiveStart}
        , electrician_effective_end = #{electricianEffectiveEnd}
        where id = #{id}
    </update>

    <select id="selectByAddress" resultType="List">
        select * from biz_branch where address_dup like CONCAT('%',CONCAT(#{address},'%'));
    </select>

</mapper>