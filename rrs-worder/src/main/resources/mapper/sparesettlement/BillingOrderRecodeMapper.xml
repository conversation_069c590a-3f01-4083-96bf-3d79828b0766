<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.sparesettlement.dao.BillingOrderRecodeMapper">

    <resultMap id="billingOrderRecordDTOResult" type="com.bonc.rrs.sparesettlement.model.entity.BillingOrderRecordDTO">
        <result column="order_id" property="orderId"/>
        <result column="serial_no" property="serialNo" />
        <result column="order_no" property="orderNo" />
        <result column="account" property="account" />
        <result column="tel" property="tel" />
        <result column="email" property="email" />
    </resultMap>
    
    <sql id="billingOrderRecodeSql">
            t.order_id,t.serial_no,t.order_no,t.account,t.tel,t.email
    </sql>
    <select id="findBillingOrderRecordByserialNo" resultMap="billingOrderRecordDTOResult">
        select
        <include refid="billingOrderRecodeSql"/>
        from  worder_billing_order_record t
        where t.serial_no = #{serialNo}
    </select>

    <insert id="inserBillingOrderRecord" parameterType="com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO">
        insert into worder_billing_order_record (
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">serial_no,</if>
                <if test="orderNo != null and orderNo != ''">order_no,</if>
                <if test="account !=null and account != ''">account,</if>
                <if test="tel != null ">tel,</if>
                <if test=" email != null">email,</if>
            </trim>
        )
        values(
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">#{serialNo},</if>
                <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
                <if test="account !=null and account != ''">#{account},</if>
                <if test="tel != null ">#{tel},</if>
                <if test=" email != null">#{email},</if>
            </trim>
        )
    </insert>
    <update id="updateEmailInfo">
        update worder_billing_order_record set email =#{email} where serial_no =#{serialNo}
    </update>
    <select id="findBillingOrderRecordByoOrderNo" resultMap="billingOrderRecordDTOResult">
        select
        <include refid="billingOrderRecodeSql"/>
        from  worder_billing_order_record t
        where t.order_no = #{orderNo}
    </select>
</mapper>