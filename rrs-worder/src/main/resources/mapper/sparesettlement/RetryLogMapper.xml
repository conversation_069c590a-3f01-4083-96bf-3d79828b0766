<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.sparesettlement.dao.RetryLlogMapper">

    <resultMap id="invoiceRecodeDTOResult" type="com.bonc.rrs.sparesettlement.model.entity.RetryLogEntity">
        <result column="retry_log_id" property="retryLogId" />
        <result column="retry_type" property="retryType" />
        <result column="oper_result_code" property="operResultCode" />
        <result column="oper_result_msg" property="operResultMsg" />
        <result column="oper_ip" property="operIp" />
        <result column="create_time" property="createTime" />
        <result column="enable" property="enable" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="uniquely_identify" property="uniquelyIdentify" />
    </resultMap>
    
    <sql id="invoiceRecodeSql">
            t.retry_log_id,t.retry_type,t.oper_result_code,t.oper_result_msg,t.oper_ip,t.create_time,t.enable,t.remark
                    ,t.create_time,t.uniquely_identify
    </sql>
    <update id="updateRetryLog">
        update retry_log set
    </update>
    <select id="findRetryLogList">
        select
            <include refid="invoiceRecodeSql"/>
         from retry_log t
            <where>
               <if  test="retryType!=null and retryType!=''"> t.retry_type = #{retryType}  </if>
               <if  test="operResultCode!=null and retryType!=''"> t.oper_result_code = #{operResultCode}  </if>
            </where>
    </select>
    <insert id="insertIntoRetryLog" parameterType="com.bonc.rrs.sparesettlement.model.entity.RetryLogEntity" >
        insert  into retry_log (
            <if test="retryType!=null and retryType != ''">retry_type,</if>
            <if test="operResultCode != null and operResultCode != ''">oper_result_code,</if>
            <if test="operResultCode != null and operResultCode != ''">oper_result_code,</if>
            <if test="operResultMsg != null and operResultMsg != ''">oper_result_msg,</if>
            <if test="operIp != null and operIp != ''">oper_ip,</if>
            create_time,
            enable,
            <if test="remark != null and enable != ''">remark,</if>
            <if test="uniquelyIdentify != null and uniquelyIdentify != ''">uniquely_identify,</if>

        )
        values (
            <if test="retryType!=null ">#{retryType},</if>
            <if test="operResultCode != null ">#{operResultCode},</if>
            <if test="operResultCode != null and operResultCode != ''">#{operResultCode},</if>
            <if test="operResultMsg != null and operResultMsg != ''">#{operResultMsg},</if>
            <if test="operIp != null and operIp != ''">#{operIp},</if>
            now(),
            #{enable},
            <if test="remark != null and enable != ''">#{remark},</if>
            <if test="uniquelyIdentify != null and uniquelyIdentify != ''">#{uniquelyIdentify},</if>
        )
    </insert>
</mapper>