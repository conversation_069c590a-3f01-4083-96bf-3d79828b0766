<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.sparesettlement.dao.InvoiceRecodeMapper">

    <resultMap id="invoiceRecodeDTOResult" type="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        <result column="invoice_no" property="invoiceNo" />
        <result column="invoice_amount" property="invoiceAmount" />
        <result column="drawer" property="drawer" />
        <result column="invoice_type" property="invoiceType" />
        <result column="invoice_company_name" property="invoiceCompanyName" />
        <result column="taxpayer_code" property="taxpayerCode" />
        <result column="company_addr" property="companyAddr" />
        <result column="invoice_status" property="invoiceStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>
    
    <sql id="invoiceRecodeSql">
            t.invoice_id,t.invoice_no,t.invoice_amount,t.drawer,t.invoice_type,t.invoice_company_name,t.taxpayer_code,t.company_addr,t.invoice_status
                    ,t.create_time,t.update_time,t.remark
    </sql>

    <select id="findInvoiceRecodeByInvoiceRecodeDTO" resultMap="invoiceRecodeDTOResult" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        select
            <include refid="invoiceRecodeSql"/>
        from worder_invoice_record t
        <where>
            <if test="invoiceNo != null and invoiceNo != '' ">t.invoice_no = #{invoiceNo}</if>
            <if test="drawer != null and drawer != '' ">t.drawer = #{drawer}</if>
            <if test="invoiceCompanyName != null and invoiceCompanyName != '' ">t.invoice_company_name = #{invoiceCompanyName}</if>
            <if test="invoiceStatus != null">t.invoice_status = #{invoiceStatus}</if>
        </where>
    </select>

    <insert id="insertInvoiceRecode" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        insert into worder_invoice_record (
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">serial_no,</if>
                <if test="invoiceNo != null and invoiceNo != '' ">invoice_no,</if>
                <if test="invoiceAmount != null ">invoice_amount,</if>
                <if test="drawer !=null and drawer != ''">drawer,</if>
                <if test="invoiceType != null ">invoice_type,</if>
                <if test="invoiceCompanyName != null and invoiceCompanyName != ''">invoice_company_name,</if>
                <if test="taxpayerCode != null and taxpayerCode != ''">taxpayer_code,</if>
                <if test="companyAddr != null and companyAddr != '' ">company_addr,</if>
                <if test="invoiceStatus !=null ">invoice_status,</if>
                <if test="payType !=null ">pay_type,</if>
                <if test="payOrderNo !=null ">pay_order_no,</if>
                <if test="payTypeCode !=null ">pay_type_code,</if>
                create_time,
                update_time,
                <if test="remark != null and remark != '' ">remark,</if>
            </trim>
        )
        values(
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">#{serialNo},</if>
                <if test="invoiceNo != null and invoiceNo != '' ">#{invoiceNo},</if>
                <if test="invoiceAmount != null ">#{invoiceAmount},</if>
                <if test="drawer !=null and drawer != ''">#{drawer},</if>
                <if test="invoiceType != null ">#{invoiceType},</if>
                <if test="invoiceCompanyName != null and invoiceCompanyName != ''">#{invoiceCompanyName},</if>
                <if test="taxpayerCode != null and taxpayerCode != ''">#{taxpayerCode},</if>
                <if test="companyAddr != null and companyAddr != '' ">#{companyAddr},</if>
                <if test="invoiceStatus !=null ">#{invoiceStatus},</if>
                <if test="payType !=null ">#{payType},</if>
                <if test="payOrderNo !=null ">#{payOrderNo},</if>
                <if test="payTypeCode !=null ">#{payTypeCode},</if>
                now(),
                now(),
                <if test="remark != null and remark != '' ">#{remark},</if>
            </trim>
        )
    </insert>

    <insert id="insertMoreInvoiceRecode" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        insert into worder_invoice_record (
        <trim suffixOverrides=",">
            <if test="invoiceNo != null and invoiceNo != '' ">invoice_no,</if>
            <if test="invoiceAmount != null ">invoice_amount,</if>
            <if test="drawer !=null and drawer != ''">drawer,</if>
            <if test="invoiceType != null ">invoice_type,</if>
            <if test="invoiceCompanyName != null and invoiceCompanyName != 0">invoice_company_name,</if>
            <if test="taxpayerCode != null and taxpayerCode != ''">taxpayer_code,</if>
            <if test="companyAddr != null and companyAddr != '' ">company_addr,</if>
            <if test="invoiceStatus !=null ">invoice_status,</if>
            create_time,
            update_time,
            <if test="remark != null and remark != '' ">remark,</if>
        </trim>
        )
        values
        <foreach collection="invoiceRecodeDTOS" item="item" open="(" separator="," close=")">
            <if test="invoiceNo != null and invoiceNo != '' ">#{item.invoiceNo},</if>
            <if test="invoiceAmount != null ">#{item.invoiceAmount},</if>
            <if test="drawer !=null and drawer != ''">#{item.drawer},</if>
            <if test="invoiceType != null ">#{item.invoiceType},</if>
            <if test="invoiceCompanyName != null and invoiceCompanyName != 0">#{item.invoiceCompanyName},</if>
            <if test="taxpayerCode != null and taxpayerCode != ''">#{item.taxpayerCode},</if>
            <if test="companyAddr != null and companyAddr != '' ">#{item.companyAddr},</if>
            <if test="invoiceStatus !=null ">#{item.invoiceStatus},</if>
            now(),
            now(),
            <if test="remark != null and remark != '' ">#{item.remark},</if>
        </foreach>
    </insert>

    <update id="updateInvoiceRecodeStatus" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        update worder_invoice_record set invoice_status = #{invoiceStatus},update_time = now()
                <if test="remark != null">#{remark}</if>
                where invoice_no = #{invoiceNo}
    </update>

    <select id="findInvoiceRecodeBySerialNo" resultMap="invoiceRecodeDTOResult" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        select
        <include refid="invoiceRecodeSql"/>
        from worder_invoice_record t
        where t.serial_no = #{serialNo}
    </select>

    <update id="updateInvoiceRecord" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceDataUpdateDTO">
        update worder_invoice_record
            set invoice_type = #{invoiceType}
                    ,invoice_status=#{invoiceStatus}
        <if test="customerCode!=null and customerCode!=''">,customer_code = #{customerCode}</if>
        <if test="customerName!=null and customerName!=''">,customer_name = #{customerName}</if>
        ,update_time = now()
        where serial_no = #{serialNo}
    </update>



    <update id="updateInvoiceRecordBySerialNo" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceDataUpdateDTO">
        update worder_invoice_record
        set finance_invoice_type = #{financeInvoiceType}
        ,update_time = now()
        where serial_no = #{serialNo}
    </update>


    <update id="updateInvoiceRecordByRespData" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
        update worder_invoice_record set invoice_code = #{invoiceCode},update_time = now()
                where serial_no = #{serialNo}
    </update>

    <resultMap id="invoiceDTOResult" type="com.bonc.rrs.sparesettlement.model.entity.InvoiceDTO">
        <result column="invoice_id" property="invoiceId" />
        <result column="taxpayer_code" property="taxpayerCode" />
        <result column="customer_name" property="customerName" />
        <result column="customer_code" property="customerCode" />
        <result column="drawer" property="drawer" />
        <result column="invoice_amount" property="totalAmount" />
        <result column="invoice_type" property="invoiceType" />
        <result column="finance_invoice_type" property="financeInvoiceType" />
        <collection property="items" ofType="com.bonc.rrs.sparesettlement.model.entity.ItemsDTO">
            <result column="order_items_type" property="type"/>
            <result column="order_items_code" property="code"/>
            <result column="order_items_name" property="name"/>
            <result column="order_items_tax_rate" property="taxRate"/>
            <result column="order_items_amount" property="amount"/>
            <result column="order_items_catalog_code" property="catalogCode"/>
        </collection>
    </resultMap>
    <sql id="invoiceSql">
           t.invoice_id,t.taxpayer_code,t.customer_name,t.customer_code,t.drawer,t.invoice_amount,t1.order_items_type,t1.order_items_code
            ,t1.order_items_name,t1.order_items_tax_rate,t1.order_items_amount,t1.order_items_catalog_code,t.invoice_type,t.finance_invoice_type
    </sql>
    <select id="findInvoiceRecodeWithItemsBySerialNo" resultMap="invoiceDTOResult">
        select
            <include refid="invoiceSql"/>
        from worder_invoice_record t
        inner join worder_invoice_order_items t1 on  t.invoice_no = t1.invoice_no
        where t.serial_no = #{serialNo}
    </select>
    <insert id="insertInvoceResultLog" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoceResultLog">
        insert into invoce_result_log
            (order_no,serial_no,result_msg,create_time)
            values
            (#{orderNo},#{serialNo},#{resultMsg},now())
    </insert>
</mapper>