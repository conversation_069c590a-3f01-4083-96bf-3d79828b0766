<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.sparesettlement.dao.InvoiceOrderItemsMapper">

    <resultMap id="invoiceOrderItemsResult" type="com.bonc.rrs.sparesettlement.model.entity.InvoiceOrderItemsDTO">
        <result column="order_items_code" property="orderItemsCode" />
        <result column="order_items_name" property="orderItemsName" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="order_items_amount" property="orderItemsAmount" />
        <result column="order_items_tax_rate" property="orderItemsTaxRate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>
    
    <sql id="invoiceOrderItemsSql">
            t.order_items_code,t.order_items_name,t.invoice_no,t.order_items_amount,t.order_items_tax_rate,t.create_time,t.update_time,t.remark
    </sql>
    <select id="findInvoiceOrderItemsList" resultMap="invoiceOrderItemsResult" parameterType="com.bonc.rrs.sparesettlement.model.entity.InvoiceOrderItemsDTO">
        select
            <include refid="invoiceOrderItemsSql"/>
        from worder_invoice_order_items t
        <where>
            <if test="orderItemsCode != null and orderItemsCode != '' ">t.order_items_code = #{orderItemsCode}</if>
            <if test="drawer != null and drawer != '' ">t.drawer = #{drawer}</if>
            <if test="orderItemsName != null and orderItemsName != '' ">t.order_items_name = #{orderItemsName}</if>
            <if test="invoiceNo != null">t.invoice_no = #{invoiceNo}</if>
        </where>
    </select>

    <sql id="itemsSql">
            t.order_items_type,t.order_items_code,t.order_items_name,t.order_items_amount,t.order_items_tax_rate,t.order_items_catalog_code
    </sql>
    <resultMap id="itemsResult" type="com.bonc.rrs.sparesettlement.model.entity.ItemsDTO">
        <result column="order_items_type" property="type"></result>
        <result column="order_items_code" property="code"></result>
        <result column="order_items_name" property="name"></result>
        <result column="order_items_tax_rate" property="taxRate"></result>
        <result column="order_items_amount" property="amount"></result>
        <result column="order_items_catalog_code" property="catalogCode"></result>
        <result column="price" property="price"></result>
        <result column="quantity" property="quantity"></result>
    </resultMap>
    <select id="findInvoiceOrderItemsListByInvoiceNo" resultMap="itemsResult" >
        select
        <include refid="itemsSql"/>
        from worder_invoice_order_items t
        where   t.invoice_no = #{invoiceNo}
    </select>
    <insert id="insertInvoiceOrderItems">
        insert into worder_invoice_order_items(
            order_items_code,
            order_items_name,
            invoice_no,
            order_items_amount,
            order_items_tax_rate,
            create_time,
            update_time,
            area_name,
            pay_type,
            pay_order_no,
            order_items_catalog_code
        )
        values
            <foreach collection="invoiceOrderItems" item="item" open="(" separator="," close=")">
                #{item.orderItemsCode},
                #{item.orderItemsName},
                #{item.invoiceNo},
                #{item.orderItemsAmount},
                #{item.orderItemsTaxRate},
                now(),
                now(),
                #{item.areaName},
                #{item.payType},
                #{item.payOrderNo},
                #{item.orderItemsCatalogCode}
            </foreach>
    </insert>
    <select id="findHadInvoice" resultType="java.lang.String">
        select t.order_items_code
                from worder_invoice_order_items t
                    inner join
                    worder_invoice_recode t1 on t.invoice_no = t1.invoice_no
            where (t1.invoice_status=1 or t1.invoice_status = 2)
                     and t.order_items_code in
            <foreach collection="orderItemsCodes" item="orderItemsCode" open="(" separator="," close=")">
                #{orderItemsCode}
            </foreach>
    </select>
    <select id="findInvoices" resultType="java.lang.String">
        select t.order_items_code from
            worder_invoice_order_items t
            inner join
            worder_invoice_record t1 on t.invoice_no = t1.invoice_no
            where t1.serial_no = #{serialNo}
    </select>

    <select id="findSerialNoByWorderNo" resultType="java.lang.String">
        select serial_no from worder_billing_order_record where order_no = #{orderNo} limit 1;
    </select>

    <select id="findApplyInvoice" resultType="java.util.HashMap"  >
        select  wbor.*,wir.invoice_amount  from worder_invoice_order_items wioi,
                   worder_invoice_record wir,
                   worder_billing_order_record wbor,
                   worder_information wi,
                   worder_template wt
                   where wioi.invoice_no=wir.invoice_no and wir.serial_no =wbor.serial_no
                    and  wi.worder_no=wbor.order_no
                    and  wt.id =wi.template_id
                    and  wt.brand_id in (34,99)
                   and  wbor.order_no =#{orderNo}
                    <if test="p.createBy!=null ">
                       and  wi.create_by = #{p.createBy}
                    </if>
                    <if test="p.pmId!=null ">
                        and  wi.pm_id = #{p.pmId}
                    </if>
                    <if test="p.dotId!=null ">
                        and wi.dot_id = #{p.dotId}
                    </if>
                    <if test="p.serviceId!=null ">
                        and wi.service_id = #{p.serviceId}
                    </if>
                   limit 1
    </select>
</mapper>