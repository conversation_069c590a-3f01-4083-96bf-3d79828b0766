<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper">

    <resultMap id="billingRecordDTOResult" type="com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO">
        <result column="serial_no" property="serialNo" />
        <result column="post_time" property="postTime" />
        <result column="billing_status" property="billingStatus" />
        <result column="oper_user_id" property="operUserId" />
        <result column="charge_nums" property="chargeNums" />
    </resultMap>
    <resultMap type="com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO" id="recodeMap">
        <id column="worder_id" property="worderId"/>
        <result column="worder_type_id" property="worderTypeId"/>
        <result column="worder_no" property="worderNo"/>
        <result column="worder_source_type" property="worderSourceType"/>
        <result column="worder_source_id" property="worderSourceId"/>
        <result column="worder_status" property="worderStatus"/>
        <result column="worder_set_status_value" property="worderSetStatusValue"/>
        <result column="worder_Incre_status_value" property="worderIncreStatusValue"/>
        <result column="worder_exci_status_value" property="worderExciStatusValue"/>
        <result column="pm_id" property="pmId"/>
        <result column="dot_id" property="dotId"/>
        <result column="service_id" property="serviceId"/>


        <collection property="invoiceRecode" ofType="com.bonc.rrs.sparesettlement.model.entity.InvoiceRecodeDTO">
            <id property="invoiceId" column="invoice_id"/>
            <result property="serialNo" column="serial_no"/>
            <result property="invoiceNo" column="invoice_no"/>
            <result property="customerName" column="customer_name"/>
            <result property="taxpayerCode" column="customer_code"/>
            <result property="invoiceType" column="invoice_type"/>
        </collection>

        <collection property="billingOrderRecord" ofType="com.bonc.rrs.sparesettlement.model.entity.BillingOrderRecordDTO">
            <id  property="orderId" column="order_id" />
            <result property="serialNo" column="serial_no"/>
            <result property="orderNo" column="order_no"/>
            <result property="account" column="account"/>
            <result property="tel" column="tel"/>
            <result property="email" column="email"/>
        </collection>

    </resultMap>
    
    <sql id="billingRecodeSql">
            t.serial_no,t.post_time,t.billing_status,t.oper_user_id,charge_nums
    </sql>
    <select id="findBillingRecord" resultMap="billingRecordDTOResult">
        select
        <include refid="billingRecodeSql"/>
        from  worder_billing_recode t
        where t.billing_id = #{billingId}
    </select>

    <insert id="insertBillingRecodeInfo" parameterType="com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO">
        insert into worder_billing_recode (
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">serial_no,</if>
                <if test="postTime != null ">post_time,</if>
                <if test="billingStatus != null ">billing_status,</if>
                <if test="operUserId != null">oper_user_id,</if>
                create_time,
                update_time,
            </trim>
        )
        values(
            <trim suffixOverrides=",">
                <if test="serialNo != null and serialNo != '' ">#{serialNo},</if>
                <if test="postTime != null ">#{postTime},</if>
                <if test="billingStatus != null ">#{billingStatus},</if>
                <if test=" operUserId != null">#{operUserId},</if>
                now(),
                now(),
            </trim>
        )
    </insert>
    <select id="selectHistoryInfo" resultMap="recodeMap">
        select b.account,b.email,b.tel,i.customer_code,i.customer_name,i.invoice_type from worder_information w
        LEFT JOIN worder_billing_order_record b on w.worder_no=b.order_no
        LEFT JOIN worder_invoice_record i on b.serial_no = i.serial_no
        where worder_no=#{worderNo}
    </select>
    <update id="updateBillingRecodeInfo">
        update worder_billing_recode set billing_status = #{billingStatus},update_time=now()
        <if test="postTime!=null and postTime!=''">
            ,post_time = #{postTime}
        </if>
        where serial_no = #{serialNo}
    </update>

    <update id="updateBillingRecodeChargeStatus">
        update worder_billing_recode
            set charge_nums = charge_nums + #{chargeNums} ,update_time=now()
        where serial_no = #{serialNo}
    </update>
    <select id="findBillingRecordBySerialNo" resultMap="billingRecordDTOResult">
        select
        <include refid="billingRecodeSql"/>
        from  worder_billing_recode t
        where t.serial_no = #{serialNo}
    </select>


    <select id="getBillingRecordByWorderNo" resultMap="billingRecordDTOResult">
        select
        <include refid="billingRecodeSql"/>
        from worder_billing_order_record a
        left join worder_billing_recode t on a.serial_no = t.serial_no
        where a.order_no = #{worderNo}
        order by a.order_id desc
    </select>
    <select id="findSerialNoByWorderNo" resultType="java.lang.String">
        select t.serial_no
                from  worder_billing_recode t
                    inner join worder_billing_order_record t1 on t.serial_no = t1.serial_no
                where t.billing_status = #{billingStatus}
                <if test="orderNos!=null and orderNos.size()>0">
                    and  t1.order_no in
                    <foreach collection="orderNos" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
    </select>
</mapper>