<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.signlocation.dao.SignLocationDao">
    <select id="getCount" resultType="java.lang.Integer" >
        select count(*) from soldier_sign_location sl where sl.worder_id = #{worderId}
    </select>

    <insert id="savaSignLocation" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity">
        insert into soldier_sign_location (worder_id, soldier_id, location_x, location_y
        , user_x, user_y,create_time,distance,sign_type)
        values (#{worderId}, #{soldierId}, #{locationX}, #{locationY}, #{userX},#{userY},now(),#{distance},#{signType})
    </insert>
    <update id="updateSignLocation" parameterType="com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity">
        update soldier_sign_location sl
        <set>
            <if test="locationX!=null  and locationX!=''  ">
                sl.location_x=${locationX},
            </if>
            <if test="locationY!=null and locationY!='' ">
                sl.location_y=${locationY},
            </if>
            <if test="userX!=null and userX!='' " >
                sl.user_x=${userX},
            </if>
            <if test="userY!=null and userY!='' ">
                sl.user_y=${userY},
            </if>
            <if test="distance!=null and distance!='' ">
                sl.distance=${distance},
            </if>
            sl.update_time=now(),
        </set>
        where 1=1
        <if test="worderId!=null and worderId!='' ">
            and sl.worder_id=${worderId}
        </if>
    </update>

    <select id="getSignLocation" resultType="com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity" parameterType="java.lang.Integer">

        select * from soldier_sign_location ss where ss.worder_id=${worderId}  and update_time=(select   max(update_time)  from soldier_sign_location ss  where ss.worder_id =${worderId} limit 1) limit 1
    </select>
</mapper>