<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="InsertXwsqZssZF" targetNamespace="http://www.example.org/InsertXwsqZssZF/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://www.example.org/InsertXwsqZssZF/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema targetNamespace="http://www.example.org/InsertXwsqZssZF/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/InsertXwsqZssZF/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:element name="InsertXwsqZssZF">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="invoicecode" type="xsd:string"/>
            <xsd:element name="type" type="xsd:string"/>
            <xsd:element name="tag1" type="xsd:string"/>
            <xsd:element name="tag2" type="xsd:string"/>
            <xsd:element name="xrrq" type="xsd:dateTime"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="InsertXwsqZssZFResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="FLAG" type="xsd:string"/>
            <xsd:element name="MESSAGE" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="InsertXwsqZssZFRequest">
    <WL5G3N0:part element="WL5G3N1:InsertXwsqZssZF" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="InsertXwsqZssZFResponse">
    <WL5G3N0:part element="WL5G3N1:InsertXwsqZssZFResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="InsertXwsqZssZF">
    <WL5G3N0:operation name="InsertXwsqZssZF">
      <WL5G3N0:input message="WL5G3N1:InsertXwsqZssZFRequest"/>
      <WL5G3N0:output message="WL5G3N1:InsertXwsqZssZFResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="InsertXwsqZssZFSOAP" type="WL5G3N1:InsertXwsqZssZF">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="InsertXwsqZssZF">
      <WL5G3N2:operation soapAction="http://www.example.org/InsertXwsqZssZF/InsertXwsqZssZF"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="InsertXwsqZssZFSOAPQSService">
    <WL5G3N0:port binding="WL5G3N1:InsertXwsqZssZFSOAP" name="InsertXwsqZssZFSOAPQSPort">
      <WL5G3N2:address location="http://************:9001/EAI/RoutingProxyService/EAI_SOAP_ServiceRoot?INT_CODE=EAI_INT_2181"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>