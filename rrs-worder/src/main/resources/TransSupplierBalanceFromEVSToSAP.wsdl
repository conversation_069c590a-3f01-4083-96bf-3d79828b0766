<?xml version="1.0" encoding="UTF-8"?>
<WL5G3N0:definitions name="TransSupplierBalanceFromEVSToSAP" targetNamespace="http://www.example.org/TransSupplierBalanceFromEVSToSAP/" xmlns:WL5G3N0="http://schemas.xmlsoap.org/wsdl/" xmlns:WL5G3N1="http://www.example.org/TransSupplierBalanceFromEVSToSAP/" xmlns:WL5G3N2="http://schemas.xmlsoap.org/wsdl/soap/">
  <WL5G3N0:types>
    <xsd:schema targetNamespace="http://www.example.org/TransSupplierBalanceFromEVSToSAP/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.example.org/TransSupplierBalanceFromEVSToSAP/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
      <xsd:element name="TransSupplierBalanceFromEVSToSAP">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="in" type="tns:inType"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TransSupplierBalanceFromEVSToSAPResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element maxOccurs="unbounded" minOccurs="0" name="out" type="tns:outType"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="inType">
        <xsd:sequence>
          <xsd:element name="BUKRS" type="xsd:string"/>
          <xsd:element name="KUNNR" type="xsd:string"/>
          <xsd:element name="FLAG" type="xsd:string"/>
          <xsd:element name="SYSNAME" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="outType">
        <xsd:sequence>
          <xsd:element name="BUKRS" type="xsd:string"/>
          <xsd:element name="BUTXT" type="xsd:string"/>
          <xsd:element name="KUNNR" type="xsd:string"/>
          <xsd:element name="NAME1" type="xsd:string"/>
          <xsd:element name="UMSKZ" type="xsd:string"/>
          <xsd:element name="RACCT" type="xsd:string"/>
          <xsd:element name="TXT50" type="xsd:string"/>
          <xsd:element name="HSLQM" type="xsd:decimal"/>
          <xsd:element name="RTCUR" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
    </xsd:schema>
  </WL5G3N0:types>
  <WL5G3N0:message name="TransSupplierBalanceFromEVSToSAPRequest">
    <WL5G3N0:part element="WL5G3N1:TransSupplierBalanceFromEVSToSAP" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:message name="TransSupplierBalanceFromEVSToSAPResponse">
    <WL5G3N0:part element="WL5G3N1:TransSupplierBalanceFromEVSToSAPResponse" name="parameters"/>
  </WL5G3N0:message>
  <WL5G3N0:portType name="TransSupplierBalanceFromEVSToSAP">
    <WL5G3N0:operation name="TransSupplierBalanceFromEVSToSAP">
      <WL5G3N0:input message="WL5G3N1:TransSupplierBalanceFromEVSToSAPRequest"/>
      <WL5G3N0:output message="WL5G3N1:TransSupplierBalanceFromEVSToSAPResponse"/>
    </WL5G3N0:operation>
  </WL5G3N0:portType>
  <WL5G3N0:binding name="TransSupplierBalanceFromEVSToSAPSOAP" type="WL5G3N1:TransSupplierBalanceFromEVSToSAP">
    <WL5G3N2:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <WL5G3N0:operation name="TransSupplierBalanceFromEVSToSAP">
      <WL5G3N2:operation soapAction="http://www.example.org/TransSupplierBalanceFromEVSToSAP/TransSupplierBalanceFromEVSToSAP"/>
      <WL5G3N0:input>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:input>
      <WL5G3N0:output>
        <WL5G3N2:body use="literal"/>
      </WL5G3N0:output>
    </WL5G3N0:operation>
  </WL5G3N0:binding>
  <WL5G3N0:service name="TransSupplierBalanceFromEVSToSAP">
    <WL5G3N0:documentation>OSB Service</WL5G3N0:documentation>
    <WL5G3N0:port binding="WL5G3N1:TransSupplierBalanceFromEVSToSAPSOAP" name="TransSupplierBalanceFromEVSToSAPSOAP">
      <WL5G3N2:address location="http://************:9001/GVS/GVS/TransSupplierBalanceFromEVSToSAP/TransSupplierBalanceFromEVSToSAP"/>
    </WL5G3N0:port>
  </WL5G3N0:service>
</WL5G3N0:definitions>