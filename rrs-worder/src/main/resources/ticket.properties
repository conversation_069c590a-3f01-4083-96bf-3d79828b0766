##\u81EA\u52A8\u5207\u6362\u914D\u7F6E\u524D\u7F00
prefix = me



## \u7531\u7535\u5B50\u53D1\u7968\u5E73\u53F0\u5206\u914D\u7684 appCode
me.appCode = PT000099
me.cmdName = chinaeinv.api.invoice.v3.kp_async
##query cmd
me.queryCmdName = chinaeinv.api.invoice.v3.cx
##\u9500\u8D27\u65B9\u7EB3\u7A0E\u4EBA\u8BC6\u522B\u53F7
me.taxpayerCode = 91310117MA1J31BJ0W
##\u8BC1\u4E66\u522B\u540D
me.certificateAlias = hjytsgs
##\u8BC1\u4E66\u5BC6\u7801
me.certificatePassword = kytre6sbf
#/Users/<USER>/Documents/rrs/keystore
##\u8BC1\u4E66\u8DEF\u5F84-linux
me.certificatePasswordPath.linux = /Users/<USER>/certificate/PT000099.keystore
##\u8BC1\u4E66\u8DEF\u5F84-windows
me.certificatePasswordPath.windows =  D:/java/PT000099.keystore
##\u94FE\u63A5\u8D85\u65F6\u65F6\u95F4
me.connectTimeOut = 1000
##\u8BFB\u53D6\u8D85\u65F6\u65F6\u95F4
me.readTimeOut = 1000
##\u7A0E\u7387
me.taxRate =  0.09
##\u56DE\u8C03\u63A5\u53E3\u8DEF\u7531
me.callbackUrl =  https://gd.rrskjfw.com.cn/rrs-ticket/rrs/callBack/billingCallback
##\u5F00\u7968\u63A5\u53E3
me.billingUrl = https://www.chinaeinv.com/igs/api/invoiceApi.jspa
##\u751F\u4EA7\u73AF\u5883
me.remark = \u751F\u4EA7\u73AF\u5883
##���ӷ�Ʊ
me.drawer = \u7535\u5b50\u53d1\u7968
##���ӷ�Ʊ
me.unit = EA
##����
me.nums = 1
##\u81EA\u52A8\u5207\u6362\u914D\u7F6E\u524D\u7F00
## \u7531\u7535\u5B50\u53D1\u7968\u5E73\u53F0\u5206\u914D\u7684 appCode
test.appCode = PTTEST17
test.cmdName = chinaeinv.api.invoice.v3.kp_async
test.queryCmdName = chinaeinv.api.invoice.v3.cx
##\u8BC1\u4E66\u522B\u540D
test.certificateAlias = PTTEST17
##\u8BC1\u4E66\u5BC6\u7801
test.certificatePassword = PTTEST17
##\u9500\u8D27\u65B9\u7EB3\u7A0E\u4EBA\u8BC6\u522B\u53F7
test.taxpayerCode = 91370200264807TEST4A
##\u8BC1\u4E66\u8DEF\u5F84-linux
test.certificatePasswordPath.linux = /Users/<USER>/certificate/PTTEST17.keystore
##\u8BC1\u4E66\u8DEF\u5F84-windows
test.certificatePasswordPath.windows = D:/java/PTTEST17.keystore
##\u94FE\u63A5\u8D85\u65F6\u65F6\u95F4
test.connectTimeOut = 60000
##\u8BFB\u53D6\u8D85\u65F6\u65F6\u95F4
test.readTimeOut = 60000
##\u7A0E\u7387
test.taxRate =  0.09
##\u56DE\u8C03\u63A5\u53E3\u8DEF\u7531

#test.callbackUrl = http://1627260d624a.ngrok.io/rrs/callBack/billingCallback
test.callbackUrl = http://106.15.124.15/rrs-ticket/rrs/callBack/billingCallback
#test.callbackUrl = http://gd.rrskjfw.com.cn/rrs-ticket/rrs/callBack/billingCallback
##\u5F00\u7968\u6D4B\u8BD5\u63A5\u53E3
test.billingUrl = https://www.chinaeinv.com:943/igs/api/invoiceApi.jspa
##\u6D4B\u8BD5\u73AF\u5883
test.remark = \u6D4B\u8BD5\u73AF\u5883
##���ӷ�Ʊ
test.drawer = \u7535\u5b50\u53d1\u7968

##���ӷ�Ʊ
test.unit = EA
##����
test.nums = 1

##\u5B89\u88C5\u8D39
name = \u5B89\u88C5\u8D39
code = 3050200000000000000
