/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.utils;

import com.google.gson.Gson;
import io.swagger.models.auth.In;
import org.apache.shiro.dao.DataAccessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 *
 * @<NAME_EMAIL>
 */
@Component
public class RedisUtils {
    @Autowired
    private  RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ValueOperations<String, String> valueOperations;
    @Autowired
    private HashOperations<String, String, Object> hashOperations;
    @Autowired
    private ListOperations<String, Object> listOperations;
    @Autowired
    private SetOperations<String, Object> setOperations;
    @Autowired
    private ZSetOperations<String, Object> zSetOperations;
    /**
     * 默认过期时长，单位：秒
     */
    public final static long DEFAULT_EXPIRE = 60 * 60 * 24;

    /**
     * 默认过期时长，单位：秒
     */
    public final static long SECOND_EXPIRE_30 = 30;
    /**
     * 默认过期时长，单位：秒
     */
    public final static long SECOND_EXPIRE_60 = 60;
    /**
     * 默认过期时长，单位：秒
     */
    public final static long SECOND_EXPIRE_15 = 15;

    public final static long MINUTES_EXPIRE_10 = 600;

    /**
     * 不设置过期时长
     */
    public final static long NOT_EXPIRE = -1;
    private final static Gson gson = new Gson();

    public void set(String key, Object value, long expire) {
        valueOperations.set(key, toJson(value));
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
    }

    public void setWithExpireAt(String key, Object value, long expireAt) {
        valueOperations.set(key, toJson(value));
        if (expireAt != NOT_EXPIRE) {
            redisTemplate.expireAt(key, new Date(expireAt));
        }
    }

    public boolean setIfPresent(String key, Object value, long expire) {
        if (expire == NOT_EXPIRE) {
            return valueOperations.setIfPresent(key, toJson(value));
        } else {
            return valueOperations.setIfPresent(key, toJson(value), expire, TimeUnit.SECONDS);
        }
    }

    public boolean setIfAbsent(String key, Object value, long expire) {
        if (expire == NOT_EXPIRE) {
            return valueOperations.setIfAbsent(key, toJson(value));
        } else {
            return valueOperations.setIfAbsent(key, toJson(value), expire, TimeUnit.SECONDS);
        }
    }


    public Long incrementKey(String key) {
        // 使用 Redis 的 INCR 命令原子性地增加键的值
        return redisTemplate.opsForValue().increment(key, 1);
    }

    public void set(String key, Object value) {
        set(key, value, DEFAULT_EXPIRE);
    }

    public <T> T get(String key, Class<T> clazz, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value == null ? null : fromJson(value, clazz);
    }
    public boolean setOpenValue(String key, Object value, long expire) {

        if (expire != NOT_EXPIRE) {
            return valueOperations.setIfAbsent(key,toJson(value),expire,TimeUnit.SECONDS);
        }
        return  false;
    }
    public boolean setOpenValue(String key, Object value) {
        return setOpenValue(key, value, DEFAULT_EXPIRE);
    }
    public boolean remove(){
        return false;
    }
    public boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }
    public boolean remove(String key) {
        if (exists(key)) {
            Boolean delete = redisTemplate.delete(key);
            return delete;
        }
        return false;
    }

    public <T> T get(String key, Class<T> clazz) {
        return get(key, clazz, NOT_EXPIRE);
    }

    public String get(String key, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value;
    }

    public String get(String key) {
        return get(key, NOT_EXPIRE);
    }

    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * Object转成JSON数据
     */
    private String toJson(Object object) {
        if (object instanceof Integer || object instanceof Long || object instanceof Float ||
                object instanceof Double || object instanceof Boolean || object instanceof String) {
            return String.valueOf(object);
        }
        return gson.toJson(object);
    }

    /**
     * JSON数据，转成Object
     */
    private <T> T fromJson(String json, Class<T> clazz) {
        return gson.fromJson(json, clazz);
    }

    @SuppressWarnings("unchecked")
    public Cursor<String> scan(String pattern, long limit) {
        ScanOptions options = ScanOptions.scanOptions().match(pattern).count(limit).build();
        RedisSerializer<String> redisSerializer = (RedisSerializer<String>) redisTemplate.getKeySerializer();
        return (Cursor) redisTemplate.executeWithStickyConnection(new RedisCallback() {
            @Override
            public Object doInRedis(RedisConnection redisConnection) throws DataAccessException {
                return new ConvertingCursor<>(redisConnection.scan(options), redisSerializer::deserialize);
            }
        });
    }

}
