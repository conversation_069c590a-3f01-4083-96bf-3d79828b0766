/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.lenmoncore.common.utils;

import io.swagger.models.auth.In;
import org.omg.CORBA.INTERNAL;

/**
 * 常量
 *
 * @<NAME_EMAIL>
 */
public class Constant {
    /**
     * 超级管理员ID
     */
    public static final int SUPER_ADMIN = 1;
    /**
     * 当前页码
     */
    public static final String PAGE = "page";
    /**
     * 每页显示记录数
     */
    public static final String LIMIT = "limit";
    /**
     * 排序字段
     */
    public static final String ORDER_FIELD = "sidx";
    /**
     * 排序方式
     */
    public static final String ORDER = "order";
    /**
     * 升序
     */
    public static final String ASC = "asc";

    public static final String Y = "Y";
    public static final String N = "N";

    public static final String XLSX = ".xlsx";
    public static final String XLS = ".xls";

    public static final int NUM_1000 = 1000;

    /**
     * 菜单类型
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2016年11月15日 下午1:24:29
     */
    public enum MenuType {
        /**
         * 目录
         */
        CATALOG(0),
        /**
         * 菜单
         */
        MENU(1),
        /**
         * 按钮
         */
        BUTTON(2);

        private int value;

        MenuType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 定时任务状态
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2016年12月3日 上午12:07:22
     */
    public enum ScheduleStatus {
        /**
         * 正常
         */
        NORMAL(0),
        /**
         * 暂停
         */
        PAUSE(1);

        private int value;

        ScheduleStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 云服务商
     */
    public enum CloudService {
        /**
         * 七牛云
         */
        QINIU(1),
        /**
         * 阿里云
         */
        ALIYUN(2),
        /**
         * 腾讯云
         */
        QCLOUD(3);

        private int value;

        CloudService(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
    /**
     *微信配置参数
     * */
    public static class WECHATCONFIG{
        public static String appid = "";
        public static String mch_id = "";
        //支付场景 APP 微信app支付 JSAPI 公众号支付  NATIVE 扫码支付
        public static String trade_type = "APP";
        public static String  fee_type = "CNY";
        public static String nonce_str = "";
        public static String key = "";
        public static int  httpconnecttimeoutms = 0;
        public static int  httpreadtimeoutms = 0;
        public static String  notify_url = "";
        public static String packageName = "WXPay";
    }

    /**
     *
     * 支付宝配置参数
     * */
    public static class ALIPAYCONFIG{
        public static String gatewayUrl = "https://openapi.alipay.com/gateway.do";
        public static String app_id = "";
        public static String merchant_private_key = "";
        public static String data_type = "json";
        public static String charset = "UTF-8";
        public static String alipay_public_key = "";
        public static String sign_type = "RSA2";
        public static String return_url = "";
        public static String notify_url = "";
    }
    /**
     *
     */
    public static final String SMS_PHONE = "sms-phone";
    /**
     * 字段类型
     * */
    public static class FIELDTYPE{
        public static Integer TEXT_CODE = 1;
        public static Integer SELECT_CODE = 2;
        public static Integer IMG_CODE = 3;
        public static Integer DOC_CODE = 4;
        public static Integer REGION_CODE = 5;
    }
    /**
     * 字段用途1、创建2、勘测3、安装
     * */
    public static class FIELDPURPOSE{
        public static Integer CREATE_CODE = 1;
        public static Integer SURVEY_CODE = 2;
        public static Integer INSTALLATION_CODE = 3;
    }
    /**
     * 字典编码
     * */
    public static class DINUMBER{
        public static String WORDERSOURCETYPE = "worder_source_type";
        public static String WORDERSTATUS = "worder_status";
    }
    /**
     *工单的执行状态
     * */
    public static class WORDEREXCUTESTATUS{
        //未派单
        public static Integer NO_DISPATCH = 0;
        //网点已接单
        public static Integer RECEIVED_ORDERS = 1;
        //待勘测预约
        public static Integer WAIT_SURVEY_APPOINTMENT = 2;
        //待勘测
        public static Integer WAIT_SURVEY = 3;
        //勘测资料未提交
        public static Integer WAIT_SURVEY_NOPUSH = 4;
        //勘测资料提交待审核
        public static Integer SURVEY_PUSH_CHECK = 5;
        //勘测资料整改中
        public static Integer  SURVEY_CHANGE = 6;
        //待客服确认
        public static Integer WAIT_CUS_CONFIRM = 7;
        //勘测资料无误待上传车企
        public static Integer SURVEY_DATA_NOPUSH_CAR  = 8;
        //等待充电桩及配件
        public static Integer ACCESSORIES  = 9;
        //待安装预约
        public static Integer WAIT_INSTALL_APPOINTMENT  =10;
        //待安装
        public static Integer WAIT_INSTALL = 11;
        //安装资料未提交
        public static Integer INSTALL_NOPUSH = 12;
        //安装资料已提交待审核
        public static Integer INSTALL_PUSHED_CHCEK = 13;
        //安装资料整改中
        public static Integer INSTALL_PUSHED_CAHNGE = 14;
        //安装资料待客服确认
        public static Integer INSTALL_CUS_CONFIRM = 15;
        //安装资料无误待上传车企
        public static Integer INSTALL_NOERRO_UP = 16;
        //安装完成
        public static Integer INSTALL_COMPELET = 17;
    }
    /**
     *审核状态
     * */
    public static class CHECKSTATUS{
        public static Integer CHECK_NO = 0; //未审核
        public static Integer CHECK_PASS = 1;//审核通过
        public static Integer CHECK_FAILED = 2; //审核不通过
    }
    /**
     *角色类型
     * */
    public static class ROELTYPE{
        public static Integer ROLE_SERVICE_SOLDIERS = 5;//服务兵
        public static Integer ROLE_SERVER_MANAGER = 3;//服务经理
        public static Integer ROLE_CURTOMER_MANAGER = 2; //客服经理
    }

    /**
     * 字典表数据
     */
    /** 物料类别 */
    public static final String MATERIEL_TYPE  = "materiel_type";

    /** 物料品牌 */
    public static final String MATERIEL_BRAND  = "materiel_brand";

    /** 物料计量单位 */
    public static final String MATERIEL_UNIT  = "materiel_unit";

    /** 技工级别 */
    public static final String TECHNICIAN_LEVEL  = "technician_level";

    public static final String COMPANY_TYPE  = "company_type";
    /** 物料类别描述*/
    public static final String MATERIEL_TYPE_VALUE  = "materiel_type_value";

    /**
     * 验证码状态
     */
    public enum CodeStatus {
        /**
         * 过期
         */
        EXPIRED(0),
        /**
         * 有效
         */
        VALID(1);

        private int value;

        CodeStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
    /**
     * 支付结果的订单状态
     *  `order_status` int(10) NOT NULL DEFAULT '0' COMMENT '订单状态（0:未知 1:支付中 2:支付成功 3:支付失败 4:作废）',
     * */
    public static class PAYRESULTORDERSTATUS{
        //未知
        public static Integer ORDER_UNKNOWN = 0;
        //支付中
        public static Integer ORDER_PAYMENTS = 1;
        //支付成功
        public static Integer ORDER_PAYMENT_SUCCESS = 2;
        //支付失败
        public static Integer ORDER_PAYMENT_FAIL = 3;
        //作废
        public static Integer ORDER_PAYMENT_CANCEL = 4;
    }
    /**
     * 开票类型
     * */
    public static class BILLINGTYPE{
        //个人
        public static Integer TYPE_PERSION = 1;
        //公司
        public static Integer TYPE_COMPANY = 2;
    }

    /**
     * 发送消息
     */
    public static class SMSINFOR{
        /** 请求路径 */
        public static String SENDURL = "http://60.205.131.217:9001/sendSMS.action";
        /** ApiKey */
        public static String APIKEY ="c7bbd677df2a4737a4921aaeafa4e7fd";
        /** 消息id */
        public static String MSGID = "";
        /** 扩展端口 */
        public static String SUBPORT = "";
        /** 定时发送 */
        public static String SENDTIME = "";
    }

    /**
     * 物流
     */
    public static class  LOGISTICSINFOR{
        /** 公司编号 */
        public static String CUSTOMER = "***";
        /** Key */
        public static String KEY ="****";
        /** 请求地址 */
        public static String URL = "http://poll.kuaidi100.com/poll/query.do";
    }

    /**
     * 性别
     */
    public static class  GENDERVALUE{
        /** 男性 */
        public static String  MALE= "男";
        /** 女性 */
        public static String FEMALE ="女";
    }

    /**
     *  开票表 开票状态（0：未知 1:开票中 2:开票成功  3:开票失败 4）
     * */
    public static class  INVOICERECODESTATUS{
        /**  未知 */
        public static Integer STATUS_UNKNOWN = 0;
        /** 未开票 */
        public static Integer STATUS_NO = 1;
        /** 开票中 */
        public static Integer STATUS_LOADING = 2;
        /** 开票成功 */
        public static Integer STATUS_SUCCSESS = 3;
        /** 开票失败 */
        public static Integer  STATUS_FAIL = 4;
        /** 取消 */
        public static Integer STATUS_CANCELl = 5;
    }
    /**
     * 记账状态 记账状态  0:未记账 1：成功 2：失败
     * */
    public static class BOOKKEEPINGSTATUS{
        /**  未知 */
        public static Integer NO = 0;
        /** 未开票 */
        public static Integer SUCCESS = 1;
        /** 开票中 */
        public static Integer FAIL = 2;
    }

    public static class MODEOFPAYMENTTOOL{
        /** 微信 */
        public static String WXPAY="WX";
        /** 阿里支付 */
        public static String ALIPAYALIPAY = "ALIPAY";
    }
    public static class REDISPARAMS{
        public static String ORDERNO = "ORDERNO";
        public static Long COUNT = 100L;
    }
    /**  */
    public static String RETURNCODE_NULL = "-1";
}
